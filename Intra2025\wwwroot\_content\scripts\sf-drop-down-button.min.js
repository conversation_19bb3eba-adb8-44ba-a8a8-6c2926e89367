/*!*  filename: sf-drop-down-button.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[23],{"./bundles/sf-drop-down-button.js":function(e,t,n){"use strict";n.r(t);n("./modules/sf-drop-down-button.js")},"./modules/sf-drop-down-button.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.DropDownButton=function(){"use strict";var e="e-colorpicker-container",t=".e-colorpicker.e-modal",n="mousedown touchstart",s=function(){function s(e,t,n,s,o){this.isPopupClick=!1,this.dataId=s,this.cp_dataId=o,this.element=e,this.dotNetRef=n,this.popup=t,window.sfBlazor.setCompInstance(this),this.addScrollEvents(!0)}return s.prototype.openPopup=function(t){this.popup.style.visibility="hidden",document.body.appendChild(this.popup),t&&sf.splitbuttons.setBlankIconStyle(this.popup),this.popup.classList.remove("e-hidden"),this.popup.classList.remove("e-transparent");var s=sf.popups.getZindexPartial(this.element),o=this.element.parentElement.classList.contains(e);o&&window.sfBlazor.getCompInstance(this.cp_dataId).setPaletteWidth(this.popup.querySelector(".e-container"),!1,s);(this.setPosition(o),sf.base.EventHandler.remove(document,n,this.mouseDownHandler),this.addEventListener(),this.popup.style.zIndex=s+"",this.popup.style.visibility="",o)&&window.sfBlazor.getCompInstance(this.cp_dataId).setOffset(this.popup,s);if(this.popup.firstElementChild)if(this.popup.firstElementChild.firstElementChild&&o)this.popup.firstElementChild.firstElementChild.focus();else{var i=this.popup.firstElementChild.querySelector(".e-palette");i?i.focus():this.popup.firstElementChild.focus()}},s.prototype.setPosition=function(e){var t,n,s,o,i=this.popup.getBoundingClientRect();i.height,i.width;this.popup.style.display="none";var l=this.element.getBoundingClientRect();e&&sf.base.Browser.isDevice?(t=document.documentElement.clientWidth/2-i.width/2+pageXOffset,n=document.documentElement.clientHeight/2-i.height/2+pageYOffset):(t=l.left+pageXOffset,n=l.bottom+pageYOffset,o=l.top+pageYOffset,s=l.left+pageXOffset,l.left+i.width>document.documentElement.clientWidth&&l.left+l.width-i.width>document.documentElement.clientLeft&&(t=t+l.width-i.width),l.top+l.height+i.height>document.documentElement.clientHeight&&(n=o-(i.height+4))<0&&((n=0)+i.height<document.documentElement.clientHeight?t=l.left+l.width+i.width<document.documentElement.clientWidth?s+l.width:s-i.width:n=l.bottom+pageYOffset)),-1!==this.element.id.indexOf("imageEditor")&&sf.base.Browser.isDevice&&(t=document.documentElement.clientWidth/2-i.width/2+pageXOffset),this.popup.style.left=Math.ceil(t)+"px",this.popup.style.top=Math.ceil(n)+"px",this.popup.style.display="block"},s.prototype.mouseDownHandler=function(n){if(this.popup.parentElement){var s=n.target,o=!0,i=this.element.parentElement.classList.contains(e);if(i){var l=window.sfBlazor.getCompInstance(this.cp_dataId);(sf.base.closest(s,".e-apply")||sf.base.closest(s,".e-cancel"))&&l.removeZIndex()}if(!sf.base.Browser.isDevice&&s.classList.contains("e-hsv-model")){var p=s.parentElement.getBoundingClientRect(),r=this.element.getBoundingClientRect();o=n.clientX>=p.left&&n.clientX<=p.right&&n.clientY>=p.top&&n.clientY<=p.bottom||n.clientX>=r.left&&n.clientX<=r.right&&n.clientY>=r.top&&n.clientY<=r.bottom}if(!o||!sf.base.closest(s,"#"+this.getDropDownButton().id)&&!sf.base.closest(n.target,"#"+this.popup.id)&&!sf.base.closest(n.target,t)){if(i)(l=window.sfBlazor.getCompInstance(this.cp_dataId)).removeZIndex();this.dotNetRef.invokeMethodAsync("ButtonClickAsync",null),this.removeEventListener()}}else this.removeEventListener()},s.prototype.keydownHandler=function(t){var n=this.getElement(),s=this.element.parentElement.classList.contains(e),o=this.element.parentElement.classList.contains("e-rtl");s&&window.sfBlazor.getCompInstance(this.cp_dataId).paletteKeyDown(t,o);if(t.altKey)38===t.keyCode&&(t.stopPropagation(),t.preventDefault(),this.dotNetRef.invokeMethodAsync("ButtonClickAsync",null),n.focus(),this.removeEventListener());else{var i=this.popup.firstElementChild;if((27===t.keyCode||9===t.keyCode&&!s)&&(t.stopPropagation(),this.dotNetRef.invokeMethodAsync("ButtonClickAsync",null),27===t.keyCode&&t.preventDefault(),n.focus(),this.removeEventListener()),!i||!i.classList.contains("e-dropdown-menu"))return;if(13===t.keyCode)return t.preventDefault(),void(t.target.classList.contains("e-item")&&t.target.classList.contains("e-focused")?(n.focus(),this.removeEventListener()):t.stopPropagation());38!==t.keyCode&&40!==t.keyCode||(t.target.classList.contains("e-dropdown-menu")&&t.stopPropagation(),t.preventDefault(),sf.splitbuttons.upDownKeyHandler(i,t.keyCode))}},s.prototype.getElement=function(){return this.element.classList.contains("e-split-btn-wrapper")?this.element.firstElementChild:this.element},s.prototype.getDropDownButton=function(){return this.element.classList.contains("e-split-btn-wrapper")?this.element.getElementsByClassName("e-dropdown-btn")[0]:this.element},s.prototype.closePopup=function(){if(this.removeEventListener(),this.isPopupClick&&(this.getElement().focus(),this.isPopupClick=!1),this.element.parentElement.classList.contains(e)&&sf.base.Browser.isDevice){var n=this.popup;if(n&&n.firstElementChild){var s=document.querySelector(t),o=n.querySelector(".e-ctrl-btn");s&&!o&&s.remove()}}},s.prototype.dropDownClickHandler=function(t){if(this.element.parentElement.classList.contains(e)){var n=window.sfBlazor.getCompInstance(this.cp_dataId);sf.base.closest(t.target,".e-dropdown-btn")&&n.removeZIndex()}},s.prototype.scrollHandler=function(t){if(this.popup&&document.getElementById(this.popup.id)){var n=this.element.parentElement.classList.contains(e);if(this.setPosition(n),n)window.sfBlazor.getCompInstance(this.cp_dataId).setOffset(this.popup)}else{var s=this.getDropDownButton();s&&document.getElementById(s.id)||sf.base.EventHandler.remove(t.target,"scroll",this.scrollHandler)}},s.prototype.clickHandler=function(e){this.isPopupClick=!0},s.prototype.addEventListener=function(e){if(sf.base.EventHandler.add(document,n,this.mouseDownHandler,this),sf.base.EventHandler.add(this.popup,"click",this.clickHandler,this),sf.base.EventHandler.add(this.popup,"keydown",this.keydownHandler,this),sf.base.EventHandler.add(this.element,"click",this.dropDownClickHandler,this),e&&this.popup.firstElementChild){var t=this.popup.querySelector(".e-focused");t?t.focus():this.popup.firstElementChild.focus()}},s.prototype.removeEventListener=function(e){if(sf.base.EventHandler.remove(document,n,this.mouseDownHandler),sf.base.EventHandler.remove(this.element,"click",this.dropDownClickHandler),this.popup.parentElement&&(sf.base.EventHandler.remove(this.popup,"click",this.clickHandler),sf.base.EventHandler.remove(this.popup,"keydown",this.keydownHandler),e)){var t=this.getDropDownButton();t&&document.getElementById(t.id)&&(this.addScrollEvents(!1),this.element.appendChild(this.popup))}},s.prototype.addScrollEvents=function(e){for(var t=0,n=sf.popups.getScrollableParent(this.element);t<n.length;t++){var s=n[t];e?sf.base.EventHandler.add(s,"scroll",this.scrollHandler,this):sf.base.EventHandler.remove(s,"scroll",this.scrollHandler)}},s}();return{openPopup:function(e,t,n,o,i,l){var p=window.sfBlazor.getCompInstance(i);sf.base.isNullOrUndefined(e)||(sf.base.isNullOrUndefined(p)?(new s(e,t,n,i,l),p=window.sfBlazor.getCompInstance(i)):p.popup=t,p.openPopup(o))},addEventListener:function(e){var t=window.sfBlazor.getCompInstance(e);t.removeEventListener(),t.addEventListener(!0)},removeEventListener:function(e){window.sfBlazor.getCompInstance(e).removeEventListener(!0)},closePopup:function(e){window.sfBlazor.getCompInstance(e).closePopup()},setIconStyle:function(e,t){var n=window.sfBlazor.getCompInstance(e);sf.splitbuttons.setBlankIconStyle(n.popup,t)}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfdropdownbutton');})})();