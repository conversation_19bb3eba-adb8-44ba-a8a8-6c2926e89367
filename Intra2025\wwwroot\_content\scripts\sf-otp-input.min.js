/*!*  filename: sf-otp-input.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[43],{"./bundles/sf-otp-input.js":function(n,t,o){"use strict";o.r(t);o("./modules/sf-otp-input.js")},"./modules/sf-otp-input.js":function(n,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.OtpInput=function(){"use strict";var n=function(){function n(n){window.sfBlazor=window.sfBlazor,this.updateContext(n),window.sfBlazor.setCompInstance(this)}return n.prototype.updateContext=function(n){sf.base.extend(this,this,n)},n}();return{initialize:function(t){t.dataId&&new n(t)},focusOut:function(n,t){for(var o=0;o<t;o++)n[o].blur()},handleTextSelection:function(n,t){n[t].select()},preventValueChange:function(n,t,o){t[o].value=n},destroy:function(n){n&&window.sfBlazor.getCompInstance(n).destroy()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfotpinput');})})();