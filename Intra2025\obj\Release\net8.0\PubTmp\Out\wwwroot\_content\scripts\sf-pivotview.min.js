/*!*  filename: sf-pivotview.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[46],{"./bundles/sf-pivotview.js":function(e,t,o){"use strict";o.r(t);o("./modules/sf-pivotview.js")},"./modules/sf-pivotview.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.PivotView=function(){"use strict";var e="e-icons",t="e-group-row",o="e-headercell",r="e-format",i="e-pivot-formula",s="e-headercontent",n="e-remove-report",l="e-pivot-toolbar",a="e-list-icon",c="e-popup",d="e-popup-open",u="e-rowsheader",p="e-columnsheader",h="e-cellselectionbackground",f="e-selectionbackground",m="e-table",g="e-dlg-closeicon-btn.e-icon-btn",y="e-pivot-content-loader",v="e-movablescrolldiv",b=function(){function e(e){this.parent=e,this.parent.pivotButtonModule=this}return e.prototype.createPivotButtonDrop=function(){for(var e=0,t=[].slice.call(this.parent.parentElement.querySelectorAll(".e-axis-content"));e<t.length;e++){var o=t[e];new sf.base.Droppable(o,{}),this.unWireEvents(o),this.wireEvents(o)}},e.prototype.setPivotButtonDrag=function(){for(var e=0,t=[].slice.call(this.parent.parentElement.querySelectorAll("."+(this.parent instanceof k?"e-grouping-bar":"e-pivotfieldlist")+"-button"));e<t.length;e++){var o=t[e],r=!!sf.base.closest(o,".e-group-all-fields"),i=o.querySelector(".e-pivot-button");this.parent.pivotButtonModule.createDraggable(r,this.parent instanceof k?i.querySelector(".e-pvt-btn-content"):i.firstElementChild,r?this.parent.commonActionModule.getFieldInfo(i.getAttribute("data-uid")).fieldItem:JSON.parse(i.getAttribute("data-fieldInfo")))}},e.prototype.createDraggable=function(e,t,o){var r=this;this.draggable=new sf.base.Draggable(t,{clone:!0,enableTailMode:!0,enableAutoScroll:!1,helper:this.createDragClone.bind(this,o,e),dragStart:this.onDragStart.bind(this,e),drag:function(e){r.draggable.setProperties({cursorAt:{top:!sf.base.isNullOrUndefined(e.event.targetTouches)||sf.base.Browser.isDevice?60:-20}})},dragStop:this.onDragStop.bind(this,e),abort:this.parent instanceof k?this.parent.groupingBarSettings.allowDragAndDrop&&o.allowDragAndDrop?"":".e-pivot-button":o.allowDragAndDrop?"":".e-pivot-button"}),e||(this.unWireEvents(this.parent instanceof k?sf.base.closest(t,".e-grouping-bar-button"):t),this.wireEvents(this.parent instanceof k?sf.base.closest(t,".e-grouping-bar-button"):t))},e.prototype.createDragClone=function(e,t,o){var r=t?null:sf.base.closest(o.element,".e-pivot-button");e=r?JSON.parse(r.getAttribute("data-fieldInfo")):e;var i=sf.base.createElement("div",{id:this.parent.element.id+"_DragClone",className:"e-button-drag-clone"}),s=sf.base.createElement("span",{className:"e-text-content"});return s.innerText=e?e.caption?e.caption:e.name:"",i.appendChild(s),document.body.appendChild(i),i},e.prototype.onDragStart=function(e,t){var o=this,r=sf.base.closest(t.element,"."+(e?this.parent instanceof k?"e-pivot-button":"e-list-item":"e-pivot-button"));this.parent.dotNetRef.invokeMethodAsync("TriggerNodeDraggingEvent",r.getAttribute("data-uid")).then((function(i){if(i.cancel)o.parent.isDragging=!1,o.draggable.intDestroy(t.event),sf.base.detach(document.getElementById(o.parent.element.id+"_DragClone"));else{o.parent.isDragging=!0;var s=o.parent.fieldList[r.getAttribute("data-uid")],n=["e-rows","e-columns","e-filters"];if(sf.base.addClass([e?o.parent instanceof k?r:r.querySelector(".e-list-text"):r],"e-list-selected"),s&&"CalculatedField"===s.aggregateType)for(var l=0,a=n;l<a.length;l++){var c=a[l];sf.base.addClass([o.parent.parentElement.querySelector("."+c)],"e-drag-restrict")}if(document.getElementById(o.parent.element.id+"_DragClone")){var d=sf.popups.getZindexPartial(r);document.getElementById(o.parent.element.id+"_DragClone").style.zIndex=d?d.toString():"100000"}t.bindEvents(t.dragElement)}}))},e.prototype.onDragStop=function(e,t){this.parent.isDragging=!1;var o=!1;t.target&&t.element&&sf.base.closest(t.element,".e-group-all-fields")&&!sf.base.closest(t.target,".e-droppable")&&(o=!0);var r=sf.base.closest(t.element,"."+(e?this.parent instanceof k?"e-pivot-button":"e-list-item":"e-pivot-button"));this.parent.parentElement.querySelector(".e-list-selected")&&sf.base.removeClass([].slice.call(this.parent.parentElement.querySelectorAll(".e-list-selected")),"e-list-selected");for(var i=0,s=["e-rows","e-columns","e-filters"];i<s.length;i++){var n=s[i];sf.base.removeClass([this.parent.parentElement.querySelector("."+n)],"e-drag-restrict")}sf.base.removeClass([e?this.parent instanceof k?r:r.querySelector(".e-list-text"):r],"e-list-selected"),document.getElementById(this.parent.element.id+"_DragClone")&&sf.base.remove(t.helper?t.helper:document.getElementById(this.parent.element.id+"_DragClone")),document.body.style.cursor="auto",!this.isNodeDropped(e,t,r)||!e&&o||this.nodeStateModified(e,t,o,r.getAttribute("data-uid"))},e.prototype.nodeStateModified=function(e,t,o,r){var i=sf.base.closest(t.target,".e-droppable")?JSON.stringify(sf.base.closest(t.target,".e-droppable").className.split(" ")):void 0,s=e||!t.element.parentElement?void 0:JSON.stringify({isvalue:t.element.parentElement.getAttribute("isvalue")});this.parent.dotNetRef.invokeMethodAsync("OnFieldDropped",window.sfBlazor.getDomObject("target",t.target).xPath,i,s,!(!e&&!o),r)},e.prototype.isNodeDropped=function(e,t,o){var r=!0,i=o.getAttribute("data-uid");if(this.parent.fieldList[i]&&this.parent.fieldList[i].isSelected||!e){var s=e?this.getButton(i):o,n=sf.base.closest(s,".e-droppable"),l=sf.base.closest(t.target,".e-droppable");if(s&&n&&n===l){for(var a=[].slice.call(n.querySelectorAll(".e-pivot-button")),c=sf.base.closest(t.target,".e-pvt-btn-div"),d=void 0,u=-1,p=0;p<a.length;p++)if(a[p].id===s.id&&(d=p),c){var h=c.querySelector(".e-pivot-button");a[p].id===h.id&&(u=p)}(d===u||d===a.length-1&&-1===u)&&(sf.base.removeClass([].slice.call(this.parent.parentElement.querySelectorAll(".e-drop-indicator")),"e-drop-hover"),r=!1)}}return r},e.prototype.getButton=function(e){for(var t=0,o=[].slice.call(this.parent.parentElement.querySelectorAll(".e-pivot-button"));t<o.length;t++){var r=o[t];if(r.id===e)return r}},e.prototype.getButtonPosition=function(e,t){var o=sf.base.closest(e,".e-pvt-btn-div");if(o){o=o.querySelector(".e-pivot-button");for(var r=this.parent instanceof k?this.parent.parentElement.querySelector(".e-chart-grouping-bar"):void 0,i=[].slice.call((r&&"Chart"===this.parent.currentView?r:this.parent.parentElement).querySelector(".e-"+t).querySelectorAll(".e-pivot-button")),s=0,n=i.length;s<n;s++)if(i[s].id===o.id)return s}return-1},e.prototype.wireEvents=function(e){sf.base.EventHandler.add(e,"mouseover",this.parent.commonActionModule.updateDropIndicator,this),sf.base.EventHandler.add(e,"mouseleave",this.parent.commonActionModule.updateDropIndicator,this)},e.prototype.unWireEvents=function(e){sf.base.EventHandler.remove(e,"mouseover",this.parent.commonActionModule.updateDropIndicator),sf.base.EventHandler.remove(e,"mouseleave",this.parent.commonActionModule.updateDropIndicator)},e.prototype.destroy=function(){for(var e=0,t=[].slice.call(this.parent.parentElement.querySelectorAll(".e-axis-content"));e<t.length;e++){var o=t[e];this.unWireEvents(o)}this.draggable&&this.draggable.isDestroyed&&this.draggable.destroy()},e}(),S=function(){function e(e){this.parent=e,this.parent.groupingBarModule=this,this.parent.pivotButtonModule=new b(this.parent)}return e.prototype.updatePivotButtons=function(){this.createPivotButtonDrop(),this.parent.pivotButtonModule.setPivotButtonDrag()},e.prototype.createPivotButtonDrop=function(){for(var e=0,t=[].slice.call(this.parent.element.querySelectorAll(".e-axis-container"));e<t.length;e++){var o=t[e].firstElementChild;this.parent.groupingBarSettings.allowDragAndDrop&&new sf.base.Droppable(o,{}),this.unWireEvent(o),this.wireEvent(o)}},e.prototype.refreshUI=function(){this.parent.element.querySelector(".e-grouping-bar")&&this.updatePivotButtons();var e=this.parent.element.querySelector(".e-pivot-grouping-bar");if(e){e.style.minWidth="400px";var r=e.querySelector(".e-group-rows"),i=e.querySelector(".e-group-values"),n=e.querySelector(".e-group-columns"),l=e.querySelector(".e-group-filters"),a=e.querySelector(".e-left-axis-fields"),c=e.querySelector(".e-right-axis-fields"),d=this.parent.element.querySelector("."+s).querySelector("colgroup").children[0],u=e.offsetWidth-parseInt(d.style.width,10)+"px";if(a.style.minWidth=d.style.width,i.style.width=d.style.width,c.style.width=u,sf.base.select("#"+this.parent.element.id+"_grid",this.parent.element)){var p=this.parent.element.querySelector("."+s).querySelector(".e-columnheader");p.querySelector("."+o).classList.add(t),p.style.height="auto",sf.base.select("#"+this.parent.element.id+"_grid",this.parent.element).querySelector(".e-group-rows")&&p.querySelector(".e-axis-row")?r?(r.style.height=this.parent.element.querySelector("."+s).offsetHeight+"px",sf.base.remove(p.querySelector(".e-axis-row")),p.querySelector("."+t).appendChild(e.querySelector(".e-axis-row"))):(r=sf.base.select("#"+this.parent.element.id+"_grid",this.parent.element).querySelector(".e-group-rows")).style.height="auto":(r.style.height=this.parent.element.querySelector("."+s).offsetHeight+"px",p.querySelector("."+t).appendChild(e.querySelector(".e-axis-row"))),p.querySelector("."+t).querySelector(".e-headercelldiv").style.display="none",p.querySelector("."+t).querySelector(".e-sortfilterdiv").style.display="none";for(var h=0,f=[].slice.call(this.parent.element.querySelector("."+s).querySelector("thead").querySelectorAll("tr")).filter((function(e){return e.childNodes.length>0}));h<f.length;h++){var m=f[h];sf.base.setStyleAttribute(m,{height:"auto"})}var g=this.parent.element.querySelector("."+s).offsetHeight;r.style.height=g+"px",n.style.height=l.style.height="auto";var y=i.offsetHeight/2;y>n.offsetHeight&&(sf.base.setStyleAttribute(l,{height:sf.base.formatUnit(y)}),sf.base.setStyleAttribute(n,{height:sf.base.formatUnit(y+2)}))}}},e.prototype.wireEvent=function(e){sf.base.EventHandler.add(e,"mouseover",this.dropIndicatorUpdate,this),sf.base.EventHandler.add(e,"mouseleave",this.dropIndicatorUpdate,this)},e.prototype.unWireEvent=function(e){sf.base.EventHandler.remove(e,"mouseover",this.dropIndicatorUpdate),sf.base.EventHandler.remove(e,"mouseleave",this.dropIndicatorUpdate)},e.prototype.dropIndicatorUpdate=function(e){(this.parent.isDragging&&e.target.classList.contains("e-droppable")&&"mouseover"===e.type||"mouseleave"===e.type)&&(sf.base.removeClass([].slice.call(this.parent.element.querySelectorAll(".e-drop-indicator")),"e-drop-hover"),sf.base.removeClass([].slice.call(this.parent.element.querySelectorAll(".e-drop-indicator-last")),"e-drop-hover"))},e.prototype.destroy=function(){for(var e=0,t=[].slice.call(this.parent.element.querySelectorAll(".e-axis-container"));e<t.length;e++){var o=t[e];this.unWireEvent(o.firstElementChild)}},e}(),q=function(){function t(e){this.parent=e,this.parent.calculatedFieldModule=this}return t.prototype.getNodeLocation=function(e,t){var o=e.querySelector('li[data-uid="'+CSS.escape(JSON.parse(t))+'"]').getBoundingClientRect();return JSON.stringify([o.top+(window.scrollY||document.documentElement.scrollTop),o.left])},t.prototype.setSelectionRange=function(e){e&&sf.base.select("#"+this.parent.element.id+"droppable").setSelectionRange(e,e),sf.base.select("#"+this.parent.element.id+"droppable").focus()},t.prototype.getIconInfo=function(e,t){var o=document.elementFromPoint(t,e);if(o){if(o.classList.contains(r))return JSON.stringify(r);if(o.classList.contains("e-edit"))return JSON.stringify("e-edit");if(o.classList.contains(n))return JSON.stringify(n);if(o.classList.contains("e-edited"))return JSON.stringify("e-edited")}},t.prototype.emptyFieldName=function(e){sf.base.addClass([document.getElementById(e+"ddlelement")],["e-empty-field","e-pivot-calc-input"]),document.getElementById(e+"ddlelement").focus()},t.prototype.editCalculatedFieldInfo=function(e,t,o,r){var i=sf.base.closest(document.elementFromPoint(o,t),"li");e?("pivot"===this.parent.options.dataType&&(sf.base.addClass(sf.base.select("#"+this.parent.element.id+"calculateddialog").querySelectorAll(".e-edited"),"e-edit"),sf.base.removeClass(sf.base.select("#"+this.parent.element.id+"calculateddialog").querySelectorAll(".e-edited"),"e-edited"),sf.base.addClass([i.querySelector("."+a)],"e-edited"),sf.base.removeClass([i.querySelector("."+a)],"e-edit"),i.querySelector(".e-edited").setAttribute("title",r)),sf.base.select("#"+this.parent.element.id+"droppable").value=i.getAttribute("data-uid")):this.updatedCalculatedFieldInfo(i,r)},t.prototype.updatedCalculatedFieldInfo=function(e,t){"pivot"===this.parent.options.dataType&&(sf.base.addClass(sf.base.select("#"+this.parent.element.id+"calculateddialog").querySelectorAll(".e-edited"),"e-edit"),sf.base.removeClass(sf.base.select("#"+this.parent.element.id+"calculateddialog").querySelectorAll(".e-edited"),"e-edited"),e.querySelector(".e-edit").setAttribute("title",t)),sf.base.select("#"+this.parent.element.id+"droppable").value=""},t.prototype.updateNodeExpandIcons=function(t,o){for(var r=t.querySelector('li[data-uid="'+CSS.escape(o)+'"]'),i=[].slice.call(r.querySelectorAll("li")),s=0;s<i.length;s++){var l=i[s].querySelector(".e-text-content");if("CalculatedField"===i[s].getAttribute("data-type")&&l&&i[s].querySelector("."+a+".e-calc-member")&&!i[s].querySelector("."+n)){var c=sf.base.createElement("span",{className:n+" "+e+" "+a});l.classList.add("e-calcfieldmember"),l.appendChild(c)}(i[s].querySelector(".e-calc-dimension-icon,.e-calc-measure-icon,.e-measure-icon")||i[s].querySelector(".e-dimensionCDB-icon,.e-attributeCDB-icon,.e-hierarchyCDB-icon")||i[s].querySelector(".e-level-members,.e-namedSetCDB-icon"))&&this.createDraggable(i[s])}return this.olapExpand(r)},t.prototype.olapExpand=function(e){if(e&&e.querySelector("."+a)&&e.querySelector(".e-icon-expandable")&&e.querySelector("."+a).className.indexOf("e-folderCDB-icon")>-1){var t=e.querySelector("."+a);return sf.base.removeClass([t],"e-folderCDB-icon"),sf.base.addClass([t],"e-folderCDB-open-icon"),JSON.stringify(!1)}return e&&e.querySelector("."+a)&&e.querySelector(".e-icon-collapsible")&&e.querySelector("."+a).className.indexOf("e-folderCDB-open-icon")>-1?(e=e.querySelector("."+a),sf.base.removeClass([e],"e-folderCDB-open-icon"),sf.base.addClass([e],"e-folderCDB-icon"),JSON.stringify(!1)):JSON.stringify(!0)},t.prototype.updateEditOptions=function(t){var o=[].slice.call(sf.base.selectAll("#"+t+" .e-acrdn-item"));sf.base.addClass(sf.base.selectAll("#"+t),"e-pivot-accord");for(var r=0;r<o.length;r++)if(o[r].querySelector('[data-type="CalculatedField"]')){var i=o[r].querySelector(".e-acrdn-header .e-toggle-icon");sf.base.removeClass([i],"e-toggle-icon"),sf.base.addClass([i],"e-acrdn-header-icon");var s=i.querySelector(".e-tgl-collapse-icon");sf.base.removeClass([s],"e-tgl-collapse-icon"),sf.base.addClass([s],[a,"e-edit"]),i.appendChild(sf.base.createElement("span",{className:n+" "+e+" "+a}))}},t.prototype.updateAccordionLabel=function(e){var t=document.elementFromPoint(JSON.parse(e).ClientX,JSON.parse(e).ClientY),o=t.parentElement.querySelector(".e-label").innerText,r=sf.base.closest(t,".e-acrdn-item").querySelector("[data-field").getAttribute("data-caption"),i=sf.base.closest(t,".e-adaptive-calc-radio-btn").querySelector("input");sf.base.closest(t,".e-acrdn-item").querySelector(".e-label").innerText=r+" ("+o+")",sf.base.closest(t,".e-acrdn-item").querySelector("[data-type").setAttribute("data-type",i.getAttribute("data-value"))},t.prototype.accordionClick=function(e,t,o){var r=document.elementFromPoint(JSON.parse(e),JSON.parse(t));if(sf.base.closest(r,".e-acrdn-header-icon")){var i=sf.base.closest(r,".e-acrdn-header").querySelector(".e-pivot-calc-check input");if(i){var s=void 0,l=sf.base.closest(r,".e-acrdn-header-icon");return l.querySelector(".e-edit")&&r.classList.contains("e-edit")?(sf.base.addClass([l.querySelector("."+a)],"e-edited"),sf.base.removeClass([l.querySelector("."+a)],"e-edit"),s="e-edit"):l.querySelector(".e-edited")&&r.classList.contains("e-edited")?(sf.base.addClass([l.querySelector("."+a)],"e-edit"),sf.base.removeClass([l.querySelector("."+a)],"e-edited"),s="e-edited"):l.querySelector("."+n)&&r.classList.contains(n)&&(s=n),JSON.stringify([i.getAttribute("data-field"),s,i.getAttribute("id").split(o+"_")[1]])}}return JSON.stringify([])},t.prototype.getAccordionValue=function(){for(var e="",t=[].slice.call(document.querySelectorAll(".e-accordion .e-check")),o=0;o<t.length;o++){var r=t[o].parentElement.querySelector("[data-field]").getAttribute("data-field"),i=t[o].parentElement.querySelector("[data-type]").getAttribute("data-type");-1===i.indexOf("CalculatedField")?e=e+'"'+i+"("+r+')"':e+=t[o].parentElement.querySelector("[data-formula]").getAttribute("data-formula")}return""===e?null:e},t.prototype.createFormulaDroppable=function(t,o,s,l,c,d,u){var p=document.getElementById(this.parent.element.id+u);if(p.querySelector("."+i)){new sf.base.Droppable(p.querySelector("."+i),{});for(var h=[].slice.call(p.querySelectorAll(".e-pivot-treeview ul li")),f=0;f<h.length;f++){var m=h[f].getAttribute("data-field");if("olap"===this.parent.options.dataType){h[f].querySelector(".e-measure-icon")&&(h[f].querySelector("."+a).style.display="none");var g=h[f].querySelector(".e-text-content");"CalculatedField"===h[f].getAttribute("data-type")&&g&&h[f].querySelector("."+a+".e-calc-member")&&(g.classList.add("e-calcfieldmember"),g.appendChild(sf.base.createElement("span",{className:n+" "+e+" "+a}))),(h[f].querySelector(".e-calc-dimension-icon,.e-calc-measure-icon,.e-measure-icon")||h[f].querySelector(".e-dimensionCDB-icon,.e-attributeCDB-icon,.e-hierarchyCDB-icon")||h[f].querySelector(".e-level-members,.e-namedSetCDB-icon"))&&this.createDraggable(h[f])}else if("pivot"===this.parent.options.dataType&&!h[f].querySelector(".e-text-content span.e-drag")&&h[f].querySelector(".e-text-content span")){var y=h[f].getAttribute("data-type"),v=sf.base.createElement("span",{attrs:{tabindex:"-1","aria-disabled":"false",title:o},className:e+" e-drag"}),b=sf.base.createElement("div",{className:" e-iconspace"});sf.base.prepend([v],h[f].querySelector(".e-text-content")),sf.base.append([b,h[f].querySelector("."+r)],h[f].querySelector(".e-text-content")),"CalculatedField"===y&&(h[f].querySelector("."+r).setAttribute("title",s),sf.base.addClass([h[f].querySelector("."+r)],n),c&&d===m&&(sf.base.select("#"+this.parent.element.id+"droppable",p).value=h[f].getAttribute("data-uid")),sf.base.addClass([h[f].querySelector(".e-iconspace")],[c&&d===m?"e-edited":"e-edit",e,a]),h[f].querySelector(".e-edit,.e-edited").setAttribute("title",c&&d===m?l:t),h[f].querySelector(".e-edit,.e-edited").setAttribute("aria-disabled","false"),h[f].querySelector(".e-edit,.e-edited").setAttribute("tabindex","-1"),sf.base.removeClass([h[f].querySelector("."+r)],r),sf.base.removeClass([h[f].querySelector(".e-iconspace")],"e-iconspace")),this.createDraggable(v)}}}},t.prototype.createDraggable=function(e){new sf.base.Draggable(e,{dragTarget:".e-list-item",clone:!0,enableTailMode:!0,enableAutoScroll:!1,helper:this.calculatedDragClone.bind(this),dragStart:this.onCalcDragStart.bind(this),drag:this.onDragging.bind(this),dragStop:this.onCalcDragStop.bind(this)})},t.prototype.onDragging=function(e){var t=sf.base.select("#"+this.parent.element.id+"_DragClone ");e.target&&e.target.classList.contains(i)?(sf.base.removeClass([t],"e-drag-restrict"),sf.base.addClass([e.target],"e-copy-drop")):(sf.base.addClass([t],"e-drag-restrict"),sf.base.removeClass([e.target],"e-copy-drop"),sf.base.addClass([t],"e-icon-expandable"),sf.base.removeClass([t],a))},t.prototype.onCalcDragStop=function(e){var t=document.getElementById(this.parent.element.id+"droppable"),o=sf.base.closest(e.element,".e-list-item");sf.base.removeClass([t],"e-copy-drop"),sf.base.removeClass([o.querySelector(".e-list-text")],"e-list-selected");var r=t.selectionStart;e.target.id===this.parent.element.id+"droppable"&&this.parent.dotNetRef.invokeMethodAsync("UpdateCalcDroppable",o.getAttribute("data-uid"),r,e.target.id),document.getElementById(this.parent.element.id+"_DragClone")&&sf.base.remove(e.helper),document.body.style.cursor="auto",this.parent.isDragging=!1},t.prototype.onCalcDragStart=function(e){var t=sf.base.closest(e.element,".e-list-item"),o=document.getElementById(this.parent.element.id+"_DragClone");if(o&&t&&("pivot"===this.parent.options.dataType&&e.target&&e.target.classList.contains("e-drag")||"olap"===this.parent.options.dataType&&(t.querySelector(".e-calc-dimension-icon,.e-calc-measure-icon,.e-measure-icon")||t.querySelector(".e-dimensionCDB-icon,.e-attributeCDB-icon,.e-hierarchyCDB-icon")||t.querySelector(".e-level-members,.e-namedSetCDB-icon")))){sf.base.addClass([t.querySelector(".e-list-text")],"e-list-selected");var r=sf.popups.getZindexPartial(t);o.style.zIndex=r?r.toString():document.querySelector(".e-pivot-calc-dialog-div.e-olap-calc-dialog-div").style.zIndex+1,o.style.display="inline",this.parent.isDragging=!0}else this.parent.isDragging=!1;e.bindEvents(e.dragElement)},t.prototype.calculatedDragClone=function(t){var o=sf.base.createElement("div",{id:this.parent.element.id+"_DragClone",className:"e-drag-item e-treeview e-pivot-calc"}),r=sf.base.createElement("div",{className:"e-text-content e-calcfieldmember"});return r.appendChild(sf.base.createElement("div",{className:e})),r.appendChild(sf.base.createElement("span",{className:"e-list-text"})),r.innerText=sf.base.closest(t.element,"li").getAttribute("data-caption"),o.appendChild(r),document.body.appendChild(o),o},t}(),w=function(){function e(e){this.parent=e,this.parent.toolbarModule=this}return e.prototype.renderToolbar=function(){this.parent.element.querySelector("."+l)&&this.updateItemElements(this.parent.element.querySelector("."+l))},e.prototype.updateItemElements=function(e){for(var t=0,o=[].slice.call(e.querySelectorAll(".e-toolbar-item"));t<o.length;t++){var r=o[t];r.querySelector("button")?r.classList.contains("e-overlay")||r.querySelector("button").setAttribute("tabindex","0"):r.querySelector(".e-menu.e-menu-parent")&&(r.querySelector(".e-menu.e-menu-parent").setAttribute("tabindex","-1"),!r.classList.contains("e-overlay")&&r.querySelector(".e-menu-item.e-menu-caret-icon")&&r.querySelector(".e-menu-item.e-menu-caret-icon").setAttribute("tabindex","0"))}},e.prototype.focusToolBar=function(){sf.base.removeClass(document.querySelector("."+l).querySelectorAll(".e-menu-item.e-focused"),"e-focused"),sf.base.removeClass(document.querySelector("."+l).querySelectorAll(".e-menu-item.e-selected"),"e-selected"),document.querySelector(".e-toolbar-items")&&sf.base.addClass([document.querySelector(".e-toolbar-items")],"e-focused")},e.prototype.selectInputRange=function(e){if(e.querySelector(".e-pivotview-report-input")){var t=e.querySelector(".e-pivotview-report-input input");t.setSelectionRange(t.value.length,t.value.length)}},e.prototype.copyMdxQuery=function(e){if(e.querySelector(".e-mdx-query-content")){var t=e.querySelector(".e-mdx-query-content");try{t.select(),document.execCommand("copy")}catch(e){window.alert("Oops, unable to copy")}}else;},e}(),C=function(){function t(e){this.parent=e,this.parent.treeRendererModule=this}return t.prototype.updateFieldListIcons=function(e,t){if(this.parent.fieldList)for(var o=document.getElementById(e),r=[].slice.call(o.querySelectorAll("ul li")),i=0;i<r.length;i++){var s=r[i].getAttribute("data-uid");this.updateTreeNode(r[i],s,t)}},t.prototype.updateTreeNode=function(t,o,r){var i=!1;i="olap"!==this.parent.options.dataType||this.updateOlapTreeNode(t);var s=t.querySelector(".e-text-content");if(t.querySelector("."+a)&&s){var n=t.querySelector("."+a);s.insertBefore(n,t.querySelector(".e-list-text"))}if(i&&!this.parent.isAdaptive){var l=this.parent.commonActionModule.getFieldInfo(o);i=!1;var c=sf.base.createElement("span",{attrs:{tabindex:"-1","aria-disabled":"false"}});c.appendChild(sf.base.createElement("span",{attrs:{tabindex:"-1",title:l.fieldItem?l.fieldItem.allowDragAndDrop?r:"":r,"aria-disabled":"false"},className:e+" e-drag "+(l.fieldItem?l.fieldItem.allowDragAndDrop?"":"e-disable-drag":"")})),t.querySelector(".e-checkbox-wrapper")&&!t.querySelector(".e-drag")&&s&&s.appendChild(c),this.parent.pivotButtonModule.createDraggable(!0,c,l.fieldItem)}t.querySelector(".e-check")&&sf.base.addClass([t.querySelector(".e-list-text")],"e-selected-node")},t.prototype.updateOlapTreeNode=function(e){if("olap"!==this.parent.options.dataType||!e)return!0;var t=e.querySelector(".e-text-content");return!!(t.querySelector(".e-hierarchyCDB-icon,.e-attributeCDB-icon,.e-namedSetCDB-icon")||t.querySelector(".e-measure-icon,.e-kpiGoal-icon,.e-kpiStatus-icon,.e-kpiTrend-icon,.e-kpiValue-icon")||t.querySelector(".e-calc-measure-icon,.e-calc-dimension-icon"))},t}(),A=function(){function e(e,t,o,r){window.sfBlazor=window.sfBlazor,this.element=t,this.dotNetRef=r,this.dataId=e,window.sfBlazor.setCompInstance(this),this.getOptions(t,o)}return e.prototype.getOptions=function(e,t){this.element=e,this.options=t,this.parentElement=document.getElementById(e.id+"_Dialog_Container"),this.isAdaptive=sf.base.Browser.isDevice,this.pivotGridModule=t.pivotGridModule?window.sfBlazor.getCompInstance(t.pivotGridModule):this.pivotGridModule,this.pivotGridModule&&sf.base.isNullOrUndefined(this.pivotGridModule.pivotFieldListModule)&&(this.pivotGridModule.pivotFieldListModule=this),this.fieldList=t.fieldList?t.fieldList:this.fieldList,this.dataSourceSettings=t.dataSourceSettings,this.parentElement&&sf.base.select("#"+this.parentElement.id+"_title",this.parentElement)&&sf.base.setStyleAttribute(sf.base.select("#"+this.parentElement.id+"_title",this.parentElement),{width:t.allowDeferLayoutUpdate&&this.isAdaptive?"auto":"100%"})},e.prototype.initModules=function(){this.treeRendererModule=new C(this),this.commonActionModule=new L(this),this.pivotButtonModule=new b(this),this.options.allowCalculatedField&&(this.calculatedFieldModule=new q(this)),this.unWireEvents(),this.wireEvents()},e.prototype.contentReady=function(){this.initModules(),this.pivotButtonModule&&(this.pivotButtonModule.createPivotButtonDrop(),this.pivotButtonModule.setPivotButtonDrag()),this.parentElement&&sf.base.select("#"+this.parentElement.id+"_title",this.parentElement)&&sf.base.setStyleAttribute(sf.base.select("#"+this.parentElement.id+"_title",this.parentElement),{width:"100%"})},e.prototype.onShowFieldList=function(e,t){e.querySelector(".e-toggle-field-list")&&sf.base.addClass([e.querySelector(".e-toggle-field-list")],"e-hide"),t.style.top=parseInt(t.style.top,10)<0?"0px":t.style.top},e.prototype.removeFieldListIcon=function(e){document.getElementById(e.id+"calculateddialog")&&!document.getElementById(e.id+"calculateddialog").classList.contains("e-popup-close")||!e.querySelector(".e-toggle-field-list")||sf.base.removeClass([e.querySelector(".e-toggle-field-list")],"e-hide")},e.prototype.updateFieldList=function(e){var t=e.querySelectorAll(".e-footer-content")[1];sf.base.select("#"+e.id+"_dialog-content",e)?(sf.base.setStyleAttribute(sf.base.select("#"+e.id+"_dialog-content",e),{padding:"0"}),t&&t.querySelector(".e-field-list-footer-content")&&sf.base.addClass([t],"e-field-list-footer")):e.querySelector(".e-adaptive-container").appendChild(t)},e.prototype.updateSelectedNodes=function(e,t){e=sf.base.closest(e,".e-text-content"),"check"===t?sf.base.addClass([e.querySelector(".e-list-text")],"e-selected-node"):sf.base.removeClass([e.querySelector(".e-list-text")],"e-selected-node");var o=sf.base.closest(e,"li");if(o&&o.querySelector("ul"))for(var r=0,i=[].slice.call(o.querySelectorAll("li"));r<i.length;r++){var s=i[r];"check"===t?sf.base.addClass([s.querySelector(".e-list-text")],"e-selected-node"):sf.base.removeClass([s.querySelector(".e-list-text")],"e-selected-node")}},e.prototype.removeFocusedElements=function(){this.element.querySelectorAll(".e-btn-focused").length>0&&sf.base.removeClass(this.element.querySelectorAll(".e-btn-focused"),"e-btn-focused")},e.prototype.wireEvents=function(){sf.base.EventHandler.add(document,"click",this.removeFocusedElements,this)},e.prototype.unWireEvents=function(){sf.base.EventHandler.remove(document,"click",this.removeFocusedElements)},e.prototype.destroy=function(e){this.unWireEvents(),this.commonActionModule.destroy(),this.pivotButtonModule.destroy(),this.isDestroyed=e},e}(),E=function(){function e(e){this.keyConfigs={shiftF:"shift+F",shiftS:"shift+S",shiftE:"shift+E",delete:"delete",enter:"enter",escape:"escape",upArrow:"upArrow",downArrow:"downArrow"},this.parent=e,this.parent.parentElement.tabIndex=-1===this.parent.parentElement.tabIndex?0:this.parent.parentElement.tabIndex,this.keyboardModule=new sf.base.KeyboardEvents(this.parent.parentElement,{keyAction:this.keyActionHandler.bind(this),keyConfigs:this.keyConfigs,eventName:"keydown"})}return e.prototype.keyActionHandler=function(e){switch(e.action){case"shiftF":this.processFilter(e);break;case"shiftS":this.processSort(e);break;case"shiftE":this.processEdit(e);break;case"delete":this.processDelete(e);break;case"enter":this.processEnter(e);break;case"escape":this.processClose(e);break;case"upArrow":case"downArrow":this.processFilterNodeSelection(e)}},e.prototype.getButtonElement=function(e){for(var t=[].slice.call(this.parent.parentElement.querySelectorAll(".e-pivot-button")),o=0,r=t.length;o<r;o++)if(t[o].getAttribute("data-uid")===e.getAttribute("data-uid"))return t[o];return e},e.prototype.processEnter=function(e){var t=e.target;if(t&&sf.base.closest(t,".e-pivot-button"))return t.querySelector(".e-dropdown-icon")&&sf.base.closest(t,".e-values")?t.querySelector(".e-dropdown-icon").click():t.querySelector(".e-edit")?t.querySelector(".e-edit").click():!t.querySelector(".e-sort")||sf.base.closest(t,".e-values")||sf.base.closest(t,".e-axis-filter")?t.querySelector(".e-btn-filter")&&!sf.base.closest(t,".e-values")&&t.querySelector(".e-btn-filter").click():(t.querySelector(".e-sort").click(),this.getButtonElement(t).focus()),e.stopPropagation(),void e.preventDefault()},e.prototype.processSort=function(e){var t=e.target;if(t&&sf.base.closest(t,".e-pivot-button")&&t.querySelector(".e-sort")&&!sf.base.closest(t,".e-values")&&!sf.base.closest(t,".e-axis-filter"))return t.querySelector(".e-sort").click(),this.getButtonElement(t).focus(),e.stopPropagation(),void e.preventDefault()},e.prototype.processEdit=function(e){var t=e.target;if(t&&sf.base.closest(t,".e-pivot-button")&&t.querySelector(".e-edit"))return t.querySelector(".e-edit").click(),e.stopPropagation(),void e.preventDefault()},e.prototype.processFilter=function(e){var t=e.target;if(t&&sf.base.closest(t,".e-pivot-button")&&t.querySelector(".e-btn-filter")&&!sf.base.closest(t,".e-values"))return t.querySelector(".e-btn-filter").click(),e.stopPropagation(),void e.preventDefault()},e.prototype.processFilterNodeSelection=function(e){var t=e.target;if(t&&sf.base.closest(t,".e-select-all")&&40===e.keyCode){if((o=sf.base.closest(t,".e-member-editor-outer-container").querySelector(".e-member-editor-container"))&&o.querySelector("li"))return(i=o.querySelector("li"))&&sf.base.removeClass([i],["e-node-focus"]),sf.base.addClass([i],["e-node-focus"]),i.focus(),e.stopPropagation(),void e.preventDefault()}else if(t&&sf.base.closest(t,".e-member-editor-container")&&38===e.keyCode){var o;if((o=sf.base.closest(t,".e-member-editor-container")).querySelector("li.e-node-focus")&&o.querySelector("li")&&o.querySelector("li")===o.querySelector("li.e-node-focus")){sf.base.removeClass(o.querySelectorAll("li.e-prev-active-node"),"e-prev-active-node");var r=sf.base.closest(t,".e-member-editor-outer-container").querySelector(".e-select-all");if(r&&r.querySelector("li")){var i=r.querySelector("li");return sf.base.addClass([i],["e-node-focus"]),i.focus(),e.stopPropagation(),void e.preventDefault()}}}},e.prototype.processDelete=function(e){var t=e.target;if(t&&sf.base.closest(t,".e-pivot-button")&&t.querySelector(".e-remove"))return t.querySelector(".e-remove").click(),e.stopPropagation(),void e.preventDefault()},e.prototype.processClose=function(e){var t=e.target;if(t&&sf.base.closest(t,"."+c+"."+d)){var o=sf.base.closest(t,"."+c+"."+d).blazor__instance,r=this;if(o&&!o.closeOnEscape){var i=o.element.getAttribute("data-fieldName");o.dotNetRef.invokeMethodAsync("CloseDialog",{altKey:e.altKey,ctrlKey:e.ctrlKey,code:e.code,key:e.key,location:e.location,repeat:e.repeat,shiftKey:e.shiftKey,metaKey:e.metaKey,type:e.type}).then((function(){if(r.parent.parentElement)for(var t=0,o=[].slice.call(r.parent.parentElement.querySelectorAll(".e-pivot-button"));t<o.length;t++){var s=o[t];if(s.getAttribute("data-uid")===i){s.focus();break}}e.preventDefault()}))}else this.closeDialog(sf.base.closest(t,"."+c+"."+d),e)}},e.prototype.closeDialog=function(e,t){return e.querySelector("."+g)?(e.querySelector("."+g).click(),void t.preventDefault()):e.querySelector(".e-cancel-btn")?(e.querySelector(".e-cancel-btn").click(),void t.preventDefault()):void 0},e.prototype.destroy=function(){this.keyboardModule&&this.keyboardModule.destroy()},e}(),L=function(){function e(e){this.parent=e,this.parent.commonActionModule=this,this.keyboardModule=new E(e),this.getElementInfo()}return e.prototype.getElementInfo=function(){if(this.parent instanceof k){var e=this.parent.element.getBoundingClientRect();this.parent.dotNetRef.invokeMethodAsync("GetElementInfo",e.width,e.height,this.parent.element.clientWidth,this.parent.element.clientHeight)}},e.prototype.updateActiveNode=function(e,t){var o=e.querySelectorAll("li.e-node-focus");return sf.base.removeClass(e.querySelectorAll("li.e-prev-active-node"),"e-prev-active-node"),sf.base.addClass([e.querySelector('li[data-uid="'+t+'"]')],"e-prev-active-node"),o&&o.length>0&&e.querySelectorAll("li")[0]===o[0]},e.prototype.isFullRowElement=function(e,t){var o=document.elementFromPoint(t,e),r=o.classList.contains("e-fullrow");return JSON.stringify(this.parent instanceof A&&sf.base.closest(o,".e-field-list-tree-outer-div")?r&&!sf.base.isNullOrUndefined(o.parentElement.querySelector(".e-drag")):r)},e.prototype.validateInputs=function(e){var t=e.value1,o=e.value2,r=e.condition,i=e.type;if(sf.base.isNullOrUndefined(t)||""===t||["Between","NotBetween"].indexOf(r)>-1&&(sf.base.isNullOrUndefined(o)||""===o)){var s=sf.base.select("#"+this.parent.element.id+"_"+i.toLowerCase()+(sf.base.isNullOrUndefined(t)||""===t?"_input_option_1":"_input_option_2"),this.parent.parentElement);sf.base.addClass([s],"e-empty-field"),s.focus()}},e.prototype.updateDropIndicator=function(e){this.parent.isDragging&&e.target.classList.contains("e-axis-content")&&"mouseover"===e.type?(sf.base.removeClass([].slice.call(this.parent.parentElement.querySelectorAll(".e-drop-indicator")),"e-drop-hover"),sf.base.removeClass([].slice.call(this.parent.parentElement.querySelectorAll(".e-drop-indicator-last")),"e-drop-hover"),(t=[].slice.call(e.target.querySelectorAll(".e-pvt-btn-div"))).length>0&&sf.base.addClass([t[t.length-1].querySelector(".e-drop-indicator-last")],"e-drop-hover")):this.parent instanceof A&&(!this.parent.isDragging||!e.target.classList.contains("e-droppable")&&"mouseleave"===e.type)&&(sf.base.removeClass([].slice.call(this.parent.parentElement.querySelectorAll(".e-drop-indicator")),"e-drop-hover"),sf.base.removeClass([].slice.call(this.parent.parentElement.querySelectorAll(".e-drop-indicator-last")),"e-drop-hover"));if(this.parent.isDragging&&sf.base.closest(e.target,".e-pvt-btn-div")&&"mouseover"===e.type&&(sf.base.removeClass([].slice.call(this.parent.parentElement.querySelectorAll(".e-drop-indicator-last")),"e-drop-hover"),sf.base.removeClass([].slice.call(this.parent.parentElement.querySelectorAll(".e-drop-indicator")),"e-drop-hover"),sf.base.closest(e.target,".e-droppable"))){var t=sf.base.closest(e.target,".e-pvt-btn-div");sf.base.addClass([t.querySelector(".e-drop-indicator")],"e-drop-hover")}},e.prototype.getFieldInfo=function(e,t){for(var o=[this.parent.dataSourceSettings.rows,this.parent.dataSourceSettings.columns,this.parent.dataSourceSettings.values,this.parent.dataSourceSettings.filters],r=0,i=o.length;r<i;r++)for(var s=0,n=o[r]?o[r].length:0;s<n&&!t;s++)if(o[r][s]&&o[r][s].name===e)return{fieldName:e,fieldItem:o[r][s],axis:0===r?"rows":1===r?"columns":2===r?"values":"filters",position:s};var l=this.parent.fieldList[e];return{fieldName:e,fieldItem:l||t?{name:e,caption:l?l.caption:e,baseField:l?l.baseField:void 0,baseItem:l?l.baseItem:void 0,isCalculatedField:!!l&&l.isCalculatedField,isNamedSet:!!l&&l.isNamedSets,showNoDataItems:!!l&&l.showNoDataItems,showSubTotals:!!l&&l.showSubTotals,type:l?l.aggregateType:void 0,showFilterIcon:!!l&&l.showFilterIcon,showSortIcon:!!l&&l.showSortIcon,showRemoveIcon:!l||l.showRemoveIcon,showValueTypeIcon:!!l&&l.showValueTypeIcon,showEditIcon:!!l&&l.showEditIcon,allowDragAndDrop:!l||l.allowDragAndDrop}:void 0,axis:t?"row"===this.parent.dataSourceSettings.valueAxis?"rows":"columns":"fieldlist",position:-1}},e.prototype.getAggregateIcoPosition=function(e){var t=this.parent instanceof k?this.parent.parentElement.querySelector(".e-chart-grouping-bar"):void 0,o=(t&&"Chart"===this.parent.currentView?t:this.parent.parentElement).querySelector(".e-pivot-button."+e);if(o&&o.querySelector(".e-dropdown-icon")){var r=o.querySelector(".e-dropdown-icon").getBoundingClientRect(),i={};return i.top=r.top,i.left=r.left,i.bottom=r.bottom,i.right=r.right,JSON.stringify(i)}return null},e.prototype.destroy=function(){this.keyboardModule.destroy()},e}(),B=function(){function o(e){this.keyConfigs={tab:"tab",shiftTab:"shift+tab",enter:"enter",shiftEnter:"shift+enter",ctrlEnter:"ctrl+enter",ctrlShiftF:"ctrl+shift+f"},this.parent=e,this.event=void 0,this.parent.element.tabIndex=-1===this.parent.element.tabIndex?0:this.parent.element.tabIndex,this.pivotViewKeyboardModule=new sf.base.KeyboardEvents(this.parent.element,{keyAction:this.keyActionHandler.bind(this),keyConfigs:this.keyConfigs,eventName:"keydown"})}return o.prototype.keyActionHandler=function(e){var t=e.target;if(!t||!sf.base.closest(t,".e-pivotfieldlist-container"))switch(e.action){case"tab":this.processTab(e);break;case"shiftTab":this.processShiftTab(e);break;case"enter":case"shiftEnter":case"ctrlEnter":this.processEnter(e);break;case"ctrlShiftF":this.toggleFieldList(e)}},o.prototype.getAdjacentButton=function(e,t){var o=this.allpivotButtons(e);if(sf.base.removeClass(o,"e-btn-focused"),this.parent.internalGrid&&o)for(var r=o.length,i=0;i<r;i++)if(o[i].getAttribute("data-uid")===e.getAttribute("data-uid")){var s="next"===t?o[i+1]:o[i-1];return s||e}return e},o.prototype.allpivotButtons=function(e){var t=[];if(e&&this.parent.options.showGroupingBar){var o=sf.base.closest(e,".e-pivot-grouping-bar"),r=sf.base.closest(e,".e-group-pivot-rows"),i=sf.base.closest(e,".e-chart-grouping-bar"),s=e.classList.contains(u),n=void 0,l=void 0,a=void 0;if(null!==o?l=o.classList.contains("e-pivot-grouping-bar"):null!==r?a=r.classList.contains("e-group-pivot-rows"):null!==i&&(n=i.classList.contains("e-chart-grouping-bar")),l||a||s){var c=[].slice.call(this.parent.element.querySelector(".e-pivot-grouping-bar").querySelectorAll(".e-pivot-button")),d=[].slice.call(this.parent.element.querySelector(".e-group-pivot-rows").querySelectorAll(".e-pivot-button"));t=c.concat(d)}else n&&(t=[].slice.call(this.parent.element.querySelector(".e-chart-grouping-bar").querySelectorAll(".e-pivot-button")))}return t},o.prototype.processTab=function(e){var o=this,r=e.target;if(r&&(sf.base.closest(r,".e-pivot-button")||r.classList.contains(t))){if(this.parent.internalGrid){if(r.parentElement.parentElement.classList.contains("e-group-rows")&&r.classList.contains("e-btn-focused")){var i=this.getAdjacentButton(r,"next");return i.getAttribute("data-uid")!==r.getAttribute("data-uid")&&(sf.base.addClass([i],"e-btn-focused"),i.focus()),e.stopPropagation(),void e.preventDefault()}var s=this.getAdjacentButton(r,"next");return s.getAttribute("data-uid")!==r.getAttribute("data-uid")&&(sf.base.addClass([s],"e-btn-focused"),s.focus()),void e.preventDefault()}}else r&&sf.base.closest(r,"."+l)&&this.parent.options.showToolbar&&(clearTimeout(this.timeOutObj),this.timeOutObj=setTimeout((function(){var e=o.getAdjacentToolbarItem(document.activeElement,"next");sf.base.removeClass(sf.base.closest(r,"."+l).querySelectorAll(".e-menu-item.e-focused"),"e-focused"),e&&e.classList.contains("e-menu-item")&&sf.base.addClass([e],"e-focused")})))},o.prototype.getAdjacentToolbarItem=function(e,t){if(e.classList.contains("e-menu-item")&&e.classList.contains("e-focused")){var o=sf.base.closest(e,".e-toolbar-item"),r=[].slice.call(sf.base.closest(e,"."+l).querySelectorAll(".e-toolbar-item:not(.e-overlay)"));if("next"===t){for(var i=0;i<r.length;i++)if(o.id===r[i].id)return this.getActiveElement(r[i+1])}else for(i=r.length-1;i>-1;i--)if(o.id===r[i].id)return this.getActiveElement(r[i-1])}return e},o.prototype.getActiveElement=function(e){if(e){if("button"===e.firstElementChild.tagName.toLowerCase())return e.firstElementChild.focus(),e.firstElementChild;if(e.querySelector(".e-menu-item"))return e.querySelector(".e-menu-item").focus(),e.querySelector(".e-menu-item")}return document.activeElement},o.prototype.processShiftTab=function(e){var o=this,r=e.target;if(r&&(sf.base.closest(r,".e-pivot-button")||r.classList.contains(t))){if(this.parent.internalGrid){if(r.classList.contains(t)&&r.querySelector(".e-btn-focused"))r=r.querySelector(".e-btn-focused");else if(r.classList.contains(t)){r=this.parent.element.querySelector(".e-btn-focused")?this.parent.element.querySelector(".e-btn-focused"):this.parent.element.querySelector(".e-pivot-grouping-bar");var i=this.allpivotButtons(r);if(i.length>0&&i[i.length-1])return i[i.length-1].focus(),sf.base.removeClass(i,"e-btn-focused"),sf.base.addClass([i[i.length-1]],"e-btn-focused"),e.stopPropagation(),void e.preventDefault()}var s=this.getAdjacentButton(r,"previous");if(s.getAttribute("data-uid")!==r.getAttribute("data-uid"))return s.focus(),e.stopPropagation(),void e.preventDefault()}}else if(r&&this.parent.internalGrid&&(r.classList.contains("e-movablefirst")||r.classList.contains(u)&&sf.base.closest(r,"tr").getAttribute("data-uid")===this.parent.internalGrid.element.querySelector(".e-frozencontent tr").getAttribute("data-uid"))){var n=this.allpivotButtons(r);if(n.length>0)return setTimeout((function(){n[n.length-1].focus()})),sf.base.removeClass(n,"e-btn-focused"),sf.base.addClass([n[n.length-1]],"e-btn-focused"),e.stopPropagation(),void e.preventDefault()}else r&&sf.base.closest(r,"."+l)&&this.parent.options.showToolbar&&(clearTimeout(this.timeOutObj),this.timeOutObj=setTimeout((function(){var e=o.getAdjacentToolbarItem(document.activeElement,"previous");sf.base.removeClass(sf.base.closest(r,"."+l).querySelectorAll(".e-menu-item.e-focused"),"e-focused"),e&&e.classList.contains("e-menu-item")&&sf.base.addClass([e],"e-focused")})))},o.prototype.processEnter=function(t){var o=t.target;if(o&&sf.base.closest(o,".e-grid"))return 13!==t.keyCode||t.shiftKey||t.ctrlKey?13===t.keyCode&&t.shiftKey&&!t.ctrlKey?this.parent.options.enableValueSorting&&(this.event=t,o.click()):13===t.keyCode&&!t.shiftKey&&t.ctrlKey&&this.parent.options.hyperlinkSettings&&o.querySelector("a")&&o.querySelector("a").click():o.querySelector("."+e)?(this.event=t,o.querySelector("."+e).click()):o.classList.contains("e-valuescontent")&&o.dispatchEvent(new MouseEvent("dblclick",{view:window,bubbles:!0,cancelable:!0})),void t.preventDefault()},o.prototype.toggleFieldList=function(e){var t=this.parent,o=e.target;if(t&&t.element&&t.options.showFieldList&&t.pivotFieldListModule){t.pivotFieldListModule=t.pivotFieldListModule&&t.pivotFieldListModule.blazor__instance?t.pivotFieldListModule.blazor__instance:t.pivotFieldListModule;var r=t.element.querySelector("#"+t.element.id+"fieldlist");if(t.options.showToolbar&&t.toolbarModule&&r&&r.querySelector("button")&&t.pivotFieldListModule.parentElement.classList.contains("e-popup-close"))return r.click(),void e.preventDefault();if(t.element.querySelector(".e-toggle-field-list")){if(!t.element.querySelector(".e-toggle-field-list").classList.contains("e-hide"))return t.element.querySelector(".e-toggle-field-list").click(),void e.preventDefault();if((t.element.querySelector(".e-toggle-field-list").classList.contains("e-hide")||t.options.showToolbar)&&o&&sf.base.closest(o,".e-pivotview")&&t.pivotFieldListModule.parentElement&&t.pivotFieldListModule.parentElement.classList.contains(d)){var i=t.pivotFieldListModule.parentElement.blazor__instance;i&&!i.closeOnEscape?i.dotNetRef.invokeMethodAsync("CloseDialog",{altKey:e.altKey,ctrlKey:e.ctrlKey,code:e.code,key:e.key,location:e.location,repeat:e.repeat,shiftKey:e.shiftKey,metaKey:e.metaKey,type:e.type}):t.closeDialog(t.pivotFieldListModule.parentElement,e)}}}},o.prototype.destroy=function(){this.pivotViewKeyboardModule&&this.pivotViewKeyboardModule.destroy()},o}(),x=function(){function e(e){this.isPopupClicked=!1,this.shiftLockedPos=[],this.savedSelectedCellsPos=[],this.cellSelectionPos=[],this.parent=e,this.parent.selectionModule=this}return e.prototype.addInternalEvents=function(){this.wireEvents()},e.prototype.wireEvents=function(){this.unWireEvents(),sf.base.EventHandler.add(this.parent.element,this.parent.isAdaptive?"touchend":"click",this.mouseClickHandler,this)},e.prototype.unWireEvents=function(){sf.base.EventHandler.remove(this.parent.element,this.parent.isAdaptive?"touchend":"click",this.mouseClickHandler)},e.prototype.mouseClickHandler=function(e){(3===e.which||0===e.which)&&(this.lastCellClicked=e.target);var t=e.target;if((t.classList.contains(o)||t.classList.contains("e-headercelldiv")||t.classList.contains(u)||t.classList.contains("e-rowcell")||t.classList.contains("e-stackedheadercelldiv")||t.classList.contains("e-headertext")||t.classList.contains("e-stackedheadertext"))&&"pivot"===this.parent.options.dataType){t.classList.contains(o)||t.classList.contains(u)||t.classList.contains("e-rowcell")?t:t.classList.contains("e-stackedheadercelldiv")||t.classList.contains("e-cellvalue")||t.classList.contains("e-headercelldiv")||t.classList.contains("e-ascending")||t.classList.contains("e-descending")?t.parentElement:t.classList.contains("e-headertext")&&t.parentElement.parentElement,this.cellClicked(t,e)}else this.cellClicked(t,e)},e.prototype.cellClicked=function(e,t){var r=this,i=null;if(e.classList.contains(o)||e.classList.contains("e-stackedheadercell")||e.classList.contains("e-stackedheadercelldiv")||e.classList.contains("e-stackedheadertext")||e.classList.contains("e-pivotcell-container")||e.classList.contains("e-headertext")||e.classList.contains("e-headercelldiv")?i=sf.base.closest(e,"th."+o):e.classList.contains("e-cellvalue")||e.classList.contains("e-rowcell")?i=sf.base.closest(e,"td.e-rowcell"):e.classList.contains("e-rowselect")&&(e.classList.contains("e-spanclicked")?this.isPopupClicked=!1:this.isPopupClicked=!0),i){var s=Number(i.getAttribute("data-colindex")),n=Number(i.getAttribute("index")),l=Number(i.getAttribute("aria-colspan"));this.parent.gridSettings.allowSelection&&(("Both"===this.parent.gridSettings.selectionSettings.mode?!i.classList.contains("e-rowcell"):"Cell"===this.parent.gridSettings.selectionSettings.mode?i.classList.contains(p):"Row"!==this.parent.gridSettings.selectionSettings.mode)?this.parent.dotNetRef.invokeMethodAsync("ColumnSelectHandler",n,s,t).then((function(e){r.clearSelection(i,t,s,n),e||(r.clearSelection(i,t,s,n),r.applyColumnSelection(t,i,s,s+(l>0?l-1:0),n),r.afterColumnSelected(i,t,s,n))})):(this.clearSelection(i,t,s,n),this.afterColumnSelected(i,t,s,n))),this.parent.gridSettings.allowSelection||e.classList.contains("e-expand")||e.classList.contains("e-collapse")||this.parent.dotNetRef.invokeMethodAsync("CellClickedHandler",n,s,t)}},e.prototype.afterColumnSelected=function(e,t,o,r){this.getSelectedCellsPos();var i=JSON.stringify(this.savedSelectedCellsPos);("Both"===this.parent.gridSettings.selectionSettings.mode?!e.classList.contains("e-rowcell"):"Cell"===this.parent.gridSettings.selectionSettings.mode?e.classList.contains(p):"Row"!==this.parent.gridSettings.selectionSettings.mode)&&(this.parent.dotNetRef.invokeMethodAsync("CellClickedHandler",r,o,t),this.parent.dotNetRef.invokeMethodAsync("SelectHandler",o,r,i),this.savedSelectedCellsPos=[])},e.prototype.clearSelection=function(e,t,o,r){if(sf.base.isNullOrUndefined(e))for(var i=0,s=[].slice.call(this.parent.element.querySelectorAll("."+f+", ."+h));i<s.length;i++){var n=s[i];sf.base.removeClass([n],["e-active",f,h])}else if(!t.shiftKey&&!t.ctrlKey||"Single"===this.parent.gridSettings.selectionSettings.type)if("Cell"===this.parent.gridSettings.selectionSettings.mode)e.classList.contains(p)?sf.base.removeClass(this.parent.element.querySelectorAll(".e-rowcell."+h),h):sf.base.removeClass(this.parent.element.querySelectorAll("."+p+".e-active"),["e-active",f]);else if("Both"===this.parent.gridSettings.selectionSettings.mode||"Row"===this.parent.gridSettings.selectionSettings.mode)if(e.classList.contains("e-rowcell"))for(var l=0,a=[].slice.call(this.parent.element.querySelectorAll("."+f+", ."+h));l<a.length;l++){n=a[l];sf.base.removeClass([n],["e-active",f,h])}else sf.base.removeClass(this.parent.element.querySelectorAll("."+h),h)},e.prototype.applyColumnSelection=function(e,t,r,i,s){var n=Number(t.getAttribute("index"));if(!t.classList.contains(u)||"Cell"!==this.parent.gridSettings.selectionSettings.mode||t.classList.contains(p)){var l=e.ctrlKey;"Multiple"===this.parent.gridSettings.selectionSettings.type&&null!==this.parent.element.querySelector(".e-rowselect")&&(this.isPopupClicked?(this.parent.element.querySelector(".e-rowselect").classList.add("e-spanclicked"),l=!0):(this.parent.element.querySelector(".e-rowselect").classList.remove("e-spanclicked"),l=!1));for(var a=[],c=this.parent.gridSettings.selectionSettings.type,d=t.classList.contains("e-active"),m=[],g={},y=r;y<=i;y++)m.push(y.toString());if(l&&"Single"!==c)d=!1;else{for(var v=0,b=[].slice.call(this.parent.element.querySelectorAll(".e-active"));v<b.length;v++){var S=b[v];sf.base.removeClass([S],["e-active",f]),-1===m.indexOf(S.getAttribute("data-colindex"))&&(d=!1);var q=Number(S.getAttribute("data-colindex"));g[q]=q}m=Object.keys(g).length>0?Object.keys(g).sort((function(e,t){return e-t})):m}"Multiple"===c&&e.shiftKey?(this.shiftLockedPos=0===this.shiftLockedPos.length?m:this.shiftLockedPos,Number(this.shiftLockedPos[0])<=r?r=Number(this.shiftLockedPos[0]):i=i<Number(this.shiftLockedPos[this.shiftLockedPos.length-1])?Number(this.shiftLockedPos[this.shiftLockedPos.length-1]):i):this.shiftLockedPos=[];var w=[];if(e.ctrlKey&&"Both"===this.parent.gridSettings.selectionSettings.mode&&"Multiple"===c&&!t.classList.contains(u))for(var C=0,A=[].slice.call(this.parent.element.querySelectorAll("."+u+"."+h));C<A.length;C++){S=A[C];w.push(S.getAttribute("index"))}for(var E=r;E<=i;)a.push('[data-colindex="'+E+'"]'+("Cell"===this.parent.gridSettings.selectionSettings.mode?'[index="'+s+'"]':"")),E++;if(!d){s=t.classList.contains(o)?s:this.parent.scrollPageInfo.rowStartPos-1;for(var L=t.classList.contains("e-active"),B=0,x=[].slice.call(this.parent.element.querySelectorAll(a.toString()));B<x.length;B++){S=x[B];Number(S.getAttribute("index"))>=s&&(L&&l&&-1===w.indexOf(n.toString())?sf.base.removeClass([S],["e-active",f]):sf.base.addClass([S],["e-active",f]))}}}},e.prototype.getSelectedCellsPos=function(){for(var e=0,t=[].slice.call(this.parent.element.querySelectorAll("."+f));e<t.length;e++){var o=t[e];this.savedSelectedCellsPos.push({rowIndex:o.getAttribute("index"),colIndex:o.getAttribute("data-colindex")})}for(var r=0,i=[].slice.call(this.parent.element.querySelectorAll("."+h));r<i.length;r++){o=i[r];this.cellSelectionPos.push({rowIndex:o.getAttribute("index"),colIndex:o.getAttribute("data-colindex")})}},e.prototype.destroy=function(){this.unWireEvents()},e}(),I=function(){function e(e){this.previousValues={top:0,left:0},this.frozenPreviousValues={top:0,left:0},this.eventType="",this.isScrolling=!1,this.isFireFox=sf.base.Browser.userAgent.toLowerCase().indexOf("firefox")>-1,this.parent=e,this.parent.virtualScrollModule=this}return e.prototype.addInternalEvents=function(){this.wireEvents()},e.prototype.wireEvents=function(){if(this.unWireEvents(),"Chart"!==this.parent.displayOptions.view){var e=this.parent.element.querySelector(".e-content-virtualtable"),t=this.parent.element.querySelector(".e-gridcontent"),o=this.parent.element.querySelector(".e-headercontent"),r=t.querySelector("."+v);sf.base.EventHandler.clearEvents(e),this.isFireFox&&sf.base.EventHandler.clearEvents(o);var i=this.parent.isAdaptive?e:t.querySelector("."+v);sf.base.EventHandler.add(i,"scroll touchmove pointermove",this.onHorizondalScroll(o,e,e),this),sf.base.EventHandler.add(e.parentElement,"scroll wheel touchmove pointermove keyup keydown",this.onVerticalScroll(e,e.parentElement),this),this.isFireFox?(sf.base.EventHandler.add(i,"mouseup touchend scroll",this.common(o,e,e),this),this.parent.isAdaptive||sf.base.EventHandler.add(e.parentElement,"mouseup touchend scroll",this.common(o,e.parentElement,e),this)):(sf.base.EventHandler.add(i,"mouseup touchend",this.common(o,e,e),this),this.parent.isAdaptive||sf.base.EventHandler.add(e.parentElement,"mouseup touchend",this.common(o,e.parentElement,e),this)),sf.base.EventHandler.add(this.parent.element.querySelector(".e-content"),"touchstart touchend touchmove scroll",this.headerScrollUpdate(),this),sf.base.EventHandler.add(r,"scroll",this.onCustomScrollbarScroll(e,o),this),sf.base.EventHandler.add(e,"scroll",this.onCustomScrollbarScroll(r,o),this),sf.base.EventHandler.add(o,"scroll",this.onCustomScrollbarScroll(r,e),this),sf.base.EventHandler.add(o,"touchstart pointerdown",this.setPageXY(),this),sf.base.EventHandler.add(o,"touchmove pointermove",this.onTouchScroll(o,e,e),this),sf.base.EventHandler.add(e,"touchstart pointerdown",this.setPageXY(),this),sf.base.EventHandler.add(e,"touchmove pointermove",this.onTouchScroll(o,e,e),this)}},e.prototype.unWireEvents=function(){if("Chart"!==this.parent.displayOptions.view){var e=this.parent.element.querySelector(".e-content-virtualtable"),t=this.parent.element.querySelector(".e-gridcontent"),o=this.parent.element.querySelector(".e-headercontent"),r=t.querySelector("."+v),i=this.parent.isAdaptive?e:t.querySelector("."+v);sf.base.EventHandler.remove(i,"scroll touchmove pointermove",this.onHorizondalScroll(o,e,e)),sf.base.EventHandler.remove(e.parentElement,"scroll wheel touchmove pointermove keyup keydown",this.onVerticalScroll(e,e.parentElement)),this.isFireFox?(sf.base.EventHandler.remove(i,"mouseup touchend scroll",this.common(o,e,e)),this.parent.isAdaptive||sf.base.EventHandler.remove(e.parentElement,"mouseup touchend scroll",this.common(o,e.parentElement,e))):(sf.base.EventHandler.remove(i,"mouseup touchend",this.common(o,e,e)),this.parent.isAdaptive||sf.base.EventHandler.remove(e.parentElement,"mouseup touchend",this.common(o,e.parentElement,e))),this.parent.element.querySelector(".e-content")&&sf.base.EventHandler.remove(this.parent.element.querySelector(".e-content"),"touchstart touchend touchmove scroll",this.headerScrollUpdate()),sf.base.EventHandler.remove(r,"scroll",this.onCustomScrollbarScroll(e,o)),sf.base.EventHandler.remove(e,"scroll",this.onCustomScrollbarScroll(r,o)),sf.base.EventHandler.remove(o,"scroll",this.onCustomScrollbarScroll(r,e)),sf.base.EventHandler.remove(o,"touchstart pointerdown",this.setPageXY()),sf.base.EventHandler.remove(o,"touchmove pointermove",this.onTouchScroll(o,e,e)),sf.base.EventHandler.remove(e,"touchstart pointerdown",this.setPageXY()),sf.base.EventHandler.remove(e,"touchmove pointermove",this.onTouchScroll(o,e,e))}},e.prototype.headerScrollUpdate=function(){var e=this;return function(t){"none"===e.parent.element.querySelector("."+v).style.display&&(e.parent.element.querySelector(".e-headercontent").scrollLeft!==e.parent.element.querySelector(".e-content").scrollLeft&&(e.parent.virtualScrollModule.direction="horizondal"),e.parent.element.querySelector(".e-headercontent").scrollLeft=e.parent.element.querySelector(".e-content").scrollLeft)}},e.prototype.alignFreezedCells=function(e,t){for(var o=0,r=this.parent.element.querySelectorAll(".e-leftfreeze");o<r.length;o++)t?this.parent.options.enableRtl?r[o].style.right=-e+"px":r[o].style.left=e+"px":this.parent.options.enableRtl?r[o].style.right=Number(e)+"px":r[o].style.left=Number(-e)+"px"},e.prototype.onWheelScroll=function(e,t){var o=this,r=e;return function(e){var t=r.parentElement.scrollTop+(1===e.deltaMode?30*e.deltaY:e.deltaY);o.frozenPreviousValues.top!==t&&(e.preventDefault(),o.frozenPreviousValues.top=t,o.eventType=e.type)}},e.prototype.getPointXY=function(e){var t={x:0,y:0};return e.touches&&e.touches.length?(t.x=e.touches[0].pageX,t.y=e.touches[0].pageY):(t.x=e.pageX,t.y=e.pageY),t},e.prototype.onCustomScrollbarScroll=function(e,t){var o=this,r=e,i=t;return function(e){if(null!==o.parent.element.querySelector(".e-content").querySelector("tbody")){var t=e.target,s=t.scrollLeft;o.previousValues.left===s||o.isFireFox&&t.classList.contains("e-headercontent")||(r.scrollLeft=s,i.scrollLeft=s,o.parent.isDestroyed)}}},e.prototype.onTouchScroll=function(e,t,o){var r=this,i=t;return function(o){if("mouse"!==o.pointerType){var s=r.getPointXY(o),n=r.parent.element.querySelector(".e-grid .e-content").scrollTop+(r.pageXY.y-s.y),l=r.parent.isAdaptive?t:sf.base.closest(i,".e-gridcontent").querySelector("."+v),a=l.scrollLeft+(r.pageXY.x-s.x);r.frozenPreviousValues.left===a||a<0||(e.scrollLeft=a,l.scrollLeft=a,r.pageXY.x=s.x,r.frozenPreviousValues.left=a,r.frozenPreviousValues.top===n||n<0||(r.pageXY.y=s.y,r.frozenPreviousValues.top=n,r.eventType=o.type))}}},e.prototype.update=function(e,t,o,r,i,s,n){var l=this,a=this.parent.element.querySelector(".e-content-virtualtable"),c=this.parent.options.enableOptimizedRendering?1:3;if(this.isScrolling=!0,this.parent.pageSettings)if("vertical"===this.direction){var d=t.parentElement.scrollHeight-(r+t.parentElement.offsetHeight),u="pivot"===this.parent.options.dataType&&"row"===this.parent.dataSourceSettings.valueAxis?this.parent.dataSourceSettings.values.length:1,p=this.parent.pageSettings.rowPageSize*u*this.parent.gridSettings.rowHeight,h=Math.ceil(r/p);if(h+=this.parent.options.enableOptimizedRendering&&d<=0?1:0,this.parent.scrollPosObject.vertical===h||this.parent.pageSettings.rowPageSize>=this.parent.scrollPageInfo.rowCount||a&&a.scrollHeight<a.parentElement.clientHeight*c)return void(this.parent.options.enableOptimizedRendering&&this.parent.element.querySelector("."+y)&&(sf.base.removeClass([o],["e-virtual-pivot-content"]),sf.base.addClass([this.parent.element.querySelector("."+y)],["e-hide-loader"])));this.parent.scrollPosObject.vertical=h,this.parent.pageSettings.currentRowPage=h>1?h:1;var f=0;this.parent.options.enableOptimizedRendering&&this.parent.internalGrid&&this.parent.internalGrid.element.querySelector(".e-spinner-inner")&&sf.base.addClass([this.parent.internalGrid.element.querySelector(".e-spinner-inner")],["e-hide-loader"]),this.parent.dotNetRef.invokeMethodAsync("UpdateScrollInfo",h,this.direction).then((function(e){l.parent.getScrollInfo(e),f=l.parent.scrollPageInfo.rowStartPos;var t=Math.ceil(f/(l.parent.pageSettings.rowPageSize*u)),r=p*t-l.parent.scrollPageInfo.rowFirstLvl*u*l.parent.gridSettings.rowHeight;l.parent.scrollPosObject.verticalSection=r,l.parent.options.enableOptimizedRendering&&l.parent.element.querySelector("."+y)&&(sf.base.removeClass([o],["e-virtual-pivot-content"]),sf.base.addClass([l.parent.element.querySelector("."+y)],["e-hide-loader"]),l.parent.internalGrid&&l.parent.internalGrid.element.querySelector(".e-spinner-inner")&&sf.base.removeClass([l.parent.internalGrid.element.querySelector(".e-spinner-inner")],["e-hide-loader"]))}))}else{var m=n.scrollWidth-(n.scrollLeft+n.offsetWidth),g="pivot"===this.parent.options.dataType&&"column"===this.parent.dataSourceSettings.valueAxis?this.parent.dataSourceSettings.values.length:1,v=this.parent.pageSettings.columnPageSize*g*this.parent.gridSettings.columnWidth;h=Math.ceil(Math.abs(i)/v);if(h+=this.parent.options.enableOptimizedRendering&&m<=0?1:0,this.parent.scrollPosObject.horizontal===h||a&&a.scrollWidth<a.parentElement.clientWidth*c)return void(this.parent.options.enableOptimizedRendering&&this.parent.element.querySelector("."+y)&&(sf.base.removeClass([e,t],["e-virtual-pivot-content"]),sf.base.addClass([this.parent.element.querySelector("."+y)],["e-hide-loader"])));this.parent.scrollPosObject.horizontal=h,this.parent.pageSettings.currentColumnPage=h>1?h:1;var b=0;this.parent.options.enableOptimizedRendering&&this.parent.internalGrid&&this.parent.internalGrid.element.querySelector(".e-spinner-inner")&&sf.base.addClass([this.parent.internalGrid.element.querySelector(".e-spinner-inner")],["e-hide-loader"]),this.parent.dotNetRef.invokeMethodAsync("UpdateScrollInfo",h,this.direction).then((function(o){l.parent.getScrollInfo(o),b=l.parent.scrollPageInfo.colStartPos;var r=Math.ceil(b/(l.parent.pageSettings.columnPageSize*g)),i=v*r-l.parent.scrollPageInfo.colFirstLvl*g*l.parent.gridSettings.columnWidth;l.parent.scrollPosObject.horizontalSection=i,l.parent.options.enableOptimizedRendering&&l.parent.element.querySelector("."+y)&&(sf.base.removeClass([e,t],["e-virtual-pivot-content"]),sf.base.addClass([l.parent.element.querySelector("."+y)],["e-hide-loader"]),l.parent.internalGrid&&l.parent.internalGrid.element.querySelector(".e-spinner-inner")&&sf.base.removeClass([l.parent.internalGrid.element.querySelector(".e-spinner-inner")],["e-hide-loader"]))}))}},e.prototype.setPageXY=function(){var e=this;return function(t){"mouse"!==t.pointerType&&(e.pageXY=e.getPointXY(t))}},e.prototype.common=function(e,t,o){var r=this;return function(i){var s=r.parent.isAdaptive?t:sf.base.closest(t,".e-gridcontent").querySelector("."+v);r.parent.options.enableOptimizedRendering&&("vertical"===r.direction?r.parent.element.querySelector("."+y)&&(sf.base.addClass([o],["e-virtual-pivot-content"]),sf.base.removeClass([r.parent.element.querySelector("."+y)],["e-hide-loader"])):r.parent.element.querySelector("."+y)&&(sf.base.addClass([e,t],["e-virtual-pivot-content"]),sf.base.removeClass([r.parent.element.querySelector("."+y)],["e-hide-loader"]))),r.update(e,t,o,r.parent.element.querySelector(".e-grid .e-content").scrollTop*r.parent.verticalScrollScale,s.scrollLeft*r.parent.horizontalScrollScale,i,s)}},e.prototype.onHorizondalScroll=function(e,t,r){var i,s=this,n=this.parent.isAdaptive?t:sf.base.closest(t,".e-gridcontent").querySelector("."+v),l=Math.abs(n.scrollLeft),a=l*this.parent.horizontalScrollScale,c=a-this.parent.scrollPosObject.horizontalSection-l;return c=this.parent.options.enableRtl?c:-c,"none"!==n.style.display&&this.alignFreezedCells(c,!1),function(d){if(l=Math.abs(n.scrollLeft),a=l*s.parent.horizontalScrollScale,"wheel"!==d.type&&"touchmove"!==d.type&&"wheel"!==s.eventType&&"touchmove"!==s.eventType||(clearTimeout(i),i=setTimeout((function(){a="touchmove"===d.type?l:a,s.update(e,t,r,t.parentElement.scrollTop*s.parent.verticalScrollScale,a,d,n)}),300)),s.previousValues.left!==a){s.scrollDirection=s.direction="horizondal",c=a-s.parent.scrollPosObject.horizontalSection-l,c=s.parent.options.enableRtl?c:-c;var u=t.querySelector("."+m).style.transform.split(",").length>1?t.querySelector("."+m).style.transform.split(",")[1].trim():"0px)";l<s.parent.scrollerBrowserLimit&&(sf.base.setStyleAttribute(t.querySelector("."+m),{transform:"translate("+c+"px,"+u}),sf.base.setStyleAttribute(e.querySelector("."+m),{transform:"translate("+c+"px,0px)"}),s.alignFreezedCells(c,!1));var p=s.parent.scrollPosObject.horizontalSection>a?-(s.parent.scrollPosObject.horizontalSection-a):a+(e.offsetWidth-e.querySelector(".e-headercell.e-leftfreeze").offsetWidth)-(s.parent.scrollPosObject.horizontalSection+(t.querySelector("."+m).offsetWidth-t.querySelector("."+m).querySelector(".e-leftfreeze.e-rowsheader").offsetWidth)),h=Math.ceil(s.parent.scrollPosObject.horizontalSection/s.parent.horizontalScrollScale)<s.parent.scrollerBrowserLimit;if(s.parent.scrollPosObject.horizontalSection>a||p>1&&h){s.parent.options.enableOptimizedRendering&&s.parent.element.querySelector("."+y)&&(sf.base.addClass([e,t],["e-virtual-pivot-content"]),sf.base.removeClass([s.parent.element.querySelector("."+y)],["e-hide-loader"])),a>e.clientWidth?(s.parent.scrollPosObject.left<1&&(s.parent.scrollPosObject.left=e.clientWidth),s.parent.scrollPosObject.left=s.parent.scrollPosObject.left-50,p=s.parent.scrollPosObject.horizontalSection>a?p-s.parent.scrollPosObject.left:p+s.parent.scrollPosObject.left):p=-s.parent.scrollPosObject.horizontalSection,c=-(a-(s.parent.scrollPosObject.horizontalSection+p)-sf.base.closest(t,".e-gridcontent").querySelector("."+v).scrollLeft);var f=s.parent.gridSettings.columnWidth*s.parent.scrollPageInfo.columnCount;f>s.parent.scrollerBrowserLimit&&(s.parent.horizontalScrollScale=f/s.parent.scrollerBrowserLimit,f=s.parent.scrollerBrowserLimit),c>f&&c>a&&(c=a,p=0),sf.base.setStyleAttribute(t.querySelector("."+m),{transform:"translate("+c+"px,"+u}),sf.base.setStyleAttribute(e.querySelector("."+m),{transform:"translate("+c+"px,0px)"}),s.alignFreezedCells(c,!1),s.parent.scrollPosObject.horizontalSection=s.parent.scrollPosObject.horizontalSection+p}var g=n.scrollWidth-(l+(n.offsetWidth-s.parent.element.querySelector(".e-grid").querySelector("."+o+".e-leftfreeze").offsetWidth));if(g<=0){var b=t.querySelector(".e-virtualtrack");b.style.display="none";var S=t.scrollWidth-(t.scrollLeft+t.offsetWidth);b.style.display="";var q=t.scrollWidth-(t.scrollLeft+t.offsetWidth);s.parent.scrollPosObject.horizontalSection-=S>g?S:-q,c=n.scrollLeft>s.parent.scrollerBrowserLimit?Number(t.querySelector("."+m).style.transform.split(",")[0].split("px")[0].trim()):-(n.scrollLeft*s.parent.horizontalScrollScale-s.parent.scrollPosObject.horizontalSection-n.scrollLeft),sf.base.setStyleAttribute(t.querySelector("."+m),{transform:"translate("+c+"px,"+u}),sf.base.setStyleAttribute(e.querySelector("."+m),{transform:"translate("+c+"px,0px)"}),s.alignFreezedCells(c,!1)}s.previousValues.left=a,s.frozenPreviousValues.left=a,s.eventType="",e.scrollLeft=n.scrollLeft,t.scrollLeft=n.scrollLeft}}},e.prototype.onVerticalScroll=function(e,t){var o,r=this,i=t.querySelector(".e-content-virtualtable")?t.querySelector(".e-content-virtualtable"):t,s=this.parent.options.enableOptimizedRendering?1:3;return function(n){if(r.parent.isAdaptive||i.scrollHeight>i.parentElement.clientHeight*s){var l=t.scrollTop*r.parent.verticalScrollScale;if("wheel"===n.type||"touchmove"===n.type||"wheel"===r.eventType||"touchmove"===r.eventType||"keyup"===n.type||"keydown"===n.type){var a=r.parent.isAdaptive?t:sf.base.closest(t,".e-gridcontent").querySelector("."+v);clearTimeout(o),o=setTimeout((function(){var o=0;if(r.parent.isAdaptive){var i=a.querySelector(".e-content-virtualtable");o=a.scrollLeft===i.scrollLeft?a.scrollLeft:i.scrollLeft}else o=a.scrollLeft;r.update(null,t,e,t.scrollTop*r.parent.verticalScrollScale,o*r.parent.horizontalScrollScale,n,a)}),300)}if(r.previousValues.top===l)return;r.parent.scrollPosObject.horizontalSection<0&&(r.parent.scrollPosObject.horizontalSection=0),r.scrollDirection=r.direction="vertical";var c=-(l-r.parent.scrollPosObject.verticalSection-t.scrollTop),d=t.querySelector("."+m).style.transform.split(",")[0].trim();c>r.virtualDiv.clientHeight&&(c=r.virtualDiv.clientHeight),t.scrollTop<r.parent.scrollerBrowserLimit&&(sf.base.setStyleAttribute(t.querySelector("."+m),{transform:"translate(0px,"+c+"px)"}),sf.base.setStyleAttribute(t.querySelector("."+m),{transform:d+","+c+"px)"}));var u=r.parent.scrollPosObject.verticalSection>l?-(r.parent.scrollPosObject.verticalSection-l):l+t.clientHeight-(r.parent.scrollPosObject.verticalSection+e.querySelector("."+m).offsetHeight),p=Math.ceil(r.parent.scrollPosObject.verticalSection/r.parent.verticalScrollScale)<r.parent.scrollerBrowserLimit;if(r.parent.scrollPosObject.verticalSection>l||u>1&&p){r.parent.options.enableOptimizedRendering&&r.parent.element.querySelector("."+y)&&(sf.base.addClass([e],["e-virtual-pivot-content"]),sf.base.removeClass([r.parent.element.querySelector("."+y)],["e-hide-loader"])),l>t.clientHeight?(r.parent.scrollPosObject.top<1&&(r.parent.scrollPosObject.top=t.clientHeight),r.parent.scrollPosObject.top=r.parent.scrollPosObject.top-50,u=r.parent.scrollPosObject.verticalSection>l?u-r.parent.scrollPosObject.top:u+r.parent.scrollPosObject.top):u=-r.parent.scrollPosObject.verticalSection;var h=r.parent.element.querySelector(".e-content").querySelector("."+m);c=-(l-(r.parent.scrollPosObject.verticalSection+u)-t.scrollTop);var f=r.parent.gridSettings.rowHeight*r.parent.scrollPageInfo.rowCount+.1-h.clientHeight;f>r.parent.scrollerBrowserLimit&&(r.parent.verticalScrollScale=f/r.parent.scrollerBrowserLimit,f=r.parent.scrollerBrowserLimit),c>f&&c>l&&(c=l,u=0),c>r.virtualDiv.clientHeight&&(c=r.virtualDiv.clientHeight),sf.base.setStyleAttribute(t.querySelector("."+m),{transform:"translate(0px,"+c+"px)"}),sf.base.setStyleAttribute(t.querySelector("."+m),{transform:d+","+c+"px)"}),r.parent.scrollPosObject.verticalSection=r.parent.scrollPosObject.verticalSection+u}r.previousValues.top=l,r.frozenPreviousValues.top=l,r.eventType=""}}},e.prototype.updateScrollInfo=function(e,t,r){if(this.parent.options.enableVirtualization){var i=this.parent.getScrollBarWidth(),n=e.querySelector(".e-content-virtualtable")?e.querySelector(".e-content-virtualtable"):e.querySelector(".e-content"),l=e.querySelector(".e-headercontent"),a=e.querySelector(".e-gridcontent"),c=n.parentElement.offsetWidth<n.querySelector("."+m).offsetWidth,d=!1;if(this.parent.options.enableOptimizedRendering){var u=e.querySelector("."+y);if(u||(u=sf.base.createElement("div",{className:y+" e-hide-loader",innerHTML:this.parent.options.localeObj.loading}),this.parent.internalGrid&&this.parent.internalGrid.element&&!e.querySelector("."+y)&&this.parent.internalGrid.element.insertBefore(u,this.parent.internalGrid.element.firstElementChild)),e.querySelector("."+s)&&e.querySelector(".e-gridcontent")){var p=e.querySelector(".e-gridcontent").offsetHeight+e.querySelector("."+s).offsetHeight;sf.base.setStyleAttribute(u,{height:p+"px",width:e.querySelector("."+s).offsetWidth+"px",padding:Math.floor(p/2)+"px"})}}e.querySelector(".e-content")&&!e.querySelector(".e-content").querySelector(".e-virtualtrack")&&(this.virtualDiv=sf.base.createElement("div",{className:"e-virtualtrack"}),e.querySelector(".e-content").appendChild(this.virtualDiv)),e.querySelector(".e-headercontent")&&!e.querySelector(".e-headercontent").querySelector(".e-virtualtrack")?(this.virtualHeaderDiv=sf.base.createElement("div",{className:"e-virtualtrack"}),e.querySelector(".e-headercontent").appendChild(this.virtualHeaderDiv)):this.virtualHeaderDiv=e.querySelector(".e-headercontent").querySelector(".e-virtualtrack");var h=a.querySelector("."+v);if(a&&!h){this.virtualTableDiv=sf.base.createElement("div",{className:v}),a.appendChild(this.virtualTableDiv),h=a.querySelector("."+v),this.virtualScrollDiv=sf.base.createElement("div",{className:"e-movablescroll"}),h.appendChild(this.virtualScrollDiv),sf.base.setStyleAttribute(h,{minHeight:i+"px",maxHeight:i+"px",overflowX:"scroll"}),sf.base.setStyleAttribute(this.virtualScrollDiv,{height:"0.1px"});var f=sf.base.createElement("div",{className:"e-content-virtualtable"});f.append.apply(f,n.childNodes),n.appendChild(f),f.scrollLeft=0,n=f,d=!0}var g=e.querySelector(".e-content").querySelector("."+m),b=this.parent.gridSettings.rowHeight*r+.1-g.clientHeight;b>this.parent.scrollerBrowserLimit&&(this.parent.verticalScrollScale=b/this.parent.scrollerBrowserLimit,b=this.parent.scrollerBrowserLimit);var S=this.parent.gridSettings.columnWidth*t;S>this.parent.scrollerBrowserLimit&&(this.parent.horizontalScrollScale=S/this.parent.scrollerBrowserLimit,S=this.parent.scrollerBrowserLimit),sf.base.setStyleAttribute(this.virtualDiv,{height:(b>.1?b:.1)+"px",width:(S>.1?S:.1)+"px"}),sf.base.setStyleAttribute(this.virtualHeaderDiv,{height:0,width:(S>.1?S:.1)+"px"}),this.parent.isAdaptive&&(d?sf.base.closest(n,".e-content").style.minHeight="1px":n.style.minHeight="1px");var q=this.parent.isAdaptive?n:a.querySelector("."+v)?a.querySelector("."+v):n,w=n.parentElement.scrollTop>this.parent.scrollerBrowserLimit||"horizondal"===this.direction?n.querySelector("."+m).style.transform.split(",")[1].trim():(this.parent.options.enableOptimizedRendering?n.parentElement.scrollTop:-(n.parentElement.scrollTop*this.parent.verticalScrollScale-this.parent.scrollPosObject.verticalSection-n.parentElement.scrollTop))+"px)",C=Math.abs(q.scrollLeft),A=C>this.parent.scrollerBrowserLimit?n.querySelector("."+m).style.transform.split(",")[0].trim()+",":"translate("+(this.parent.options.enableRtl?1:-1)*(C*this.parent.horizontalScrollScale-this.parent.scrollPosObject.horizontalSection-C)+"px,";sf.base.setStyleAttribute(n.querySelector("."+m),{transform:A+w}),sf.base.setStyleAttribute(l.querySelector("."+m),{transform:A+0+"px)"}),!c&&a&&h||this.parent.isAdaptive?h.style.display="none":h.style.display="block";var E=Number(-n.querySelector("."+m).style.transform.split("(")[1].split(",")[0].split("px")[0]);if(this.alignFreezedCells(E,!0),c){var L=0;L=t<25?S+n.querySelector("."+m).querySelector(".e-leftfreeze").offsetWidth+(a.offsetWidth-a.clientWidth):S+(a.offsetWidth-a.clientWidth),"auto"!==this.parent.options.height?e.querySelector(".e-movablescroll").style.width=L+i+"px":e.querySelector(".e-movablescroll").style.width=L+"px"}var B="pivot"===this.parent.options.dataType&&"column"===this.parent.options.dataSourceSettings.valueAxis?this.parent.options.dataSourceSettings.values.length:1,x=this.parent.pageSettings.columnPageSize*B*this.parent.options.gridSettings.columnWidth,I=q.scrollWidth-(Math.abs(C)+q.offsetWidth);if(this.parent&&x>0&&I<=x&&q.scrollLeft>0){var M=n.scrollWidth-(Math.abs(n.scrollLeft)+n.parentElement.offsetWidth);M<1&&"horizondal"===this.scrollDirection&&(this.virtualDiv.style.display="none",M=n.scrollWidth-(Math.abs(n.scrollLeft)+n.parentElement.offsetWidth)),this.virtualDiv.style.display="";var k=n.scrollWidth-(Math.abs(n.scrollLeft)+n.parentElement.offsetWidth);M>1&&(this.parent.scrollPosObject.horizontalSection-=I<=0?M>I?M:-k:k===M?M-I:M<k&&(I===k||I>M)?-(k-M):0,"auto"!==this.parent.options.height&&(this.parent.scrollPosObject.horizontalSection-=i),this.parent.scrollPosObject.horizontalSection=this.parent.scrollPosObject.horizontalSection>=-e.querySelector(".e-grid").querySelector("."+o+".e-leftfreeze").offsetWidth?this.parent.scrollPosObject.horizontalSection:-e.querySelector(".e-grid").querySelector("."+o+".e-leftfreeze").offsetWidth),A=q.scrollLeft>this.parent.scrollerBrowserLimit?n.querySelector("."+m).style.transform.split(",")[0].trim()+",":"translate("+-(q.scrollLeft*this.parent.horizontalScrollScale-this.parent.scrollPosObject.horizontalSection-q.scrollLeft)+"px,",sf.base.setStyleAttribute(n.querySelector("."+m),{transform:A+w}),sf.base.setStyleAttribute(l.querySelector("."+m),{transform:A+0+"px)"}),E=Number(-n.querySelector("."+m).style.transform.split("(")[1].split(",")[0].split("px")[0]),this.alignFreezedCells(E,!0)}if(e.querySelector(".e-movablescroll")){var z=e.querySelector(".e-movablescroll").style.width;z&&Number(z.replace("px",""))>0||(sf.base.closest(e.querySelector(".e-movablescroll"),"."+v).style.display="none")}if(this.parent.options.isEmptyData&&(sf.base.closest(n,".e-content").style.height=(g?g.querySelector("tr").offsetHeight:30)+"px"),this.parent.virtualScrollModule&&!this.parent.options.isEmptyData&&this.onHorizondalScroll(l,n,n),!this.parent.isAdaptive&&this.parent.options.enableVirtualization&&n&&!this.parent.options.enableOptimizedRendering){var D=n.parentElement.parentElement.querySelector("."+v);if(S<3*n.parentElement.clientWidth)n.style.overflowX="visible",D.style.display="none",l.scrollLeft=n.parentElement.scrollLeft,sf.base.setStyleAttribute(n.querySelector("."+m),{transform:"translate(0px,"+w}),sf.base.setStyleAttribute(l.querySelector("."+m),{transform:"translate(0px, 0px)"}),this.alignFreezedCells(0,!0);else{sf.base.EventHandler.remove(this.parent.element.querySelector(".e-content"),"touchstart touchend touchmove scroll",this.headerScrollUpdate());var O=this.parent.element.querySelector(".e-content").scrollLeft;n.style.overflowX="auto",D.style.display="block",0!==O&&(D.scrollLeft=O),D.scrollLeft!==n.scrollLeft&&0!==n.scrollLeft&&(D.scrollLeft=n.scrollLeft)}}}},e.prototype.destroy=function(){this.unWireEvents()},e}(),M=function(){function e(e){this.keyConfigs={escape:"escape"},this.parent=e,this.event=void 0,this.parent.element.tabIndex=-1===this.parent.element.tabIndex?0:this.parent.element.tabIndex;var t=document.getElementById(this.parent.element.id+"_drillthrough");this.drillThroughKeyboardModule=new sf.base.KeyboardEvents(t,{keyAction:this.keyActionHandler.bind(this),keyConfigs:this.keyConfigs,eventName:"keydown"})}return e.prototype.keyActionHandler=function(e){switch(e.action){case"escape":this.processClose(e)}},e.prototype.processClose=function(e){var t=e.target;if(t&&sf.base.closest(t,"."+c+"."+d)){var o=sf.base.closest(t,"."+c+"."+d).blazor__instance,r=this;if(o&&!o.closeOnEscape){var i=o.element.getAttribute("data-fieldName");o.dotNetRef.invokeMethodAsync("CloseDialog",{altKey:e.altKey,ctrlKey:e.ctrlKey,code:e.code,key:e.key,location:e.location,repeat:e.repeat,shiftKey:e.shiftKey,metaKey:e.metaKey,type:e.type}).then((function(){if(r.parent.parentElement)for(var t=0,o=[].slice.call(r.parent.parentElement.querySelectorAll(".e-pivot-button"));t<o.length;t++){var s=o[t];if(s.getAttribute("data-uid")===i){s.focus();break}}e.preventDefault()}))}else r.parent.closeDialog(sf.base.closest(t,"."+c+"."+d),e)}},e.prototype.destroy=function(){this.drillThroughKeyboardModule&&!this.drillThroughKeyboardModule.isDestroyed&&this.drillThroughKeyboardModule.destroy()},e}(),k=function(){function t(e,t,o,r){this.scrollPageInfo={rowCount:0,columnCount:0,colFirstLvl:0,rowFirstLvl:0,colStartPos:0,rowStartPos:0},this.verticalScrollScale=1,this.horizontalScrollScale=1,this.scrollerBrowserLimit=8e6,this.scrollPosObject={vertical:0,horizontal:0,verticalSection:0,horizontalSection:0,top:0,left:0,scrollDirection:{direction:"",position:0}},window.sfBlazor=window.sfBlazor,this.element=t,this.dotNetRef=r,this.dataId=e,window.sfBlazor.setCompInstance(this),this.getOptions(t,o),this.initModules()}return t.prototype.getOptions=function(e,t){this.element=e,this.options=t,this.parentElement=e,this.isAdaptive=sf.base.Browser.isDevice,this.internalGrid=t.internalGrid?window.sfBlazor.getCompInstance(t.internalGrid):this.internalGrid,this.pivotFieldListModule=t.fieldListModule?window.sfBlazor.getCompInstance(t.fieldListModule):this.pivotFieldListModule,this.fieldList=t.fieldList?t.fieldList:this.fieldList,this.dataSourceSettings=t.dataSourceSettings,this.gridSettings=t.gridSettings,this.editSettings=t.editSettings,this.displayOptions=t.displayOptions,this.enableValueSorting=t.enableValueSorting,this.currentView="Both"===this.displayOptions.view?this.displayOptions.primary:this.displayOptions.view,this.groupingBarSettings=this.options.groupingBarsettings,this.pageSettings=this.options.pageSettings,this.options.scrollPageInfo&&this.getScrollInfo(this.options.scrollPageInfo)},t.prototype.initModules=function(){this.options.showGroupingBar&&(this.groupingBarModule=new S(this)),this.options.showToolbar&&(this.toolbarModule=new w(this)),this.options.allowCalculatedField&&(this.calculatedFieldModule=new q(this)),this.options.enableVirtualization&&(this.virtualScrollModule=new I(this)),this.gridSettings.allowSelection&&(this.selectionModule=new x(this)),this.keyboardModule=new B(this),this.commonActionModule=new L(this),this.contentReady(),this.unWireEvents(),this.wireEvents()},t.prototype.updateModuleProperties=function(){this.options.showGroupingBar&&(this.groupingBarModule?this.groupingBarModule.parent=this.pivotButtonModule.parent=this:this.groupingBarModule=new S(this)),this.options.showToolbar&&(this.toolbarModule?this.toolbarModule.parent=this:this.toolbarModule=new w(this)),this.options.allowCalculatedField&&(this.calculatedFieldModule?this.calculatedFieldModule.parent=this:this.calculatedFieldModule=new q(this)),this.gridSettings.allowSelection&&(this.selectionModule?this.selectionModule.parent=this:this.selectionModule=new x(this)),this.options.enableVirtualization&&(this.virtualScrollModule?this.virtualScrollModule.parent=this:this.virtualScrollModule=new I(this)),this.editSettings.allowEditing&&this.drillThroughModule&&(this.drillThroughModule.parent=this),this.keyboardModule.parent=this.commonActionModule.parent=this.commonActionModule.keyboardModule.parent=this},t.prototype.getChartHeight=function(e){var t;return this.element.querySelector(".e-pivotchart")&&this.element.querySelector(".e-chart-grouping-bar")&&(this.element.querySelector(".e-chart-grouping-bar").style.width=this.element.querySelector(".e-pivotchart").style.width,e=this.element.querySelector(".e-chart-grouping-bar").clientHeight),this.options.showToolbar&&this.options.showGroupingBar?t=(e-(this.element.querySelector(".e-pivot-toolbar")?this.element.querySelector(".e-pivot-toolbar").clientHeight:42)-(this.element.querySelector(".e-chart-grouping-bar")?this.element.querySelector(".e-chart-grouping-bar").clientHeight:76)).toString():this.options.showToolbar?t=(e-(this.element.querySelector(".e-pivot-toolbar")?this.element.querySelector(".e-pivot-toolbar").clientHeight:42)).toString():this.options.showGroupingBar&&(t=(e-(this.element.querySelector(".e-chart-grouping-bar")?this.element.querySelector(".e-chart-grouping-bar").clientHeight:76)).toString()),t},t.prototype.contentReady=function(){if(this.updateModuleProperties(),this.options.renderGrid){if(("Both"!==this.displayOptions.view||this.options.showToolbar&&"Table"!==this.displayOptions.primary)&&"Table"!==this.displayOptions.view?(this.options.showToolbar&&"Both"===this.displayOptions.view&&"Chart"===this.displayOptions.primary||"Chart"===this.displayOptions.view)&&this.options.showGroupingBar&&this.groupingBarModule&&this.groupingBarModule.refreshUI():this.updateGridUI(this.element,this.options,this.scrollPageInfo.columnCount,this.scrollPageInfo.rowCount),(!this.options.showGroupingBar||!this.groupingBarModule)&&this.element&&this.element.querySelectorAll(".e-axis-container").length>0)for(var e=0,t=[].slice.call(this.element.querySelectorAll(".e-axis-container"));e<t.length;e++){var o=t[e];sf.base.closest(o,".e-pivotfieldlist-container")||sf.base.remove(o)}this.options.enableVirtualization&&this.virtualScrollModule&&this.virtualScrollModule.addInternalEvents(),this.gridSettings.allowSelection&&this.selectionModule&&this.selectionModule.addInternalEvents()}},t.prototype.getSelectedCellDom=function(e){return e?JSON.stringify(window.sfBlazor.getDomObject("currentCell",e)):null},t.prototype.selectedCell=function(e,t,o){return o?this.getSelectedCellDom(this.element.querySelector("."+(0===e?"e-frozenheader":"e-headercontent")+' th[index="'+t+'"][data-colindex="'+e+'"]')):this.getSelectedCellDom(this.element.querySelector("."+(0===e?"e-frozencontent":"e-movablecontent")+' td[index="'+t+'"][data-colindex="'+e+'"]'))},t.prototype.hyperlinkCellclick=function(e,t){if(null!=e){e.currentCell=window.sfBlazor.getElementByXpath(t);var o=e.currentCell.getAttribute("data-url")?e.currentCell.getAttribute("data-url"):e.currentCell.querySelector("a").getAttribute("data-url");window.open(o)}},t.prototype.createSheet=function(e,t,o){var r=".format"+t+o;this.createStyleSheet().insertRule(r+", "+r+" .e-cellvalue{"+e+"}",0)},t.prototype.getScrollInfo=function(e){e&&(this.scrollPageInfo.colFirstLvl=e.colFirstLvl,this.scrollPageInfo.rowFirstLvl=e.rowFirstLvl,this.scrollPageInfo.columnCount=e.columnCount,this.scrollPageInfo.rowCount=e.rowCount,this.scrollPageInfo.colStartPos=e.colStartPos,this.scrollPageInfo.rowStartPos=e.rowStartPos)},t.prototype.exportDocument=function(e,t){if(navigator.msSaveBlob){for(var o=window.atob(t),r=new Uint8Array(o.length),i=0;i<o.length;i++)r[i]=o.charCodeAt(i);var s=new Blob([r.buffer],{type:"application/octet-stream"});navigator.msSaveBlob(s,e)}else{var n=document.createElement("a");n.download=e,n.href="data:application/octet-stream;base64,"+t,document.body.appendChild(n),n.click(),document.body.removeChild(n)}},t.prototype.createStyleSheet=function(){var e=document.createElement("style");return e.appendChild(document.createTextNode("")),document.head.appendChild(e),e.sheet},t.prototype.getSelectedCells=function(){for(var e=[],t=0,o=[].slice.call(this.element.querySelectorAll("."+h+",."+f));t<o.length;t++){var r=o[t],i=Number(r.getAttribute("data-colindex")),s=Number(r.getAttribute("index")),n={};sf.base.isNullOrUndefined(i)||sf.base.isNullOrUndefined(s)||(n.index=s,n["data-colindex"]=i,e.push(n))}return JSON.stringify(e)},t.prototype.onContextMenuOpen=function(){if(this.options.allowGrouping&&this.lastCellClicked){var e=sf.base.closest(this.lastCellClicked,"td."+u+",th."+p);if(this.lastCellClicked=void 0,e&&!e.classList.contains("e-valuesheader")){var t=Number(e.getAttribute("data-colindex")),o=Number(e.getAttribute("index")),r={};if(!sf.base.isNullOrUndefined(t)&&!sf.base.isNullOrUndefined(o))return r.index=o,r["data-colindex"]=t,JSON.stringify(r)}return null}return null},t.prototype.mouseRclickHandler=function(e){this.lastCellClicked=e.target},t.prototype.mouseClickHandler=function(e){this.element.querySelectorAll(".e-btn-focused").length>0&&sf.base.removeClass(this.element.querySelectorAll(".e-btn-focused"),"e-btn-focused"),this.element.querySelector("."+l)&&this.element.querySelector("."+l).querySelectorAll(".e-menu-item.e-focused").length>0&&sf.base.removeClass(this.element.querySelector("."+l).querySelectorAll(".e-menu-item.e-focused"),"e-focused");var t=e.target;if(this.enableValueSorting&&(t.classList.contains("e-sortfilterdiv")||t.classList.contains("e-stackedheadertext")||t.classList.contains("e-stackedheadercelldiv")||t.classList.contains("e-headertext")||t.classList.contains("e-stackedheadercelldiv")||t.classList.contains("e-headercelldiv")||t.classList.contains(o)||t.classList.contains("e-cellvalue")||t.classList.contains(p)||t.classList.contains(u)||t.classList.contains("e-pivotcell-container"))){if((n=sf.base.closest(t,"td."+u+",th."+p))&&(sf.base.closest(n,"."+s)&&"column"===this.dataSourceSettings.valueAxis||sf.base.closest(n,"tr.e-row")&&"row"===this.dataSourceSettings.valueAxis&&(n.classList.contains(u)||n.classList.contains("e-stot")))){if(this.enableValueSorting){var r=Number(n.getAttribute("data-colindex")),i=Number(n.getAttribute("index"));"column"===this.dataSourceSettings.valueAxis&&(this.dataSourceSettings.values.length>1||this.dataSourceSettings.alwaysShowValueHeader)&&(r=Number(n.getAttribute("data-colindex"))+Number(n.getAttribute("colspan"))-1),this.dotNetRef.invokeMethodAsync("MouseClickHandler",i,r)}e.preventDefault()}}else if(!this.gridSettings.allowSelection){var n;if(null!==(n=sf.base.closest(t,"td."+u+",th."+p+",td.e-rowcell"))){r=Number(n.getAttribute("data-colindex")),i=Number(n.getAttribute("index"));t.classList.contains("e-expand")||t.classList.contains("e-collapse")||this.dotNetRef.invokeMethodAsync("CellClickedHandler",i,r,e)}}},t.prototype.getHeightAsNumber=function(){var e;return isNaN(this.options.height)?this.options.height.toString().indexOf("%")>-1?e=parseFloat(this.options.height.toString())/100*this.element.offsetHeight:this.options.height.toString().indexOf("px")>-1?e=Number(this.options.height.toString().split("px")[0]):"auto"===this.options.height&&(e=this.element.offsetHeight):e=Number(this.options.height),e<this.getminHeightAsNumber()&&(e=this.getminHeightAsNumber()),e},t.prototype.getminHeightAsNumber=function(){var e;return isNaN(this.options.minHeight)?this.options.minHeight.toString().indexOf("%")>-1?e=parseFloat(this.options.minHeight.toString())/100*this.element.offsetHeight:this.options.minHeight.toString().indexOf("px")>-1?e=Number(this.options.minHeight.toString().split("px")[0]):"auto"===this.options.minHeight&&(e=this.element.offsetHeight):e=300,e},t.prototype.calculateGridHeight=function(e,t,o){var r=this.options.height,i=this.getHeightAsNumber(),s=t*this.gridSettings.rowHeight;isNaN(i)&&(i=i>this.getminHeightAsNumber()?i:this.getminHeightAsNumber());var n=this.element.getBoundingClientRect(),a={width:n.width.toString(),height:n.height.toString(),clientWidth:this.element.clientWidth.toString(),clientHeight:this.element.clientHeight.toString(),gridHeight:r?r.toString():r};if("auto"===this.gridSettings.height&&i&&this.element.querySelector(".e-gridheader")){if(r=i-(this.element.querySelector(".e-gridheader").offsetHeight+(this.element.querySelector(".e-grouping-bar")?this.element.querySelector(".e-grouping-bar").offsetHeight:0)+(this.element.querySelector("."+l)?this.element.querySelector("."+l).clientHeight:0)+(this.element.querySelector(".pivot-pager-container")?this.element.querySelector(".pivot-pager-container").offsetHeight:0))-1,e){var c=this.internalGrid&&this.internalGrid.options&&!isNaN(this.internalGrid.options.height)?Number(this.internalGrid.options.height):this.element.querySelector(".e-content ."+m).offsetHeight,d=this.element.querySelector(".e-content ."+m).offsetWidth,u=this.element.querySelector(".e-content ."+m).offsetWidth<=d;if(this.options.enableVirtualization){var p=this.element.querySelector(".e-scrollbar")?this.element.querySelector(".e-scrollbar").offsetHeight:0;r=u?r-p:r}if(r>=s&&(!u||r>=c)&&!(c<s)&&!!!(u&&r-s<18))return a.gridHeight="auto",JSON.stringify(a)}else if(r>t*this.gridSettings.rowHeight)return a.gridHeight="auto",JSON.stringify(a)}else r=this.gridSettings.height;return a.gridHeight=r<this.gridSettings.rowHeight?this.gridSettings.rowHeight.toString():s<r?"auto":r.toString(),JSON.stringify(a)},t.prototype.updateGridUI=function(e,t,o,r){if(t&&this.getOptions(e,t),this.options.showGroupingBar&&this.groupingBarModule)this.groupingBarModule.refreshUI();else{var i=e.querySelector("."+s).querySelector(".e-leftfreeze");i&&i.querySelector(".e-rhandler")&&(i.style.height=i.querySelector(".e-rhandler").style.height="auto",i.style.height=i.querySelector(".e-rhandler").style.height=e.querySelector("."+s).offsetHeight+"px")}if(e.querySelector("."+s+" ."+m)&&"0px"===e.querySelector("."+s+" ."+m).style.width&&(e.querySelector(".e-content ."+m).style.width=e.querySelector("."+s+" ."+m).style.width=""),e.querySelector("."+s+" ."+m+" th.e-firstcell.e-lastcell")&&e.querySelector("."+s+" ."+m+" th.e-firstcell.e-lastcell").offsetWidth<=0){var n=e.querySelector("."+s+" ."+m+" colgroup").children,l=e.querySelector(".e-content ."+m+" colgroup").children,a=sf.base.formatUnit(this.options.gridSettings.columnWidth);n.length>1&&"auto"===n[n.length-1].style.width&&(n[n.length-1].style.width=a),l.length>1&&"auto"===l[l.length-1].style.width&&(l[l.length-1].style.width=a)}t&&this.virtualScrollModule&&this.virtualScrollModule.updateScrollInfo(e,o,r)},t.prototype.getScrollBarWidth=function(){if(!sf.base.isNullOrUndefined(this.scrollWidth))return this.scrollWidth;var e,t=document.createElement("div");return t.style.cssText="width:100px;height: 100px;overflow: scroll;position: absolute;top: -9999px;",document.body.appendChild(t),e=t.offsetWidth-t.clientWidth|0,document.body.removeChild(t),this.scrollWidth=e},t.prototype.updateView=function(e,t){e&&(this.displayOptions=JSON.parse(t),"Both"===this.displayOptions.view&&"Table"===this.displayOptions.primary?(this.element.querySelector(".e-pivotchart").style.display="none",sf.base.select("#"+this.element.id+"_grid",this.element).style.display="",this.options.showGroupingBar&&this.element.querySelector(".e-pivotchart")&&this.element.querySelector(".e-chart-grouping-bar")&&(this.element.querySelector(".e-chart-grouping-bar").style.display="none",this.element.querySelector(".e-pivot-grouping-bar").style.display="")):"Both"===this.displayOptions.view&&"Chart"===this.displayOptions.primary&&(this.element.querySelector(".e-pivotchart").style.display="",sf.base.select("#"+this.element.id+"_grid",this.element).style.display="none",this.options.showGroupingBar&&this.element.querySelector(".e-pivot-grouping-bar")&&(this.element.querySelector(".e-pivot-grouping-bar").style.display="none",this.element.querySelector(".e-chart-grouping-bar").style.display="")))},t.prototype.updateGridSettings=function(e,t,o){e&&null!==t&&(this.gridSettings=JSON.parse(t)),o&&this.selectionModule&&this.selectionModule.clearSelection()},t.prototype.getClientWidth=function(e,t){return e&&null!==t?document.getElementById(t).clientWidth:null},t.prototype.getTableCellNode=function(e,t,o){if(e){var r=document.elementFromPoint(o,t);if(r){var i=Number(r.getAttribute("data-colindex")),s=Number(r.getAttribute("index")),n={};if(i&&s)return n.index=s,n["data-colindex"]=i,JSON.stringify(n)}}return null},t.prototype.updateColorPickerUI=function(t){if(t){for(var o=[].slice.call(t.querySelectorAll(".e-colorpicker-container.e-format-font-color-picker-container")),r=[].slice.call(t.querySelectorAll(".e-colorpicker-container.e-format-back-color-picker-container")),i=0,s=o;i<s.length;i++){var n=s[i];sf.base.addClass([n],["e-format-color-picker","e-format-font-color-picker"]),sf.base.addClass([n.querySelector(".e-colorpicker")],"e-format-font-color"),sf.base.addClass([n.querySelector(".e-selected-color")],e)}for(var l=0,a=r;l<a.length;l++){n=a[l];sf.base.addClass([n],"e-format-color-picker"),sf.base.addClass([n.querySelector(".e-colorpicker")],"e-format-back-color"),sf.base.addClass([n.querySelector(".e-selected-color")],e)}}},t.prototype.onWindowResize=function(){var e=this;this.isDestroyed||(clearTimeout(this.timeOutObj),this.timeOutObj=setTimeout((function(){var t=e.element.getBoundingClientRect();e.dotNetRef.invokeMethodAsync("LayoutRefresh",t.width,t.height)}),500))},t.prototype.drillthroughIntialize=function(e){e&&this.editSettings.allowEditing&&(this.drillThroughModule=new M(this))},t.prototype.closeDialog=function(e,t){return e.querySelector("."+g)?(e.querySelector("."+g).click(),void t.preventDefault()):e.querySelector(".e-cancel-btn")?(e.querySelector(".e-cancel-btn").click(),void t.preventDefault()):void 0},t.prototype.wireEvents=function(){sf.base.EventHandler.add(this.element,this.isAdaptive?"touchend":"click",this.mouseClickHandler,this),sf.base.EventHandler.add(this.element,this.isAdaptive?"touchend":"contextmenu",this.mouseRclickHandler,this),window.addEventListener("resize",this.onWindowResize.bind(this),!0)},t.prototype.unWireEvents=function(){sf.base.EventHandler.remove(this.element,this.isAdaptive?"touchend":"click",this.mouseClickHandler),sf.base.EventHandler.remove(this.element,this.isAdaptive?"touchend":"contextmenu",this.mouseRclickHandler),window.removeEventListener("resize",this.onWindowResize.bind(this),!0)},t.prototype.destroy=function(e){this.unWireEvents(),this.keyboardModule.destroy(),this.commonActionModule.destroy(),this.options.showGroupingBar&&(this.groupingBarModule.destroy(),this.pivotButtonModule.destroy()),this.options.showFieldList&&this.pivotFieldListModule&&!this.pivotFieldListModule.isDestroyed&&this.pivotFieldListModule.destroy(e),this.options.enableVirtualization&&this.virtualScrollModule&&this.virtualScrollModule.destroy(),this.gridSettings.allowSelection&&this.selectionModule&&this.selectionModule.destroy(),this.editSettings.allowEditing&&this.drillThroughModule&&this.drillThroughModule.destroy(),this.isDestroyed=e},t}();return{initialize:function(e,t,o,r){sf.base.enableBlazorMode(),new k(e,t,o,r)},initializeFieldList:function(e,t,o,r){sf.base.enableBlazorMode(),new A(e,t,o,r)},contentReady:function(e,t,o){var r=window.sfBlazor.getCompInstance(e);r&&r.element&&(r.options=t,r.getOptions(r.element,t),o||r.contentReady())},getChartHeight:function(e,t){var o=window.sfBlazor.getCompInstance(e);o&&o.element&&o.getChartHeight(t)},onShowFieldList:function(e,t){var o=window.sfBlazor.getCompInstance(e);o&&o.element&&o.onShowFieldList(o.element,document.getElementById(t))},removeFieldListIcon:function(e){var t=window.sfBlazor.getCompInstance(e);t&&t.element&&t.removeFieldListIcon(t.element)},updateFieldListIcons:function(e,t,o){var r=window.sfBlazor.getCompInstance(e);r&&r.element&&r.treeRendererModule.updateFieldListIcons(t,o)},setPivotButtonDraggable:function(e,t){var o=window.sfBlazor.getCompInstance(e);o&&o.element&&o.pivotButtonModule&&(o.options=t,o.getOptions(o.element,t),o.pivotButtonModule.createPivotButtonDrop(),o.pivotButtonModule.setPivotButtonDrag())},isFullRowElement:function(e,t,o){var r=window.sfBlazor.getCompInstance(e);return r&&r.element?r.commonActionModule.isFullRowElement(t,o):JSON.stringify(!1)},updateActiveNode:function(e,t,o){var r=!1,i=window.sfBlazor.getCompInstance(e);return i&&i.element&&(r=i.commonActionModule.updateActiveNode(document.getElementById(t),o)),JSON.stringify(r)},updateSelectedNodes:function(e,t,o){var r=window.sfBlazor.getCompInstance(e);r&&r.element&&r.updateSelectedNodes(window.sfBlazor.getElementByXpath(t),o)},updateFieldList:function(e,t){var o=window.sfBlazor.getCompInstance(e);o&&o.element&&o.updateFieldList(document.getElementById(t))},renderToolbar:function(e){var t=window.sfBlazor.getCompInstance(e);t&&t.element&&t.toolbarModule.renderToolbar()},focusToolBar:function(e){var t=window.sfBlazor.getCompInstance(e);t&&t.element&&t.toolbarModule.focusToolBar()},getButtonPosition:function(e,t,o){var r=window.sfBlazor.getCompInstance(e);return r&&r.element?JSON.stringify(r.pivotButtonModule.getButtonPosition(window.sfBlazor.getElementByXpath(t),o)):null},selectInputRange:function(e,t){var o=window.sfBlazor.getCompInstance(e);o&&o.element&&o.toolbarModule.selectInputRange(document.getElementById(t))},copyMdxQuery:function(e,t){var o=window.sfBlazor.getCompInstance(e);o&&o.element&&o.toolbarModule.copyMdxQuery(document.getElementById(t))},validateInputs:function(e,t){var o=window.sfBlazor.getCompInstance(e);o&&o.element&&o.commonActionModule.validateInputs(t)},selectedCell:function(e,t,o,r){var i=window.sfBlazor.getCompInstance(e);if(i&&i.element)return i.selectedCell(t,o,r)},hyperlinkCellclick:function(e,t,o){var r=window.sfBlazor.getCompInstance(e);r&&r.element&&r.hyperlinkCellclick(JSON.parse(t),JSON.parse(o))},updateEditOptions:function(e,t){var o=window.sfBlazor.getCompInstance(e);o&&o.element&&o.calculatedFieldModule.updateEditOptions(t)},accordionClick:function(e,t,o,r){var i=window.sfBlazor.getCompInstance(e);if(i&&i.element)return i.calculatedFieldModule.accordionClick(t,o,r)},getAccordionValue:function(e){var t=window.sfBlazor.getCompInstance(e);if(t&&t.element)return t.calculatedFieldModule.getAccordionValue()},updateAccordionLabel:function(e,t){var o=window.sfBlazor.getCompInstance(e);o&&o.element&&o.calculatedFieldModule.updateAccordionLabel(t)},getNodeLocation:function(e,t,o){var r=window.sfBlazor.getCompInstance(e);if(r&&r.element)return r.calculatedFieldModule.getNodeLocation(document.getElementById(t),o)},getIconInfo:function(e,t,o){var r=window.sfBlazor.getCompInstance(e);if(r&&r.element)return r.calculatedFieldModule.getIconInfo(t,o)},emptyFieldName:function(e){var t=window.sfBlazor.getCompInstance(e);t&&t.element&&t.calculatedFieldModule.emptyFieldName(t.element.id)},updateCalculatedFields:function(e,t,o,r,i){var s=window.sfBlazor.getCompInstance(e);s&&s.element&&s.calculatedFieldModule.editCalculatedFieldInfo(t,o,r,i)},updateCalculatedFieldExpandIcons:function(e,t,o){var r=window.sfBlazor.getCompInstance(e);return r&&r.element?r.calculatedFieldModule.updateNodeExpandIcons(document.getElementById(t),o):JSON.stringify(!1)},createFormulaDroppable:function(e,t,o,r,i,s,n,l){var a=window.sfBlazor.getCompInstance(e);a&&a.element&&a.calculatedFieldModule.createFormulaDroppable(t,o,r,i,s,n,l)},createSheet:function(e,t,o,r){var i=window.sfBlazor.getCompInstance(e);i&&i.element&&i.createSheet(t,o,r)},updateScrollInfo:function(e,t,o){var r=window.sfBlazor.getCompInstance(e);r&&r.element&&r.virtualScrollModule.updateScrollInfo(r.element,t,o)},getScrollInfo:function(e,t){var o=window.sfBlazor.getCompInstance(e);o&&o.element&&o.getScrollInfo(t)},exportDocument:function(e,t,o){var r=window.sfBlazor.getCompInstance(e);r&&r.element&&r.exportDocument(t,o)},calculateGridHeight:function(e,t,o,r){var i=window.sfBlazor.getCompInstance(e);return i&&i.element?i.calculateGridHeight(t,o,r):null},onContextMenuOpen:function(e){var t=window.sfBlazor.getCompInstance(e);return t&&t.element?t.onContextMenuOpen():null},getSelectedCells:function(e){var t=window.sfBlazor.getCompInstance(e);return t&&t.element?t.getSelectedCells():null},updateGridUI:function(e,t,o,r){var i=window.sfBlazor.getCompInstance(e);i&&i.element&&i.updateGridUI(i.element,t,o,r)},updateView:function(e,t){var o=window.sfBlazor.getCompInstance(e);o&&o.element&&o.updateView(o.element,t)},updateGridSettings:function(e,t,o){var r=window.sfBlazor.getCompInstance(e);r&&r.element&&r.updateGridSettings(r.element,t,o)},getClientWidth:function(e,t){var o=window.sfBlazor.getCompInstance(e);return o&&o.element?o.getClientWidth(o.element,t):null},getTableCellNode:function(e,t,o){var r=window.sfBlazor.getCompInstance(e);return r&&r.element?r.getTableCellNode(r.element,t,o):null},updateColorPickerUI:function(e,t){var o=window.sfBlazor.getCompInstance(e);if(o&&o.element)return o.updateColorPickerUI(document.getElementById(t))},drillthroughIntialize:function(e){var t=window.sfBlazor.getCompInstance(e);t&&t.element&&t.drillthroughIntialize(t.element)},getElementInfo:function(e){var t=window.sfBlazor.getCompInstance(e);if(t&&t.element){var o=t.element.getBoundingClientRect(),r={width:o.width,height:o.height,clientWidth:t.element.clientWidth,clientHeight:t.element.clientHeight};return JSON.stringify(r)}return null},getAggregateIcoPosition:function(e,t){var o=window.sfBlazor.getCompInstance(e);return o&&o.element?o.commonActionModule.getAggregateIcoPosition(t):null},destroy:function(e,t){var o=window.sfBlazor.getCompInstance(e);o&&o.element&&o.destroy(t)}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfpivotview');})})();