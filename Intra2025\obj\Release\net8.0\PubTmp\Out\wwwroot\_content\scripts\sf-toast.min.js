/*!*  filename: sf-toast.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[67],{"./bundles/sf-toast.js":function(t,e,s){"use strict";s.r(e);s("./modules/sf-toast.js")},"./modules/sf-toast.js":function(t,e){function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Toast=function(){"use strict";var t="e-toast-progress",e=function(){function e(t){this.progressObj=[],window.sfBlazor=window.sfBlazor,this.progressObj=[],this.updateContext(t),window.sfBlazor&&(sf.base.isNullOrUndefined(window.sfBlazor.instances)&&(window.sfBlazor.instances=[]),window.sfBlazor.instances[this.dataId]=this)}return e.prototype.initialize=function(){this.isDevice=sf.base.Browser.isDevice,"300px"===this.width&&(this.width=this.isDevice&&screen.width<768?"100%":"300px",this.toastContainer.classList.add("e-toast-full-width")),this.isDevice&&screen.width<768&&new sf.base.Touch(this.element,{swipe:this.swipeHandler.bind(this)})},e.prototype.show=function(e){sf.base.isNullOrUndefined(this.refElement)&&(this.refElement=sf.base.createElement("div",{className:"e-toast-ref-element"}),this.toastContainer=this.element.parentElement,this.toastContainer.parentElement.insertBefore(this.refElement,this.element.parentElement));this.setAnimation(e);var i="string"===s(this.target)?document.querySelector(this.target):document.body;if(e.rootElement.style.zIndex=sf.popups.getZindexPartial(e.rootElement)+"",!sf.base.isNullOrUndefined(i)){"BODY"===i.tagName?this.toastContainer.style.position="fixed":(this.toastContainer.style.position="absolute",i.style.position="relative"),i.appendChild(this.toastContainer),this.appendToast(e.element);var n=parseInt(e.element.id.split("toast_")[1],10);this.progressObj[n]={hideEstimatedTimeOfArrival:null,intervalId:null,maxHideTime:null,element:null,timeOutId:null,progressEle:null},this.progressObj[n].element=e.element,this.extendedTimeout>0&&this.timeOut>0&&(sf.base.EventHandler.add(e.element,"mouseover",this.toastHoverAction.bind(this,n)),sf.base.EventHandler.add(e.element,"mouseleave",this.delayedToastProgress.bind(this,n))),e.showProgressBar&&(this.progressObj[n].progressEle=e.element.querySelector("."+t)),sf.base.EventHandler.add(e.element,"keydown",this.keyDownHandler,this)}},e.prototype.getDomObject=function(t,e){return null!=e?window.sfBlazor.getDomObject(t,e):null},e.prototype.swipeHandler=function(t){var e=sf.base.closest(t.originalEvent.target,".e-toast:not(.e-toast-container)"),s=this.hideAnimation.effect;sf.base.isNullOrUndefined(e)||("Right"===t.swipeDirection?(this.hideAnimation.effect="SlideRightOut",this.hide("swipe",e)):"Left"===t.swipeDirection&&(this.hideAnimation.effect="SlideLeftOut",this.hide("swipe",e)),this.hideAnimation.effect=s)},e.prototype.delayedToastProgress=function(e){var s=this.progressObj[e],i=s.element;s.timeOutId=window.setTimeout(this.destroyToast.bind(this,i),this.extendedTimeout),s.maxHideTime=parseFloat(this.extendedTimeout+""),s.hideEstimatedTimeOfArrival=(new Date).getTime()+s.maxHideTime,sf.base.isNullOrUndefined(i.querySelector("."+t))||(s.intervalId=setInterval(this.updateProgressBar.bind(this,s),10))},e.prototype.toastHoverAction=function(e){this.dotNetRef.invokeMethodAsync("ClearTimeout",e),clearTimeout(this.progressObj[e].timeOutId),clearInterval(this.progressObj[e].intervalId),this.progressObj[e].hideEstimatedTimeOfArrival=0;var s=this.progressObj[e].element;sf.base.isNullOrUndefined(s.querySelector("."+t))||(this.progressObj[e].progressEle.style.width="0%"),this.dotNetRef.invokeMethodAsync("MouseoverEvent",e)},e.prototype.updateProgressBar=function(t){var e=(t.hideEstimatedTimeOfArrival-(new Date).getTime())/t.maxHideTime*100;e="LTR"===this.progressDirection?100-e:e,t.progressEle.style.width=e+"%"},e.prototype.appendToast=function(t){this.newestOnTop&&0!==this.toastContainer.childElementCount&&this.toastContainer.insertBefore(t,this.toastContainer.children[0]),sf.base.removeClass([t],"e-blazor-toast-hidden")},e.prototype.setAnimation=function(t){var e=this,s=this,i=this.showAnimation,n={duration:i.duration,name:i.effect,timingFunction:i.easing,begin:function(){t.element.style.display=""},end:function(){sf.base.isNullOrUndefined(document.querySelector("#"+s.element.id))||s.dotNetRef.invokeMethodAsync("OpenEvent",t.index,e.getDomObject("element",t.element))}};new sf.base.Animation(n).animate(t.element)},e.prototype.hide=function(t,e){if(!sf.base.isNullOrUndefined(this.toastContainer)&&0!==this.toastContainer.childElementCount)if("string"!==s(e)||"All"!==e){if("string"===s(e)&&"All"!==e){var i=this.toastContainer.querySelector("#toast_"+e);i&&(this.destroyToast(i,t),this.dotNetRef.invokeMethodAsync("DestroyTimer",parseInt(e,10)))}sf.base.isNullOrUndefined(e)&&(e=this.newestOnTop?this.toastContainer.lastElementChild:this.toastContainer.firstElementChild),this.destroyToast(e,t);var n=parseInt(e.id.split("toast_")[1],10);this.dotNetRef.invokeMethodAsync("DestroyTimer",n)}else for(var o=0;o<this.toastContainer.childElementCount;o++)this.destroyToast(this.toastContainer.children[o],t)},e.prototype.destroyToast=function(t,e){if(0!==this.toastContainer.attributes.length){var s=parseInt(t.id.split("toast_")[1],10);this.dotNetRef.invokeMethodAsync("OnCloseEvent",s,e)}},e.prototype.hideAnimationToast=function(t,e){var s=this,i=this.hideAnimation,n={duration:i.duration,name:i.effect,timingFunction:i.easing,end:function(){sf.base.detach(t),s.dotNetRef.invokeMethodAsync("CloseEvent",e)}};new sf.base.Animation({}).animate(t,n)},e.prototype.destroy=function(){if(!sf.base.isNullOrUndefined(this.toastContainer))for(;this.toastContainer.attributes.length>0;)this.toastContainer.removeAttribute(this.toastContainer.attributes[0].name);for(var t=this.toastContainer.children,e=t.length-1;e>=0;e--)sf.base.detach(t[e]);sf.base.isNullOrUndefined(this.refElement)||sf.base.isNullOrUndefined(this.refElement.parentElement)||(this.refElement.parentElement.insertBefore(this.toastContainer,this.refElement),sf.base.detach(this.refElement),this.refElement=void 0),this.toastContainer=null,this.element=null,window.sfBlazor.instances[this.dataId]=null},e.prototype.keyDownHandler=function(t){if(t.target.classList.contains("e-toast-close-icon")&&(13===t.keyCode||32===t.keyCode)){var e=t.target,s=sf.base.closest(e,".e-toast");this.destroyToast(s,"key")}},e.prototype.updateContext=function(t){sf.base.extend(this,this,t)},e}();return{initialize:function(t){t.dataId&&(new e(t),window.sfBlazor.instances[t.dataId].initialize())},show:function(t){t.rootElement&&(window.sfBlazor.instances[t.dataId].updateContext(t),window.sfBlazor.instances[t.dataId].show(t))},hideAnimationToast:function(t,e){if(t){var s=window.sfBlazor.instances[t].rootElement;window.sfBlazor.instances[t].hideAnimationToast(s.querySelector("#toast_"+e),e)}},hide:function(t,e,s,i){t&&(window.sfBlazor.instances[t].updateContext(s),window.sfBlazor.instances[t].hide(i,e))},appendToast:function(t,e){t&&window.sfBlazor.instances[t].appendToast(e)},destroy:function(t){t&&window.sfBlazor.instances[t].destroy()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sftoast');})})();