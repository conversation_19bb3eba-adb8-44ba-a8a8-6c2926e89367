@using Microsoft.AspNetCore.Components.Web
@namespace ComRemitBlazor.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<!DOCTYPE html>
<html lang="zh-tw">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="~/" />
    <link rel="stylesheet" href="css/bootstrap/bootstrap.min.css" />
    <link href="css/site.css" rel="stylesheet" />
    <link href="ComRemitBlazor.styles.css" rel="stylesheet" />
    <component type="typeof(HeadOutlet)" render-mode="ServerPrerendered" />
</head>
<body>
    @RenderBody()

    <div id="blazor-error-ui">
        <environment include="Staging,Production">
            發生錯誤。此應用程式可能不再回應，直到重新載入為止。
        </environment>
        <environment include="Development">
            發生未處理的例外狀況。查看瀏覽器開發人員工具以取得詳細資訊。
        </environment>
        <a href="" class="reload">重新載入</a>
        <a class="dismiss">🗙</a>
    </div>

    <script src="_framework/blazor.server.js"></script>
</body>
</html> 