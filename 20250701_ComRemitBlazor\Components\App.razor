﻿<!DOCTYPE html>
<html lang="zh-tw">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <link rel="stylesheet" href="/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="/app.css" />
    <link rel="stylesheet" href="/ComRemitBlazor.styles.css" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <HeadOutlet />
</head>

<body>
    <Router AppAssembly="@typeof(App).Assembly">
        <Found Context="routeData">
            <RouteView RouteData="@routeData" DefaultLayout="@typeof(ComRemitBlazor.Components.Layout.MainLayout)" />
            <FocusOnNavigate RouteData="@routeData" Selector="h1" />
        </Found>
        <NotFound>
            <PageTitle>找不到頁面</PageTitle>
            <LayoutView Layout="@typeof(ComRemitBlazor.Components.Layout.MainLayout)">
                <p role="alert">很抱歉，找不到此頁面。</p>
            </LayoutView>
        </NotFound>
    </Router>
    
    <script src="/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="_framework/blazor.web.js"></script>
</body>

</html>
