/*!*  filename: sf-accumulation-chart.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[7],{"./bundles/sf-accumulation-chart.js":function(e,t,n){"use strict";n.r(t);n("./modules/sf-accumulation-chart.js")},"./modules/sf-accumulation-chart.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.AccumulationChart=function(){"use strict";var e=function(e,t){this.x=e,this.y=t},t=function(){function e(e,t,n,i,o){window.sfBlazor=window.sfBlazor,this.chartOnMouseDownRef=null,this.mouseMoveRef=null,this.mouseMoveThreshold=null,this.mouseEndRef=null,this.chartOnMouseClickRef=null,this.chartRightClickRef=null,this.mouseLeaveRef=null,this.chartMouseWheelRef=null,this.domMouseMoveRef=null,this.domMouseUpRef=null,this.chartKeyDownRef=null,this.chartKeyUpRef=null,this.longPressBound=null,this.touchObject=null,this.mouseY=0,this.mouseX=0,this.eventInterval=80,this.documentKeyHandler=function(e){e.altKey&&74===e.keyCode&&!sf.base.isNullOrUndefined(this.element)&&this.element.focus()},this.id=t,this.element=n,this.dotnetref=i,this.options=o,this.currentLegendIndex=0,this.currentPointIndex=0,this.previousTargetId="",this.dataId=e,window.sfBlazor.setCompInstance(this)}return e.prototype.render=function(){this.unWireEvents(),this.wireEvents()},e.prototype.destroy=function(){this.unWireEvents()},e.prototype.wireEvents=function(){var e=document.getElementById(this.id);if(e){n.dotnetrefCollection.push({id:this.id,dotnetref:this.dotnetref});
/*! Find the Events type */
var t=sf.base.Browser.isPointer?"pointerleave":"mouseleave";this.chartOnMouseDownRef=this.chartOnMouseDown.bind(this,this.dotnetref,this.id),this.mouseMoveRef=this.mouseMove.bind(this,this.dotnetref,this.id),this.mouseEndRef=this.mouseEnd.bind(this,this.dotnetref,this.id),this.chartOnMouseClickRef=this.chartOnMouseClick.bind(this,this.dotnetref,this.id),this.chartRightClickRef=this.chartRightClick.bind(this,this.dotnetref,this.id),this.chartKeyDownRef=this.chartOnKeyDown.bind(this,this.dotnetref,this.id),this.chartKeyUpRef=this.chartOnKeyUp.bind(this,this.dotnetref,this.id),this.mouseLeaveRef=this.mouseLeave.bind(this,this.dotnetref,this.id),
/*! Bind the Event handler */
e.addEventListener("mousemove",this.mouseMoveRef),e.addEventListener("touchmove",this.mouseMoveRef),sf.base.EventHandler.add(e,sf.base.Browser.touchEndEvent,this.mouseEndRef),sf.base.EventHandler.add(e,"click",this.chartOnMouseClickRef),sf.base.EventHandler.add(e,"contextmenu",this.chartRightClickRef),sf.base.EventHandler.add(e,t,this.mouseLeaveRef),sf.base.EventHandler.add(e,"keydown",this.chartKeyDownRef),sf.base.EventHandler.add(e,"keyup",this.chartKeyUpRef),sf.base.EventHandler.add(document.body,"keydown",this.documentKeyHandler),n.resizeBound=n.chartResize.bind(this,n.dotnetrefCollection);var i=sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize";sf.base.EventHandler.add(window,i,n.resizeBound),this.longPressBound=this.longPress.bind(this,this.dotnetref,this.id),this.touchObject=new sf.base.Touch(e,{tapHold:this.longPressBound,tapHoldThreshold:500})}},e.prototype.unWireEvents=function(){var e=this,t=document.getElementById(this.id);if(t){n.dotnetrefCollection=n.dotnetrefCollection.filter((function(t){return t.id!==e.id}));
/*! Find the Events type */
var i=sf.base.Browser.isPointer?"pointerleave":"mouseleave";
/*! Bind the Event handler */sf.base.EventHandler.remove(t,sf.base.Browser.touchStartEvent,this.chartOnMouseDownRef),t.removeEventListener("mousemove",this.mouseMoveRef),t.removeEventListener("touchmove",this.mouseMoveRef),sf.base.EventHandler.remove(t,sf.base.Browser.touchEndEvent,this.mouseEndRef),sf.base.EventHandler.remove(t,"click",this.chartOnMouseClickRef),sf.base.EventHandler.remove(t,"contextmenu",this.chartRightClickRef),sf.base.EventHandler.remove(t,i,this.mouseLeaveRef),sf.base.EventHandler.remove(t,"keydown",this.chartKeyDownRef),sf.base.EventHandler.remove(t,"keyup",this.chartKeyUpRef),sf.base.EventHandler.remove(document.body,"keydown",this.documentKeyHandler);var o=sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize";sf.base.EventHandler.remove(window,o,n.resizeBound),this.touchObject&&(this.touchObject.destroy(),this.touchObject=null)
/*! Apply the style for chart */}},e.prototype.getEventArgs=function(e){var t=e.changedTouches?e.changedTouches[0].clientX:e.clientX,n=e.changedTouches?e.changedTouches[0].clientY:e.clientY;return this.setMouseXY(t,n),{type:e.type,clientX:e.clientX,clientY:e.clientY,mouseX:this.mouseX,mouseY:this.mouseY,pointerType:e.pointerType,target:e.target.id,changedTouches:{clientX:e.changedTouches?e.changedTouches[0].clientX:0,clientY:e.changedTouches?e.changedTouches[0].clientY:0}}},e.prototype.setMouseXY=function(e,t){var n=document.getElementById(this.id+"_svg").getBoundingClientRect(),i=document.getElementById(this.id).getBoundingClientRect();this.mouseY=t-i.top-Math.max(n.top-i.top,0),this.mouseX=e-i.left-Math.max(n.left-i.left,0)},e.prototype.chartOnMouseDown=function(e,t,n){return this.dotnetref=e,this.id=t,this.dotnetref.invokeMethodAsync("OnChartMouseDown",this.getEventArgs(n)),!1},e.prototype.chartOnKeyDown=function(e,t,n){this.dotnetref=e,this.id=t;var i="";return this.options.showTooltip&&("Tab"==n.code&&this.previousTargetId.indexOf("Series")>-1||"Escape"===n.code)&&(i="ESC"),(n.code.indexOf("Arrow")>-1||"Enter"==n.code||"Space"==n.code)&&n.preventDefault(),n.ctrlKey&&"p"===n.key&&(n.preventDefault(),i="CtrlP"),""!=i&&e.invokeMethodAsync("OnAccumulationChartKeyboardNavigations",i,n.target.id),!1},e.prototype.chartOnKeyUp=function(e,t,i){this.dotnetref=e,this.id=t;var o="",s=i.target.id,r=document.getElementById(this.element.id+"_chart_legend_translate_g"),d=document.getElementById(this.element.id+"_chart_legend_pageup");if(r){var a=r.firstElementChild,h=a.getAttribute("class");h&&-1===h.indexOf("e-chart-focused")?h+=" e-chart-focused":h="e-chart-focused",a.setAttribute("class",h)}if(d&&d.setAttribute("class","e-chart-focused"),"Tab"==i.code){if(""!=this.previousTargetId)if(this.previousTargetId.indexOf("_Point_")>-1&&-1==s.indexOf("_Point_")){var l=document.getElementById(this.previousTargetId).parentElement;n.setTabIndex(l.children[this.currentPointIndex],l.firstElementChild),this.currentPointIndex=0}else this.previousTargetId.indexOf("_chart_legend_page")>-1&&-1==s.indexOf("_chart_legend_page")&&-1==s.indexOf("_chart_legend_g_")?n.setTabIndex(i.target,d):this.previousTargetId.indexOf("_chart_legend_")>-1&&-1==s.indexOf("chart_legend_")&&n.setTabIndex(r.children[this.currentLegendIndex],r.firstElementChild);this.previousTargetId=s,s.indexOf("_chart_legend_g_")>-1?(s=i.target.lastElementChild.id,this.currentLegendIndex=+s.split("_chart_legend_text_")[1],o="Tab"):s.indexOf("_Point_")>-1&&(this.options.enableHighlight||this.options.showTooltip)&&(o="Tab")}else if(i.code.indexOf("Arrow")>-1){if(i.preventDefault(),s.indexOf("_chart_legend_page")>-1)i.target.removeAttribute("tabindex"),this.previousTargetId=s=this.element.id+"_chart_legend_page"+("ArrowRight"==i.code?"up":"down"),n.focusTarget(document.getElementById(s));else if(s.indexOf("_chart_legend_")>-1){i.target.removeAttribute("tabindex");var c=+r.children[0].id.split("_chart_legend_g_")[1];this.currentLegendIndex+="ArrowUp"==i.code||"ArrowRight"==i.code?0==c?1:-1:0==c?-1:1,this.currentLegendIndex=n.getActualIndex(this.currentLegendIndex,r.children.length);var u=document.getElementById(this.element.id+"_chart_legend_g_"+this.currentLegendIndex);n.focusTarget(u),this.previousTargetId=s=u.lastElementChild.id,o=this.options.enableHighlight?"ArrowMove":""}else if(s.indexOf("_Point_")>-1){i.target.removeAttribute("tabindex"),this.currentPointIndex+="ArrowUp"==i.code||"ArrowRight"==i.code?1:-1;var f=n.GetSeriesElements();this.currentPointIndex=n.getActualIndex(this.currentPointIndex,f.length),s=f[this.currentPointIndex].id,n.focusTarget(document.getElementById(s)),o=this.options.showTooltip?"ArrowMove":""}}else"Enter"!=i.code&&"Space"!=i.code||!(s.indexOf("_chart_legend_")>-1||s.indexOf("_Point_")>-1)||(s=s.indexOf("_chart_legend_g")>-1?i.target.lastElementChild.id:s,o="Enter");return""!=o&&e.invokeMethodAsync("OnAccumulationChartKeyboardNavigations",o,s),!1},e.prototype.mouseMove=function(e,t,n){if(null==this.mouseMoveThreshold||(new Date).getTime()-this.mouseMoveThreshold>this.eventInterval){this.mouseMoveThreshold=(new Date).getTime(),this.dotnetref=e,this.id=t;var i=void 0,o=void 0,s=void 0;"touchmove"===n.type?(this.isTouch=!0,i=(s=n).changedTouches[0].clientX,o=s.changedTouches[0].clientY):(this.isTouch="touch"===n.pointerType||"2"===n.pointerType||this.isTouch,i=n.clientX,o=n.clientY),document.getElementById(this.id+"_svg")&&(this.setMouseXY(i,o),this.dotnetref.invokeMethodAsync("OnChartMouseMove",this.getEventArgs(n)))}return!1},e.prototype.mouseEnd=function(e,t,n){return this.dotnetref=e,this.id=t,this.dotnetref.invokeMethodAsync("OnChartMouseEnd",this.getEventArgs(n)),!1},e.prototype.chartOnMouseClick=function(e,t,n){return this.dotnetref=e,this.id=t,this.dotnetref.invokeMethodAsync("OnChartMouseClick",this.getEventArgs(n)),!1},e.prototype.chartRightClick=function(e,t,n){return this.dotnetref=e,this.id=t,this.dotnetref.invokeMethodAsync("OnChartRightClick",this.getEventArgs(n)),!1},e.prototype.mouseLeave=function(e,t,n){return this.dotnetref=e,this.id=t,this.dotnetref.invokeMethodAsync("OnChartMouseLeave",this.getEventArgs(n)),!1},e.prototype.longPress=function(e,t,n){return this.dotnetref=e,this.id=t,this.dotnetref.invokeMethodAsync("OnChartLongPress",n),!1},e}(),n={initialize:function(e,n,i,o){var s=new t(e,n.id,n,i,o);n&&(this.id=n.id),s.render()},destroy:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.destroy()},focusTargetId:function(e){var t,n;if(e.indexOf("_chart_legend_")>-1){n=(t=document.getElementById(e).parentElement).getAttribute("class");var i=document.getElementById(this.id+"_SeriesCollection");i&&i.firstElementChild.children[0].removeAttribute("tabindex");var o=this.GetSeriesElements();o.length>0&&this.setTabIndex(t,o[0]),t.setAttribute("tabindex","0");var s=document.getElementById(this.id+"_chart_legend_translate_g").firstElementChild;s&&s.removeAttribute("tabindex"),n&&-1===n.indexOf("e-chart-focused")?n+=" e-chart-focused":n||(n="e-chart-focused")}else t=this.getElement(e),n="e-chart-focused";t.setAttribute("tabindex","0"),t.setAttribute("class",n),t.focus()},focusTarget:function(e){var t=e.getAttribute("class");return e.setAttribute("tabindex","0"),t&&-1===t.indexOf("e-chart-focused")?t+=" e-chart-focused":t||(t="e-chart-focused"),e.setAttribute("tabindex","0"),e.setAttribute("class",t),e.focus(),e.id},getActualIndex:function(e,t){return e>t-1?0:e<0?t-1:e},setTabIndex:function(e,t){e.removeAttribute("tabindex"),t.setAttribute("tabindex","0")},GetSeriesElements:function(){for(var e=document.getElementById(this.id+"_SeriesCollection"),t=[],n=0;n<e.firstElementChild.children.length;n++)if(e.firstElementChild.children[n].id.indexOf("_Point_")>-1){var i=e.firstElementChild.children[n].getAttribute("visibility");i&&"visible"==i&&t.push(e.firstElementChild.children[n])}return t},id:"",getParentElementBoundsById:function(e){var t=document.getElementById(e);if(t){t.style.width="100%",t.style.height="100%";var n=t.getBoundingClientRect();return{width:n.width||t.clientWidth||t.offsetWidth,height:n.height||t.clientHeight||t.offsetHeight,left:n.left,top:n.top,right:n.right,bottom:n.bottom}}return{width:0,height:0,left:0,top:0,right:0,bottom:0}},getElementBoundsById:function(e,t){void 0===t&&(t=!0),t&&(this.id=e);var n=document.getElementById(e);if(n){var i=n.getBoundingClientRect();return{width:n.clientWidth||n.offsetWidth,height:n.clientHeight||n.offsetHeight,left:i.left,top:i.top,right:i.right,bottom:i.bottom}}return{width:0,height:0,left:0,top:0,right:0,bottom:0}},getAllCharacters:function(){for(var e=[],t=33;t<591;t++)e.push(String.fromCharCode(t));return e},measureText:function(e,t,n,i){var o=document.getElementById("chartmeasuretext");return null===o&&(o=sf.base.createElement("text",{id:"chartmeasuretext"}),document.body.appendChild(o))," "===e&&(e="&nbsp;"),o.innerHTML=e,o.style.position="fixed",o.style.fontSize="100px",o.style.fontWeight=t,o.style.fontStyle=n,o.style.fontFamily=i,o.style.visibility="hidden",o.style.top="-100",o.style.left="0",o.style.whiteSpace="nowrap",o.style.lineHeight="normal",{Width:o.clientWidth,Height:o.clientHeight}},getCharCollectionSize:function(e){for(var t=[],n=this.getAllCharacters(),i=n.length,o=e.length,s=0;s<o;s++)for(var r=e[s].split("_"),d=r[0],a=r[1],h=r[2],l=0;l<i;l++)t.push(this.measureText(n[l],d,a,h).Width+"");return JSON.stringify(t)},dotnetref:{},dotnetrefCollection:[],resizeBound:{},resize:{},chartResize:function(e,t){var n=this;return this.resize&&clearTimeout(this.resize),this.resize=setTimeout((function(){for(var t=e.length,i=0;i<t;i++)document.getElementById(e[i].id)&&e[i].dotnetref.invokeMethodAsync("RemoveElements");for(i=0;i<t;i++)document.getElementById(e[i].id)&&e[i].dotnetref.invokeMethodAsync("OnChartResize");clearTimeout(n.resize)}),500),!1},performAnimation:function(e,t,n,i,o,s,r,d,a){var h=this,l=/translate\((-?\d+\.?\d*),?\s*(-?\d+[.]?\d*)?\)/.exec(d);sf.base.isNullOrUndefined(d)||""===d||(o=+l[1],s=+l[2]);var c="";if(r<=0)return this.setElementTransform(t,e,"transform","translate("+o+", "+s+")",c),null;var u,f,m=t.replace("Series_0","datalabel").replace("Point","Series_0");if(!sf.base.isNullOrUndefined(document.getElementById(m+"text_"+e))){var g=(c=document.getElementById(m+"text_"+e).getAttribute("transform")).split(" ");g.length>2&&(c=g[2])}new sf.base.Animation({}).animate(sf.base.createElement("div"),{duration:r,progress:function(r){u=h.linear(r.timeStamp,n,o,r.duration),f=h.linear(r.timeStamp,i,s,r.duration),h.setElementTransform(t,e,"transform","translate("+(a?o-u:u)+", "+(a?s-f:f)+")",c)},end:function(i){h.setElementTransform(t,e,"transform","translate("+(a?n:o)+", "+(a?n:s)+")",c)}})},setElementTransform:function(e,t,n,i,o){var s=e.replace("Series_0","datalabel").replace("Point","Series_0");this.setElementAttribute(e+t,"transform",i),this.setElementAttribute(s+"shape_"+t,"transform",i),this.setElementAttribute(s+"text_"+t,"transform",i+" "+o),this.setElementAttribute(s+"connector_"+t,"transform",i)},linear:function(e,t,n,i){return-n*Math.cos(e/i*(Math.PI/2))+n+t},setElementAttribute:function(e,t,n){var i=document.getElementById(e);i&&i.setAttribute(t,n)},getElementAttribute:function(e,t){var n=document.getElementById(e);return n?n.getAttribute(t):""},createStyleElement:function(e,t){document.body.appendChild(sf.base.createElement("style",{id:e,innerHTML:t}))},renderTooltip:function(e,t,n,i){var o=document.getElementById(t+"_svg"),s=!(o&&parseInt(o.getAttribute("opacity"),10)>0),r=JSON.parse(e),d=window.sfBlazor.getCompInstance(i);s&&!sf.base.isNullOrUndefined(d)?(d.tooltip=new sf.svgbase.Tooltip(r),d.tooltip.enableRTL=r.enableRTL,d.tooltip.animationComplete=function(e){e.tooltip.fadeOuted&&n.invokeMethodAsync("TooltipAnimationComplete")},d.tooltip.appendTo("#"+t)):sf.base.isNullOrUndefined(d.tooltip)||(d.tooltip.location=new sf.svgbase.TooltipLocation(r.location.x,r.location.y),d.tooltip.content=r.content,d.tooltip.header=r.header,d.tooltip.offset=r.offset,d.tooltip.palette=r.palette,d.tooltip.shapes=r.shapes,d.tooltip.data=r.data,d.tooltip.template=r.template,d.tooltip.textStyle.color=r.textStyle.color||d.tooltip.textStyle.color,d.tooltip.textStyle.fontFamily=r.textStyle.fontFamily||d.tooltip.textStyle.fontFamily,d.tooltip.textStyle.fontStyle=r.textStyle.fontStyle||d.tooltip.textStyle.fontStyle,d.tooltip.textStyle.fontWeight=r.textStyle.fontWeight||d.tooltip.textStyle.fontWeight,d.tooltip.textStyle.opacity=r.textStyle.opacity||d.tooltip.textStyle.opacity,d.tooltip.textStyle.size=r.textStyle.size||d.tooltip.textStyle.size,d.tooltip.isNegative=r.isNegative,d.tooltip.clipBounds=new sf.svgbase.TooltipLocation(r.clipBounds.x,r.clipBounds.y),d.tooltip.arrowPadding=r.arrowPadding,d.tooltip.dataBind())},fadeOut:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||!sf.base.isNullOrUndefined(t)&&sf.base.isNullOrUndefined(t.tooltip)||(this.removeTooltipCommentElement(t),t.tooltip.fadeOut())},removeTooltipCommentElement:function(e){var t=document.getElementById(e.tooltip.element.id);if(t&&!e.isRemoveCommentElement&&t.childNodes.length>1){for(var n=t.childNodes,i=[],o=0;o<n.length;o++)(n[o].nodeName.match("#comment")||n[o].nodeName.match("#text"))&&i.push(n[o]);for(var s=0,r=i;s<r.length;s++){var d=r[s];sf.base.remove(d),e.isRemoveCommentElement=!0}}},animateRedrawElement:function(e,t,n,i,o,s,r,d){var a=this;void 0===r&&(r="x"),void 0===d&&(d="y");var h=document.getElementById(e);if(!h)return null;var l="DIV"===h.tagName,c=function(e,t){l?(h.style[r]=e+"px",h.style[d]=t+"px"):(h.setAttribute(r,e+""),h.setAttribute(d,t+""))};c(n,i),new sf.base.Animation({}).animate(sf.base.createElement("div"),{duration:t,progress:function(e){c(a.linear(e.timeStamp,n,o-n,e.duration),a.linear(e.timeStamp,i,s-i,e.duration))},end:function(){c(o,s)}})},doAnimation:function(e,t,n,i,o,s,r,d){var a=this,h=document.getElementById(e);t-=90;var l,c=s||i;d.x+=1,r+=.414*r,new sf.base.Animation({}).animate(h,{duration:c,delay:o,progress:function(e){l=a.linear(e.timeStamp,t,n,e.duration),h.setAttribute("d",a.getPathArc(d,t,l,r,0))},end:function(e){d.x-=1,h.setAttribute("d",a.getPathArc(d,t,t-9e-5,r,0));var n=document.getElementById(h.id.split("Series")[0]+"datalabel_Series_0")||document.getElementById(h.id.split("Series")[0]+"chart_datalabel_Series_0");n&&n.setAttribute("style","visibility: visible")}})},getPathArc:function(e,t,n,i,o){var s=n-t,r=(s=s<0?s+360:s)<180?0:1;return o||0!==o?this.getDoughnutPath(e,this.degreeToLocation(t,i,e),this.degreeToLocation(n,i,e),i,this.degreeToLocation(t,o,e),this.degreeToLocation(n,o,e),o,r):this.getPiePath(e,this.degreeToLocation(t,i,e),this.degreeToLocation(n,i,e),i,r)},getPiePath:function(e,t,n,i,o){return"M "+e.x+" "+e.y+" L "+t.x+" "+t.y+" A "+i+" "+i+" 0 "+o+" 1 "+n.x+" "+n.y+" Z"},getDoughnutPath:function(e,t,n,i,o,s,r,d){return"M "+t.x+" "+t.y+" A "+i+" "+i+" 0 "+d+" 1 "+n.x+" "+n.y+" L "+s.x+" "+s.y+" A "+r+" "+r+" 0 "+d+",0 "+o.x+" "+o.y+" Z"},degreeToLocation:function(t,n,i){var o=t*Math.PI/180;return new e(Math.cos(o)*n+i.x,Math.sin(o)*n+i.y)},ChangePiePath:function(e,t,n){for(var i=0,o=e;i<o.length;i++){var s=o[i];this.ChangePointPath(s.point,s.degree,s.start,s.pathOption,n,t,s.radius,s.innerRadius)}},GetPathOption:function(e,t,n,i,o){return t?this.getPathArc(e,n,(n+t)%360,i,o):""},ChangePointPath:function(e,t,n,i,o,s,r,d){var a,h,l=this,c=document.getElementById(i.id);new sf.base.Animation({}).animate(sf.base.createElement("div"),{duration:o,delay:0,progress:function(i){h=l.linear(i.timeStamp,e.degree,t-e.degree,i.duration),a=((a=l.linear(i.timeStamp,e.start,n-e.start,i.duration))/(Math.PI/180)+360)%360,c.setAttribute("d",l.GetPathOption(s,h,a,r,d)),e.isExplode,c.style.visibility="visible"},end:function(o){c.style.visibility=e.visible?"visible":"hidden",c.setAttribute("d",i.direction),e.degree=t,e.start=n}})}};return n}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfaccumulationchart');})})();