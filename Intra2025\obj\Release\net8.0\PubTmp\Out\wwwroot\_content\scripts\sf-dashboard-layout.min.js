/*!*  filename: sf-dashboard-layout.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[18],{"./bundles/sf-dashboard-layout.js":function(e,t,i){"use strict";i.r(t);i("./modules/sf-dashboard-layout.js")},"./modules/sf-dashboard-layout.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.DashboardLayout=function(){"use strict";var e=function(){function e(e,t,i){this.cellAspectRatio=1,this.mOffX=0,this.mOffY=0,this.maxTop=9999,this.maxRows=100,this.mouseX=0,this.mouseY=0,this.minTop=0,this.minLeft=0,this.allowPushing=!0,this.allowResizing=!1,this.panelElements=[],this.renderedElement=[],this.allowDragging=!1,this.isRenderComplete=!1,this.allowFloating=!1,this.overlapElement=[],this.panelsSizeY=0,this.shouldRestrict=!1,this.overlapElementClone=[],this.addPanelCalled=!1,this.rows=0,this.cellSpacing=[],this.resizeCalled=!1,this.movePanelCalled=!1,this.isResizing=!1,this.resizeTimeCount=0,this.isChanged=!1,this.isDragged=!1,this.isRefreshCalled=!1,this.changedId=[],this.panelMove=!1,this.element=t,this.dataId=e,window.sfBlazor.setCompInstance(this),this.dotnetRef=i}return e.prototype.preRender=function(e){this.dragCollection=[],this.renderedElement=[],this.overlapSubElementClone=[],this.panels=[],this.sortedArray=[],this.allItems=[],this.oldRowCol={},this.cellSpacing=[],this.checkCollision=[],this.cloneObject={},this.maxColumnValue=this.columns,this.mainElement=null,e&&this.getProperty(e),this.isMediaQuery=this.checkMediaQuery(),this.calculateCellSizeValue()},e.prototype.initialize=function(e){this.preRender(e),sf.base.isNullOrUndefined(this.panels)&&(this.panels=[]);for(var t=0;t<this.element.querySelectorAll(".e-panel").length;t++)this.panelElements.push(this.element.querySelectorAll(".e-panel")[t]);this.panelsInitialModel=this.cloneModels(this.panels),this.renderDashBoardCells(this.panels),!this.allowDragging||this.mediaQuery&&window.matchMedia("("+this.mediaQuery+")").matches||this.enableDraggingContent(this.panelElements),this.checkColumnValue=this.maxColumnValue,this.isRenderComplete=!0,this.bindEvents();var i=this.calculateCellSize(),s=this.contentHeight();return JSON.stringify({parentOffset:i,enableMediaQuery:this.isMediaQuery,panelHeaderModel:s})},e.prototype.clearPanelElements=function(){this.panelElements=[]},e.prototype.calculateCellSize=function(){return this.cellSizeValue=[],this.cellSizeValue[0]=this.element.parentElement&&this.element.parentElement.offsetWidth,this.cellSizeValue},e.prototype.checkMediaQuery=function(){return this.mediaQuery&&window.matchMedia("("+this.mediaQuery+")").matches},e.prototype.getProperty=function(e){this.mediaQuery=e.MediaQuery,this.allowDragging=e.AllowDragging,this.columns=e.Columns,this.allowFloating=e.AllowFloating,this.rows=e.MaxRow+1,this.cellSpacing=e.CellSpacing,this.cellAspectRatio=e.CellAspectRatio,this.allowResizing=e.AllowResizing,this.panels=e.Panels,this.draggableHandle=e.DraggableHandle},e.prototype.movePanel=function(e,t,i){this.panelMove=!0,this.movePanelCalled=!0,this.panelsInitialModel=this.cloneModels(this.panels);var s=this.getCellInstance(e),l=document.getElementById(e),n=this.getCellInstance(l.id);this.mainElement=l,this.startRow=this.dragRow=n.row,this.startCol=this.dragColumn=n.col,this.updatePanelPosition(n.id,i,t),this.updateOldRowColumn(),this.updatePanelLayout(l,s),this.updatePanels(),this.updateCloneArrayObject(),this.mainElement=null,this.allowFloating&&this.moveItemsUpwards(),this.movePanelCalled=!1,this.panelMove||this.dotnetRef.invokeMethodAsync("UpdatedPanelsValues",this.panels)},e.prototype.removePanel=function(e){this.panelsSizeY=0;for(var t=this.renderedElement.length-1;t>=0;t--)if(this.renderedElement[t].id===e){this.renderedElement.splice(t,1);break}for(t=this.panelElements.length-1;t>=0;t--)this.panelElements[t].id===e&&this.panelElements.splice(t,1),this.panels[t].id===e&&(this.panels.splice(t,1),this.updateOldRowColumn(),this.sortedPanel());this.updatePanels(),this.updateCloneArrayObject(),this.panelsInitialModel=this.cloneModels(this.panels)},e.prototype.renderDashBoardCells=function(e){if(this.element.querySelectorAll(".e-panel").length>0||this.panels.length>0){this.updateOldRowColumn();for(var t=0;t<this.panels.length;t++){if(this.columns<this.panels[t].col||this.columns<this.panels[t].col+this.panels[t].sizeX){var i=this.columns-this.panels[t].sizeX;this.updatePanelPosition(this.panels[t].id,i<0?0:i,this.panels[t].row)}this.renderedElement.push(this.panelElements[t]);var s=this.renderCell(e[t]);this.updatePanelLayout(s,this.panels[t])}this.updatePanels(),this.updateCloneArrayObject();var l=this.checkForChanges();l.length>0&&this.dotnetRef.invokeMethodAsync("UpdatedPanelsValues",l)}},e.prototype.resizePanel=function(e,t,i){var s=this.getCellInstance(e);this.resizeCalled=!0;var l=document.getElementById(e);this.updatePanelSize(s.id,t,i),this.mainElement=l,this.updatePanelLayout(l,s),this.updatePanels(),this.resizeCalled=!1,this.dotnetRef.invokeMethodAsync("UpdatedPanelsValues",this.panels)},e.prototype.removeAll=function(){this.removeAllCalled=!0;for(var e=0;e<this.panelElements.length;e++)this.panelElements.splice(e,1);for(e=this.renderedElement.length-1;e>=0;e--)this.renderedElement.splice(e,1);this.rows=0,this.sortedPanel(),this.sortedArray=[],this.overlapElementClone=[],this.overlapElement=[],this.overlapSubElementClone=[],this.panelElements=[],this.oldRowCol={},this.cloneObject={},this.panels=[],this.updateCloneArrayObject(),this.removeAllCalled=!1},e.prototype.onPropertyChange=function(e){if(e.AllowDragging===this.allowDragging&&e.DraggableHandle==this.draggableHandle||(this.allowDragging=e.AllowDragging,this.draggableHandle=e.DraggableHandle,this.checkDragging(this.dragCollection)),e.AllowResizing!==this.allowResizing&&(this.allowResizing=e.AllowResizing),this.allowResizing&&this.resizeEvents(),e.CellSpacing!==this.cellSpacing&&(this.cellSpacing=e.CellSpacing,this.calculateCellSizeValue()),e.AllowFloating!==this.allowFloating)this.allowFloating=e.AllowFloating,this.moveItemsUpwards(),this.updatePanels(),this.updateOldRowColumn(),this.updateCloneArrayObject();else if(e.Columns!==this.columns){this.columns=e.Columns,this.calculateCellSizeValue();for(var t=0;t<this.panels.length;t++){if(this.columns<this.panels[t].col||this.columns<this.panels[t].col+this.panels[t].sizeX){var i=this.columns-this.panels[t].sizeX;this.updatePanelPosition(this.panels[t].id,i<0?0:i,this.panels[t].row)}this.updatePanelLayout(document.getElementById(this.panels[t].id),this.panels[t])}this.renderDashBoardCells(this.panels),this.updatePanels(),this.dotnetRef.invokeMethodAsync("UpdatedPanelsValues",this.panels)}this.getProperty(e)},e.prototype.updatePanelLayout=function(e,t){var i=this;this.collisionChecker={};var s=[],l=[];if((s=this.mainElement&&this.isRenderComplete?this.resetLayout(t):this.collisions(t.row,t.col,t.sizeX,t.sizeY,e)).length>0)if(s=this.sortCollisionItems(s),s=this.updatedModels(s,t,e),!sf.base.isNullOrUndefined(this.startRow)&&this.checkForSwapping(s,e))this.swapItems(s,e,t);else for(var n=0;n<s.length;n++){var o=this.getCellInstance(s[n].id);if(this.checkingElement=s[n],this.spacedRowValue=null,this.spacedColumnValue=null,this.adjustmentAvailable(o.row,o.col,o.sizeY,o.sizeX,e)&&!sf.base.isNullOrUndefined(this.spacedColumnValue)){if(this.updatePanelPosition(o.id,this.spacedColumnValue,this.spacedRowValue),this.oldRowCol[s[n].id]={row:this.spacedRowValue,col:this.spacedColumnValue},s[n].setAttribute("data-row",this.spacedRowValue.toString()),s[n].setAttribute("data-col",this.spacedColumnValue.toString()),this.topAdjustable&&this.allowFloating&&(this.updatePanels(),this.updateCloneArrayObject()),this.spacedRowValue=null,n<s.length)continue}else l.push(s[n])}l.length>0&&(l.forEach((function(e){-1===i.overlapElement.indexOf(e)&&i.overlapElement.push(e)})),this.overlapElement&&-1!==this.overlapElement.indexOf(e)&&this.overlapElement.splice(this.overlapElement.indexOf(e),1),l.length>0&&(this.updateRowColumn(t.row,this.overlapElement,e),this.checkForCompletePushing())),this.isSubValue||this.sortedPanel()},e.prototype.addPanel=function(e){for(var t=!1,i=0;i<this.panels.length;i++)if(this.panels[i].id===e.id){t=!0;break}if(!t){this.panelsSizeY=0,e.minSizeX||(e.minSizeX=1),e.minSizeY||(e.minSizeY=1),this.panels.push(e);var s=this.renderCell(e);if(this.panelElements.push(s),this.renderedElement.push(s),this.panelsInitialModel=this.cloneModels(this.panels),this.oldRowCol[e.id]={row:e.row,col:e.col},this.cloneObject[e.id]={row:e.row,col:e.col},this.updateOldRowColumn(),this.element.insertAdjacentElement("afterbegin",s),this.addPanelCalled=!0,this.checkMediaQuery()||(this.mainElement=s,this.checkCollision||(this.checkCollision=[]),this.updatePanelLayout(s,e)),this.addPanelCalled=!1,this.allowDragging&&this.mediaQuery&&!this.checkMediaQuery()&&this.enableDraggingContent([document.getElementById(e.id)]),this.allowFloating&&(this.mainElement=null,this.moveItemsUpwards()),this.updateOldRowColumn(),this.sortedPanel(),this.updateCloneArrayObject(),this.allowResizing)for(i=0;i<s.querySelectorAll(".e-resize").length;i++){var l="msie"===sf.base.Browser.info.name?"mousedown pointerdown":"mousedown";sf.base.EventHandler.add(s.querySelectorAll(".e-resize")[i],l,this.downResizeHandler,this),"msie"!==sf.base.Browser.info.name&&sf.base.EventHandler.add(s.querySelectorAll(".e-resize")[i],"touchstart",this.touchDownResizeHandler,this)}this.dotnetRef.invokeMethodAsync("UpdatePanelContentHeight",this.panels,this.contentHeight())}},e.prototype.checkForSwapping=function(e,t){if(!this.mainElement||0===e.length)return!1;var i,s=parseInt(e[0].getAttribute("data-row"),10);if(this.startRow<s?i=1:this.startRow>s&&(i=0),!this.swapAvailability(e,t))return!1;for(var l=!1,n=0;n<e.length;n++)if(e.length>=1&&this.cloneObject[this.mainElement.id].row===this.oldRowCol[this.mainElement.id].row)return!1;for(var o=0===i?this.getCellInstance(this.mainElement.id).row+this.getCellInstance(this.mainElement.id).sizeY:this.startRow,a=0;a<e.length;a++){var h=this.getCellInstance(e[a].id),r=this.getCellInstance(t.id),d=[];if(1===h.sizeY&&-1===d.indexOf(e[a]))d.push(e[a]);else if(h.sizeY>1&&-1===d.indexOf(e[a]))if(1===i&&r.row===this.cloneObject[h.id].row+h.sizeY-1)d.push(e[a]);else{if(0!==i||r.row!==this.cloneObject[h.id].row)return!1;d.push(e[a])}if(h.sizeY<=r.sizeY&&-1===d.indexOf(e[a])&&d.push(e[a]),d.push(this.mainElement),this.collisions(o,h.col,h.sizeX,h.sizeY,d).length>0)return l=!1;a===e.length-1&&(l=!0)}return l},e.prototype.swapAvailability=function(e,t){for(var i=!0,s=this.getCellInstance(t.id),l=0;l<e.length;l++)for(var n=this.getCellInstance(e[l].id),o=1;o<s.sizeY;o++){var a=[];if(a.push(t),a.push(e[l]),this.collisions(s.row+o,n.col,n.sizeX,n.sizeY,a).length>0)return i=!1,!1}return i},e.prototype.swapItems=function(e,t,i){var s,l=this,n=[];n.push(t);var o=this.getCellInstance(e[0].id).row;this.startRow<o?s=1:this.startRow>o&&(s=0);var a=0===s?o+i.sizeY:this.startRow;if(!this.movePanelCalled){var h=this.getCellInstance(e[0].id);this.updatePanelPosition(i.id,i.col,0===s?o:a+h.sizeY)}for(var r=0;r<e.length;r++)n.push(e[r]),this.updatePanelPosition(e[r].id,this.getCellInstance(e[r].id).col,a);this.updatePanelPosition(t.id,this.getCellInstance(t.id).col,i.row),this.setPanelPosition(this.shadowEle,i.row,i.col);for(var d=0;d<this.panels.length;d++)this.oldRowCol[this.panels[d].id]={row:this.panels[d].row,col:this.panels[d].col};this.startRow=this.dragRow=i.row,this.updateOldRowColumn(),n.forEach((function(e){l.cloneObject[e.id]=l.oldRowCol[e.id];for(var t=l.getCellInstance(e.id),i=0;i<l.sortedArray.length;i++)if(l.sortedArray[i])for(var s=0;s<l.sortedArray[i].length;s++)l.sortedArray[i][s]===e&&(l.sortedArray[i][s]=void 0);l.sortedArray[t.row]||(l.sortedArray[t.row]=[]),l.sortedArray[t.row][t.col]=e,l.cloneArray=l.sortedArray}))},e.prototype.resetLayout=function(e){var t=this.collisions(e.row,e.col,e.sizeX,e.sizeY,this.mainElement);if(!this.mainElement||this.addPanelCalled||this.resizeCalled||this.movePanelCalled)return t;if(this.mainElement&&this.oldRowCol!==this.cloneObject)for(var i=0;i<this.panels.length;i++){var s=document.getElementById(this.panels[i].id);if(s!==this.mainElement){var l=this.cloneObject[s.id].row,n=this.cloneObject[s.id].col;this.updatePanelPosition(s.id,n,l),s.setAttribute("data-col",n.toString()),s.setAttribute("data-row",l.toString()),this.updateOldRowColumn()}}return this.sortedArray=this.cloneArray,t=this.collisions(e.row,e.col,e.sizeX,e.sizeY,this.mainElement),this.sortedPanel(),this.updateOldRowColumn(),this.checkCollision&&this.checkCollision.length>0&&-1===t.indexOf(this.checkCollision[0])&&this.cloneObject[this.checkCollision[0].id].row===e.row&&t.push(this.checkCollision[0]),t},e.prototype.sortCollisionItems=function(e){for(var t,i=[],s=function(s){t=[],e.forEach((function(e){e&&e.getAttribute("data-row")===s.toString()&&t.push(e)}));for(var n=function(e){t.forEach((function(t){t&&t.getAttribute("data-col")===e.toString()&&i.push(t)}))},o=l.columns-1;o>=0;o--)n(o)},l=this,n=this.rows-1;n>=0;n--)s(n);return i},e.prototype.updatedModels=function(e,t,i){var s=this,l=[];return this.mainElement||this.sortedPanel(),e.forEach((function(n){s.checkingElement=n;var o=s.getCellInstance(n.id),a=!s.adjustmentAvailable(o.row,o.col,o.sizeY,o.sizeX,i);if(o.sizeX>1&&a)for(var h=o.row;h<t.row+t.sizeY;h++){s.collisions(h,o.col,o.sizeX,o.sizeY,n).forEach((function(t){e.indexOf(t)>=0&&-1===l.indexOf(t)&&l.push(t)}))}})),l.forEach((function(t){l.indexOf(t)>=0&&e.splice(e.indexOf(t),1)})),e},e.prototype.checkForCompletePushing=function(){for(var e=0;e<this.panels.length;e++)if(this.collisionChecker[this.panels[e].id]&&null!==this.collisionChecker[this.panels[e].id]){this.overlapElement=[this.collisionChecker[this.panels[e].id].ele];var t=this.panels[e].id;this.updateRowColumn(this.collisionChecker[t].row,this.overlapElement,this.collisionChecker[t].srcEle)}},e.prototype.updateRowColumn=function(e,t,i){if(i){var s=this.getCellInstance(i.id),l=s.sizeY,n=s.row;this.overlapElementClone=(this.overlapElement&&this.shouldRestrict,this.overlapElement);for(var o=0;o<this.overlapElementClone.length;o++){if(0===this.overlapElementClone.length)return;for(var a=0;a<this.overlapElementClone.length;a++)this.collisionChecker[this.overlapElementClone[a].id]={ele:this.overlapElementClone[a],row:e,srcEle:i};var h=n+l,r=this.getCellInstance(this.overlapElementClone[o].id),d=r.sizeY,u=r.col,c=r.sizeX,p=void 0,m=void 0;if(-1===this.overlapSubElementClone.indexOf(i)&&this.overlapSubElementClone.push(i),-1===this.overlapSubElementClone.indexOf(this.overlapElementClone[o])&&this.overlapSubElementClone.push(this.overlapElementClone[o]),d>1||c>1){var g=this.getCellInstance(this.overlapElementClone[o].id);p=g.col;for(var f=document.getElementById(g.id),v=g.row;v<n+l;v++){this.isSubValue=!0;var C=this.getCellInstance(g.id);f.setAttribute("data-row",(n+l).toString()),this.updatePanelPosition(C.id,C.col,g.row+1),this.updateCollisionChecked(f),this.oldRowCol[f.id]={row:g.row,col:p};var w=this.getCellInstance(f.id),z=w.row,y=w.col,E=w.sizeX,b=w.sizeY,R=[];R.push(f),R.push(i),m=this.collisions(z,y,E,b,R),this.mainElement&&-1!==m.indexOf(this.mainElement)&&m.splice(m.indexOf(this.mainElement),1),this.collisionPanel(m,y,z,f)}this.isSubValue=!1}else{this.addPanelCalled&&(this.addPanelCalled=!1);var S=[];S.push(this.overlapElementClone[o]),S.push(i),m=this.collisions(h,u,c,d,S),this.mainElement&&-1!==m.indexOf(this.mainElement)&&m.splice(m.indexOf(this.mainElement),1);var P=this.getCellInstance(this.overlapElementClone[o].id);p=P.col,this.updatePanelPosition(P.id,p,h),this.updateCollisionChecked(this.overlapElementClone[o]),this.oldRowCol[this.overlapElementClone[o].id]={row:h,col:p},this.collisionPanel(m,p,h,this.overlapElementClone[o])}}}},e.prototype.updateCollisionChecked=function(e){for(var t=0;t<Object.keys(this.collisionChecker).length;t++)this.collisionChecker[e.id]=null},e.prototype.collisionPanel=function(e,t,i,s){var l=this;if(e.length>0){this.overlapElement=[],this.shouldRestrict=!0,e.forEach((function(e){l.overlapElement.push(e)}));for(var n=this.getCellInstance(s.id).row,o=0;o<this.overlapElement.length;o++)this.updateRowColumn(n,this.overlapElement,s);this.shouldRestrict=!1}else{if(this.addPanelCalled||this.sortedPanel(),this.overlapSubElementClone.length>0)for(var a=0;a<this.overlapSubElementClone.length;a++){var h=this.getCellInstance(this.overlapSubElementClone[a].id),r=h.row,d=h.col,u=h.sizeX,c=h.sizeY,p=this.collisions(r,d,u,c,this.overlapSubElementClone);this.mainElement&&-1!==p.indexOf(this.mainElement)&&p.splice(p.indexOf(this.mainElement),1),p.forEach((function(e){l.overlapElement.push(e)})),p.length>0&&this.updateRowColumn(r,this.overlapElement,this.overlapSubElementClone[a])}this.overlapSubElementClone=[]}},e.prototype.collisions=function(e,t,i,s,l){var n,o=[];i&&s||(i=s=1),!l||l instanceof Array||(l=[l]);for(var a=0;a<s;++a)for(var h=0;h<i;++h)!(n=this.getPanel(e+a,t+h,l))||l&&-1!==l.indexOf(document.getElementById(n.id))||-1!==o.indexOf(document.getElementById(n.id))||o.push(document.getElementById(n.id));return o},e.prototype.getPanel=function(e,t,i){!i||i instanceof Array||(i=[i]);for(var s=1;e>-1;){for(var l=1,n=t;n>-1;){var o=this.sortedArray[e];if(o){var a=o[n];if(!sf.base.isNullOrUndefined(a)){var h=this.getCellInstance(a.id);if(a&&(!i||-1===i.indexOf(a))&&h.sizeX>=l&&h.sizeY>=s)return a}}++l,--n}--e,++s}return null},e.prototype.renderCell=function(e){return document.getElementById(e.id)},e.prototype.sortedPanel=function(){this.sortedArray=[];for(var e=0,t=this.renderedElement.length;e<t;++e)this.sortItem(this.renderedElement[e])},e.prototype.updatePanels=function(){this.moveItemsUpwards(),this.updateOldRowColumn(),this.sortedPanel()},e.prototype.setRootHeight=function(e){this.element.style.height=e+"px"},e.prototype.sortItem=function(e,t,i){this.overlapElement=[];var s=this.getCellInstance(e.id),l=s.col,n=s.row;if(this.sortedArray[n]||(this.sortedArray[n]=[]),this.sortedArray[n][l]=e,void 0!==e&&void 0!==t&&void 0!==i){if(void 0!==this.oldRowCol[e.id]&&null!==this.oldRowCol[e.id].row&&void 0!==this.oldRowCol[e.id].col){var o=this.sortedArray[this.oldRowCol[e.id].row];sf.base.isNullOrUndefined(o)||this.oldRowCol[e.id]&&o[this.oldRowCol[e.id].col]===e&&(delete o[this.oldRowCol[e.id].col],this.updateOldRowColumn(),this.sortedPanel())}this.oldRowCol[e.id].row=t,this.oldRowCol[e.id].row=i,this.sortedArray[n]||(this.sortedArray[n]=[]),this.sortedArray[n][l]=e,-1===this.allItems.indexOf(e)&&this.allItems.push(e);var a=this.getCellInstance(e.id);this.updatePanelPosition(a.id,i,t),this.sortedPanel()}},e.prototype.updateOldRowColumn=function(){this.oldRowCol={};for(var e=0;e<this.panels.length;e++){var t=this.panels[e].id;if(document.getElementById(t)){var i=this.panels[e].row,s=this.panels[e].col;this.oldRowCol[this.panels[e].id]={row:i,col:s}}}},e.prototype.getCellInstance=function(e){for(var t,i=0;i<this.panels.length;i++)if(this.panels[i].id===e){t=this.panels[i];break}return t},e.prototype.getRowElements=function(e){for(var t=[],i=0;i<e.length;i++)t.push(e[i]);return t},e.prototype.adjustmentAvailable=function(e,t,i,s,l){this.leftAdjustable=void 0,this.rightAdjustable=void 0;var n,o=!1,a=[],h=[];this.topAdjustable=void 0;var r=this.getCellInstance(l.id),d=r.sizeX,u=r.col;if(-1===(h=this.getRowElements(this.collisions(e,0,this.columns,i,[]))).indexOf(l)&&h.push(l),(n=this.leftWardsSpaceChecking(h,t,l)).length>0?(this.leftAdjustable=this.isLeftAdjustable(n,l,e,t,s,i),this.spacedColumnValue!==u-this.getCellInstance(this.checkingElement.id).sizeX&&(this.leftAdjustable=!1),this.leftAdjustable?this.rightAdjustable=!1:(this.leftAdjustable=!1,a=this.rightWardsSpaceChecking(h,t,l),this.rightAdjustable=a.length>0&&this.isRightAdjustable(a,l,e,t,s,i),this.spacedColumnValue!==d+u&&(this.rightAdjustable=!1))):(a=this.rightWardsSpaceChecking(h,t,l),this.rightAdjustable=a.length>0&&this.isRightAdjustable(a,l,e,t,s,i),this.spacedColumnValue!==d+u&&(this.rightAdjustable=!1),this.rightAdjustable&&(this.leftAdjustable=!1)),!this.rightAdjustable&&!this.leftAdjustable&&e>0){var c=this.getCellInstance(l.id).row,p=!1;this.startRow!==c&&(p=!0);for(var m=e;m>=0;m--){var g=this.getCellInstance(l.id).sizeY>1&&p?this.checkingElement:l;m!==c&&m===c-i&&0===this.collisions(m,t,s,i,g).length&&(p=!1,this.topAdjustable=!0,this.spacedRowValue=sf.base.isNullOrUndefined(this.spacedRowValue)?m:this.spacedRowValue,this.spacedColumnValue=t)}}return(this.rightAdjustable||this.leftAdjustable||this.topAdjustable)&&(o=!0,sf.base.isNullOrUndefined(this.spacedRowValue)&&(this.spacedRowValue=e)),o},e.prototype.updatePanelPosition=function(e,t,i){if(this.panels.length>0)for(var s=0;s<this.panels.length;s++)this.panels[s].id===e&&(sf.base.isNullOrUndefined(t)||(this.panels[s].col=t,this.panels[s].column=t),sf.base.isNullOrUndefined(i)||(this.panels[s].row=i));this.isResizing||this.isDragged||this.isRefreshCalled||this.updatePanelProps(e)},e.prototype.rightWardsSpaceChecking=function(e,t,i){var s=this,l=[],n=[];if(e.forEach((function(e){var i=s.getCellInstance(e.id),n=i.col,o=i.sizeX;if(t<s.columns&&n>=t)if(o>1)for(var a=n;a<n+o;a++)l.push(a);else l.push(n)})),l.length>0)for(var o=t+1;o<=this.columns-1;o++)-1===l.indexOf(o)&&o!==t&&-1===n.indexOf(o)&&n.push(o);var a=this.getOccupiedColumns(i);return a.forEach((function(e){e>t&&-1!==n.indexOf(e)&&n.splice(n.indexOf(e),1)})),this.getOccupiedColumns(this.checkingElement).forEach((function(e){e>s.getCellInstance(i.id).col&&-1===a.indexOf(e)&&-1===n.indexOf(e)&&n.push(e)})),n=n.sort((function(e,t){return e-t}))},e.prototype.isRightAdjustable=function(e,t,i,s,l,n){var o;if(1===l&&1===n&&e.length>0)this.spacedColumnValue=e[0],o=!0;else if(l>1&&1===n)o=this.isXSpacingAvailable(e,l);else if(n>1)if(1===l){var a=void 0;if(e.length>=1&&(a=!0),a)for(var h=0;h<e.length;h++){for(var r=this.collisions(i,e[h],l,n,this.checkingElement),d=0;d<r.length;d++){this.getCellInstance(t.id).col!==e[h]&&r.splice(r.indexOf(r[d]),1)}if(0===r.length)return o=!0,this.spacedColumnValue=e[h],o;o=!1}}else o=this.replacable(e,l,i,n,t);return o},e.prototype.isXSpacingAvailable=function(e,t){for(var i=!1,s=[],l=0;l<e.length;l++)if(e[l+1]-e[l]==1||e[l+1]-e[l]==-1){if(s.push(e[l]),2===t&&s.push(e[l+1]),l===e.length-2&&(s.push(e[l+1]),s.length>t&&s.splice(-1)),s.length===t)return i=!0,this.spacedColumnValue=s.sort((function(e,t){return e-t}))[0],this.spacedColumnValue<0&&(this.spacedColumnValue=1),i}else s=[];return i},e.prototype.leftWardsSpaceChecking=function(e,t,i){var s=this,l=[],n=[];if(e.forEach((function(e){var i=s.getCellInstance(e.id),l=i.col,o=i.sizeX;if(t<=s.columns&&l<=t)if(o>1)for(var a=l;a<l+o;a++)n.push(a);else n.push(l)})),n.length>0)for(var o=0;o<=t;o++)-1===n.indexOf(o)&&o!==t&&-1===l.indexOf(o)&&l.push(o);var a=this.getOccupiedColumns(i);return a.forEach((function(e){e<t&&-1!==l.indexOf(e)&&l.splice(l.indexOf(e),1)})),this.getOccupiedColumns(this.checkingElement).forEach((function(e){e<s.getCellInstance(i.id).col&&-1===a.indexOf(e)&&-1===l.indexOf(e)&&l.push(e)})),l=(l=l.sort((function(e,t){return e-t}))).reverse()},e.prototype.isLeftAdjustable=function(e,t,i,s,l,n){var o;if(1===l&&1===n&&e.length>0)this.spacedColumnValue=e[0],o=!0;else if(l>1&&1===n)o=this.isXSpacingAvailable(e,l);else if(n>1)if(1===l){var a=void 0;if(e.length>=1&&(a=!0),a)for(var h=0;h<e.length;h++){if(0===this.collisions(i,e[h],l,n,this.checkingElement).length)return this.spacedColumnValue=e[h],o=!0;o=!1}}else o=this.replacable(e,l,i,n,t);return o},e.prototype.replacable=function(e,t,i,s,l){for(var n,o=[],a=0;a<e.length;a++){if(this.isXSpacingAvailable(e,t)){var h=[];if(h.push(this.checkingElement),h.push(l),0===o.length)return n=!0,this.spacedColumnValue=this.spacedColumnValue,n;n=!1}}return n},e.prototype.getOccupiedColumns=function(e){for(var t=[],i=this.getCellInstance(e.id),s=i.sizeX,l=i.col,n=l;n<l+s&&n<=this.columns;n++)t.push(n);return t},e.prototype.moveItemsUpwards=function(){if(this.allowFloating)for(var e=0,t=this.sortedArray.length;e<t;++e){var i=this.sortedArray[e];if(i)for(var s=0,l=i.length;s<l;++s){var n=i[s];n&&this.moveItemUpwards(n)}}},e.prototype.moveItemUpwards=function(e){if(this.allowFloating&&e!==this.mainElement){var t=this.getCellInstance(e.id).col,i=parseInt(e.getAttribute("data-sizeY"),10),s=parseInt(e.getAttribute("data-sizeX"),10),l=null,n=null,o=parseInt(e.getAttribute("data-row"),10)-1;if(!this.panelMove){var a=this.getCellInstance(e.id);o+1!=a.row&&(o=a.row)}for(;o>-1;){if(0!==this.collisions(o,t,s,i,e).length)break;l=o,n=t,--o}null!==l&&this.sortItem(e,l,n)}},e.prototype.updatePanelProps=function(e){if(this.isRenderComplete){var t=this.checkForChanges();if(this.panelsInitialModel=this.cloneModels(this.panels),t.length>0){for(var i=0;i<t.length;i++)this.changedId&&-1===this.changedId.indexOf(t[i].id)&&this.changedId.push(t[i].id);this.dotnetRef.invokeMethodAsync("UpdatedPanelsValues",t)}}},e.prototype.enableDraggingContent=function(e){for(var t=this,i=0;i<e.length;i++){var s=[".e-resize",".e-drag-restrict"],l=e[i];this.dragobj=new sf.base.Draggable(l,{preventDefault:!1,clone:!1,dragArea:this.element,isDragScroll:!0,handle:this.draggableHandle?this.draggableHandle:".e-panel",abort:s,dragStart:this.onDraggingStart.bind(this),dragStop:function(e){var i=t.getCellInstance(t.mainElement.id);t.allowPushing&&t.collisions(i.row,i.col,i.sizeX,i.sizeY,t.mainElement).length>0?(t.setHolderPosition(e),t.updatePanelPosition(i.id,i.col,i.row),t.updatePanelLayout(t.mainElement,i)):t.updatePanelPosition(i.id,i.col,i.row),t.mainElement=null;var s=t.getPanelBase(e);t.shadowEle&&sf.base.detach(t.shadowEle),sf.base.removeClass([t.element],["e-prevent"]),sf.base.removeClass([e.element],["e-dragging"]),t.shadowEle=null,e.element.classList.remove("e-dragging");var l=t.getRowColumnDragValues(e)[0],n=t.getRowColumnDragValues(e)[1],o=t.getCellInstance(e.element.id);if(t.allowPushing&&0===t.collisions(l,n,o.sizeX,o.sizeY,document.getElementById(s.id)).length)t.oldRowCol[e.element.id].row=l,t.oldRowCol[e.element.id].col=n,t.updatePanelPosition(e.element.id,n,l),t.sortedPanel();else{var a=t.getCellInstance(e.element.id);t.updatePanelPosition(a.id,a.col,a.row),t.sortedPanel()}var h=t.getCellInstance(e.element.id);t.setPanelPosition(e.element,h.row,h.col),t.updatePanels(),t.updateCloneArrayObject(),t.updatePanelProps(i.id),t.isDragged=!1,t.dragStopEventArgs={event:e.event,element:e.element},t.dotnetRef.invokeMethodAsync("UpdateMaximumRow",e.element.id,t.changedId),t.isChanged=!1,t.changedId=[],t.resizeEvents(),t.updateDragArea()},drag:function(e){t.draggedEventArgs={event:e.event,element:e.element,target:sf.base.closest(e.target,".e-panel")},t.onDragStart(e)}}),-1===this.dragCollection.indexOf(this.dragobj)&&this.dragCollection.push(this.dragobj)}},e.prototype.getPanelBase=function(e){for(var t,i=0;i<this.panelElements.length;i++)this.panelElements[i].id===(e.element&&e.element.id||e)&&(t=this.panelElements[i]);return t},e.prototype.updateCloneArrayObject=function(){this.cloneArray=this.sortedArray,this.cloneObject=JSON.parse(JSON.stringify(this.oldRowCol))},e.prototype.getRowColumnDragValues=function(e){var t=parseFloat(e.element.style.top),i=parseFloat(e.element.style.left);return[Math.round(t/(this.getCellSize()[1]+this.cellSpacing[1])),Math.round(i/(this.getCellSize()[0]+ +this.cellSpacing[0]))]},e.prototype.onDragStart=function(e){var t,i,s,l=s=this.getRowColumnDragValues(e)[1],n=this.getRowColumnDragValues(e)[0];if(!(l<0||n<0)){var o=this.getCellInstance(e.element.id);if(this.dotnetRef.invokeMethodAsync("SetDragPanelHeight",o.row,o.sizeY,1),this.updateDragArea(),this.allowPushing&&(this.updatePanelPosition(o.id,l,n),t=this.oldRowCol[e.element.id].col,i=this.oldRowCol[e.element.id].row,this.oldRowCol[e.element.id]={row:n,col:l},this.updateOldRowColumn(),(this.dragColumn!==t||this.dragRow!==i)&&(e.element.setAttribute("data-row",n.toString()),e.element.setAttribute("data-col",l.toString()),this.setHolderPosition(e),this.dragRow=i,this.dragColumn=t,this.startCol!==t&&(this.startRow=i),this.startRow!==i&&(this.startCol=t),this.allowPushing))){this.mainElement=e.element;var a=o;this.checkCollision=this.collisions(a.row,a.col,a.sizeX,a.sizeY,e.element),o.col>=this.checkColumnValue&&(this.checkCollision=[]),this.updatePanelLayout(e.element,o),this.moveItemsUpwards(),this.updatePanelProps(o.id)}this.oldRowCol[e.element.id].row===n&&this.oldRowCol[e.element.id].col===l||(this.updatePanelPosition(o.id,l,n),this.updatePanelProps(o.id)),this.startCol!==s&&(this.startCol=this.dragColumn=t,this.moveItemsUpwards()),this.allowPushing||this.setHolderPosition(e)}},e.prototype.bindEvents=function(){this.refreshListener=this.refresh.bind(this,!0),sf.base.EventHandler.add(window,"resize",this.refreshListener),this.resizeEvents()},e.prototype.refresh=function(e){if(this.dotnetRef&&document.getElementById(this.element.id)){if(this.panelsSizeY=0,this.updateDragArea(),this.isRefreshCalled=!0,this.isMediaQuery=this.checkMediaQuery(),!this.checkMediaQuery()){if(this.element.classList.contains("e-responsive")){sf.base.removeClass([this.element],["e-responsive"]);for(var t=0;t<this.element.querySelectorAll(".e-panel").length;t++){var i=this.element.querySelectorAll(".e-panel")[t];if(0!=this.panels.length){var s=this.getCellInstance(i.id);if(!sf.base.isNullOrUndefined(s)){var l=s.row,n=s.col;this.updatePanelPosition(s.id,n,l)}}}}this.element.classList.add("e-responsive"),this.calculateCellSizeValue()}this.dotnetRef.invokeMethodAsync("CalculateSize",this.calculateCellSize(),this.isMediaQuery,e,this.contentHeight()),this.resizeEvents(),this.checkDragging(this.dragCollection),this.isRefreshCalled=!1}},e.prototype.checkDragging=function(e){for(var t=0;t<e.length;t++)e[t].destroy();!this.checkMediaQuery()&&this.allowDragging&&this.enableDraggingContent(this.panelElements)},e.prototype.resizeEvents=function(){if(this.allowResizing)for(var e=this.element.querySelectorAll(".e-panel .e-resize"),t=0;t<e.length;t++){var i="msie"===sf.base.Browser.info.name?"mousedown pointerdown":"mousedown";sf.base.EventHandler.remove(e[t],i,this.downResizeHandler),sf.base.EventHandler.add(e[t],i,this.downResizeHandler,this),"msie"!==sf.base.Browser.info.name&&(sf.base.EventHandler.remove(e[t],"touchstart",this.touchDownResizeHandler),sf.base.EventHandler.add(e[t],"touchstart",this.touchDownResizeHandler,this))}},e.prototype.touchDownResizeHandler=function(e){this.downHandler(e),this.lastMouseX=e.changedTouches[0].pageX,this.lastMouseY=e.changedTouches[0].pageY,this.isMouseMoveBound||(sf.base.EventHandler.add(document,"touchmove",this.touchMoveResizeHandler,this),this.isMouseMoveBound=!0),this.isMouseUpBound||(sf.base.EventHandler.add(document,"touchend",this.upResizeHandler,this),this.isMouseUpBound=!0)},e.prototype.upResizeHandler=function(e){if(!sf.base.isNullOrUndefined(this.downTarget)){this.upTarget=this.downTarget;var t=sf.base.closest(this.upTarget,".e-panel"),i=!0;if(this.isResizing=!1,t){sf.base.addClass([t],"e-panel-transition");var s="msie"===sf.base.Browser.info.name?"mousemove pointermove":"mousemove",l="msie"===sf.base.Browser.info.name?"mouseup pointerup":"mouseup";sf.base.EventHandler.remove(document,s,this.moveResizeHandler),sf.base.EventHandler.remove(document,l,this.upResizeHandler),"msie"!==sf.base.Browser.info.name&&(sf.base.EventHandler.remove(document,"touchmove",this.touchMoveResizeHandler),sf.base.EventHandler.remove(document,"touchend",this.upResizeHandler)),this.isMouseUpBound=!1,this.isMouseMoveBound=!1,this.shadowEle&&sf.base.detach(this.shadowEle),this.shadowEle=null;var n=this.getCellInstance(t.getAttribute("id")),o=n.sizeY*this.getCellSize()[1]+(n.sizeY-1)*this.cellSpacing[1],a=n.sizeX*this.getCellSize()[0]+(n.sizeX-1)*this.cellSpacing[0],h=n.col*(this.getCellSize()[0]+this.cellSpacing[0]),r=n.row*(this.getCellSize()[1]+this.cellSpacing[1]);sf.base.setStyleAttribute(t,{left:h+"px",top:r+"px",height:o+"px",width:a+"px"}),this.updatePanelPosition(n.id,n.col,n.row)}sf.base.removeClass([t],["e-dragging"]),this.isResizing=!1,this.dotnetRef.invokeMethodAsync("TriggerResizeStart",i,t.id,"ResizeStop"),this.resizeCalled=!1,this.lastMouseX=this.lastMouseY=void 0,this.mOffX=this.mOffY=0,this.mainElement=null,this.allowFloating&&this.moveItemsUpwards(),this.updatePanels(),this.updateCloneArrayObject(),this.changedId.length>0&&this.dotnetRef.invokeMethodAsync("TriggerChange",i,this.changedId),this.changedId=[]}},e.prototype.moveResizeHandler=function(e){this.updateMaxTopLeft(e);var t=sf.base.closest(this.moveTarget,".e-panel");if(this.lastMouseX!==e.pageX&&this.lastMouseY!==e.pageY){this.updateResizeElement(t);var i=this.getCellInstance(t.getAttribute("id"));this.mouseX=e.pageX,this.mouseY=e.pageY;var s=this.mouseY-this.lastMouseY+this.mOffY,l=this.mouseX-this.lastMouseX+this.mOffX;this.mOffX=this.mOffY=0,this.lastMouseY=this.mouseY,this.lastMouseX=this.mouseX,this.resizingPanel(t,i,l,s)}},e.prototype.getMinWidth=function(e){return e.minSizeX*this.getCellSize()[0]},e.prototype.getMaxWidth=function(e){return e.maxSizeX*this.getCellSize()[0]},e.prototype.getMinHeight=function(e){return e.minSizeY*this.getCellSize()[1]},e.prototype.getMaxHeight=function(e){return e.maxSizeY*this.getCellSize()[1]},e.prototype.resizingPanel=function(e,t,i,s){var l=this.getCellInstance(e.id).sizeX,n=this.getCellInstance(e.id).sizeY,o=s,a=i;this.handleClass.indexOf("north")>=0&&(this.elementHeight-o<this.getMinHeight(t)?(s=this.elementHeight-this.getMinHeight(t),this.mOffY=o-s):t.maxSizeY&&this.elementHeight-o>this.getMaxHeight(t)?(s=this.elementHeight-this.getMaxHeight(t),this.mOffY=o-s):this.elementY+o<this.minTop&&(s=this.minTop-this.elementY,this.mOffY=o-s),this.elementY+=s,this.elementHeight-=s),this.handleClass.indexOf("south")>=0&&(this.elementHeight+o<this.getMinHeight(t)?(s=this.getMinHeight(t)-this.elementHeight,this.mOffY=o-s):t.maxSizeY&&this.elementHeight+o>this.getMaxHeight(t)?(s=this.getMaxHeight(t)-this.elementHeight,this.mOffY=o-s):this.elementY+this.elementHeight+o>this.maxTop&&(s=this.maxTop-this.elementY-this.elementHeight,this.mOffY=o-s),this.elementHeight+=s),this.handleClass.indexOf("west")>=0&&(this.elementWidth-a<this.getMinWidth(t)?(i=this.elementWidth-this.getMinWidth(t),this.mOffX=a-i):t.maxSizeX&&this.elementWidth-a>this.getMaxWidth(t)?(i=this.elementWidth-this.getMaxWidth(t),this.mOffX=a-i):this.elementX+a<this.minLeft&&(i=this.minLeft-this.elementX,this.mOffX=a-i),this.elementX+=i,this.elementWidth-=i),this.handleClass.indexOf("east")>=0&&(this.elementWidth+a<this.getMinWidth(t)?(i=this.getMinWidth(t)-this.elementWidth,this.mOffX=a-i):t.maxSizeX&&this.elementWidth+a>this.getMaxWidth(t)?(i=this.getMaxWidth(t)-this.elementWidth,this.mOffX=a-i):this.elementX+this.elementWidth+a>this.maxLeft&&(i=this.maxLeft-this.elementX-this.elementWidth,this.mOffX=a-i),this.elementWidth+=i),e.style.top=this.elementY+"px",e.style.left=this.elementX+"px",e.style.width=this.elementWidth+"px",e.style.height=this.elementHeight+"px";var h=this.getResizeRowColumn(t);if(h.col+h.sizeX>this.columns&&this.updatePanelSize(h.id,h.sizeX-1,null),this.shadowEle.style.top=h.row*this.getCellSize()[1]+h.row*this.cellSpacing[1]+"px",this.handleClass.indexOf("west")>=0?this.shadowEle.style.left=h.col*this.getCellSize()[0]+(h.col-1)*this.cellSpacing[0]+"px":this.shadowEle.style.left=h.col*this.getCellSize()[0]+h.col*this.cellSpacing[0]+"px",this.shadowEle.style.height=h.sizeY*(this.getCellSize()[1]+this.cellSpacing[1])+"px",this.shadowEle.style.width=h.sizeX*(this.getCellSize()[0]+this.cellSpacing[0])+"px",l!==h.sizeX||n!==h.sizeY){l=h.sizeX,n=h.sizeY;var r=this.getCellInstance(e.id);this.updatePanelSize(e.id,r.sizeX,r.sizeY),this.updatePanelPosition(e.id,r.col,r.row),this.mainElement=e,this.updatePanelLayout(e,this.getCellInstance(e.id)),this.updateOldRowColumn(),this.sortedPanel()}},e.prototype.pixelsToColumns=function(e,t){return t?Math.round(e/this.cellSize[0]):Math.round(e/(this.cellSize[0]+this.cellSpacing[0]))},e.prototype.pixelsToRows=function(e,t){return t?Math.round(e/this.cellSize[1]):Math.round(e/(this.cellSize[1]+this.cellSpacing[0]))},e.prototype.getResizeRowColumn=function(e){var t=!1,i=!1,s=e.col;-1!==["e-west","e-south-west"].indexOf(this.handleClass)&&(s=this.pixelsToColumns(this.elementX,!1),this.previousCol!==s&&(this.previousCol=s,i=!0));var l=e.row;-1!==["e-north"].indexOf(this.handleClass)&&(l=this.pixelsToRows(this.elementY,!1),this.previousRow!==l&&(this.previousRow=l,t=!0));var n=e.sizeX;-1===["e-north","e-south"].indexOf(this.handleClass)&&(("e-west"!==this.handleClass&&"e-south-west"!==this.handleClass||i)&&(n=this.pixelsToColumns(this.elementWidth-n*this.cellSpacing[1],!0)),e.sizeX===n&&(s=e.col));var o=e.sizeY;return-1===["e-east","e-west"].indexOf(this.handleClass)&&("e-north"!==this.handleClass||t)&&(o=this.pixelsToRows(this.elementHeight-o*this.cellSpacing[0],!0)),e.col+e.sizeX>this.columns&&(e.sizeX=e.sizeX-1),(l>-1&&s>-1&&n+s<=this.columns&&o+l<=this.rows&&0===this.collisions(l,s,n,o,this.getPanelBase(e.id)).length||this.allowPushing)&&(this.updatePanelSize(e.id,n,o),this.updatePanelPosition(e.id,s,l)),e},e.prototype.touchMoveResizeHandler=function(e){this.updateMaxTopLeft(e);var t=sf.base.closest(this.moveTarget,".e-panel");if(this.lastMouseX!==e.changedTouches[0].pageX&&this.lastMouseY!==e.changedTouches[0].pageY){this.updateResizeElement(t);var i=this.getCellInstance(t.getAttribute("id"));this.mouseX=e.changedTouches[0].pageX,this.mouseY=e.changedTouches[0].pageY;var s=this.mouseX-this.lastMouseX+this.mOffX,l=this.mouseY-this.lastMouseY+this.mOffY;this.mOffX=this.mOffY=0,this.lastMouseX=this.mouseX,this.lastMouseY=this.mouseY,this.resizingPanel(t,i,s,l)}},e.prototype.updateResizeElement=function(e){this.maxLeft=this.element.offsetWidth-1,this.maxTop=this.cellSize[1]*this.maxRows-1,sf.base.removeClass([e],"e-panel-transition"),sf.base.addClass([e],["e-dragging"]);for(var t=["e-east","e-west","e-north","e-south","e-south-east","e-north-east","e-north-west","e-south-west"],i=0;i<this.moveTarget.classList.length;i++)-1!==t.indexOf(this.moveTarget.classList[i])&&(this.handleClass=this.moveTarget.classList[i])},e.prototype.updateMaxTopLeft=function(e){var t=this;this.moveTarget=this.downTarget;var i=sf.base.closest(this.moveTarget,".e-panel"),s=!0;this.resizeTimeCount=setTimeout((function(){return t.resizeCall(s,i.id,"Resizing")}),100)},e.prototype.resizeCall=function(e,t,i){(this.isResizing&&this.resizeTimeCount>100||void 0===this.resizeTimeCount)&&(this.dotnetRef.invokeMethodAsync("TriggerResizeStart",e,t,i),this.resizeTimeCount=1)},e.prototype.downResizeHandler=function(e){for(var t=sf.base.closest(e.currentTarget,".e-panel"),i=0;this.panels.length>i;i++)if(this.panels[i].enabled&&this.panels[i].id===t.id){this.downHandler(e),this.lastMouseX=e.pageX,this.lastMouseY=e.pageY;var s="msie"===sf.base.Browser.info.name?"mousemove pointermove":"mousemove",l="msie"===sf.base.Browser.info.name?"mouseup pointerup":"mouseup";this.isMouseMoveBound||(sf.base.EventHandler.add(document,s,this.moveResizeHandler,this),this.isMouseMoveBound=!0),this.isMouseUpBound||(sf.base.EventHandler.add(document,l,this.upResizeHandler,this),this.isMouseUpBound=!0)}},e.prototype.downHandler=function(e){this.resizeCalled=!1,this.panelsInitialModel=this.cloneModels(this.panels);var t=sf.base.closest(e.currentTarget,".e-panel");this.isChanged=!0;var i=!0;this.dotnetRef.invokeMethodAsync("TriggerResizeStart",i,t.id,"ResizeStart"),this.isResizing=!0,this.downTarget=e.currentTarget,this.shadowEle=document.createElement("div"),this.shadowEle.classList.add("e-holder"),sf.base.addClass([this.element],["e-prevent"]),this.element.appendChild(this.shadowEle),this.elementX=parseFloat(t.style.left),this.elementY=parseFloat(t.style.top),this.elementWidth=t.offsetWidth,this.elementHeight=t.offsetHeight,this.originalWidth=this.getCellInstance(t.id).sizeX,this.originalHeight=this.getCellInstance(t.id).sizeY,this.previousRow=this.getCellInstance(t.id).row,this.previousCol=this.getCellInstance(t.id).col},e.prototype.onDraggingStart=function(e){this.isDragged=!0;var t=e;this.isChanged=!0,t.bindEvents(e.element),this.panelsInitialModel=this.cloneModels(this.panels),this.mainElement=e.element;var i=this.getCellInstance(e.element.id);this.startCol=this.dragColumn=i.col;var s=this.startRow=this.dragRow=parseInt(e.element.getAttribute("data-row"),10),l=parseInt(e.element.getAttribute("data-sizeY"),10);this.dotnetRef.invokeMethodAsync("TriggerDragStart",e.element.id,s,l,l),this.rows=s+l>=this.rows?this.rows+l:this.rows,this.updateDragArea(),this.shadowEle=document.createElement("div"),this.shadowEle.classList.add("e-holder"),this.shadowEle.classList.add("e-holder-transition"),sf.base.setStyleAttribute(this.shadowEle,{position:"absolute"}),sf.base.addClass([this.element],["e-prevent"]),sf.base.addClass([e.element],["e-dragging"]),this.element.appendChild(this.shadowEle),this.shadowEle=document.querySelector(".e-holder"),this.shadowEle.style.height=this.getCellInstance(e.element.id).sizeY*this.cellSize[1]+"px",this.shadowEle.style.width=this.getCellInstance(e.element.id).sizeX*this.cellSize[0]+"px";var n=this.getCellInstance(e.element.id);this.updatePanelPosition(n.id,n.col,n.row),this.setPanelPosition(this.shadowEle,n.row,n.col)},e.prototype.updatePanelSize=function(e,t,i){if(this.panels.length>0)for(var s=0;s<this.panels.length;s++)this.panels[s].id===e&&(sf.base.isNullOrUndefined(t)||(this.panels[s].sizeX=t),sf.base.isNullOrUndefined(i)||(this.panels[s].sizeY=i))},e.prototype.updateDragArea=function(){this.dragCollection.forEach((function(e){e.setDragArea()}))},e.prototype.setPanelPosition=function(e,t,i){if(e){var s=this.getCellSize()[1],l=this.getCellSize()[0],n=0===i?0:i*(l+this.cellSpacing[0]),o=0===t?0:t*(s+this.cellSpacing[1]);sf.base.setStyleAttribute(e,{left:n+"px",top:o+"px"})}},e.prototype.calculateCellSizeValue=function(){this.cellSize=[],this.checkMediaQuery()?this.cellSize[1]=this.element.parentElement&&this.element.parentElement.offsetWidth/this.cellAspectRatio:(this.cellSize[0]=this.element.parentElement&&this.element.parentElement.offsetWidth,this.cellSize[0]=this.element.parentElement&&(this.element.parentElement.offsetWidth-(this.columns-1)*this.cellSpacing[0])/this.columns,this.cellSize[1]=this.cellSize[0]/this.cellAspectRatio)},e.prototype.cloneModels=function(e,t){void 0===t&&(t=[]);for(var i=0;i<e.length;i++)for(var s in t[i]||(t[i]={}),e[i])t[i][s]=e[i][s];return t},e.prototype.checkForChanges=function(){for(var e=[],t=0;t<this.panels.length;t++)this.panels[t].row===this.panelsInitialModel[t].row&&this.panels[t].col===this.panelsInitialModel[t].col&&this.panels[t].sizeX===this.panelsInitialModel[t].sizeX&&this.panels[t].sizeY===this.panelsInitialModel[t].sizeY||(e.push(this.panels[t]),this.panels[t].sizeY=this.panels[t].sizeY<this.panels[t].minSizeY?this.panels[t].minSizeY:this.panels[t].sizeY);return e},e.prototype.getCellSize=function(){return[this.cellSize[0],this.cellSize[1]]},e.prototype.destroy=function(){sf.base.EventHandler.remove(window,"resize",this.refreshListener),this.element.removeAttribute("style");for(var e=0;e<this.dragCollection.length;e++)this.dragCollection[e].destroy();this.removeAll()},e.prototype.setHolderPosition=function(e){var t=parseInt(e.element.getAttribute("data-sizeY"),10),i=parseInt(e.element.getAttribute("data-col"),10),s=parseInt(e.element.getAttribute("data-row"),10),l=parseInt(e.element.getAttribute("data-sizeX"),10),n=this.getCellSize()[0],o=this.getCellSize()[1],a=0===s?0:s*(o+this.cellSpacing[1]),h=0===i?0:i*(n+this.cellSpacing[0]),r=this.getCellSize()[1],d=this.getCellSize()[0];this.shadowEle.style.top=a+"px",this.shadowEle.style.left=h+"px",this.shadowEle.style.height=t*r+(t-1)*this.cellSpacing[1]+"px",this.shadowEle.style.width=l*d+(l-1)*this.cellSpacing[0]+"px"},e.prototype.contentHeight=function(){for(var e=[],t=0;t<this.element.querySelectorAll(".e-panel").length;t++){var i=this.element.querySelectorAll(".e-panel")[t];if(i.querySelector(".e-panel-header")){var s=window.getComputedStyle(i.querySelector(".e-panel-header")).height,l={id:i.id,height:s||"0px"};e.push(l)}}return e},e}();return{initialize:function(t,i,s,l){new e(t,i,s);var n=window.sfBlazor.getCompInstance(t);return sf.base.isNullOrUndefined(n)?null:n.initialize(l)},movePanel:function(e,t,i,s){var l=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(l)||l.movePanel(t,i,s)},removePanel:function(e,t){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.removePanel(t)},resizePanel:function(e,t,i,s){var l=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(l)||l.resizePanel(t,i,s)},removeAll:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.removeAll()},addPanel:function(e,t,i){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||(s.rows=i,s.addPanel(t))},onPropertyChange:function(e,t){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.onPropertyChange(t)},destroy:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.destroy()},refresh:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.refresh(!1)},updatePanelModels:function(e,t){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||(i.clearPanelElements(),i.getProperty(t),i.initialize(t))},setRootHeight:function(e,t){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.setRootHeight(t)}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfdashboardlayout');})})();