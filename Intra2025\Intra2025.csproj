<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Base\BasePage.cs" />
    <Compile Remove="Base\SSO.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="LibreOfficeLibrary" Version="1.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="OAKsIlanApp">
      <HintPath>..\SSO\OAKsIlanApp.dll</HintPath>
    </Reference>
    <Reference Include="OAKsIlanAppUser">
      <HintPath>..\SSO\OAKsIlanAppUser.dll</HintPath>
    </Reference>
    <Reference Include="OAKsIlanOrgTree">
      <HintPath>..\SSO\OAKsIlanOrgTree.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\exports\" />
    <Folder Include="wwwroot\uploads\" />
  </ItemGroup>

</Project>
