/*!*  filename: sf-dialog.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[22],{"./bundles/sf-dialog.js":function(e,t,s){"use strict";s.r(t);s("./modules/sf-dialog.js")},"./modules/sf-dialog.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Dialog=function(){"use strict";var e="e-dlg-target",t=function(){function t(e){this.popupObj=null,this.zIndex=1e3,this.height="auto",this.width="100%",this.resizeIconDirection="SouthEast",this.visible=!0,this.isModal=!1,this.enableRtl=!1,this.enableResize=!1,this.closeOnEscape=!0,this.allowDragging=!1,this.allowMaxHeight=!0,this.allowPrerender=!1,this.hasFocusableNode=!1,this.openedEnabled=!1,this.closedEnabled=!1,this.onDragEnabled=!1,this.onDragStartEnabled=!1,this.onDragStopEnabled=!1,this.resizingEnabled=!1,this.onResizeStartEnabled=!1,this.onResizeStopEnabled=!1,this.isOpenFullScreen=!1,this.enablePersistence=!1,this.dlgContainer=void 0,this.animationSettings={delay:0,duration:400,effect:"Fade"},this.position={X:"center",Y:"center"},window.sfBlazor=window.sfBlazor,this.updateContext(e),this.isInitialLoad=e.isInitial,window.sfBlazor.setCompInstance(this),this.initialize()}return t.prototype.updateContext=function(e){sf.base.extend(this,this,e)},t.prototype.initialize=function(){this.calculatezIndex=1e3===this.zIndex,this.render(),this.element.classList.remove("e-blazor-hidden"),this.setWidth(),this.setMinHeight(),this.enableResize&&(this.setResize(!0),("None"===this.animationSettings.effect&&"Enable"!==sf.base.animationMode||"Disable"===sf.base.animationMode)&&this.getMinHeight()),this.bindEvent(this.element)},t.prototype.setWidth=function(){"100%"===this.width?this.element.style.width="":(sf.base.setStyleAttribute(this.element,{width:sf.base.formatUnit(this.width)}),"auto"===this.width&&this.refreshPosition())},t.prototype.setHeight=function(){sf.base.setStyleAttribute(this.element,{height:sf.base.formatUnit(this.height)})},t.prototype.setMinHeight=function(){""!==this.minHeight&&sf.base.setStyleAttribute(this.element,{minHeight:sf.base.formatUnit(this.minHeight)})},t.prototype.render=function(){var e=this;if(this.checkPositionData(),this.targetEle=this.getTargetEle(this.target),sf.base.Browser.isDevice&&sf.base.addClass([this.element],"e-device"),sf.base.isNullOrUndefined(this.headerContent)&&(this.headerContent=this.element.querySelector(".e-dlg-header-content")),sf.base.isNullOrUndefined(this.contentEle)&&(this.contentEle=this.element.querySelector(".e-dlg-content")),this.setMaxHeight(),1e3===this.zIndex&&this.setzIndex(this.element,!1),this.allowDragging&&!sf.base.isNullOrUndefined(this.headerContent)&&this.setAllowDragging(),this.isModal&&sf.base.isNullOrUndefined(this.dlgContainer)&&(this.dlgContainer=this.element.parentElement,this.dlgOverlay=this.element.parentElement.getElementsByClassName("e-dlg-overlay")[0]),!sf.base.isNullOrUndefined(this.element.parentElement)&&(this.allowPrerender||!this.allowPrerender&&(!this.isInitialLoad||this.visible&&this.isInitialLoad))){var t=this.isModal?this.dlgContainer.parentElement:this.element.parentElement;this.refElement=sf.base.createElement("div",{className:"e-dlg-ref-element"}),t.insertBefore(this.refElement,this.isModal?this.dlgContainer:this.element)}sf.base.isNullOrUndefined(this.targetEle)||(this.isModal?this.targetEle.appendChild(this.dlgContainer):this.targetEle.appendChild(this.element)),this.popupObj=new sf.popups.Popup(this.element,{height:this.height,width:this.width,zIndex:this.zIndex,relateTo:this.targetEle,actionOnScroll:"none",enableRtl:this.enableRtl,position:{X:this.position.X,Y:this.position.Y},open:function(){e.enableResize&&e.resetResizeIcon(),e.openedEnabled?e.dotNetRef.invokeMethodAsync("OpenEvent",e.element.classList.toString()):e.focusContent()},close:function(){e.isModal&&(sf.base.addClass([e.dlgOverlay],"e-fade"),e.dlgContainer.style.display="none"),e.hasFocusableNode=!1,e.closedEnabled?e.dotNetRef.invokeMethodAsync("CloseEvent",e.element.classList.toString()):e.popupCloseHandler()}}),this.isModal&&this.positionChange()},t.prototype.checkPositionData=function(){sf.base.isNullOrUndefined(this.position)||(this.isNumberString(this.position.X)&&(this.position.X=parseFloat(this.position.X)),this.isNumberString(this.position.Y)&&(this.position.Y=parseFloat(this.position.Y)))},t.prototype.isNumberString=function(e){return!sf.base.isNullOrUndefined(e)&&"number"!=typeof e&&/^[-+]?\d*\.?\d+$/.test(e)},t.prototype.getTargetEle=function(e){var t;return sf.base.isNullOrUndefined(e)||"string"!=typeof e||(t=document.querySelector(e)),sf.base.isNullOrUndefined(t)?document.body:t},t.prototype.setMaxHeight=function(){if(this.allowMaxHeight){var e=this.element.style.display;this.element.style.display="none",this.element.style.maxHeight=!sf.base.isNullOrUndefined(this.target)&&this.targetEle.offsetHeight<window.innerHeight?this.targetEle.offsetHeight-20+"px":window.innerHeight-20+"px",this.element.style.display=e,sf.base.Browser.isIE&&"auto"===this.height&&!sf.base.isNullOrUndefined(this.contentEle)&&this.element.offsetHeight<this.contentEle.offsetHeight&&(this.element.style.height="inherit")}},t.prototype.setzIndex=function(e,t){this.zIndex=sf.popups.getZindexPartial(e),t&&(this.popupObj.zIndex=this.zIndex)},t.prototype.updatezIndex=function(){this.popupObj.zIndex=this.zIndex,this.isModal&&this.setOverlayZindex(this.zIndex),this.calculatezIndex=this.element.style.zIndex===this.zIndex.toString()},t.prototype.updateTarget=function(){this.targetEle=this.getTargetEle(this.target),this.popupObj.relateTo=this.targetEle,this.dragObj&&(this.dragObj.dragArea=this.targetEle),this.setMaxHeight(),this.isModal&&this.targetEle.appendChild(this.dlgContainer),this.enableResize&&this.setResize(!1)},t.prototype.resetResizeIcon=function(){var e=this.getMinHeight();if(this.targetEle.offsetHeight<e){var t=this.element.querySelector("."+this.resizeIconDirection);sf.base.isNullOrUndefined(t)||(t.style.bottom="-"+e.toString()+"px")}},t.prototype.getMouseEvtArgs=function(e){return{altKey:e.altKey,button:e.button,buttons:e.buttons,clientX:e.clientX,clientY:e.clientY,ctrlKey:e.ctrlKey,detail:e.detail,metaKey:e.metaKey,screenX:e.screenX,screenY:e.screenY,shiftKey:e.shiftKey,type:e.type}},t.prototype.setAllowDragging=function(){var e=this,t=this;this.dragObj=new sf.base.Draggable(this.element,{clone:!1,isDragScroll:!0,abort:".e-dlg-closeicon-btn",handle:".e-dlg-header-content",dragStart:function(s){e.onDragStartEnabled&&t.dotNetRef.invokeMethodAsync("DragStartEvent",{target:{ID:s.target.id},event:t.getMouseEvtArgs(s.event)}),s.bindEvents(s.dragElement)},drag:function(s){e.onDragEnabled&&t.dotNetRef.invokeMethodAsync("DragEvent",{target:{ID:s.target.id},event:t.getMouseEvtArgs(s.event)})},dragStop:function(s){if(t.isModal){sf.base.isNullOrUndefined(t.position)||t.dlgContainer.classList.remove("e-dlg-"+t.position.X+"-"+t.position.Y);var i=document.querySelector("html").getAttribute("dir");t.element.style.position=sf.base.isNullOrUndefined(i)||"rtl"!==i?"relative":t.element.style.position}e.onDragStopEnabled&&t.dotNetRef.invokeMethodAsync("DragStopEvent",{target:{ID:s.target.id},event:t.getMouseEvtArgs(s.event)}),t.element.classList.remove("e-restrict-left"),e.updatePersistData()}}),sf.base.isNullOrUndefined(this.targetEle)||(this.dragObj.dragArea=this.targetEle)},t.prototype.positionChange=function(){this.isModal&&(isNaN(parseFloat(this.position.X))||isNaN(parseFloat(this.position.Y)))?!isNaN(parseFloat(this.position.X))&&isNaN(parseFloat(this.position.Y))||isNaN(parseFloat(this.position.X))&&!isNaN(parseFloat(this.position.Y))?this.setPopupPosition():(this.element.style.top="0px",this.element.style.left="0px",this.dlgContainer.classList.add("e-dlg-"+this.position.X+"-"+this.position.Y)):this.setPopupPosition()},t.prototype.setPopupPosition=function(){this.popupObj.setProperties({position:{X:this.position.X,Y:this.position.Y}})},t.prototype.setEnableRTL=function(){var e=this.element.querySelector(".e-resize-handle");sf.base.isNullOrUndefined(e)||e.parentElement!==this.element||(sf.popups.removeResize(),this.setResize(!1))},t.prototype.setResize=function(e){if(this.enableResize){if(!sf.base.isNullOrUndefined(this.element.querySelector(".e-icons.e-resize-handle")))return;var t=getComputedStyle(this.element).minHeight,s=getComputedStyle(this.element).minWidth;this.isModal&&this.enableRtl?this.element.classList.add("e-restrict-left"):!this.isModal||this.target!==document.body&&"body"!==this.target||this.element.classList.add("e-resize-viewport"),sf.popups.createResize({element:this.element,direction:this.resizeIconDirection,minHeight:parseInt(t.slice(0,s.indexOf("p")),10),maxHeight:this.targetEle.clientHeight,minWidth:parseInt(s.slice(0,s.indexOf("p")),10),maxWidth:this.targetEle.clientWidth,boundary:"body"===this.target||"document.body"===this.target?null:this.targetEle,resizeBegin:this.onResizeStart.bind(this),resizeComplete:this.onResizeComplete.bind(this),resizing:this.onResizing.bind(this),proxy:this}),this.wireWindowResizeEvent()}else{if(e)return;sf.popups.removeResize(),this.unWireWindowResizeEvent(),this.isModal?this.element.classList.remove("e-restrict-left"):this.element.classList.remove("e-resize-viewport")}},t.prototype.getMinHeight=function(){var e="0px",t="0px";sf.base.isNullOrUndefined(this.element.querySelector(".e-dlg-header-content"))||(e=getComputedStyle(this.headerContent).height);var s=sf.base.select(".e-footer-content",this.element);sf.base.isNullOrUndefined(s)||(t=getComputedStyle(s).height);var i=parseInt(e.slice(0,e.indexOf("p")),10),n=parseInt(t.slice(0,t.indexOf("p")),10);return sf.popups.setMinHeight(i+30+(isNaN(n)?0:n)),i+30+n},t.prototype.changePosition=function(e){this.isModal&&this.dlgContainer.classList.contains("e-dlg-"+this.position.X+"-"+this.position.Y)&&this.dlgContainer.classList.remove("e-dlg-"+this.position.X+"-"+this.position.Y),this.updateContext(e),this.checkPositionData(),this.positionChange(),this.updatePersistData()},t.prototype.setOverlayZindex=function(e){var t;t=sf.base.isNullOrUndefined(e)?parseInt(this.element.style.zIndex,10)?parseInt(this.element.style.zIndex,10):this.zIndex:e,this.dlgOverlay.style.zIndex=(t-1).toString(),this.dlgContainer.style.zIndex=t.toString()},t.prototype.focusContent=function(){var e=this.getAutoFocusNode(this.element);(sf.base.isNullOrUndefined(e)?this.element:e).focus(),this.hasFocusableNode=!0},t.prototype.getAutoFocusNode=function(e){var t=e.querySelector(".e-dlg-closeicon-btn"),s=e.querySelectorAll("[autofocus]"),i=this.getValidFocusNode(s);if(this.primaryButtonEle=this.element.getElementsByClassName("e-primary")[0],sf.base.isNullOrUndefined(i)){if(i=this.focusableElements(this.contentEle),!sf.base.isNullOrUndefined(i))return i;if(!sf.base.isNullOrUndefined(this.primaryButtonEle))return this.element.querySelector(".e-primary")}else t=i;return t},t.prototype.getValidFocusNode=function(e){for(var t,s=0;s<e.length;s++){if(((t=e[s]).clientHeight>0||"a"===t.tagName.toLowerCase()&&t.hasAttribute("href"))&&t.tabIndex>-1&&!t.disabled&&!this.disableElement(t,'[disabled],[aria-disabled="true"],[type="hidden"]'))return t;t=null}return t},t.prototype.disableElement=function(e,t){var s=e?e.matches||e.webkitMatchesSelector||e.msGetRegionContent:null;if(s)for(;e;e=e.parentNode)if(e instanceof Element&&s.call(e,t))return e;return null},t.prototype.focusableElements=function(e){if(!sf.base.isNullOrUndefined(e)){var t=e.querySelectorAll('input,select,textarea,button,a,[contenteditable="true"],[tabindex]');return this.getValidFocusNode(t)}return null},t.prototype.getMaxHeight=function(){return this.element.style.maxHeight},t.prototype.OnPropertyChanged=function(e){this.updateContext(e);for(var t=0,s=Object.keys(e);t<s.length;t++){switch(s[t]){case"width":sf.base.setStyleAttribute(this.element,{width:sf.base.formatUnit(this.width)}),this.refreshPosition(),this.updatePersistData();break;case"height":this.setHeight(),this.refreshPosition(),this.updatePersistData();break;case"minHeight":this.setMinHeight();break;case"target":this.updateTarget();break;case"zIndex":this.updatezIndex();break;case"allowDragging":this.allowDragging?this.setAllowDragging():this.destroyDraggable();break;case"enableRtl":this.setEnableRTL();break;case"enableResize":this.setResize(!1)}}},t.prototype.fullScreen=function(t){if(t){sf.base.addClass([this.element],"e-dlg-fullscreen"),this.isModal||(this.element.style.top=document.scrollingElement.scrollTop+"px");var s=this.element.style.display;this.element.style.display="none",this.element.style.maxHeight=sf.base.isNullOrUndefined(this.target)?window.innerHeight+"px":this.targetEle.offsetHeight+"px",this.element.style.display=s,sf.base.addClass([document.body],[e,"e-scroll-disabled"]),this.allowDragging&&!sf.base.isNullOrUndefined(this.dragObj)&&(this.dragObj.destroy(),this.dragObj=void 0)}else sf.base.removeClass([this.element],"e-dlg-fullscreen"),sf.base.removeClass([document.body],[e,"e-scroll-disabled"]),this.allowDragging&&!sf.base.isNullOrUndefined(this.headerContent)&&sf.base.isNullOrUndefined(this.dragObj)&&this.setAllowDragging()},t.prototype.show=function(t,s){if(this.isOpenFullScreen=t,!this.element.classList.contains("e-popup-open")||!sf.base.isNullOrUndefined(t)){sf.base.isNullOrUndefined(t)||this.fullScreen(t),this.element.style.maxHeight!==s&&(this.allowMaxHeight=!1,this.element.style.maxHeight=s),this.storeActiveElement=document.activeElement,this.element.tabIndex=-1,this.isModal&&sf.base.isNullOrUndefined(this.dlgOverlay)&&(this.dlgOverlay=this.element.parentElement.querySelector(".e-dlg-overlay")),this.isModal&&!sf.base.isNullOrUndefined(this.dlgOverlay)&&(this.dlgOverlay.style.display="block",this.dlgContainer.style.display="flex",sf.base.removeClass([this.dlgOverlay],"e-fade"),sf.base.isNullOrUndefined(this.targetEle)?sf.base.addClass([document.body],[e,"e-scroll-disabled"]):(this.targetEle===document.body?this.dlgContainer.style.position="fixed":this.dlgContainer.style.position="absolute",this.dlgOverlay.style.position="absolute",this.element.style.position="relative",sf.base.addClass([this.targetEle],[e,"e-scroll-disabled"])));var i={name:"None"===this.animationSettings.effect&&"Enable"===sf.base.animationMode?"Fade":this.animationSettings.effect+"In",duration:this.animationSettings.duration,delay:this.animationSettings.delay},n=this.isModal?this.element.parentElement:this.element;this.calculatezIndex&&(this.setzIndex(n,!0),sf.base.setStyleAttribute(this.element,{zIndex:this.zIndex}),this.isModal&&this.setOverlayZindex(this.zIndex)),"None"===this.animationSettings.effect&&"Enable"!==sf.base.animationMode||"Disable"===sf.base.animationMode?this.popupObj.show():this.popupObj.show(i)}},t.prototype.hide=function(){(this.isModal||this.isOpenFullScreen)&&sf.base.removeClass([this.targetEle,document.body],[e,"e-scroll-disabled"]);var t={name:"None"===this.animationSettings.effect&&"Enable"===sf.base.animationMode?"Fade":this.animationSettings.effect+"Out",duration:this.animationSettings.duration,delay:this.animationSettings.delay};return"None"===this.animationSettings.effect&&"Enable"!==sf.base.animationMode||"Disable"===sf.base.animationMode?this.popupObj.hide():this.popupObj.hide(t),this.allowPrerender||this.destroyRefElement(),this.element.classList.toString()},t.prototype.refreshPosition=function(){this.isModal&&isNaN(parseFloat(this.position.X))&&isNaN(parseFloat(this.position.Y))?(this.element.style.top=this.element.style.left="0px",this.dlgContainer.classList.add("e-dlg-"+this.position.X+"-"+this.position.Y)):this.popupObj.refreshPosition()},t.prototype.getDimension=function(){return{width:this.element.offsetWidth,height:this.element.offsetHeight}},t.prototype.destroyDraggable=function(){sf.base.isNullOrUndefined(this.dragObj)||(this.dragObj.destroy(),this.dragObj=void 0)},t.prototype.destroyRefElement=function(){sf.base.isNullOrUndefined(this.refElement)||sf.base.isNullOrUndefined(this.refElement.parentElement)||(this.refElement.parentElement.insertBefore(this.isModal?this.dlgContainer:this.element,this.refElement),sf.base.detach(this.refElement),this.refElement=void 0)},t.prototype.destroy=function(t,s){this.updateContext(t);var i=["role","aria-modal","aria-labelledby","aria-describedby","aria-grabbed","tabindex","style"];if(!sf.base.isNullOrUndefined(this.cssClass)&&""!==this.cssClass){var n=this.cssClass.split(" ");sf.base.removeClass([this.element],n)}if(sf.base.Browser.isDevice&&sf.base.removeClass([this.element],"e-device"),sf.base.removeClass([this.getTargetEle(this.target)],[e,"e-scroll-disabled"]),this.unBindEvent(this.element),this.element.classList.contains("e-dlg-fullscreen")&&sf.base.removeClass([document.body],[e,"e-scroll-disabled"]),this.isModal&&sf.base.removeClass([sf.base.isNullOrUndefined(this.targetEle)?document.body:this.targetEle],"e-scroll-disabled"),this.element.classList.contains("e-dlg-resizable")&&this.element.classList.remove("e-dlg-resizable"),this.element.classList.contains("e-draggable")&&(this.dragObj.destroy(),this.dragObj=void 0),this.element.classList.contains("e-popup")&&(this.popupObj.destroy(),this.popupObj=void 0),this.destroyRefElement(),s)for(var o=0;o<this.element.children.length;o++)this.element.children[o].classList.add("e-hide");else if(!sf.base.isNullOrUndefined(this.element.children))for(o=0;o<=this.element.children.length;o++)o-=o,sf.base.detach(this.element.children[o]);for(o=0;o<i.length;o++)this.element.removeAttribute(i[o]);if(this.isModal){sf.base.detach(this.element.nextElementSibling);var a=this.element.parentElement;a.removeAttribute("class"),a.removeAttribute("style"),this.element.classList.remove("e-dlg-modal")}this.element.classList.remove("e-dialog")},t.prototype.bindEvent=function(e){sf.base.EventHandler.add(e,"keydown",this.keyDown,this),sf.base.Browser.isDevice&&sf.base.EventHandler.add(window,"orientationchange",this.orientationOnChange,this)},t.prototype.unBindEvent=function(e){sf.base.EventHandler.remove(e,"keydown",this.keyDown),sf.base.Browser.isDevice&&sf.base.EventHandler.remove(window,"orientationchange",this.orientationOnChange)},t.prototype.wireWindowResizeEvent=function(){window.addEventListener("resize",this.windowResizeHandler.bind(this))},t.prototype.unWireWindowResizeEvent=function(){window.removeEventListener("resize",this.windowResizeHandler.bind(this))},t.prototype.orientationOnChange=function(){var e=this;this.element.style.display="none",setTimeout((function(){sf.popups.setMaxWidth(e.targetEle.clientWidth),sf.popups.setMaxHeight(e.targetEle.clientHeight),e.setMaxHeight(),e.refreshPosition(),e.element.style.display=""}),250)},t.prototype.popupCloseHandler=function(){var e=document.activeElement;sf.base.isNullOrUndefined(e)||sf.base.isNullOrUndefined(e.blur)||e.blur(),sf.base.isNullOrUndefined(this.storeActiveElement)||sf.base.isNullOrUndefined(this.storeActiveElement.focus)||this.storeActiveElement.focus()},t.prototype.windowResizeHandler=function(){sf.popups.setMaxWidth(this.targetEle.clientWidth),sf.popups.setMaxHeight(this.targetEle.clientHeight),this.setMaxHeight()},t.prototype.onResizeStart=function(e,t){var s=t.getMouseEvtArgs(e);t.onResizeStartEnabled&&t.dotNetRef.invokeMethodAsync("ResizeStartEvent",s)},t.prototype.onResizing=function(e,t){t.resizingEnabled&&t.dotNetRef.invokeMethodAsync("ResizingEvent",t.getMouseEvtArgs(e))},t.prototype.onResizeComplete=function(e,t){t.onResizeStopEnabled&&t.dotNetRef.invokeMethodAsync("ResizeStopEvent",t.getMouseEvtArgs(e)),t.updatePersistData()},t.prototype.updatePersistData=function(){this.enablePersistence&&window.localStorage.setItem(this.dataId,JSON.stringify({width:this.element.style.width,height:this.element.style.height,X:parseFloat(this.element.style.left),Y:parseFloat(this.element.style.top)}))},t.prototype.getFocusElement=function(e){var t=e.querySelectorAll('input,select,textarea,button:enabled,a,[contenteditable="true"],[tabindex]');return t[t.length-1]},t.prototype.keyDown=function(e){var t=this;if(9===e.keyCode&&this.isModal){var s=void 0,i=void 0,n=this.element.querySelector(".e-footer-content");sf.base.isNullOrUndefined(n)||(i=n.querySelectorAll("button"),!sf.base.isNullOrUndefined(s)&&i.length>0&&(s=i[i.length-1]),sf.base.isNullOrUndefined(s)&&n.childNodes.length>0&&(s=this.getFocusElement(n))),sf.base.isNullOrUndefined(n)&&!sf.base.isNullOrUndefined(this.contentEle)&&(s=this.getFocusElement(this.contentEle)),sf.base.isNullOrUndefined(s)||document.activeElement!==s||e.shiftKey||(e.preventDefault(),this.focusableElements(this.element).focus()),document.activeElement===this.focusableElements(this.element)&&e.shiftKey&&(e.preventDefault(),sf.base.isNullOrUndefined(s)||s.focus())}if(27===e.keyCode&&this.closeOnEscape&&(document.querySelector(".e-popup-open:not(.e-dialog, .e-tooltip-wrap, .e-toolbar-pop)")||this.dotNetRef.invokeMethodAsync("CloseDialog",{altKey:e.altKey,ctrlKey:e.ctrlKey,code:e.code,key:e.key,location:e.location,repeat:e.repeat,shiftKey:e.shiftKey,metaKey:e.metaKey,type:e.type})),this.hasFocusableNode){var o=document.activeElement,a=["input","textarea"].indexOf(o.tagName.toLowerCase())>-1,l=!1;a||(l=o.hasAttribute("contenteditable")&&"true"===o.getAttribute("contenteditable")),(13===e.keyCode&&!e.ctrlKey&&"textarea"!==o.tagName.toLowerCase()&&a&&!sf.base.isNullOrUndefined(this.primaryButtonEle)||13===e.keyCode&&e.ctrlKey&&("textarea"===o.tagName.toLowerCase()||l)&&!sf.base.isNullOrUndefined(this.primaryButtonEle))&&setTimeout((function(){var e=t.element.querySelector(".e-footer-content button.e-btn.e-primary");null!==e&&e.click()}))}},t}();return{initialize:function(e){e.dataId&&new t(e)},getClassList:function(e){return e&&e.classList.toString()},getMaxHeight:function(e){return e&&window.sfBlazor.getCompInstance(e).getMaxHeight()},changePosition:function(e){e.dataId&&window.sfBlazor.getCompInstance(e.dataId).changePosition(e)},updateAnimation:function(e){e.dataId&&window.sfBlazor.getCompInstance(e.dataId).updateContext(e)},focusContent:function(e){e&&window.sfBlazor.getCompInstance(e).focusContent()},refreshPosition:function(e){e&&window.sfBlazor.getCompInstance(e).refreshPosition()},getDimension:function(e){return e&&window.sfBlazor.getCompInstance(e).getDimension()},popupCloseHandler:function(e){e&&window.sfBlazor.getCompInstance(e).popupCloseHandler()},propertyChanged:function(e){e.dataId&&window.sfBlazor.getCompInstance(e.dataId).OnPropertyChanged(e)},show:function(e){e.dataId&&window.sfBlazor.getCompInstance(e.dataId).show(e.fullScreen,e.maxHeight)},hide:function(e){return e&&window.sfBlazor.getCompInstance(e).hide()},destroy:function(e){e.dataId&&window.sfBlazor.getCompInstance(e.dataId).destroy(e,e.isClient)}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfdialog');})})();