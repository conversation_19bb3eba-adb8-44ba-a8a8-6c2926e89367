﻿using System.Text.Json;

namespace Intra2025.Servervices
{
    public class LaunchSettingsService
    {
        public string GetApplicationUrl()
        {
            var launchSettingsPath = Path.Combine(Directory.GetCurrentDirectory(), "Properties", "launchSettings.json");
            if (File.Exists(launchSettingsPath))
            {
                var json = File.ReadAllText(launchSettingsPath);
                using (JsonDocument document = JsonDocument.Parse(json))
                {
                    var root = document.RootElement;
                    var profilesElement = root.GetProperty("profiles");

                    // 嘗試獲取 HTTPS 配置
                    if (profilesElement.TryGetProperty("https", out var httpsProfile))
                    {
                        var applicationUrl = httpsProfile.GetProperty("applicationUrl").GetString();
                        #pragma warning disable CS8602 // Possible null reference return
                        return applicationUrl.Split(';')[0]; // 返回第一個 HTTPS URL
                    }

                    // 如果沒有 HTTPS，嘗試獲取 HTTP 配置
                    else if (profilesElement.TryGetProperty("http", out var httpProfile))
                    {
                        #pragma warning disable CS8603 // Possible null reference return
                        return httpProfile.GetProperty("applicationUrl").GetString();
                    }

                    // 如果都沒有，嘗試獲取 IIS Express 配置
                    else if (root.TryGetProperty("iisSettings", out var iisSettings))
                    {
                        var iisExpress = iisSettings.GetProperty("iisExpress");
                        return $"http://localhost:{iisExpress.GetProperty("sslPort").GetInt32()}";
                    }
                }
            }
            return "https://localhost:7175"; // 默認值
        }
    }
}
