/*!*  filename: sf-range-navigator.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[48],{"./bundles/sf-range-navigator.js":function(e,t,i){"use strict";i.r(t);i("./modules/sf-range-navigator.js")},"./modules/sf-range-navigator.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.RangeNavigator=function(){"use strict";var e=function(){function e(e,t,i,n,s){window.sfBlazor=window.sfBlazor,this.mouseY=0,this.mouseX=0,this.reSizeTo=0,this.isTooltipHide=!0,this.isDrag=!1,this.tooltip=[],this.chartKeyUpRef=null,this.documentKeyHandler=function(e){e.altKey&&74===e.keyCode&&!sf.base.isNullOrUndefined(this.element)&&this.element.focus()},this.id=t,this.element=i,this.dotNetRef=n,this.dataId=e,this.labels=s,window.sfBlazor.setCompInstance(this)}return e.prototype.destroy=function(){this.unWireEvents(this.id,this.dotNetRef)},e.prototype.unWireEvents=function(e,i){this.dotNetRef=i,t.dotnetrefCollection=t.dotnetrefCollection.filter((function(t){return t.id!==e}));var n=sf.base.Browser.isPointer?"pointerleave":"mouseleave";sf.base.EventHandler.remove(this.element,sf.base.Browser.touchStartEvent,this.rangeOnMouseDown),sf.base.EventHandler.remove(this.element,sf.base.Browser.touchMoveEvent,this.mouseMove),sf.base.EventHandler.remove(this.element,sf.base.Browser.touchEndEvent,this.mouseEnd),sf.base.EventHandler.remove(this.element,"click",this.rangeOnMouseClick),sf.base.EventHandler.remove(this.element,n,this.mouseLeave),sf.base.EventHandler.remove(document.body,"keydown",this.documentKeyHandler),sf.base.EventHandler.remove(this.element,"keyup",this.chartKeyUpRef);var s=sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize";sf.base.EventHandler.remove(window,s,t.resizeBound)},e.prototype.wireEvents=function(e,i){this.dotNetRef=i,t.dotnetrefCollection.push({id:e,dotnetref:i});var n=sf.base.Browser.isPointer?"pointerleave":"mouseleave";sf.base.EventHandler.add(this.element,sf.base.Browser.touchStartEvent,this.rangeOnMouseDown,this),sf.base.EventHandler.add(this.element,sf.base.Browser.touchMoveEvent,this.mouseMove,this),sf.base.EventHandler.add(this.element,sf.base.Browser.touchEndEvent,this.mouseEnd,this),sf.base.EventHandler.add(this.element,"click",this.rangeOnMouseClick,this),sf.base.EventHandler.add(this.element,n,this.mouseLeave,this),this.chartKeyUpRef=this.chartOnKeyUp.bind(this,this.dotNetRef,this.id),t.resizeBound=t.rangeResize.bind(this,t.dotnetrefCollection);var s=sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize";sf.base.EventHandler.add(document.body,"keydown",this.documentKeyHandler,this),sf.base.EventHandler.add(this.element,"keyup",this.chartKeyUpRef),sf.base.EventHandler.add(window,s,t.resizeBound)},e.prototype.chartOnKeyUp=function(e,t,i){var n,s=document.getElementById(this.element.id+"_LeftSlider"),a=document.getElementById(this.element.id+"_SelectedArea"),o=document.getElementById(this.element.id+"_RightSlider");s&&((n=s.getAttribute("class"))&&-1===n.indexOf("e-chart-focused")?n+=" e-chart-focused":n||(n="e-chart-focused"),s.setAttribute("class",n));a&&((n=a.getAttribute("class"))&&-1===n.indexOf("e-chart-focused")?n+=" e-chart-focused":n||(n="e-chart-focused"),a.setAttribute("class",n),a.getAttribute("aria-label")||a.setAttribute("aria-label","Selected Area"));o&&((n=o.getAttribute("class"))&&-1===n.indexOf("e-chart-focused")?n+=" e-chart-focused":n||(n="e-chart-focused"),o.setAttribute("class",n));return!1},e.prototype.rangeOnMouseDown=function(e){var t=window.sfBlazor.getCompInstance(this.dataId);this.setMouseX(this.getPageX(e)),e.target.id.indexOf("_Thumb")>-1&&(t.isDrag=!0),this.dotNetRef.invokeMethodAsync("OnRangeMouseDown",this.getEventArgs(e))},e.prototype.mouseMove=function(e){var t=window.sfBlazor.getCompInstance(this.dataId);document.getElementById(this.id.replace("_stockChart_rangeSelector","")+"_svg")&&(this.setMouseX(this.getPageX(e)),t.isDrag&&this.sliderChangeValue&&(t.changeSlider(),this.id.indexOf("stockchart")>-1&&this.dotNetRef.invokeMethodAsync("OnRangeMouseMove",this.getEventArgs(e))))},e.prototype.changeSlider=function(){var e,t;if(this.sliderChangeValue.isDrag&&this.mouseX>=this.sliderChangeValue.boundsX){switch(this.sliderChangeValue.currentSlider){case"Left":this.sliderChangeValue.startValue=this.getRangeValue(Math.abs(this.mouseX-this.sliderChangeValue.boundsX));break;case"Right":this.sliderChangeValue.endValue=this.getRangeValue(Math.abs(this.mouseX-this.sliderChangeValue.boundsX)),this.sliderChangeValue.endValue="DateTimeCategory"===this.sliderChangeValue.valueType&&this.sliderChangeValue.endValue>this.labels.length-1?this.labels.length-1:this.sliderChangeValue.endValue;break;case"Middle":e=Math.max(this.getRangeValue(Math.abs(this.sliderChangeValue.startX-(this.sliderChangeValue.previousMoveX-this.mouseX)-this.sliderChangeValue.boundsX)),this.sliderChangeValue.rangeMin),t=Math.min(this.getRangeValue(Math.abs(this.sliderChangeValue.endX-(this.sliderChangeValue.previousMoveX-this.mouseX)-this.sliderChangeValue.boundsX)),this.sliderChangeValue.rangeMax),Math.floor(Math.abs(this.getXLocation(t)-this.getXLocation(e)))===Math.floor(this.sliderChangeValue.sliderWidth)&&(this.sliderChangeValue.startValue=e,this.sliderChangeValue.endValue=t)}this.setSlider(this.sliderChangeValue.startValue,this.sliderChangeValue.endValue),this.sliderChangeValue.previousMoveX=this.mouseX}},e.prototype.setSlider=function(e,t){var i=window.sfBlazor.getCompInstance(this.dataId),n=document.getElementById(i.id+"_SelectedArea"),s=document.getElementById(i.id+"_leftUnSelectedArea"),a=document.getElementById(i.id+"_rightUnSelectedArea"),o=document.getElementById(i.id+"_LeftSlider"),l=document.getElementById(i.id+"_RightSlider");t>=e||(e=[t,t=e][0]);var h=this.sliderChangeValue.boundsX;e=t>=e?e:[t,t=e][0],e=Math.max(e,this.sliderChangeValue.rangeMin),t=Math.min(t,this.sliderChangeValue.rangeMax),this.sliderChangeValue.startX=h+this.getXLocation(e),this.sliderChangeValue.endX=h+this.getXLocation(t);var r=this.sliderChangeValue.enableRtl?this.sliderChangeValue.endX:this.sliderChangeValue.startX,d=this.sliderChangeValue.enableRtl?this.sliderChangeValue.startX:this.sliderChangeValue.endX;this.sliderChangeValue.sliderWidth=Math.abs(this.sliderChangeValue.endX-this.sliderChangeValue.startX),n.setAttribute("x",r+""),n.setAttribute("width",this.sliderChangeValue.sliderWidth+""),s.setAttribute("width",r-h+""),a.setAttribute("x",d+""),a.setAttribute("width",this.sliderChangeValue.boundsWidth-(d-h)+""),o.setAttribute("transform","translate("+(this.sliderChangeValue.startX-this.sliderChangeValue.thumpPadding)+", 0)"),l.setAttribute("transform","translate("+(this.sliderChangeValue.endX-this.sliderChangeValue.thumpPadding)+", 0)");var u=this.sliderChangeValue.enableRtl?this.sliderChangeValue.endX:this.sliderChangeValue.startX,g=this.sliderChangeValue.enableRtl?this.sliderChangeValue.startX:this.sliderChangeValue.endX,c={x:this.sliderChangeValue.isLeightWeight?0+h:h,y:this.sliderChangeValue.isLeightWeight?0:this.sliderChangeValue.boundsY,width:this.sliderChangeValue.isLeightWeight?u-h:u,height:this.sliderChangeValue.isLeightWeight?this.sliderChangeValue.thumpY:this.sliderChangeValue.boundsHeight},f={x:this.sliderChangeValue.isLeightWeight?0+g:g,y:this.sliderChangeValue.isLeightWeight?0:this.sliderChangeValue.boundsY,width:this.sliderChangeValue.boundsWidth-(d-h),height:this.sliderChangeValue.isLeightWeight?this.sliderChangeValue.thumpY:this.sliderChangeValue.boundsHeight},m={x:this.sliderChangeValue.isLeightWeight?u+0:0,y:this.sliderChangeValue.isLeightWeight?0:this.sliderChangeValue.boundsY,width:this.sliderChangeValue.isLeightWeight?Math.abs(this.sliderChangeValue.endX-this.sliderChangeValue.startX):g,height:this.sliderChangeValue.isLeightWeight?this.sliderChangeValue.thumpY:this.sliderChangeValue.boundsHeight};i.tooltip.length>0&&this.updateTooltip(c,f,m,this.sliderChangeValue.startX,this.sliderChangeValue.endX)},e.prototype.updateTooltip=function(e,i,n,s,a){var o=window.sfBlazor.getCompInstance(this.dataId),l=this.getTooltipContent(this.sliderChangeValue.endValue),h=this.sliderChangeValue.enableRtl?e:i,r=o.tooltip[0].textStyle;t.measureText(l,r.size,r.fontWeight,r.fontStyle,r.fontFamily).Width>h.width&&(h=n),o.tooltip[0].location.x=a,o.tooltip[0].areaBounds=h,o.tooltip[0].content=[l],o.tooltip[0].dataBind(),l=this.getTooltipContent(this.sliderChangeValue.startValue),h=this.sliderChangeValue.enableRtl?i:e,r=o.tooltip[1].textStyle,t.measureText(l,r.size,r.fontWeight,r.fontStyle,r.fontFamily).Width>h.width&&((h=n).x=o.sliderChangeValue.isLeightWeight?0:h.x),o.tooltip[1].location.x=s,o.tooltip[1].content=[l],o.tooltip[1].areaBounds=h,o.tooltip[1].dataBind()},e.prototype.getTooltipContent=function(e){var t=this.sliderChangeValue.format,i=null!==t.match("{value}");e="DateTimeCategory"===this.sliderChangeValue.valueType?Math.floor(parseInt(this.labels[Math.floor(e)])):e;var n=new Date(e);return n.setTime(n.getTime()+60*n.getTimezoneOffset()*1e3),"DateTime"===this.sliderChangeValue.valueType||"DateTimeCategory"===this.sliderChangeValue.valueType?(new sf.base.Internationalization).getDateFormat({format:t||"MM/dd/yyyy"})(n):(new sf.base.Internationalization).getNumberFormat({format:i?"":t,useGrouping:this.sliderChangeValue.useGrouping})("Logarithmic"===this.sliderChangeValue.valueType?Math.pow(this.sliderChangeValue.logBase,e):e)},e.prototype.getRangeValue=function(e){return(this.sliderChangeValue.enableRtl?1-e/this.sliderChangeValue.boundsWidth:e/this.sliderChangeValue.boundsWidth)*this.sliderChangeValue.rangeDelta+this.sliderChangeValue.rangeMin},e.prototype.getXLocation=function(e){var t=(e-this.sliderChangeValue.rangeMin)/this.sliderChangeValue.rangeDelta;return(this.sliderChangeValue.enableRtl?1-t:t)*this.sliderChangeValue.boundsWidth},e.prototype.mouseEnd=function(e){var t=window.sfBlazor.getCompInstance(this.dataId);t.isDrag&&this.sliderChangeValue&&(this.dotNetRef.invokeMethodAsync("SetStartEndValue",this.sliderChangeValue.startValue,this.sliderChangeValue.endValue,!0,this.sliderChangeValue.enableTooltip),t.isDrag=!1),e.type.indexOf("touch")>-1&&this.isTooltipHide&&this.fadeOutTooltip(),this.dotNetRef.invokeMethodAsync("OnRangeMouseEnd",this.getEventArgs(e))},e.prototype.rangeOnMouseClick=function(e){this.dotNetRef.invokeMethodAsync("OnRangeMouseClick",this.getEventArgs(e))},e.prototype.mouseLeave=function(e){var t=window.sfBlazor.getCompInstance(this.dataId);this.setMouseX(this.getPageX(e)),t.isDrag=!1,this.dotNetRef.invokeMethodAsync("OnRangeMouseLeave",this.getEventArgs(e)),this.isTooltipHide&&this.fadeOutTooltip()},e.prototype.fadeOutTooltip=function(e){if(void 0===e&&(e=!1),this.sliderChangeValue&&this.sliderChangeValue.isTooltipHide||e){e||window.clearInterval(this.toolTipInterval);var t=window.sfBlazor.getCompInstance(this.dataId);(t.tooltip[1]||e)&&(this.toolTipInterval=window.setTimeout((function(){t.tooltip[1]&&(t.tooltip[0].fadeOut(),t.tooltip[1].fadeOut())}),1e3))}},e.prototype.getPageX=function(e){return e.type.indexOf("touch")>-1?e.changedTouches[0].clientX:e.clientX},e.prototype.setMouseX=function(e){var t=document.getElementById(this.id.replace("_stockChart_rangeSelector","")+"_svg");if(t){var i=t.getBoundingClientRect(),n=document.getElementById(this.id).getBoundingClientRect();this.mouseX=e-n.left-Math.max(i.left-n.left,0)}},e.prototype.getEventArgs=function(e){return{type:e.type,clientX:e.clientX,clientY:e.clientY,mouseX:this.mouseX,mouseY:this.mouseY,pointerType:e.pointerType,target:e.target.id,changedTouches:{clientX:e.changedTouches?e.changedTouches[0].clientX:0,clientY:e.changedTouches?e.changedTouches[0].clientY:0}}},e}(),t={getParentElementBoundsById:function(t,i,n,s){var a=document.getElementById(i);if(a){var o=new e(t,i,a,n,s);o.unWireEvents(i,n),o.wireEvents(i,n),a.style.width="100%",a.style.height="100%";var l=a.getBoundingClientRect();return{width:l.width||a.clientWidth||a.offsetWidth,height:l.height||a.clientHeight||a.offsetHeight,left:l.left,top:l.top,right:l.right,bottom:l.bottom}}return{width:0,height:0,left:0,top:0,right:0,bottom:0}},getElementBoundsById:function(t,i,n,s,a,o,l){if(s=document.getElementById(i)){var h=new e(t,i,s,n,a);return h.unWireEvents(i,n),h.wireEvents(i,n),this.getElementSize(s,o,l)}return{width:0,height:0}},getElementSize:function(e,t,i){return e?(e.style.height=t,e.style.width=i,{width:e.clientWidth||e.offsetWidth,height:e.clientHeight||e.offsetHeight}):{width:0,height:0}},destroy:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.destroy()},charCollection:["0","1","2","3","4","5","6","7","8","9","!",'"',"#","$","%","&","(",")","*","+",",","-",".","/",":",";","<","=",">","?","@","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","[","]","^","_","`","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","{","|","}","~"," "],measureText:function(e,t,i,n,s){var a=document.getElementById("chartmeasuretext");return null===a&&(a=sf.base.createElement("text",{id:"chartmeasuretext"}),document.body.appendChild(a))," "===e&&(e="&nbsp;"),a.innerHTML=e,a.style.position="fixed",a.style.fontSize=t,a.style.fontWeight=i,a.style.fontStyle=n,a.style.fontFamily=s,a.style.visibility="hidden",a.style.top="-100",a.style.left="0",a.style.whiteSpace="nowrap",a.style.lineHeight="normal",{Width:a.clientWidth,Height:a.clientHeight}},getCharSizeByFontKeys:function(e){for(var t={},i=this.charCollection,n=e.length,s=0;s<n;s++)for(var a=e[s].split("_"),o=a[0],l=a[1],h=a[2],r="_"+o+"_"+l+"_"+h,d=0;d<i.length;d++)t[i[d]+r]=this.measureText(i[d],"100px",o,l,h);return JSON.stringify(t)},getCharSizeByCharKey:function(e){var t=e.split("_");return this.measureText(t[0],"100px",t[2],t[3],t[4])},dotnetref:{},setValueOnSliderSelect:function(e,t,i){t=document.getElementById(t.id);var n=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(n)||(n.sliderChangeValue=i,n.isTooltipHide=i.isTooltipHide,n.isDrag=!0)},getElementRect:function(e){var t,i=document.getElementById(e);return i&&(t=i.getBoundingClientRect(),sf.base.remove(i)),{Left:t.left,Right:t.right,Top:t.top,Bottom:t.bottom,Width:t.width,Height:t.height}},dotnetrefCollection:[],renderTooltip:function(e,t,i,n,s,a){var o,l,h,r=window.sfBlazor.getCompInstance(a),d=[i,n],u=[e,t];s=document.getElementById(s.id);for(var g=1;g>=0;g--)l=d[g],!((o=document.getElementById(l.replace("_stockChart_rangeSelector","")+"_svg"))&&parseInt(o.getAttribute("opacity"),10)>0),h=JSON.parse(u[g]),r.tooltip[g]=new sf.svgbase.Tooltip(h),r.tooltip[g].appendTo("#"+l)},performSliderAnimation:function(e,t,i,n,s,a,o,l,h,r,d){var u=this;e=document.getElementById(e.id);var g=window.sfBlazor.getCompInstance(d);g.sliderChangeValue=r,new sf.base.Animation({}).animate(sf.base.createElement("div"),{duration:0===n?500:n,progress:function(e){var n=u.linear(e.timeStamp,0,t-s,e.duration)+s,o=u.linear(e.timeStamp,0,i-a,e.duration)+a;g.setSlider(n,o)},end:function(){g.setSlider(t,i),l?h.invokeMethodAsync("PerformSnapping",t,i,!0,o):g.dotNetRef.invokeMethodAsync("SetStartEndValue",t,i,!0,o)}})},linear:function(e,t,i,n){return-i*Math.cos(e/n*(Math.PI/2))+i+t},setAttribute:function(e,t,i){var n=document.getElementById(e);n&&n.setAttribute(t,i)},getAndSetTextContent:function(e,t,i){var n=document.getElementById(e);if(n){if(t)return n.textContent;n.textContent=i}return null},resizeBound:{},resize:{},rangeResize:function(e,t){var i=this;return window.sfBlazor.getCompInstance(this.dataId).isDrag=!1,this.resize&&clearTimeout(this.resize),this.resize=setTimeout((function(){for(var n,s=e.length,a=0;a<s;a++)n=e[a].dotnetref,e[a].id.indexOf("_stockChart_")<0&&n.invokeMethodAsync("OnRangeResize",t);clearTimeout(i.resize)}),500),!1}};return t}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfrangenavigator');})})();