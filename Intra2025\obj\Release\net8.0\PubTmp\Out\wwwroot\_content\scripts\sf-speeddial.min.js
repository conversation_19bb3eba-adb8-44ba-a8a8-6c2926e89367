/*!*  filename: sf-speeddial.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[57],{"./bundles/sf-speeddial.js":function(t,e,i){"use strict";i.r(e);i("./modules/sf-speeddial.js")},"./modules/sf-speeddial.js":function(t,e){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.SpeedDial=function(){"use strict";var t="e-speeddial-fixed",e="e-speeddial-hidden",i="e-speeddial-li",s=function(){function s(t){window.sfBlazor=window.sfBlazor,this.updateContext(t),window.sfBlazor.setCompInstance(this),this.initialize()}return s.prototype.updateContext=function(t){sf.base.extend(this,this,t)},s.prototype.initialize=function(){this.isControl=!1,this.checkTarget(),this.setClientProps(!0),this.bindEvent()},s.prototype.checkTarget=function(){this.targetEle=null,sf.base.isNullOrUndefined(this.target)||"string"!=typeof this.target||(this.targetEle=document.querySelector(this.target)),this.isFixed=sf.base.isNullOrUndefined(this.targetEle),this.isFixed?(this.popupEle.classList.add(t),this.overlayEle&&this.overlayEle.classList.add(t)):(this.popupEle.classList.remove(t),this.targetEle.appendChild(this.popupEle),this.overlayEle&&(this.overlayEle.classList.remove(t),this.targetEle.appendChild(this.overlayEle)))},s.prototype.bindEvent=function(){sf.base.EventHandler.add(window,"resize",this.resizeHandler,this),sf.base.EventHandler.add(document.body,"click",this.documentClickhandler,this),sf.base.EventHandler.add(this.popupEle,"click",this.speeddialClickHandler,this),sf.base.EventHandler.add(this.element,"click",this.speeddialClickHandler,this),sf.base.EventHandler.add(this.popupEle,"mouseleave",this.popupMouseLeaveHandle,this),this.isHover&&this.bindHoverEvents(),sf.base.EventHandler.add(window,"resize",this.resizeHandler,this)},s.prototype.unBindEvent=function(){sf.base.EventHandler.remove(window,"resize",this.resizeHandler),sf.base.EventHandler.remove(document.body,"click",this.documentClickhandler),sf.base.EventHandler.remove(this.popupEle,"click",this.speeddialClickHandler),sf.base.EventHandler.remove(this.element,"click",this.speeddialClickHandler),sf.base.EventHandler.remove(this.popupEle,"mouseleave",this.popupMouseLeaveHandle),this.isHover&&this.unBindHoverEvents()},s.prototype.bindHoverEvents=function(){sf.base.EventHandler.add(this.element,"mouseover",this.mouseOverHandle,this),sf.base.EventHandler.add(this.element,"mouseleave",this.mouseLeaveHandle,this)},s.prototype.unBindHoverEvents=function(){sf.base.EventHandler.remove(this.element,"mouseover",this.mouseOverHandle),sf.base.EventHandler.remove(this.element,"mouseleave",this.mouseLeaveHandle)},s.prototype.speeddialClickHandler=function(){this.checkPosition(),this.isControl=!0},s.prototype.documentClickhandler=function(){this.isControl?this.isControl=!1:this.popupEle.classList.contains(e)||this.callShowHidePopup(!1)},s.prototype.popupMouseLeaveHandle=function(t){var e=t.relatedTarget;!this.isHover||e.classList.contains("e-speeddial")||sf.base.closest(e,".e-speeddial")||this.callShowHidePopup(!1)},s.prototype.mouseOverHandle=function(){this.callShowHidePopup(!0),this.checkPosition()},s.prototype.mouseLeaveHandle=function(t){var e=t.relatedTarget;e.classList.contains("e-speeddial-popup")||sf.base.closest(e,".e-speeddial-popup")||this.callShowHidePopup(!1)},s.prototype.checkPosition=function(){this.popupEle.classList.contains(e)&&("Radial"===this.mode||this.isTemplate?(this.setCustomRadialPosition(),this.dotNetRef.invokeMethodAsync("UpdateRadialPosition",this.horzDist,this.vertDist)):(this.getOffset(),this.dotNetRef.invokeMethodAsync("UpdatePosition",this.yOffset,this.xOffset)))},s.prototype.callShowHidePopup=function(t){this.dotNetRef.invokeMethodAsync(t?"ShowAsync":"HideAsync")},s.prototype.setCustomRadialPosition=function(){var t=document.documentElement.clientWidth,e=document.documentElement.clientHeight;if(-1!==["TopLeft","BottomLeft","MiddleLeft"].indexOf(this.position)&&(this.popupEle.classList.contains("e-rtl")?this.isFixed?this.horzDist=t-(this.element.offsetLeft+this.element.offsetWidth)+"px":this.horzDist=this.targetEle.clientWidth-(this.element.offsetLeft+this.element.offsetWidth)+"px":this.horzDist=this.element.offsetLeft+"px",this.popupEle.style.setProperty("--speeddialRadialHorzDist",this.horzDist)),-1!==["TopLeft","TopCenter","TopRight"].indexOf(this.position)&&(this.vertDist=this.element.offsetTop+"px",this.popupEle.style.top=this.vertDist),-1!==["TopRight","BottomRight","MiddleRight"].indexOf(this.position)&&(this.popupEle.classList.contains("e-rtl")?this.horzDist=this.element.offsetLeft+"px":this.isFixed?this.horzDist=t-(this.element.offsetLeft+this.element.offsetWidth)+"px":this.horzDist=this.targetEle.clientWidth-(this.element.offsetLeft+this.element.offsetWidth)+"px",this.popupEle.style.setProperty("--speeddialRadialHorzDist",this.horzDist)),-1!==["BottomLeft","BottomCenter","BottomRight"].indexOf(this.position)&&(this.isFixed?this.vertDist=e-(this.element.offsetTop+this.element.offsetHeight)+"px":this.vertDist=this.targetEle.clientHeight-(this.element.offsetTop+this.element.offsetHeight)+"px",this.popupEle.style.bottom=this.vertDist),-1!==["TopCenter","MiddleCenter","BottomCenter"].indexOf(this.position)){if(this.popupEle.classList.contains("e-rtl"))if(this.isFixed)this.horzDist=t-(this.element.offsetLeft+this.element.offsetWidth)-this.popupEle.offsetWidth/2+"px";else{var i=this.targetEle.clientWidth,s=this.popupEle.offsetWidth;this.horzDist=i-(this.element.offsetLeft+this.element.offsetWidth)-s/2+"px"}else this.horzDist=this.element.offsetLeft-this.popupEle.offsetWidth/2+"px";this.popupEle.style.setProperty("--speeddialRadialHorzDist",this.horzDist)}-1!==["MiddleLeft","MiddleCenter","MiddleRight"].indexOf(this.position)&&(this.vertDist=this.element.offsetTop-this.popupEle.offsetHeight/2+"px",this.popupEle.style.top=this.vertDist)},s.prototype.resizeHandler=function(){this.getOffset(),(this.isMiddle||this.isCenter)&&this.dotNetRef.invokeMethodAsync("UpdatePosition",this.yOffset,this.xOffset)},s.prototype.setClientProps=function(t){this.getOffset(),("Radial"===this.mode||this.isTemplate)&&this.setCustomRadialPosition();var e={yOffset:this.yOffset,xOffset:this.xOffset};t&&(e.isFixed=this.isFixed?"Fixed":"Absolute"),"Radial"!==this.mode||this.isTemplate||(e.liHeight=this.liHeight,e.liWidth=this.liWidth),("Radial"===this.mode||this.isTemplate)&&(e.horzDist=this.horzDist,e.vertDist=this.vertDist),this.clientProps=JSON.stringify(e)},s.prototype.getOffset=function(){if("Radial"===this.mode||this.isTemplate){if(this.yOffset=this.xOffset="",!this.isTemplate){var t=this.popupEle.querySelector(".e-speeddial-li");this.liHeight=t.offsetHeight+"px",this.liWidth=t.offsetWidth+"px",this.popupEle.style.setProperty("--speeddialRadialMinHeight",this.liHeight),this.popupEle.style.setProperty("--speeddialRadialMinWidth",this.liWidth)}this.isMiddle&&(this.yOffset=((this.targetEle?this.targetEle.clientHeight:window.innerHeight)-this.popupEle.offsetHeight)/2+"px",this.popupEle.style.setProperty("--speeddialVertDist",this.yOffset)),this.isCenter&&(this.xOffset=((this.targetEle?this.targetEle.clientWidth:window.innerWidth)-this.popupEle.offsetWidth)/2+"px",this.popupEle.style.setProperty("--speeddialHorzDist",this.xOffset))}else{var e=this.element.offsetHeight/2,i=this.element.offsetWidth/2;if(this.isTop)this.yOffset=this.element.offsetTop+(this.isVertical?this.element.offsetHeight:0)+"px",this.isMiddle&&("Right"!==this.actualDirection&&"Left"!==this.actualDirection||(this.yOffset=this.element.offsetTop-e+"px"),"Down"===this.actualDirection&&(this.yOffset=parseInt(this.yOffset)-e+"px"));else{var s=this.isFixed?window.document.documentElement.clientHeight:this.targetEle.clientHeight;this.yOffset=s-this.element.offsetTop-(this.isVertical?0:this.element.offsetHeight)+"px",this.isMiddle&&("Auto"!==this.actualDirection&&"Up"!==this.actualDirection||(this.yOffset=parseInt(this.yOffset)+e+"px"))}if(this.popupEle.style.setProperty("--speeddialVertDist",this.yOffset),this.isLeft)this.xOffset=this.element.offsetLeft+(this.isVertical?0:this.element.offsetWidth)+"px",this.isCenter&&("Auto"===this.actualDirection||"Down"===this.actualDirection||"Up"===this.actualDirection?this.xOffset=this.element.offsetLeft-i+"px":this.xOffset="Right"===this.actualDirection?this.element.offsetLeft+i+"px":parseInt(this.xOffset)+i+"px");else{var o=this.isFixed?window.document.documentElement.clientWidth:this.targetEle.clientWidth;this.xOffset=o-this.element.offsetLeft-(this.isVertical?this.element.offsetWidth:0)+"px",this.isCenter&&"Left"===this.actualDirection&&(this.xOffset=o-this.element.offsetLeft-(this.isVertical?this.element.offsetWidth:0)+i+"px"),this.popupEle.classList.contains("e-rtl")&&this.isCenter&&(this.xOffset=parseInt(this.xOffset)-this.element.offsetWidth/2+"px")}this.popupEle.style.setProperty("--speeddialHorzDist",this.xOffset)}},s.prototype.animateClose=function(t){var s=this;if(this.updateContext(t),"None"!==this.animation.effect){var o={name:this.animation.effect+"Out",timingFunction:"easeOut"},n=this.isTemplate?[this.popupEle.firstElementChild]:sf.base.selectAll("."+i,this.popupEle);n&&0!==n.length||this.dotNetRef.invokeMethodAsync("OnAnimationCompleted",!1);var h=this.animation.duration/(n.length+1);o.duration=2*h;!function t(i){if(o.delay=i===n.length-1?s.animation.delay:0,o.begin=function(){},o.end=function(){n[i].classList.add(e),0===i&&(s.popupEle.classList.add(e),s.dotNetRef.invokeMethodAsync("OnAnimationCompleted",!1))},new sf.base.Animation(o).animate(n[i]),0!==i){var l=i-1;setTimeout((function(){t(l)}),h)}}(n.length-1)}},s.prototype.animateOpen=function(t){var s=this;if(this.updateContext(t),"None"!==this.animation.effect){var o={name:this.animation.effect+"In",timingFunction:"easeIn"},n=this.isTemplate?[this.popupEle.firstElementChild]:sf.base.selectAll("."+i,this.popupEle);n&&0!==n.length||this.dotNetRef.invokeMethodAsync("OnAnimationCompleted",!0);var h=this.animation.duration/(n.length+1);o.duration=2*h;!function t(i){if(o.delay=0===i?s.animation.delay:0,o.begin=function(){0===i&&s.popupEle.classList.remove(e),n[i].classList.remove(e)},o.end=function(){i===n.length-1&&(s.isTemplate&&s.popupEle.focus(),s.dotNetRef.invokeMethodAsync("OnAnimationCompleted",!0))},new sf.base.Animation(o).animate(n[i]),i!==n.length-1){var l=i+1;setTimeout((function(){t(l)}),h)}}(0)}},s.prototype.validateTarget=function(t){this.updateContext(t),this.checkTarget(),this.setClientProps(!0)},s.prototype.setPosition=function(t){this.updateContext(t),this.setClientProps(!1)},s.prototype.updateOnHover=function(t){this.updateContext(t),this.isHover?this.bindHoverEvents():this.unBindHoverEvents()},s.prototype.destroy=function(){this.unBindEvent()},s}();return{initialize:function(t){return t.dataId?new s(t).clientProps:{}},validateTarget:function(t){if(t.dataId){var e=window.sfBlazor.getCompInstance(t.dataId);return e.validateTarget(t),e.clientProps}return{}},setPosition:function(t){if(t.dataId){var e=window.sfBlazor.getCompInstance(t.dataId);return e.setPosition(t),e.clientProps}return{}},updateOnHover:function(t){t.dataId&&window.sfBlazor.getCompInstance(t.dataId).updateOnHover(t)},animateClose:function(t){t.dataId&&window.sfBlazor.getCompInstance(t.dataId).animateClose(t)},animateOpen:function(t){t.dataId&&window.sfBlazor.getCompInstance(t.dataId).animateOpen(t)},destroy:function(t){t&&window.sfBlazor.getCompInstance(t).destroy()},focusElement:function(t){t.focus()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfspeeddial');})})();