using ComRemitBlazor.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace ComRemitBlazor.Services
{
    public class RemitService
    {
        private readonly ComRemitDbContext _context;
        private readonly PayeeService _payeeService;

        public RemitService(ComRemitDbContext context, PayeeService payeeService)
        {
            _context = context;
            _payeeService = payeeService;
        }

        public async Task<List<RemitedList>> GetRemitListByConSnoAsync(int conSno)
        {
            var conSnoString = conSno;
            // 只返回未完成彙整的資料（CashDate 為 null）
            return await _context.RemitedList
                .Where(r => r.ConSno == conSnoString && r.CashDate == null)
                .OrderBy(r => r.Sno)
                .ToListAsync();
        }

        public async Task<RemitedList?> GetRemitItemBySnoAsync(int sno)
        {
            return await _context.RemitedList.FindAsync(sno);
        }

        public async Task AddRemitItemAsync(RemitedList item)
        {
            _context.RemitedList.Add(item);
            await _context.SaveChangesAsync();
        }

        public async Task AddRemitItemsAsync(List<RemitedList> items)
        {
            await _context.RemitedList.AddRangeAsync(items);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateRemitItemAsync(RemitedList item)
        {
            var existing = await _context.RemitedList.FindAsync(item.Sno);
            if (existing != null)
            {
                // 手動更新，避免 EF Core 追蹤問題
                existing.RemitPrice = item.RemitPrice;
                existing.IfFee = item.IfFee;
                existing.RemitMemo = item.RemitMemo;

                _context.RemitedList.Update(existing);
                await _context.SaveChangesAsync();
            }
        }

        public async Task DeleteRemitItemAsync(int sno)
        {
            var item = await _context.RemitedList.FindAsync(sno);
            if (item != null)
            {
                _context.RemitedList.Remove(item);
                await _context.SaveChangesAsync();
            }
        }

        /// <summary>
        /// 獲取下一個彙整編號，格式為 YYMMDD00n
        /// </summary>
        public async Task<int> GetNextConSnoAsync()
        {
            try
            {
                // 獲取當前日期並格式化為 YYMMDD
                var today = DateTime.Now;
                var datePrefix = $"{today.Year % 100:D2}{today.Month:D2}{today.Day:D2}";
                var datePrefixInt = int.Parse(datePrefix) * 1000; // 轉換為 YYMMDD000 格式的基礎數字

                Console.WriteLine($"GetNextConSnoAsync: Today={today:yyyy-MM-dd}, datePrefix={datePrefix}, datePrefixInt={datePrefixInt}");

                // 查詢今天的所有編號（包含所有資料，不論是否已彙整）
                var allConSnos = await _context.RemitedList
                    .Where(r => r.ConSno.HasValue)
                    .Select(r => r.ConSno!.Value)
                    .ToListAsync();

                Console.WriteLine($"GetNextConSnoAsync: All ConSnos in database: [{string.Join(", ", allConSnos.OrderBy(x => x))}]");

                // 篩選今天的編號
                var todayConSnos = allConSnos
                    .Where(conSno => conSno >= datePrefixInt && conSno < datePrefixInt + 1000)
                    .ToList();

                Console.WriteLine($"GetNextConSnoAsync: Today's ConSnos: [{string.Join(", ", todayConSnos.OrderBy(x => x))}]");

                var maxConSno = todayConSnos.DefaultIfEmpty(datePrefixInt).Max();

                // 如果今天還沒有編號，返回 YYMMDD001，否則返回最大編號+1
                var nextConSno = maxConSno == datePrefixInt ? datePrefixInt + 1 : maxConSno + 1;

                Console.WriteLine($"GetNextConSnoAsync: maxConSno={maxConSno}, nextConSno={nextConSno}");

                return nextConSno;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GetNextConSnoAsync error: {ex.Message}");

                // 如果發生任何錯誤，返回今天日期的第一個編號
                var today = DateTime.Now;
                var datePrefix = $"{today.Year % 100:D2}{today.Month:D2}{today.Day:D2}";
                return int.Parse(datePrefix) * 1000 + 1; // 返回 YYMMDD001
            }
        }

        /// <summary>
        /// 完成彙整 - 將指定 ConSno 的所有資料標記為已彙整
        /// </summary>
        /// <param name="conSno">彙整編號</param>
        /// <returns>是否成功彙整</returns>
        public async Task<bool> CompleteRemitAsync(int conSno)
        {
            try
            {
                var items = await _context.RemitedList
                    .Where(r => r.ConSno == conSno && r.CashDate == null) // 只更新未彙整的資料
                    .ToListAsync();

                if (!items.Any())
                {
                    return false; // 沒有待彙整的資料
                }

                // 標記為已彙整
                foreach (var item in items)
                {
                    item.CashDate = DateTime.Now; // 設定彙整完成時間
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 檢查指定 ConSno 是否已完成彙整
        /// </summary>
        /// <param name="conSno">彙整編號</param>
        /// <returns>是否已完成彙整</returns>
        public async Task<bool> IsRemitCompletedAsync(int conSno)
        {
            try
            {
                // 檢查是否有待彙整的資料（CashDate 為 null）
                var hasUncompletedItems = await _context.RemitedList
                    .AnyAsync(r => r.ConSno == conSno && r.CashDate == null);

                // 只要有待彙整的資料，就表示尚未完成彙整
                return !hasUncompletedItems;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 完成選中項目的彙整 - 將指定 Sno 的資料標記為已彙整
        /// </summary>
        /// <param name="snoList">要彙整的 Sno 列表</param>
        /// <param name="purpose">彙整單用途說明</param>
        /// <returns>是否成功彙整</returns>
        public async Task<bool> CompleteSelectedRemitAsync(List<int> snoList, string purpose = "")
        {
            try
            {
                if (!snoList.Any())
                {
                    return false;
                }

                var items = await _context.RemitedList
                    .Where(r => snoList.Contains(r.Sno) && r.CashDate == null) // 只更新未彙整的資料
                    .ToListAsync();

                if (!items.Any())
                {
                    return false; // 沒有待彙整的資料
                }

                // 標記為已彙整
                foreach (var item in items)
                {
                    item.CashDate = DateTime.Now; // 設定彙整完成時間
                    item.ConDate = DateTime.Now; // 設定彙整日期
                    if (!string.IsNullOrWhiteSpace(purpose))
                    {
                        // 儲存彙整單用途說明到 ConMemo 欄位
                        item.ConMemo = purpose;
                    }
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
