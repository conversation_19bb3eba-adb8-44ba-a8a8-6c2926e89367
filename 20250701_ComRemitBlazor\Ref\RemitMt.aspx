﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="RemitMt.aspx.cs" Inherits="RemitMt" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>匯款彙整作業</title>
    <style type="text/css">
        .style2 {
            font-size: large;
            font-weight: bold;
            color: #0033CC;
        }

        .style3 {
            font-size: large;
            font-weight: bold;
            color: #000000;
        }

        .style4 {
            font-size: medium;
            font-weight: bold;
            color: #000000;
        }

        body {
            color: #808080;
            font-family: Microsoft JhengHei;
        }

        .auto-style1 {
            width: 180px;
        }
        .auto-style2 {
            color: #FF0066;
        }
    </style>
    <link rel="stylesheet" href="func/jquery-ui.css" />
    <script src="func/jquery.min.js"></script>
    <script src="func/jquery-ui.min.js"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            var val;
            //function split(val) {
            //    return val.split(/,\s*/);
            //}
            function getCollectAcc() {
                $.ajax({
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    url: "RemitMt.aspx/GetCollectAcc",
                    data: "{'term':'" + $("#CollecName").val() + "'}",
                    async: false,
                    dataType: "json",
                    success: function (data) {
                        var mydata = JSON.parse(JSON.stringify(data.d));
                        $("#CollectAcc").attr('readonly', false);
                        $("#CollectAcc").val(mydata);
                        $("#CollectAcc").attr('readonly', true);
                    },
                    error: function (result) {
                        //alert("getCollectAcc");
                    }
                });
            }
            function getCollectNo() {  //取得局號
                $.ajax({
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    url: "RemitMt.aspx/GetCollectNo",
                    data: "{'term':'" + $("#CollecName").val() + "'}",
                    async: false,
                    dataType: "json",
                    success: function (data) {
                        var mydata = JSON.parse(JSON.stringify(data.d));
                        $("#CollectNo").attr('readonly', false);
                        $("#CollectNo").val(mydata);
                        $("#CollectNo").attr('readonly', true);
                    },
                    error: function (result) {
                        //alert("getCollectNo");
                    }
                });
            }
            function getCollectID() {
                $.ajax({
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    url: "RemitMt.aspx/GetCollectID",
                    data: "{'term':'" + $("#CollecName").val() + "'}",
                    async: false,
                    dataType: "json",
                    success: function (data) {
                        var mydata = JSON.parse(JSON.stringify(data.d));
                        $("#CollectId").attr('readonly', false);
                        $("#CollectId").val(mydata);
                        $("#CollectId").attr('readonly', true);
                    },
                    error: function (result) {
                        //alert("getCollectID");
                    }
                });
            }
            function getCollectTel() {
                $.ajax({
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    url: "RemitMt.aspx/GetCollectTel",
                    data: "{'term':'" + $("#CollecName").val() + "'}",
                    async: false,
                    dataType: "json",
                    success: function (data) {
                        var mydata = JSON.parse(JSON.stringify(data.d));
                        $("#Tel").attr('readonly', false);
                        $("#Tel").val(mydata);
                        $("#Tel").attr('readonly', true);
                    },
                    error: function (result) {
                        $("#Tel").val('');
                    }
                });
            }
            function getCollectZip() {
                $.ajax({
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    url: "RemitMt.aspx/GetCollectZip",
                    data: "{'term':'" + $("#CollecName").val() + "'}",
                    async: false,
                    dataType: "json",
                    success: function (data) {
                        var mydata = JSON.parse(JSON.stringify(data.d));
                        $("#Zip").attr('readonly', false);
                        $("#Zip").val(mydata);
                        $("#Zip").attr('readonly', true);
                    },
                    error: function (result) {
                        $("#Zip").val('');
                    }
                });
            }
            function getCollectAddr() {
                $.ajax({
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    url: "RemitMt.aspx/GetCollectAddr",
                    data: "{'term':'" + $("#CollecName").val() + "'}",
                    async: false,
                    dataType: "json",
                    success: function (data) {
                        var mydata = JSON.parse(JSON.stringify(data.d));
                        $("#Addr").attr('readonly', false);
                        $("#Addr").val(mydata);
                        $("#Addr").attr('readonly', true);
                    },
                    error: function (result) {
                        $("#Addr").val('');
                    }
                });
            }
            function getFinancialName() {
                $.ajax({
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    url: "RemitMt.aspx/GetFinancialName",
                    data: "{'term':'" + $("#CollectNo").val() + "'}",
                    async: false,
                    dataType: "json",
                    success: function (data) {
                        var mydata = JSON.parse(JSON.stringify(data.d));
                        $("#FinancialName").attr('readonly', false);
                        $("#FinancialName").val(mydata);
                        $("#FinancialName").attr('readonly', true);
                    },
                    error: function (result) {
                        $("#FinancialName").val('');
                    }
                });
            }
            $("#CollecName").autocomplete({
                source: function (request, response) {
                    $.ajax({
                        type: "POST",
                        contentType: "application/json; charset=utf-8",
                        url: "RemitMt.aspx/GetAccName",
                        data: "{'term':'" + $("#CollecName").val() + "'}",
                        dataType: "json",
                        success: function (data) {
                            response(data.d);
                        },
                        error: function (result) {
                            // alert("GetAccName");
                        }
                    });
                }
            });
            $("#CollecName").blur(function () {

                getCollectAcc();  //帶出帳號帳號
                getCollectNo();  //帶出金融局號
                getCollectID();   //帶出身份證字號
                getCollectTel();   //帶出電話
                getCollectZip();   //帶出區號
                getCollectAddr();   //帶出住址
                getFinancialName();  //帶出金融名稱
            });
        });
    </script>
</head>
<body>
    <form id="form1" runat="server">
        <div>
            <span class="style2">【匯款彙整作業(step2)】</span><table width="100%">
                <tr>
                    <td></td>
                    <td colspan="4">資料新增模式:<asp:RadioButtonList ID="RadioButtonList3" runat="server" AutoPostBack="True" RepeatDirection="Horizontal" RepeatLayout="Flow" OnSelectedIndexChanged="RadioButtonList3_SelectedIndexChanged">
                        <asp:ListItem Selected="True" Value="0">單筆</asp:ListItem>
                        <asp:ListItem Value="1">整批匯入</asp:ListItem>
                    </asp:RadioButtonList></td>
                </tr>
                <tr>
                    <td></td>
                    <td bgcolor="#ccffff">
                        <span runat="server" id="DataNew">
                            <table width="80%" id="tb1" runat="server">
                                <tr>
                                    <td class="auto-style1"><font color='red' size="5">*</font>收款人戶名</td>
                                    <td>
                                        <asp:TextBox ID="CollecName" Width="300" runat="server"></asp:TextBox></td>
                                    <td>身分證字號(統編)</td>
                                    <td>
                                        <asp:TextBox ID="CollectId" runat="server" BackColor="#cccccc"></asp:TextBox></td>
                                </tr>
                                <tr>
                                    <td class="auto-style1"><font color='red' size="5">*</font>收款人帳號</td>
                                    <td>
                                        <asp:TextBox ID="CollectAcc" runat="server" BackColor="#cccccc"></asp:TextBox>
                                    </td>
                                    <td width="180">解款行代號</td>
                                    <td>
                                        <asp:TextBox ID="CollectNo" runat="server" BackColor="#cccccc"></asp:TextBox>
                                        <asp:TextBox ID="FinancialName" runat="server" BackColor="#cccccc"></asp:TextBox></td>
                                </tr>
                                <tr>
                                    <td class="auto-style1">電話</td>
                                    <td>
                                        <asp:TextBox ID="Tel" runat="server" BackColor="#cccccc"></asp:TextBox>
                                    </td>
                                    <td width="180">郵遞區號</td>
                                    <td>
                                        <asp:TextBox ID="Zip" runat="server" BackColor="#cccccc"></asp:TextBox></td>
                                </tr>
                                <tr>
                                    <td class="auto-style1">住址</td>
                                    <td colspan="3">
                                        <asp:TextBox ID="Addr" runat="server" Width="300" BackColor="#cccccc"></asp:TextBox>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="auto-style1">
                                        <span runat="server" id="DataNew0"><font color='red' size="5">*</font>帳款金額</span></td>
                                    <td>
                                        <asp:TextBox ID="RemitPrice" runat="server"></asp:TextBox></td>
                                    <td>
                                        <span runat="server" id="DataNew1"><font color='red' size="5">*</font>帳款是否有手續費<br />
                                            (詳備註三)</span></td>
                                    <td>
                                        <span runat="server" id="DataNew2">
                                            <asp:RadioButtonList ID="RadioButtonList2" runat="server" RepeatDirection="Horizontal">
                                                <asp:ListItem Value="是">是</asp:ListItem>
                                                <asp:ListItem Value="否">否</asp:ListItem>
                                            </asp:RadioButtonList>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="auto-style1">備註</td>
                                    <td>
                                        <asp:TextBox ID="RemitMemo" runat="server" Width="300" MaxLength="40"></asp:TextBox></td>
                                    <td>&nbsp;</td>
                                    <td>彙整編號:
                                        <asp:Label ID="Num" runat="server"></asp:Label></td>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <asp:Button ID="Button3" runat="server" Text="確認新增此筆彙款資料" OnClick="Button3_Click" />&nbsp;</td>
                                    <td></td>
                                </tr>
                            </table>

                        </span>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td>&nbsp;
                        <table id="tb2" runat="server" width="100%"  bgcolor="#ccffcc" visible="false">
                            <tr>
                                <td>
                                    <asp:FileUpload ID="FileUpload2" runat="server" />
                                    <asp:Button ID="Button2" runat="server" Text="整批匯入" OnClick="Button2_Click" /> (範例檔請<a href="ExportTxt/匯入範例檔.csv">按我</a>下載)
                                <span class="auto-style2"><strong>(PS:備註欄位字數請控制在40字內，避免使用全形或特殊符號)</strong></span></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td width="5%"></td>
                    <td width="90%">
                        <asp:GridView ID="GridView1" runat="server" CellPadding="4" DataSourceID="SqlDataSource2" ForeColor="#333333" GridLines="None" Width="100%" AutoGenerateColumns="False" DataKeyNames="sno" AllowPaging="True" AllowSorting="True" OnRowDataBound="GridView1_RowDataBound" OnRowEditing="GridView1_RowEditing" OnRowUpdating="GridView1_RowUpdating" OnDataBound="GridView1_DataBound">
                            <Columns>
                                <asp:TemplateField ShowHeader="False">
                                    <EditItemTemplate>
                                        <asp:LinkButton ID="LinkButton1" runat="server" CausesValidation="True" CommandName="Update" Text="更新"></asp:LinkButton>
                                        &nbsp;<asp:LinkButton ID="LinkButton2" runat="server" CausesValidation="False" CommandName="Cancel" Text="取消"></asp:LinkButton>
                                    </EditItemTemplate>
                                    <ItemTemplate>
                                        <asp:LinkButton ID="LinkButton1" runat="server" CausesValidation="False" CommandName="Edit" Text="編輯"></asp:LinkButton>
                                        &nbsp;<asp:LinkButton ID="LinkButton2" runat="server" CausesValidation="False" CommandName="Delete" Text="刪除" OnClientClick="return confirm('確定刪除?');"></asp:LinkButton>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:BoundField DataField="sno" HeaderText="sno" InsertVisible="False" ReadOnly="True" SortExpression="sno" Visible="false" />
                                <asp:BoundField DataField="CollectNo" HeaderText="收款人金融局號" SortExpression="CollectNo" ReadOnly />
                                <asp:BoundField DataField="CollecAcc" HeaderText="收款人帳號" SortExpression="CollecAcc" ReadOnly />
                                <asp:BoundField DataField="CollecName" HeaderText="收款人戶名" SortExpression="CollecName" ReadOnly />
                                <asp:BoundField DataField="CollectId" HeaderText="身分證字號(統編)" SortExpression="CollectId" ReadOnly />
                                <asp:BoundField DataField="RemitPrice" HeaderText="帳款金額" SortExpression="RemitPrice" />
                                <asp:BoundField DataField="RemitMemo" HeaderText="備註" SortExpression="RemitMemo" />
                                <asp:TemplateField HeaderText="帳款是否有手續費" SortExpression="IfFee">
                                    <EditItemTemplate>
                                        <asp:Label ID="Label1" runat="server" Text='<%# Bind("IfFee") %>' Visible="false"></asp:Label>
                                        <asp:RadioButtonList ID="RadioButtonList1" runat="server" RepeatDirection="Horizontal">
                                            <asp:ListItem Value="是">是</asp:ListItem>
                                            <asp:ListItem Value="否">否</asp:ListItem>
                                        </asp:RadioButtonList>
                                    </EditItemTemplate>
                                    <ItemTemplate>
                                        <asp:Label ID="Label1" runat="server" Text='<%# Bind("IfFee") %>' Visible="false"></asp:Label>
                                        <asp:RadioButtonList ID="RadioButtonList1" runat="server" RepeatDirection="Horizontal">
                                            <asp:ListItem Value="是">是</asp:ListItem>
                                            <asp:ListItem Value="否">否</asp:ListItem>
                                        </asp:RadioButtonList>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                            <EditRowStyle BackColor="Aqua" />
                            <FooterStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
                            <HeaderStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
                            <PagerStyle BackColor="#2461BF" ForeColor="White" HorizontalAlign="Center" />
                            <RowStyle BackColor="#EFF3FB" />
                            <SelectedRowStyle BackColor="#D1DDF1" Font-Bold="True" ForeColor="#333333" />
                            <SortedAscendingCellStyle BackColor="#F5F7FB" />
                            <SortedAscendingHeaderStyle BackColor="#6D95E1" />
                            <SortedDescendingCellStyle BackColor="#E9EBEF" />
                            <SortedDescendingHeaderStyle BackColor="#4870BE" />
                        </asp:GridView>
                        <asp:Label ID="Label2" runat="server" Text=""></asp:Label>
                        <br />
                        <table width="600px">
                            <tr>
                                <td><font color='red' size="5">*</font>彙整單用途說明</td>
                                <td>
                                    <asp:TextBox ID="TextBox1" runat="server" TextMode="MultiLine" Height="50" Width="450px"></asp:TextBox><br /></td>
                            </tr>
                        </table>
                        <asp:Button ID="Button1" runat="server" Text="整筆彙整單送出申請" BackColor="#ff9900" Height="34px" OnClick="Button1_Click" />
                        <asp:SqlDataSource ID="SqlDataSource2" runat="server" ConnectionString="<%$ ConnectionStrings:ComRemitConnectionString %>" SelectCommand="SELECT [sno], [CollectNo], [CollecAcc], [CollecName], [CollectId],[RemitPrice],[IfFee],[RemitMemo] FROM [RemitedList] WHERE ConSno=@ConSno order by sno desc" UpdateCommand="UPDATE [RemitedList] SET [RemitPrice] = @RemitPrice,  [IfFee] = @IfFee,[RemitMemo] = @RemitMemo WHERE [sno] = @sno and ConPer= @ConPer" DeleteCommand="DELETE FROM [RemitedList] WHERE [sno] = @sno and ConPer= @ConPer" InsertCommand="INSERT INTO [RemitedList] ([CollectNo], [CollecAcc], [CollecName], [CollectId],[RemitPrice],[IfFee],[RemitMemo]) VALUES (@CollectNo, @CollecAcc, @CollecName, @CollectId, @Tel, @Zipcode, @Addr, @IfFee, @RemitMemo)">
                            <DeleteParameters>
                                <asp:Parameter Name="sno" Type="Int32" />
                                <asp:SessionParameter Name="ConPer" SessionField="account" Type="String" />
                            </DeleteParameters>
                            <InsertParameters>
                                <asp:Parameter Name="CollectNo" Type="String" />
                                <asp:Parameter Name="CollecAcc" Type="String" />
                                <asp:Parameter Name="CollecName" Type="String" />
                                <asp:Parameter Name="CollectId" Type="String" />
                                <asp:Parameter Name="RemitPrice" Type="String" />
                                <asp:Parameter Name="IfFee" Type="String" />
                                <asp:Parameter Name="RemitMemo" Type="String" />
                            </InsertParameters>
                            <SelectParameters>
                                <asp:ControlParameter Name="ConSno" ControlID="Num" Type="String" />
                            </SelectParameters>
                            <UpdateParameters>
                                <asp:Parameter Name="RemitPrice" Type="String" />
                                <asp:Parameter Name="IfFee" Type="String" />
                                <asp:Parameter Name="RemitMemo" Type="String" />
                                <asp:Parameter Name="sno" Type="Int32" />
                                <asp:SessionParameter Name="ConPer" SessionField="account" Type="String" />
                            </UpdateParameters>
                        </asp:SqlDataSource>

                        <br />
                        【備註】<br />
                        <font color="red"><b>一、本匯款欄位(收款人金融局號)(含代碼及名稱)由本府財政稅務局庫款支付科維護,如有疑問或資料不足時請逕洽分機2813~2816庫款支付科查詢。<br />
                        二、依據「宜蘭縣縣庫集中支付作業程序」第二十一點(十七)項、三十點(十五)項規定，如係退休金(撫卹金)應加註核准文號及生效日期，如係考績(核)獎金、動支災害準備金應加註核定文號；墊付款應加註核准文號。<br />
                        三、檢附「<a href="http://intra.e-land.gov.tw/ComRemit/Docs/台灣銀行宜蘭分行扣收匯款手續費30元概括原則參考表.odt">台灣銀行宜蘭分行扣收匯款手續費30元審核原則</a>」、「<a href="http://intra.e-land.gov.tw/ComRemit/Docs/台灣銀行代庫免收通匯手續費項目表.odt">台灣銀行代庫免收通匯手續費項目表</a>」，謹供本府同仁參考。</b></font></td>
                </tr>
            </table>

        </div>
    </form>

</body>
</html>
