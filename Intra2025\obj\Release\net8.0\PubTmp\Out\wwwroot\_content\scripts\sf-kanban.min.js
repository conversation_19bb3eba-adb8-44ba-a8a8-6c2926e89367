/*!*  filename: sf-kanban.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[33],{"./bundles/sf-kanban.js":function(e,t,n){"use strict";n.r(t);n("./modules/sf-kanban.js")},"./modules/sf-kanban.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Kanban=function(){"use strict";var e="e-kanban-content",t="e-show-add-button",n=function(){function n(e){this.parent=e,this.dragObj={element:null,cloneElement:null,instance:null,targetClone:null,draggedClone:null,targetCloneMulti:null,selectedCards:[],pageX:0,pageY:0,navigationInterval:null,cardDetails:[],modifiedData:[]},this.isDragging=!1,this.dragEdges={left:!1,right:!1,top:!1,bottom:!1},this.isExternalDrop=!1}return n.prototype.wireDragEvents=function(t){var n;null!=!this.parent.element&&this.parent.externalDropId&&0===this.parent.externalDropId.length&&(n=this.parent.element.querySelector("."+e)),this.dragObj.instance=new sf.base.Draggable(t,{clone:!0,enableTapHold:this.parent.isAdaptive,enableTailMode:!0,cursorAt:{top:-10,left:-10},dragArea:n,dragStart:this.dragStart.bind(this),drag:this.drag.bind(this),dragStop:this.dragStop.bind(this),enableAutoScroll:!1,helper:this.dragHelper.bind(this)})},n.prototype.dragHelper=function(e){if(this.dragObj.element=sf.base.closest(e.sender.target,".e-card"),sf.base.isNullOrUndefined(this.dragObj.element))return null;this.dragObj.element.style.width=sf.base.formatUnit(this.dragObj.element.offsetWidth);var t=this.dragObj.element.cloneNode(!0);return this.dragObj.cloneElement=t,sf.base.addClass([this.dragObj.cloneElement],"e-cloned-card"),this.dragObj.element.parentElement.appendChild(this.dragObj.cloneElement),this.dragObj.targetCloneMulti=sf.base.createElement("div",{className:"e-target-multi-clone"}),this.dragObj.targetClone=sf.base.createElement("div",{className:"e-target-dropped-clone",styles:"width:100%;height:"+sf.base.formatUnit(this.dragObj.element.offsetHeight)}),this.dragObj.modifiedData=[],this.dragObj.cloneElement},n.prototype.dragStart=function(e){var t=this;if(this.dragObj.selectedCards=this.dragObj.element,this.dragObj.element.classList.contains("e-selection")){var n=sf.base.closest(this.dragObj.element,".e-content-row");this.dragObj.selectedCards=[].slice.call(n.querySelectorAll(".e-card.e-selection:not(.e-cloned-card)"))}var s=[];if(this.dragObj.selectedCards instanceof HTMLElement){var i=this.dragObj.selectedCards.getAttribute("data-id");s.push(i)}else this.dragObj.selectedCards&&this.dragObj.selectedCards.length>0&&this.dragObj.selectedCards.forEach((function(e){s.push(e.getAttribute("data-id"))}));var o=this.getXYValue(e,"X"),l=this.getXYValue(e,"Y");this.dragObj.cloneElement.style.display="none",this.parent.dotNetRef.invokeMethodAsync("DragStart",s,o,l).then((function(n){var s=document.body.querySelector(".e-kanban-dialog");if(n.cancel||s&&s.classList.contains("e-popup-open"))return t.removeElement(t.dragObj.cloneElement),t.dragObj.instance.intDestroy(e),t.dragObj.element=null,t.dragObj.targetClone=null,t.dragObj.draggedClone=null,t.dragObj.cloneElement=null,void(t.dragObj.targetCloneMulti=null);if(t.dragObj.cloneElement.style.display="block",e.bindEvents(e.dragElement),t.dragObj.element.classList.contains("e-selection")){if(t.dragObj.selectedCards.forEach((function(e){t.draggedClone(e)})),t.dragObj.selectedCards.length>1){t.dragObj.cloneElement.innerHTML="";var i=sf.base.createElement("div",{className:"e-multi-card-text",innerHTML:t.dragObj.selectedCards.length+" Cards"});t.dragObj.cloneElement.appendChild(i),sf.base.classList(t.dragObj.cloneElement,["e-multi-card-clone"],["e-selection"]),t.dragObj.cloneElement.style.width="90px"}}else t.draggedClone(t.dragObj.element);sf.base.EventHandler.add(document.body,"keydown",t.keydownHandler,t)}))},n.prototype.drag=function(e){var t=this;if(e.target){var n=sf.base.closest(e.target,".e-card"),s=e.target;sf.base.isNullOrUndefined(e.target.parentElement)||("SPAN"===e.target.nodeName&&e.target.classList.contains("e-empty-card")||"DIV"===e.target.nodeName&&e.target.classList.contains("e-kanban-border")&&this.parent.element.querySelector(".e-target-dropped-clone")!==e.target.nextElementSibling.firstChild)&&(s=e.target.parentElement);var i=n||s,o=sf.base.closest(i,".e-content-row:not(.e-swimlane-row) .e-content-cells.e-droppable");if(this.externalDrop(i,"drag"),this.target=this.parent.isExternalKanbanDrop?this.externalTarget:this.parent.element,this.calculateArgs(e),o&&"not-allowed"!==document.body.style.cursor){var l=this.getColumnKey(o),r=l.split(",");this.multiCloneRemove();var a=l===this.getColumnKey(sf.base.closest(this.dragObj.draggedClone,".e-content-cells"));if(1===r.length||a)if(i.classList.contains("e-card")||i.classList.contains("e-target-dragged-clone")){var c=i.classList.contains("e-target-dragged-clone")&&i.previousElementSibling.classList.contains("e-kanban-dragged-card")?null:i.previousElementSibling,d="afterend";if(sf.base.isNullOrUndefined(c)){var h=i.classList.contains("e-target-dragged-clone")?this.dragObj.pageY/2:this.dragObj.pageY,u=i.classList.contains("e-target-dragged-clone")?i.offsetHeight:i.offsetHeight/2;h-(this.target.getBoundingClientRect().top+i.offsetTop)<u&&(d="beforebegin")}i.insertAdjacentElement(d,this.dragObj.targetClone)}else i.classList.contains("e-content-cells")&&!sf.base.closest(i,".e-swimlane-row")?i.querySelector(".e-card-container").appendChild(this.dragObj.targetClone):i.classList.contains("e-card-container")&&!sf.base.closest(i,".e-swimlane-row")&&0===o.querySelectorAll(".e-card").length&&i.appendChild(this.dragObj.targetClone);else r.length>1&&o.classList.contains("e-dropping")&&this.multiCloneCreate(r,o)}0===this.target.querySelectorAll(".e-dropping").length&&this.cellDropping();var p=sf.base.closest(i,".e-column-key");if(p){var g=[].slice.call(this.target.querySelectorAll(".e-column-key:not(.e-disabled)")).filter((function(e){return t.getColumnKey(e)===t.getColumnKey(p)}));g.length>0&&(sf.base.addClass(g,"e-multi-active"),g[0].previousElementSibling&&sf.base.addClass([g[0].previousElementSibling],"e-multi-bottom-border"))}document.body.style.cursor=o&&o.classList.contains("e-dropping")?"":"not-allowed",n&&!sf.base.closest(n,".e-content-cells").classList.contains("e-dropping")&&(n.style.cursor="not-allowed",document.body.style.cursor="not-allowed"),this.isExternalDrop&&this.removeElement(this.dragObj.targetClone),this.isExternalDrop&&"not-allowed"===document.body.style.cursor&&(document.body.style.cursor=""),"not-allowed"===document.body.style.cursor&&(this.removeElement(this.dragObj.targetClone,this.target),this.multiCloneRemove()),this.updateScrollPosition(),this.parent.isExternalKanbanDrop=!1,this.isExternalDrop=!1}},n.prototype.dragStop=function(e){var t,n=sf.base.closest(this.dragObj.targetClone,".e-content-cells"),s=0;if(sf.base.EventHandler.remove(document.body,"keydown",this.keydownHandler),this.externalDrop(e.target,"dragStop"),this.dragObj.targetClone.parentElement){s=[].slice.call(this.dragObj.targetClone.parentElement.querySelectorAll(".e-card:not(.e-kanban-dragged-card),.e-target-dropped-clone")).indexOf(this.dragObj.targetClone)}this.target&&this.target.querySelector(".e-target-multi-clone")&&(t=sf.base.closest(e.target,".e-column-key:not(.e-disabled)"));var i=this.getXYValue(e,"X"),o=this.getXYValue(e,"Y"),l=[];if(this.dragObj.selectedCards instanceof HTMLElement){var r=this.dragObj.selectedCards.getAttribute("data-id");l.push(r)}else this.dragObj.selectedCards&&this.dragObj.selectedCards.length>0&&this.dragObj.selectedCards.forEach((function(e){l.push(e.getAttribute("data-id"))}));if(n||t){var a=void 0;n?a=this.getColumnKey(n):(a=this.getColumnKey(t),n=sf.base.closest(t,".e-content-cells"));var c=void 0;if(this.parent.swimlaneSettings.keyField&&this.parent.swimlaneSettings.allowDragAndDrop){var d=sf.base.closest(n,".e-content-row").previousElementSibling;c=this.getColumnKey(d)}var h=!1,u=void 0,p="";this.parent.sortSettings.field&&"Index"===this.parent.sortSettings.sortBy&&(!(u="Ascending"===this.parent.sortSettings.direction?e.helper===this.dragObj.targetClone.previousElementSibling&&this.dragObj.targetClone.previousElementSibling&&this.dragObj.targetClone.previousElementSibling.previousElementSibling?this.dragObj.targetClone.previousElementSibling.previousElementSibling:this.dragObj.targetClone.previousElementSibling:this.dragObj.targetClone.nextElementSibling)||u.classList.contains("e-kanban-dragged-card")||u.classList.contains("e-cloned-card")||u.classList.contains("e-target-dragged-clone")?this.dragObj.targetClone.nextElementSibling&&"Ascending"===this.parent.sortSettings.direction?u=this.dragObj.targetClone.nextElementSibling:this.dragObj.targetClone.previousElementSibling&&"Descending"===this.parent.sortSettings.direction&&(u=this.dragObj.targetClone.previousElementSibling):h=!0),u&&(p=u.getAttribute("data-id")),this.parent.dotNetRef.invokeMethodAsync("DragStop",l,a,c,s,h,p,this.parent.isExternalKanbanDrop,i,o)}else this.isExternalDrop&&this.parent.dotNetRef.invokeMethodAsync("DragStop",l,"","",0,!1,"",!0,i,o);this.dragStopClear()},n.prototype.dragStopClear=function(){this.removeElement(this.dragObj.draggedClone),this.removeElement(this.dragObj.targetClone,this.target),this.removeElement(this.dragObj.cloneElement),[].slice.call(this.parent.element.querySelectorAll(".e-target-dragged-clone")).forEach((function(e){return sf.base.remove(e)})),this.dragObj.element.style.removeProperty("width"),this.multiCloneRemove(),this.dragObj.selectedCards instanceof HTMLElement?sf.base.removeClass([this.dragObj.selectedCards],"e-kanban-dragged-card"):sf.base.removeClass(this.dragObj.selectedCards,"e-kanban-dragged-card"),clearInterval(this.dragObj.navigationInterval),this.dragObj.navigationInterval=null,"not-allowed"===document.body.style.cursor&&(document.body.style.cursor=""),[].slice.call(this.parent.element.querySelectorAll(".e-card[style]")).forEach((function(e){e.style.cursor=""}));[].slice.call(this.parent.element.querySelectorAll(".e-content-row:not(.e-swimlane-row) .e-content-cells")).forEach((function(e){return sf.base.removeClass([e],"e-dropping")})),this.parent.isAdaptive&&(this.parent.touchModule.tabHold=!1),this.dragObj.cardDetails=this.dragObj.modifiedData=[],this.isDragging=!1,this.parent.isExternalKanbanDrop=!1,this.target=null,this.externalTarget=null},n.prototype.keydownHandler=function(e){"Escape"===e.code&&this.dragObj.cloneElement&&(sf.base.EventHandler.remove(this.dragObj.cloneElement,"keydown",this.keydownHandler),this.dragObj.element.removeAttribute("aria-grabbed"),this.dragStopClear())},n.prototype.draggedClone=function(e){this.dragObj.draggedClone=sf.base.createElement("div",{className:"e-target-dragged-clone",styles:"width:"+sf.base.formatUnit(e.offsetWidth-1)+";height:"+sf.base.formatUnit(e.offsetHeight)}),e.insertAdjacentElement("afterend",this.dragObj.draggedClone),sf.base.addClass([e],"e-kanban-dragged-card")},n.prototype.calculateArgs=function(e){var t=this.getPageCoordinates(e);this.dragObj.pageY=t.pageY,this.dragObj.pageX=t.pageX,this.isDragging=!0},n.prototype.getPageCoordinates=function(e){var t=e.event;return t&&t.changedTouches?t.changedTouches[0]:e.changedTouches?e.changedTouches[0]:t||e},n.prototype.getColumnKey=function(e){return e&&e.getAttribute("data-key")?e.getAttribute("data-key").trim():""},n.prototype.multiCloneRemove=function(){if(this.target){var e=[].slice.call(this.target.querySelectorAll(".e-target-multi-clone"));if(e.length>0)[].slice.call(this.target.querySelectorAll(".e-column-key")).forEach((function(e){return sf.base.remove(e)})),e.forEach((function(e){var n=sf.base.closest(e,".e-content-cells");n&&(n.style.borderStyle="",n.querySelector("."+t)&&sf.base.removeClass([n.querySelector("."+t)],"e-multi-card-container"),sf.base.removeClass([n.querySelector(".e-card-container")],"e-multi-card-container"))})),this.removeElement(this.dragObj.targetCloneMulti,this.target)}},n.prototype.removeElement=function(e,t){t=t||this.parent.element,e&&t&&t.getElementsByClassName(e.className).length>0&&sf.base.remove(e)},n.prototype.externalDrop=function(e,t){var n=this;this.parent.externalDropId&&this.parent.externalDropId.forEach((function(s){if(n.externalTarget=sf.base.closest(e,s),n.externalTarget)if(n.externalTarget.classList.contains("e-kanban")){n.parent.isExternalKanbanDrop=!0;var i=[].slice.call(n.externalTarget.querySelectorAll(".e-content-row:not(.e-swimlane-row):not(.e-collapsed) .e-content-cells"));"drag"===t?sf.base.addClass(i,"e-dropping"):sf.base.removeClass(i,"e-dropping")}else n.isExternalDrop=!0}))},n.prototype.multiCloneCreate=function(e,n){var s=n.offsetHeight,i=n.querySelector(".e-limits");i&&(s-=i.offsetHeight),this.dragObj.targetCloneMulti.style.height=sf.base.formatUnit(s),n.querySelector("."+t)&&sf.base.addClass([n.querySelector("."+t)],"e-multi-card-container"),sf.base.addClass([n.querySelector(".e-card-container")],"e-multi-card-container"),n.querySelector(".e-card-container").style.height="auto",n.style.borderStyle="none",this.removeElement(this.dragObj.targetClone);for(var o=0,l=e;o<l.length;o++){var r=l[o],a=sf.base.closest(this.dragObj.draggedClone,".e-content-cells"),c=this.parent.transition[a.cellIndex].transitionColumn,d=this.allowedTransition(this.dragObj.element.getAttribute("data-key"),r,c),h=d?"":" e-disabled",u=sf.base.createElement("div",{className:"e-column-key"+h,attrs:{"data-key":r.trim()}}),p=sf.base.createElement("div",{className:"e-text",innerHTML:r.trim()});n.appendChild(this.dragObj.targetCloneMulti).appendChild(u).appendChild(p),u.style.cursor=d?"":"not-allowed",u.style.lineHeight=u.style.height=sf.base.formatUnit(s/e.length),p.style.top=sf.base.formatUnit(s/2-p.offsetHeight/2)}},n.prototype.allowedTransition=function(e,t,n){for(var s=!0,i=t.split(","),o=0;o<i.length;o++){if(e===i[o].trim())return!0;if(n){if(1===n.length&&0===n[0].length)return!0;for(var l=0;l<n.length;l++){if(i[o].trim()===n[l].trim())return!0;s=!1}}}return s},n.prototype.cellDropping=function(){var e=this,t=sf.base.closest(this.dragObj.draggedClone,".e-content-cells"),n=sf.base.closest(this.dragObj.draggedClone,".e-content-row");if(this.addDropping(n,t),t&&t.classList.contains("e-drop")&&sf.base.addClass([t],"e-dropping"),this.parent.swimlaneSettings.keyField&&this.parent.swimlaneSettings.allowDragAndDrop){var s=[].slice.call(this.target.querySelectorAll(".e-content-row:not(.e-swimlane-row):not(.e-collapsed)"));[].slice.call(s).forEach((function(s){n!==s&&e.addDropping(s,t)}))}},n.prototype.addDropping=function(e,t){var n=this;t&&e&&[].slice.call(e.children).forEach((function(e){var s=n.parent.transition[t.cellIndex].transitionColumn;e!==t&&e.classList.contains("e-drop")&&n.allowedTransition(t.getAttribute("data-key"),e.getAttribute("data-key"),s)&&sf.base.addClass([e],"e-dropping")}))},n.prototype.updateScrollPosition=function(){var e=this;sf.base.isNullOrUndefined(this.dragObj.navigationInterval)&&(this.dragObj.navigationInterval=window.setInterval((function(){e.autoScroll()}),100))},n.prototype.autoScrollValidation=function(){var t=this.dragObj.pageY,n=this.dragObj.pageX,s={left:!1,right:!1,top:!1,bottom:!1},i=this.target.querySelector("."+e).getBoundingClientRect();t<i.top+30+window.pageYOffset&&t>i.top+window.pageYOffset&&(s.top=!0),t>i.bottom-30+window.pageYOffset&&t<i.bottom+window.pageYOffset&&(s.bottom=!0),n<i.left+30+window.pageXOffset&&n>i.left+window.pageXOffset&&(s.left=!0),n>i.right-30+window.pageXOffset&&n<i.right+window.pageXOffset&&(s.right=!0),this.dragEdges=s},n.prototype.autoScroll=function(){this.autoScrollValidation();if(this.parent.isAdaptive){var t;if(this.dragEdges.top||this.dragEdges.bottom?t=this.dragObj.targetClone?sf.base.closest(this.dragObj.targetClone,".e-card-container"):sf.base.closest(this.dragObj.draggedClone,".e-card-container"):(this.dragEdges.right||this.dragEdges.left)&&(t=this.target.querySelector("."+e)),t){var n=t.offsetHeight<=t.scrollHeight,s=t.offsetWidth<=t.scrollWidth,i=t.scrollTop>=0&&t.scrollTop+t.offsetHeight<=t.scrollHeight,o=t.scrollLeft>=0&&t.scrollLeft+t.offsetWidth<=t.scrollWidth;if(n&&i&&(this.dragEdges.top||this.dragEdges.bottom)&&(t.scrollTop+=this.dragEdges.top?-66:30),s&&o&&(this.dragEdges.left||this.dragEdges.right))(this.getWidth()*(this.target.querySelector(".e-content-row:not(.e-swimlane-row)").childElementCount-1)>t.scrollLeft||this.dragEdges.left)&&(t.scrollLeft+=this.dragEdges.left?-30:30)}}else{var l=this.target.querySelector("."+e),r=this.dragObj.targetClone.parentElement,a=l.offsetHeight<=l.scrollHeight,c=l.offsetWidth<=l.scrollWidth,d=a&&l.scrollTop>=0&&l.scrollTop+l.offsetHeight<=l.scrollHeight,h=c&&l.scrollLeft>=0&&l.scrollLeft+l.offsetWidth<=l.scrollWidth;d&&(this.dragEdges.top||this.dragEdges.bottom)&&(l.scrollTop+=this.dragEdges.top?-30:30,r&&(r.scrollTop+=this.dragEdges.top?-30:30)),h&&(this.dragEdges.left||this.dragEdges.right)&&(l.scrollLeft+=this.dragEdges.left?-30:30,r&&(r.scrollLeft+=this.dragEdges.left?-30:30)),this.dragObj.pageY-window.scrollY<30?window.scrollTo(window.scrollX,window.scrollY-30):window.innerHeight-(this.dragObj.pageY-window.scrollY)<30&&window.scrollTo(window.scrollX,window.scrollY+30)}},n.prototype.getWidth=function(){return 80*window.innerWidth/100},n.prototype.unWireDragEvents=function(){this.dragObj.instance&&!this.dragObj.instance.isDestroyed&&this.dragObj.instance.destroy()},n.prototype.destroy=function(){this.unWireDragEvents()},n.prototype.getXYValue=function(e,t){var n,s=e.event,i=s.changedTouches;if(!(n="X"===t?i?i[0].clientX:s.clientX:i?i[0].clientY:s.clientY)&&"focus"===e.type&&e.target){var o=e.target.getBoundingClientRect();n=o?"X"===t?o.left:o.top:null}return Math.ceil(n)},n}(),s=function(){function e(e){this.keyConfigs={firstCardSelection:"36",lastCardSelection:"35",upArrow:"38",downArrow:"40",rightArrow:"39",leftArrow:"37",multiSelectionEnter:"ctrl+13",multiSelectionSpace:"ctrl+32",multiSelectionByUpArrow:"shift+38",multiSelectionByDownArrow:"shift+40",shiftTab:"shift+tab",enter:"13",tab:"tab",delete:"46",escape:"27",space:"32"},this.parent=e,this.parent.element.tabIndex=-1===this.parent.element.tabIndex?0:this.parent.element.tabIndex,this.keyboardModule=new sf.base.KeyboardEvents(this.parent.element,{keyAction:this.keyActionHandler.bind(this),keyConfigs:this.keyConfigs,eventName:"keydown"}),this.multiSelection=!1}return e.prototype.keyActionHandler=function(e){var n,s=this.parent.element.querySelectorAll(".e-card.e-selection").item(0);if(s||sf.base.closest(document.activeElement,".e-kanban"))switch(e.action){case"upArrow":case"downArrow":case"multiSelectionByUpArrow":case"multiSelectionByDownArrow":e.preventDefault(),this.processUpDownArrow(e.action,s);break;case"rightArrow":case"leftArrow":this.processLeftRightArrow(e);break;case"firstCardSelection":case"lastCardSelection":this.processCardSelection(e.action,s);break;case"multiSelectionEnter":case"multiSelectionSpace":document.activeElement&&this.parent.cardSelection(document.activeElement,!0,!1);break;case"space":case"enter":this.processEnter(e,s);break;case"escape":if(document.activeElement.classList.contains("e-card")||document.activeElement.classList.contains(t))if(document.activeElement.classList.contains("e-selection"))sf.base.removeClass([document.activeElement],"e-selection"),document.activeElement.focus();else{var i=sf.base.closest(document.activeElement,".e-content-cells"),o=[].slice.call(i.querySelectorAll(".e-card"));sf.base.removeClass(o,"e-selection"),i.focus(),this.cardTabIndexRemove(),this.addRemoveTabIndex("Add")}break;case"tab":case"shiftTab":n=sf.base.closest(document.activeElement,".e-content-cells"),document.activeElement.classList.contains("e-card")&&(document.activeElement.nextElementSibling||"tab"!==e.action||e.preventDefault(),!document.activeElement.previousElementSibling&&n.querySelector("."+t)&&"tab"===e.action&&sf.base.addClass([n.querySelector("."+t)],"e-show-add-focus")),document.activeElement.classList.contains(t)&&(!n.querySelector(".e-card")&&"tab"===e.action||"shiftTab"===e.action)&&e.preventDefault(),document.activeElement.classList.contains("e-kanban")&&(this.cardTabIndexRemove(),this.addRemoveTabIndex("Add"))}},e.prototype.processCardSelection=function(e,t){if(t){sf.base.removeClass([t],"e-selection"),this.parent.disableAttributeSelection(t);var n=this.parent.selectionArray;n.splice(n.indexOf(t.getAttribute("data-id")),1)}this.cardTabIndexRemove();var s=[].slice.call(this.parent.element.querySelectorAll(".e-card")),i="firstCardSelection"===e?s[0]:s[s.length-1];this.parent.cardSelection(i,!1,!1),this.addRemoveTabIndex("Remove"),i.focus(),[].slice.call(sf.base.closest(i,".e-content-cells").querySelectorAll(".e-card")).forEach((function(e){e.setAttribute("tabindex","0")}))},e.prototype.processLeftRightArrow=function(e){document.activeElement.classList.contains("e-content-cells")&&("rightArrow"===e.action&&document.activeElement.nextElementSibling?document.activeElement.nextElementSibling.focus():"leftArrow"===e.action&&document.activeElement.previousElementSibling&&document.activeElement.previousElementSibling.focus())},e.prototype.processUpDownArrow=function(e,n){if("upArrow"===e&&document.activeElement){if(document.activeElement.classList.contains("e-card")&&document.activeElement.previousElementSibling)document.activeElement.previousElementSibling.focus();else if(document.activeElement.classList.contains(t)){document.activeElement.setAttribute("tabindex","-1"),sf.base.removeClass([document.activeElement],"e-show-add-focus");var s=sf.base.closest(document.activeElement,".e-content-cells");s.querySelectorAll(".e-card").length>0&&[].slice.call(s.querySelectorAll(".e-card")).slice(-1)[0].focus()}this.removeSelection()}else if("downArrow"===e&&document.activeElement&&document.activeElement.classList.contains("e-card")){if(document.activeElement.nextElementSibling)document.activeElement.nextElementSibling.focus();else if(sf.base.closest(document.activeElement,".e-card-container").nextElementSibling){var i=sf.base.closest(document.activeElement,".e-card-container").nextElementSibling;i.setAttribute("tabindex","0"),sf.base.addClass([i],"e-show-add-focus"),i.focus()}this.removeSelection()}if(("multiSelectionByUpArrow"===e||"multiSelectionByDownArrow"===e)&&n&&"Multiple"===this.parent.cardSettings.selectionType){var o=void 0;(o="multiSelectionByUpArrow"===e?document.activeElement.previousElementSibling:document.activeElement.nextElementSibling)&&(this.parent.cardSelection(o,!1,!0),o.focus(),this.multiSelection=!0)}},e.prototype.removeSelection=function(){if(this.multiSelection){var e=this.parent.getSelectedCards();e.length>0&&(sf.base.removeClass(e,"e-selection"),this.parent.disableAttributeSelection(e)),this.multiSelection=!1}},e.prototype.cardTabIndexRemove=function(){[].slice.call(this.parent.element.querySelectorAll(".e-card")).forEach((function(e){e.setAttribute("tabindex","-1")})),[].slice.call(this.parent.element.querySelectorAll("."+t)).forEach((function(e){e.setAttribute("tabindex","-1"),sf.base.removeClass([e],"e-show-add-focus")}))},e.prototype.processEnter=function(e,n){"space"===e.action&&e.preventDefault();var s=e.target;if(s.classList.contains("e-header-icon")&&s.click(),(s.classList.contains("e-swimlane-row-expand")||s.classList.contains("e-swimlane-row-collapse"))&&s.click(),document.activeElement.classList.contains("e-card")&&this.parent.cardSelection(document.activeElement,!1,!1),document.activeElement.classList.contains(t)&&document.activeElement.focus(),s.classList.contains("e-content-cells")){var i=[].slice.call(s.querySelectorAll(".e-card"));this.addRemoveTabIndex("Remove"),i.length>0&&(s.querySelector(".e-card").focus(),i.forEach((function(e){e.setAttribute("tabindex","0")}))),s.querySelector("."+t)&&(s.querySelector("."+t).setAttribute("tabindex","0"),s.querySelector("."+t).focus())}n===document.activeElement&&1===this.parent.element.querySelectorAll(".e-selection").length&&(this.parent.dotNetRef.invokeMethodAsync("OpenDialog",n.getAttribute("data-id")),n.focus())},e.prototype.addRemoveTabIndex=function(e){var t="Add"===e?"0":"-1",n=[].slice.call(this.parent.element.querySelectorAll(".e-header-icon"));n.length>0&&n.forEach((function(e){e.setAttribute("tabindex",t)}));var s=[].slice.call(this.parent.element.querySelectorAll(".e-swimlane-row-expand"));s.length>0&&s.forEach((function(e){e.setAttribute("tabindex",t)}));[].slice.call(this.parent.element.querySelectorAll(".e-content-row:not(.e-swimlane-row) .e-content-cells")).forEach((function(e){e.setAttribute("tabindex",t)}))},e.prototype.destroy=function(){this.keyboardModule.destroy()},e}(),i=function(){function t(e){this.parent=e,this.tabHold=!1}return t.prototype.wireTouchEvents=function(){this.element=this.parent.element.querySelector("."+e),this.touchObj=new sf.base.Touch(this.element,{tapHold:this.tapHoldHandler.bind(this)})},t.prototype.tapHoldHandler=function(e){this.tabHold=!0,sf.base.closest(e.originalEvent.target,".e-card")&&"Multiple"===this.parent.cardSettings.selectionType&&(this.mobilePopup||(this.renderMobilePopup(),this.mobilePopup.show()),this.updatePopupContent())},t.prototype.renderMobilePopup=function(){if("Multiple"===this.parent.cardSettings.selectionType){var e=sf.base.createElement("div",{className:"e-mobile-popup-wrapper e-popup-close",innerHTML:'<div class="e-popup-header"><button class="e-btn e-close e-flat e-round e-small"><span class="e-icons e-close-icon"></span></button></div><div class="e-popup-content"></div>'});document.body.appendChild(e),sf.base.addClass([e],"e-device"),this.mobilePopup=new sf.popups.Popup(e,{targetType:"container",enableRtl:this.parent.enableRtl,hideAnimation:{name:"ZoomOut"},showAnimation:{name:"ZoomIn"},collision:{X:"fit",Y:"fit"},position:{X:"left",Y:"top"},viewPortElement:document.body,zIndex:1004,close:this.popupClose.bind(this)});var t=this.mobilePopup.element.querySelector(".e-close");sf.base.EventHandler.add(t,"click",this.closeClick,this)}},t.prototype.closeClick=function(){this.mobilePopup.hide()},t.prototype.popupClose=function(){this.popupDestroy()},t.prototype.getPopupContent=function(){var e,t=this.parent.getSelectedCards();return t.length>1?e="("+t.length+") Cards Selected":1===t.length&&(e=" "+t[0].getAttribute("data-id")),e},t.prototype.updatePopupContent=function(){if(this.mobilePopup){var e=this.getPopupContent();e?this.mobilePopup.element.querySelector(".e-popup-content").textContent=e:this.mobilePopup.hide()}},t.prototype.popupDestroy=function(){this.mobilePopup&&this.mobilePopup.element&&(this.mobilePopup.destroy(),sf.base.remove(this.mobilePopup.element),this.mobilePopup=null)},t.prototype.unWireTouchEvents=function(){this.touchObj&&this.touchObj.destroy(),this.touchObj=null,this.element=null},t.prototype.destroy=function(){this.popupDestroy(),this.unWireTouchEvents(),this.tabHold=!1},t}(),o=function(){function o(e){window.sfBlazor=window.sfBlazor,this.updateContext(e),this.columnToggleArray=[],this.selectionArray=[],this.lastCardSelection=null,this.lastSelectionRow=null,this.lastCard=null,this.selectedCardsElement=[],this.selectedCardsData=[],this.hideColumnKeys=[],this.frozenOrder=0,this.scrollPosition={content:{},column:{}},this.initializeModules(),this.scrollUiUpdate(),this.wireEvents(),window.sfBlazor&&(sf.base.isNullOrUndefined(window.sfBlazor.instances)&&(window.sfBlazor.instances=[]),window.sfBlazor.instances[this.dataId]=this)}return o.prototype.initializeModules=function(){(this.isAdaptive||sf.base.Browser.isTouch)&&(this.touchModule=new i(this)),this.allowDragAndDrop&&(this.dragAndDropModule=new n(this)),this.allowKeyboard&&(this.keyboardModule=new s(this)),this.scrollPosition.content={left:0,top:0},this.initializeSwimlaneTree()},o.prototype.updateContext=function(e){sf.base.extend(this,this,e)},o.prototype.getSelectedCards=function(){return[].slice.call(this.element.querySelectorAll(".e-card.e-selection"))},o.prototype.documentClick=function(e){if(e.target&&this.element.querySelector(".e-swimlane-resource")&&e.target.classList.contains("e-swimlane-overlay")&&this.element.querySelector(".e-swimlane-resource").classList.contains("e-popup-open")&&(this.treePopup.hide(),sf.base.removeClass([this.popupOverlay],"e-enable")),!sf.base.closest(e.target,".e-kanban")){var t=[].slice.call(this.element.querySelectorAll(".e-card.e-selection"));sf.base.removeClass(t,"e-selection"),this.disableAttributeSelection(t)}},o.prototype.disableAttributeSelection=function(e){e instanceof Element?e.setAttribute("aria-selected","false"):e.forEach((function(e){e.setAttribute("aria-selected","false")}))},o.prototype.wireDragEvent=function(){var e=this;if(this.allowDragAndDrop){var t=[].slice.call(this.element.querySelectorAll(".e-content-cells.e-drag .e-card:not(e-draggable)"));sf.base.addClass(t,"e-droppable"),t.forEach((function(t){return e.dragAndDropModule.wireDragEvents(t)}))}},o.prototype.unWireDragEvent=function(){this.dragAndDropModule.unWireDragEvents()},o.prototype.initializeSwimlaneTree=function(){if(this.swimlaneSettings.keyField&&this.isAdaptive){var t=this.element.querySelector(".e-swimlane-header").offsetHeight,n=window.innerHeight-t;this.popupOverlay=this.element.querySelector(".e-swimlane-content .e-swimlane-overlay"),sf.base.setStyleAttribute(this.element.querySelector(".e-swimlane-overlay"),{height:n+"px"}),sf.base.setStyleAttribute(this.element.querySelector(".e-swimlane-content"),{top:t+"px"});var s=this.element.querySelector(".e-swimlane-resource");sf.base.setStyleAttribute(s,{height:n+"px"});var i={targetType:"relative",actionOnScroll:"none",enableRtl:this.enableRtl,zIndex:10,hideAnimation:{name:"SlideLeftOut",duration:500},showAnimation:{name:"SlideLeftIn",duration:500},viewPortElement:this.element.querySelector("."+e)};this.treePopup=new sf.popups.Popup(s,i)}},o.prototype.cardSelection=function(e,t,n){var s=this;if(e){var i=this.getSelectedCards();if("None"!==this.cardSettings.selectionType){var o=sf.base.closest(e,".e-content-row");if((sf.base.isNullOrUndefined(this.lastSelectionRow)?o.rowIndex:this.lastSelectionRow.rowIndex)!==o.rowIndex&&(t||n)&&"Multiple"===this.cardSettings.selectionType)return;if(0===i.length||t&&"Single"!==this.cardSettings.selectionType||(sf.base.removeClass(i,"e-selection"),this.disableAttributeSelection(i),i.forEach((function(e){s.selectionArray.splice(s.selectionArray.indexOf(e.getAttribute("data-id")),1),s.selectedCardsElement.splice(s.selectedCardsElement.indexOf(e),1)}))),i.length>0&&n&&"Multiple"===this.cardSettings.selectionType){var l=[],r=void 0,a=void 0,c=void 0,d=[].slice.call(o.querySelectorAll(".e-card"));d.forEach((function(e){return l.push(e.getAttribute("data-id"))}));var h=e.getAttribute("data-id"),u=this.lastCard.getAttribute("data-id"),p=a=l.indexOf(h),g=r=l.indexOf(u),f=p>g?"next":"prev";for("prev"===f&&(r=p,a=g),c=r;c<=a;c++){var m=d[c];sf.base.addClass([m],"e-selection"),m.setAttribute("aria-selected","true"),m.setAttribute("tabindex","0"),this.selectionArray.push(m.getAttribute("data-id")),this.selectedCardsElement.push(m),this.lastCardSelection=m,"prev"===f&&(this.lastCardSelection=d[r])}}else if(sf.base.addClass([e],"e-selection"),e.setAttribute("aria-selected","true"),e.setAttribute("tabindex","0"),this.selectionArray.push(e.getAttribute("data-id")),this.selectedCardsElement.push(e),this.lastCard=this.lastCardSelection=e,this.lastSelectionRow=sf.base.closest(e,".e-content-row"),this.lastSelectionRow.previousElementSibling){var b=this.lastSelectionRow.previousElementSibling.querySelector(".e-swimlane-row-expand,.e-swimlane-row-collapse");b&&b.classList.contains("e-swimlane-row-collapse")&&b.click()}}}},o.prototype.scrollUiUpdate=function(){var t=this,n=this.element.querySelector(".e-kanban-header"),s=this.element.querySelector("."+e),i=this.element.offsetHeight-n.offsetHeight;if(this.isAdaptive){i=window.innerHeight-(n.offsetHeight+25);var o=this.element.querySelector(".e-swimlane-header");o&&(i-=o.offsetHeight),[].slice.call(this.element.querySelectorAll(".e-content-cells")).forEach((function(e){var n=e.querySelector(".e-card-container");n.classList.contains("e-multi-card-container")||(n.style.height=sf.base.formatUnit(i),sf.base.EventHandler.add(e,"touchmove",t.onAdaptiveScroll,t))}))}"auto"!==this.height&&"100%"!==this.height&&(s.style.height=sf.base.formatUnit(i)),[].slice.call(n.children).forEach((function(e){var n=0;s.offsetWidth-s.clientWidth>0&&(n=17,s.offsetHeight-s.clientHeight>0&&(e.style.width=sf.base.formatUnit(s.clientWidth))),t.enableRtl?e.style.paddingLeft=sf.base.formatUnit(n):e.style.paddingRight=sf.base.formatUnit(n)}))},o.prototype.onContentScroll=function(e){var t=e.target,n=this.element.querySelector(".e-kanban-header");[].slice.call(n.children).forEach((function(e){e.scrollLeft=t.scrollLeft})),this.scrollPosition.content={left:t.scrollLeft,top:t.scrollTop},!sf.base.isNullOrUndefined(this.swimlaneSettings.keyField)&&this.swimlaneSettings.enableFrozenRows&&this.frozenRows(e)},o.prototype.frozenRows=function(t){var n=this.element.querySelector(".e-swimlane-row"),s=this.element.querySelector(".e-kanban-header"),i=this.element.querySelector("."+e);if(sf.base.isNullOrUndefined(this.frozenSwimlaneRow)){this.frozenSwimlaneRow=sf.base.createElement("div",{className:"e-frozen-swimlane-row"});var o=sf.base.createElement("div",{className:"e-frozen-row"});this.frozenSwimlaneRow.appendChild(o),this.element.insertBefore(this.frozenSwimlaneRow,this.element.firstElementChild),n&&(o.appendChild(n.querySelector(".e-swimlane-header").cloneNode(!0)),sf.base.addClass([o.querySelector(".e-icons")],"e-frozen-icons"),sf.base.setStyleAttribute(this.frozenSwimlaneRow,{height:sf.base.formatUnit(n.getBoundingClientRect().height),width:sf.base.formatUnit(i.querySelector(".e-swimlane").getBoundingClientRect().width),top:sf.base.formatUnit(s.getBoundingClientRect().height.toString())}),sf.base.setStyleAttribute(s,{position:"relative",top:sf.base.formatUnit(-this.frozenSwimlaneRow.getBoundingClientRect().height)}),sf.base.setStyleAttribute(i,{position:"relative",top:sf.base.formatUnit(-this.frozenSwimlaneRow.getBoundingClientRect().height)}))}else{var l=[].slice.call(this.element.querySelectorAll(".e-swimlane-row")),r=l[this.frozenOrder],a=l[this.frozenOrder-1],c=l[this.frozenOrder+1],d=void 0,h=void 0,u=void 0;r&&(d=r.getBoundingClientRect().top+r.getBoundingClientRect().height),a&&(h=a.getBoundingClientRect().top+a.getBoundingClientRect().height),c&&(u=c.getBoundingClientRect().top+c.getBoundingClientRect().height);var p=i.getBoundingClientRect().top+this.frozenSwimlaneRow.getBoundingClientRect().height,g=this.frozenSwimlaneRow.querySelector(".e-frozen-row");u&&p>=u&&this.frozenOrder<l.length-1?(g&&(sf.base.remove(g.querySelector(".e-swimlane-header")),c&&g.appendChild(c.querySelector(".e-swimlane-header").cloneNode(!0)),sf.base.addClass([g.querySelector(".e-icons")],"e-frozen-icons")),++this.frozenOrder):h&&p<d&&p>h&&this.frozenOrder>0&&(g&&(sf.base.remove(g.querySelector(".e-swimlane-header")),a&&g.appendChild(a.querySelector(".e-swimlane-header").cloneNode(!0)),sf.base.addClass([g.querySelector(".e-icons")],"e-frozen-icons")),--this.frozenOrder)}t&&0==t.target.scrollTop&&this.removeFrozenRows()},o.prototype.removeFrozenRows=function(){sf.base.remove(this.frozenSwimlaneRow),this.frozenSwimlaneRow=null;var t=this.element.querySelector(".e-kanban-header"),n=this.element.querySelector("."+e);sf.base.setStyleAttribute(t,{position:"",top:""}),sf.base.setStyleAttribute(n,{position:"",top:""}),this.scrollPosition.content={left:this.scrollPosition.content.left,top:0},n.scrollTop=0},o.prototype.onColumnScroll=function(e){var t=e.target;if(t.offsetParent){var n=t.offsetParent.getAttribute("data-key");this.scrollPosition.column[n]={left:t.scrollLeft,top:t.scrollTop}}},o.prototype.onAdaptiveScroll=function(e){this.touchModule.tabHold&&!this.touchModule.mobilePopup&&e.preventDefault()},o.prototype.updateScrollPosition=function(){var t=this,n=this.element.querySelector("."+e);n&&n.scrollTo(this.scrollPosition.content.left,this.scrollPosition.content.top),[].slice.call(this.element.querySelectorAll(".e-card-container")).forEach((function(e){if(e.offsetParent){var n=t.scrollPosition.column[e.offsetParent.getAttribute("data-key")];n&&e.scrollTo(n.left,n.top)}}))},o.prototype.wireEvents=function(){var t=this,n=this.element.querySelector("."+e);sf.base.EventHandler.add(n,"scroll",this.onContentScroll,this),sf.base.EventHandler.add(document,sf.base.Browser.touchStartEvent,this.documentClick,this),[].slice.call(this.element.querySelectorAll(".e-card-container")).forEach((function(e){e.offsetParent&&(t.scrollPosition.column[e.offsetParent.getAttribute("data-key")]={left:0,top:0}),sf.base.EventHandler.add(e,"scroll",t.onColumnScroll,t)})),this.isAdaptive&&this.touchModule.wireTouchEvents(),this.wireDragEvent()},o.prototype.unWireEvents=function(){var t=this,n=this.element.querySelector("."+e);(sf.base.EventHandler.remove(n,"scroll",this.onContentScroll),sf.base.EventHandler.remove(document,sf.base.Browser.touchStartEvent,this.documentClick),[].slice.call(this.element.querySelectorAll(".e-card-container")).forEach((function(e){sf.base.EventHandler.remove(e,"scroll",t.onColumnScroll)})),this.isAdaptive)&&([].slice.call(this.element.querySelectorAll(".e-content-cells")).forEach((function(e){sf.base.EventHandler.remove(e,"touchmove",t.onAdaptiveScroll)})),this.touchModule.unWireTouchEvents())},o.prototype.onCardClick=function(e,t){if(e){if(e.classList.contains("e-selection"))sf.base.removeClass([e],"e-selection"),this.disableAttributeSelection(e);else{var n=t.ctrlKey;this.isAdaptive&&this.touchModule&&(n=this.touchModule.mobilePopup&&this.touchModule.tabHold||n),this.cardSelection(e,n,t.shiftKey)}this.isAdaptive&&this.touchModule&&this.touchModule.updatePopupContent();var s=sf.base.closest(e,".e-content-cells");if(this.allowKeyboard)[].slice.call(s.querySelectorAll(".e-card")).forEach((function(e){e.setAttribute("tabindex","0")})),this.keyboardModule.addRemoveTabIndex("Remove")}},o.prototype.onMenuClick=function(){this.element.querySelector(".e-swimlane-resource").classList.contains("e-popup-open")?(this.treePopup.hide(),sf.base.removeClass([this.popupOverlay],"e-enable")):(this.treePopup.show(),sf.base.addClass([this.popupOverlay],"e-enable"))},o.prototype.onListViewClick=function(){this.treePopup.hide(),sf.base.removeClass([this.popupOverlay],"e-enable")},o.prototype.onPropertyChanged=function(e){for(var t={},s=0,i=Object.keys(e);s<i.length;s++){t[r=i[s]]=this[r]}this.updateContext(e);for(var o=0,l=Object.keys(e);o<l.length;o++){var r;switch(r=l[o]){case"width":this.setWidth();break;case"height":this.setHeight();break;case"enableRtl":this.enableRtl?sf.base.addClass([this.element],"e-rtl"):sf.base.removeClass([this.element],"e-rtl");break;case"allowDragAndDrop":e[r]?(this.dragAndDropModule=new n(this),this.wireDragEvent()):this.unWireDragEvent()}}},o.prototype.getWidth=function(){return 80*window.innerWidth/100},o.prototype.onToggleColumn=function(e,n){var s=this,i=[].slice.call(this.element.querySelectorAll('.e-kanban-table col[data-key="'+e+'"]')),o=[].slice.call(this.element.querySelectorAll('.e-content-cells[data-key="'+e+'"]')),l=this.element.querySelector('.e-header-cells[data-key="'+e+'"]');n?(sf.base.removeClass(i,"e-collapsed"),this.isAdaptive&&i.forEach((function(e){return e.style.width=sf.base.formatUnit(s.getWidth())})),sf.base.removeClass(o,"e-collapsed"),sf.base.removeClass([l],"e-collapsed"),sf.base.classList(l.querySelector(".e-header-icon"),["e-column-expand"],["e-column-collapse"]),l.setAttribute("aria-expanded","true"),o.forEach((function(e){e.querySelector(".e-collapse-header-text").style.display="none",e.querySelector("."+t)&&(e.querySelector("."+t).style.display=""),e.setAttribute("aria-expanded","true")})),l.querySelector(".e-header-icon").setAttribute("aria-label",e+" Expand")):(sf.base.addClass(i,"e-collapsed"),this.isAdaptive&&i.forEach((function(e){return e.style.width=sf.base.formatUnit(50)})),sf.base.addClass(o,"e-collapsed"),sf.base.addClass([l],"e-collapsed"),sf.base.classList(l.querySelector(".e-header-icon"),["e-column-collapse"],["e-column-expand"]),l.setAttribute("aria-expanded","false"),o.forEach((function(e){e.querySelector(".e-collapse-header-text").style.display="",e.querySelector("."+t)&&(e.querySelector("."+t).style.display="none"),e.setAttribute("aria-expanded","false")})),l.querySelector(".e-header-icon").setAttribute("aria-label",e+" Collapse"))},o.prototype.onSwimlaneProperties=function(e,t,n){this.swimlaneSettings.allowDragAndDrop=e,this.swimlaneSettings.keyField=t,this.swimlaneSettings.enableFrozenRows=n,this.frozenSwimlaneRow&&!this.swimlaneSettings.enableFrozenRows&&this.removeFrozenRows()},o.prototype.setWidth=function(){"100%"===this.width?this.element.style.width="":sf.base.setStyleAttribute(this.element,{width:sf.base.formatUnit(this.width)})},o.prototype.setHeight=function(){sf.base.setStyleAttribute(this.element,{height:sf.base.formatUnit(this.height)})},o.prototype.destroy=function(){this.touchModule&&this.touchModule.destroy(),this.dragAndDropModule&&this.dragAndDropModule.destroy(),this.keyboardModule&&this.keyboardModule.destroy(),this.unWireEvents()},o}();return{initialize:function(e){e.dataId&&new o(e)},isDevice:function(e){e.invokeMethodAsync("WindowWidth",70*window.innerWidth/100)},updateScrollPosition:function(e){e&&window.sfBlazor.instances[e].updateScrollPosition()},wireDragEvents:function(e){e&&window.sfBlazor.instances[e].wireDragEvent()},propertyChanged:function(e,t){e&&window.sfBlazor.instances[e].onPropertyChanged(t)},swimlaneProperties:function(e,t,n,s){e&&window.sfBlazor.instances[e].onSwimlaneProperties(t,n,s)},cardClick:function(e,t,n){window.sfBlazor.instances[e].onCardClick(t,n)},menuClick:function(e){window.sfBlazor.instances[e].onMenuClick()},listViewClick:function(e){window.sfBlazor.instances[e].onListViewClick()},toggleColumn:function(e,t,n){e&&window.sfBlazor.instances[e].onToggleColumn(t,n)},getTargetDetails:function(e,t,n,s,i){if(e&&window.sfBlazor){var o=window.sfBlazor.instances[e].element,l=document.elementFromPoint(t,n),r=void 0,a=null;if("tooltip"===i)return s?(r=sf.base.closest(l,".e-card"),a=JSON.stringify(r.getAttribute("data-id"))):l.classList.contains("e-tooltip-text")&&(a=JSON.stringify(l.innerText)),o.querySelector(".e-cloned-card")&&(a=null),a;if(r=sf.base.closest(l,".e-content-cells")){var c={};c.columnKeyField=r.getAttribute("data-key"),r.parentElement.previousElementSibling&&(c.swimlaneKeyField=r.parentElement.previousElementSibling.getAttribute("data-key"));var d=sf.base.closest(l,".e-card");if(d){var h=[].slice.call(r.querySelectorAll(".e-card"));c.index=h.indexOf(d),c.currentCardId=d.getAttribute("data-id"),d.previousElementSibling?c.previousCardId=d.previousElementSibling.getAttribute("data-id"):c.previousCardId=null}else{var u=r,p=null!=o.querySelector(".e-card")?parseInt(getComputedStyle(o.querySelector(".e-card")).marginBottom.replace("px","")):null;n-r.getBoundingClientRect().top>=p&&(u=document.elementFromPoint(t,n-p));var g=sf.base.closest(u,".e-card");if(g){h=[].slice.call(r.querySelectorAll(".e-card"));c.index=h.indexOf(g),c.previousCardId=g.getAttribute("data-id")}else{h=[].slice.call(r.querySelectorAll(".e-card"));n-r.getBoundingClientRect().top>=p&&h.length>0?c.previousCardId=h[h.length-1].getAttribute("data-id"):c.previousCardId=null}}return JSON.stringify(c)}}return null},destroy:function(e){e&&window.sfBlazor.instances[e].destroy()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfkanban');})})();