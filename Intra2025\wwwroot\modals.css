
.modal {
    display: block;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgb(0,0,0);
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: #b3ffff;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    border-radius: 20px;
    width: 25%;
    @* 50% *@ box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    animation-name: modalopen;
    animation-duration: 0.4s;
    text-align: center;
}

.modalM-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 50%;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    animation-name: modalopen;
    animation-duration: 0.4s;
}

@keyframes modalopen {
    from {
        top: -300px;
        opacity: 0
    }

    to {
        top: 0;
        opacity: 1
    }
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

    .close:hover,
    .close:focus {
        color: black;
        text-decoration: none;
        cursor: pointer;
    }

.detail-table {
    width: 100%;
    border-collapse: collapse;
}

    .detail-table td {
        padding: 8px;
        border: 1px solid #ddd;
    }

        .detail-table td:first-child {
            font-weight: bold;
            background-color: #f2f2f2;
        }

.hover {
    --c: #1095c1; /* the color */
    --s: .1em;
    line-height: 1.1em;
    padding-bottom: calc(2.1*var(--s));
    background: conic-gradient(from 135deg at top,var(--c) 90deg,#0000 0) left 0 bottom var(--s)/calc(2*var(--s)) var(--s) repeat-x, conic-gradient(from -45deg at bottom,var(--c) 90deg,#0000 0) left var(--s) bottom 0/calc(2*var(--s)) var(--s) repeat-x;
}

    .hover:hover {
        --_i: 1;
    }

#requied {
    color: red;
    font-size: 18px;
    font-weight: bold;
    margin-right: 3px;
}

.e-custom {
    width: 650px;
}