@page "/finunit"
@rendermode @(new InteractiveServerRenderMode(prerender: false))
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@inject DatabaseInitializationService DatabaseService
@using ComRemitBlazor.Services
@using ComRemitBlazor.Models

<PageTitle>金融機關代號維護</PageTitle>

<!-- 簡化的樣式覆蓋 -->
<style>
    .finunit-page {
        width: 100%;
        margin: 0;
        padding: 20px;
        box-sizing: border-box;
    }
</style>

<!-- 使用簡潔的容器 -->
<div class="finunit-page">
    <div class="card">
        <div class="card-header">
            <h4>【金融機關代號維護】</h4>
        </div>
        <div class="card-body" style="padding: 20px;">
            <!-- 搜尋區 -->
            <div style="margin-bottom: 20px;">
                <div style="display: flex; flex-wrap: wrap; align-items: center; gap: 15px;">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <label class="form-label" style="white-space: nowrap; margin: 0;">請輸入要找尋的金融機關名稱或代號:</label>
                        <input type="text" class="form-control" @bind="searchText" @onkeypress="OnSearchKeyPress" 
                               placeholder="輸入機關名稱或代號" style="width: 300px;" />
                        <button class="btn btn-primary" @onclick="Search">搜尋</button>
                    </div>
                    <div style="margin-left: auto;">
                        <button class="btn btn-success" @onclick="AddNew">新增金融機關</button>
                        @if (!string.IsNullOrEmpty(errorMessage))
                        {
                            <button class="btn btn-info ms-2" @onclick="ReloadData">重新載入</button>
                        }
                    </div>
                </div>
            </div>

            <!-- 載入狀態 -->
            @if (isLoading)
            {
                <div style="text-align: center; padding: 50px 0;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">載入中...</span>
                    </div>
                    <p style="margin-top: 10px;">正在載入資料...</p>
                </div>
            }
            else if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger">
                    <h5>發生錯誤:</h5>
                    <p>@errorMessage</p>
                    <button class="btn btn-warning" @onclick="ReloadData">重試</button>
                </div>
            }
            else
            {
                <!-- 資料表格 -->
                <div style="width: 100%; overflow-x: auto;">
                    <table class="table table-bordered table-striped" style="width: 100%; min-width: 800px;">
                        <thead>
                            <tr>
                                <th style="background-color: #9C7EC6; color: white; padding: 12px; text-align: center;">金融機關名稱</th>
                                <th style="background-color: #9C7EC6; color: white; padding: 12px; text-align: center;">金融機關代號</th>
                                <th style="background-color: #9C7EC6; color: white; padding: 12px; text-align: center;">金融機關住址</th>
                                <th style="background-color: #9C7EC6; color: white; padding: 12px; text-align: center;">金融機關類別</th>
                                <th style="background-color: #9C7EC6; color: white; padding: 12px; text-align: center; width: 120px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (currentPageData != null && currentPageData.Any())
                            {
                                @foreach (var institution in currentPageData)
                                {
                                    <tr>
                                        <td style="padding: 8px;">@(institution.Name ?? "N/A")</td>
                                        <td style="padding: 8px;">@(institution.Code ?? "N/A")</td>
                                        <td style="padding: 8px;">@(institution.Address ?? "N/A")</td>
                                        <td style="padding: 8px;">@(institution.Category ?? "N/A")</td>
                                        <td style="padding: 8px; text-align: center;">
                                            <button class="btn btn-link text-primary p-0 me-2" @onclick="() => Edit(institution.Id)">編輯</button>
                                            <button class="btn btn-link text-primary p-0" @onclick="() => Delete(institution.Id)">刪除</button>
                                        </td>
                                    </tr>
                                }
                            }
                            else
                            {
                                <tr>
                                    <td colspan="5" style="text-align: center; padding: 40px;">目前沒有資料</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- 分頁 -->
                @if (totalPages > 1)
                {
                    <div style="display: flex; justify-content: center; align-items: center; margin-top: 20px; gap: 10px;">
                        <!-- 第1頁 -->
                        <button class="btn @(currentPage == 1 ? "btn-secondary" : "btn-outline-primary") btn-sm" 
                                @onclick="() => GoToPage(1)" 
                                disabled="@(currentPage == 1)">
                            第1頁
                        </button>
                        
                        <!-- 上1頁 -->
                        <button class="btn @(currentPage == 1 ? "btn-secondary" : "btn-outline-primary") btn-sm" 
                                @onclick="() => GoToPage(currentPage - 1)" 
                                disabled="@(currentPage == 1)">
                            上1頁
                        </button>
                        
                        <!-- 目前第N頁 -->
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            目前第 @currentPage 頁 (共 @totalPages 頁)
                        </span>
                        
                        <!-- 下1頁 -->
                        <button class="btn @(currentPage == totalPages ? "btn-secondary" : "btn-outline-primary") btn-sm" 
                                @onclick="() => GoToPage(currentPage + 1)" 
                                disabled="@(currentPage == totalPages)">
                            下1頁
                        </button>
                        
                        <!-- 最後1頁 -->
                        <button class="btn @(currentPage == totalPages ? "btn-secondary" : "btn-outline-primary") btn-sm" 
                                @onclick="() => GoToPage(totalPages)" 
                                disabled="@(currentPage == totalPages)">
                            最後1頁
                        </button>
                    </div>
                }

                <!-- 資料統計資訊 -->
                <div style="margin-top: 20px; color: #6c757d; font-size: 0.9rem;">
                    <p>共 @allInstitutions.Count 筆資料 @(filteredInstitutions.Count != allInstitutions.Count ? $"，搜尋結果: {filteredInstitutions.Count} 筆" : "")</p>
                </div>
            }
        </div>
    </div>
</div>

@code {
    private List<Financial> allInstitutions = new List<Financial>();
    private List<Financial> filteredInstitutions = new List<Financial>();
    private List<Financial> currentPageData = new List<Financial>();
    
    private string searchText = "";
    private int currentPage = 1;
    private int pageSize = 15;
    private int totalPages = 0;
    private bool isLoading = true;
    private string errorMessage = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            errorMessage = "";
            StateHasChanged();
            
            // 使用資料庫初始化服務載入資料
            allInstitutions = await DatabaseService.GetFinancialDataAsync();
            filteredInstitutions = allInstitutions.ToList();
            UpdatePagination();
        }
        catch (Exception ex)
        {
            errorMessage = $"載入資料時發生錯誤: {ex.Message}";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task ReloadData()
    {
        await LoadData();
    }

    private void Search()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                filteredInstitutions = allInstitutions.ToList();
            }
            else
            {
                filteredInstitutions = allInstitutions.Where(i => 
                    (!string.IsNullOrEmpty(i.Name) && i.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase)) ||
                    (!string.IsNullOrEmpty(i.Code) && i.Code.Contains(searchText, StringComparison.OrdinalIgnoreCase))).ToList();
            }
            
            currentPage = 1;
            UpdatePagination();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            errorMessage = $"搜尋時發生錯誤: {ex.Message}";
            StateHasChanged();
        }
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            Search();
        }
        await Task.CompletedTask;
    }

    private void UpdatePagination()
    {
        try
        {
            totalPages = (int)Math.Ceiling((double)filteredInstitutions.Count / pageSize);
            int skip = (currentPage - 1) * pageSize;
            currentPageData = filteredInstitutions.Skip(skip).Take(pageSize).ToList();
        }
        catch (Exception ex)
        {
            errorMessage = $"更新分頁時發生錯誤: {ex.Message}";
        }
    }

    private void GoToPage(int page)
    {
        currentPage = page;
        UpdatePagination();
        StateHasChanged();
    }

    private void Edit(int id)
    {
        Navigation.NavigateTo($"/finunit/edit/{id}");
    }

    private async Task Delete(int id)
    {
        try
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "確定要刪除這筆資料嗎？");
            if (confirmed)
            {
                // 使用資料庫服務真正刪除資料
                var success = await DatabaseService.DeleteFinancialAsync(id);
                
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "刪除成功！");
                    
                    // 重新載入資料以反映刪除的變更
                    await LoadData();
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "刪除失敗！");
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"刪除失敗: {ex.Message}");
        }
    }

    private void AddNew()
    {
        Navigation.NavigateTo("/finunit/edit");
    }
} 