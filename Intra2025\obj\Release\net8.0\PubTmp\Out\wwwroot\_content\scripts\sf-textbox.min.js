/*!*  filename: sf-textbox.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[65],{"./bundles/sf-textbox.js":function(t,e,n){"use strict";n.r(e);n("./modules/sf-textbox.js")},"./modules/sf-textbox.js":function(t,e){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.TextBox=function(){"use strict";var t=function(){function t(t,e,n){this.element=t,this.container=n,this.element.blazor_input_instance=this,this.dotNetRef=e,this.isDestroyed=!1}return t.prototype.initialize=function(){sf.base.EventHandler.add(this.element,"blur",this.blurHandler,this);var t=this.container?this.container.querySelectorAll(".e-input-group-icon"):null;if(t&&t.length>0)for(var e=0;e<t.length;e++)sf.base.EventHandler.add(t[e],"mousedown",this.onMouseDownRipple,this),sf.base.EventHandler.add(t[e],"mouseup",this.onMouseUpRipple,this)},t.prototype.calculateWidth=function(){var t=document.getElementsByClassName("e-float-text");if(null!==t)for(var e=0;e<t.length;e++)if(this.container.classList.contains("e-outline")&&this.container.classList.contains("e-prepend")&&t[e].classList.contains("e-label-top")){var n=this.container.clientWidth-this.element.clientWidth;t[e].style.left=-n.toString()+"px",t[e].style.width="auto"}else t[e].style.left="0px"},t.prototype.onMouseDownRipple=function(t){var e=t?t.currentTarget:null;!e||this.container.classList.contains("e-disabled")||this.container.querySelector("input").readOnly||e.classList.add("e-input-btn-ripple")},t.prototype.onMouseUpRipple=function(t){var e=t?t.currentTarget:null;e&&setTimeout((function(){e.classList.remove("e-input-btn-ripple")}),500)},t.prototype.blurHandler=function(){this.isDestroyed?sf.base.EventHandler.remove(this.element,"blur",this.blurHandler):this.dotNetRef.invokeMethodAsync("BlurHandler").catch((function(){}))},t.prototype.destroy=function(){sf.base.EventHandler.remove(this.element,"blur",this.blurHandler)},t}();return{initialize:function(e,n,i){if(e){var s=new t(e,n,i);if(e&&e.blazor_input_instance&&(e.blazor_input_instance.initialize(),!sf.base.isNullOrUndefined(sf.base.closest(e,"fieldset"))&&sf.base.closest(e,"fieldset").disabled)){s.dotNetRef.invokeMethodAsync("UpdateFieldSetStatus",!1)}}},calculateWidth:function(e,n,i){e&&new t(e,n,i),e&&e.blazor_input_instance&&e.blazor_input_instance.calculateWidth()},focusIn:function(t){t.focus()},focusOut:function(t){t.blur()},destroyInput:function(t){t&&t.blazor_input_instance&&(t.blazor_input_instance.isDestroyed=!0,t.blazor_input_instance.destroy())}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sftextbox');})})();