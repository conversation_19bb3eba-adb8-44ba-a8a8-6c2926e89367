﻿using Intra2025.Data;
using Intra2025.Models.YouthCareReportService;
using Microsoft.AspNetCore.Components;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Net;

namespace Intra2025.Components.Base
{
    public class BaseFunc
    {         

    }

    /// <summary>
    /// 日期轉換
    /// </summary>
    public static class DateConverter
    {
        // 西元轉民國 (輸出格式: 民國年/MM/dd)
        public static string ToTaiwanCalendar(DateTime date)
        {
            var taiwanYear = date.Year - 1911; // 西元轉民國
            return $"{taiwanYear}/{date:MM/dd}";
        }

        // 民國轉西元 (輸入格式: 民國年/MM/dd)
        public static DateTime ToADCalendar(string taiwanDate)
        {
            var parts = taiwanDate.Split('/');
            if (parts.Length != 3 ||
                !int.TryParse(parts[0], out int taiwanYear) ||
                !int.TryParse(parts[1], out int month) ||
                !int.TryParse(parts[2], out int day))
            {
                throw new FormatException("日期格式不正確。正確格式為: 民國年/MM/dd");
            }

            var year = taiwanYear + 1911; // 民國轉西元
            return new DateTime(year, month, day);
        }
    }

    /// <summary>
    /// 紀錄操作紀錄 (增、修、刪)
    /// </summary>
    public static class AccessLogHelper
    {
        /// <summary>
        /// 儲存操作日誌到資料庫中。
        /// </summary>
        /// <param name="sn">資料序號（需要操作的資料 ID）。</param>
        /// <param name="action">操作動作描述（如 Create, Update, Delete）。</param>
        /// <param name="account">操作人員帳號。</param>
        /// <param name="ip">來源 IP 位址（可選）。</param>
        public static async Task SaveAccessLogAsync(YCRSDbContext dbContext,string sn, string action, string account,string ip)
        {
            if (string.IsNullOrWhiteSpace(action))
            {
                throw new ArgumentException("Action cannot be null or empty.", nameof(action));
            }

            if (string.IsNullOrWhiteSpace(account))
            {
                throw new ArgumentException("Account cannot be null or empty.", nameof(account));
            }

            var accessLog = new YCRS_AccessLog
            {
                Sn = sn,
                Action = action,
                Account = account,
                IP = ip,
                Timestamp = DateTime.Now // 使用 UTC 時間存儲
            };           

            dbContext.YCRS_AccessLog.Add(accessLog);
            await dbContext.SaveChangesAsync();
        }
    }  

}
