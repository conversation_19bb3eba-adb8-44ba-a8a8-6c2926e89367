/*!*  filename: sf-chart3D.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[14],{"./bundles/sf-chart3D.js":function(e,t,i){"use strict";i.r(t);i("./modules/sf-chart3D.js")},"./modules/sf-chart3D.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Chart3D=function(){"use strict";var e=function(){function e(e,o,n,r,l,a,h,d,c,u){window.sfBlazor=window.sfBlazor,this.isDisposed=!1,this.mouseDownX=0,this.mouseDownY=0,this.previousMouseMoveX=0,this.previousMouseMoveY=0,this.isTouch=!1,this.rotateActivate=!1,this.isPointMouseDown=!1,this.mouseX=0,this.mouseY=0,this.scaleX=1,this.scaleY=1,this.eventInterval=80,this.isRemove=!1,this.chart3DAxes=[],this.chart3DSeries=[],this.visible3DSeries=[],this.tooltipBase=new t,this.userInteractionBase=new i,this.secondaryElementOffset=new s(0,0,0,0,0,0),this.highlightMode="None",this.selectionMode="None",this.isSeriesMode=!1,this.selectedDataIndexes=[],this.highlightDataIndexes=[],this.previousSelectedEle=[],this.highlightColor="",this.highlightPattern="None",this.selectionPattern="None",this.toggleVisibility=!0,this.previousTargetId="",this.currentPointIndex=0,this.currentSeriesIndex=0,this.currentLegendIndex=0,this.isLegendClicked=!1,this.isChartMove=!0,this.chart3DMouseDownRef=null,this.chart3DMouseEndRef=null,this.chart3DMouseLeaveRef=null,this.chart3DMouseMoveRef=null,this.chart3DMouseClickRef=null,this.chart3DKeyDownRef=null,this.chart3DKeyUpRef=null,this.mouseMoveThreshold=null,this.long3DPressBound=null,this.touch3DObject=null,this.pinchtarget=null,this.dataId=e,this.id=o,this.svgId=o+"_svg",this.element=n,this.dotnetref=r,this.options=l,window.sfBlazor.setCompInstance(this),this.isDisposed=!1,this.userInteractionBase=a,this.chart3DAxes=h,this.chart3DSeries=d,this.tooltipBase.tooltipModule=a.tooltip,this.tooltipBase.tooltipModule.template=a.templateString?a.templateString:this.tooltipBase.tooltipModule.template,this.tooltipBase.tooltipDuration=0!=this.tooltipBase.tooltipModule.duration?this.tooltipBase.tooltipModule.duration:300,this.dateValuePairs=c,this.numberValuePairs=u,this.tooltipBase.tooltipEventCalled=a.tooltipEventCalled,this.selectionHighlight3DOptions(a),this.unSelected=o+"_ej2_deselected"}return e.prototype.render3D=function(){this.unwireEvents3D(this.id,this.dotnetref),this.wireEvents3D(this.id,this.dotnetref)},e.prototype.destroy3D=function(){this.isDisposed=!0,this.unwireEvents3D(this.id,this.dotnetref),this.dotnetref.invokeMethodAsync("Chart3DDisposeDotNetReference")},e.prototype.wireEvents3D=function(e,t){var i=document.getElementById(e);if(i){this.dotnetref=t,r.dotnetrefCollection.push({id:e,dotnetref:t});
/*! Find the Events type */
var s=sf.base.Browser.isPointer?"pointerleave":"mouseleave";this.chart3DMouseDownRef=this.chart3DOnMouseDown.bind(this,t,e),this.chart3DMouseEndRef=this.chart3DMouseEnd.bind(this,t,e),this.chart3DMouseLeaveRef=this.chart3DOnMouseLeave.bind(this,t,e),this.chart3DMouseMoveRef=this.chart3DOnMouseMove.bind(this,t,e),this.chart3DMouseClickRef=this.chart3DOnMouseClick.bind(this,t,e),this.chart3DKeyDownRef=this.chart3DOnKeyDown.bind(this,this.dotnetref,this.id),this.chart3DKeyUpRef=this.chart3DOnKeyUp.bind(this,this.dotnetref,this.id),
/*! Bind the Event handler */
i.addEventListener("mousemove",this.chart3DMouseMoveRef),i.addEventListener("touchmove",this.chart3DMouseMoveRef),sf.base.EventHandler.add(i,sf.base.Browser.touchStartEvent,this.chart3DMouseDownRef),sf.base.EventHandler.add(i,sf.base.Browser.touchEndEvent,this.chart3DMouseEndRef),sf.base.EventHandler.add(i,s,this.chart3DMouseLeaveRef),sf.base.EventHandler.add(i,"click",this.chart3DMouseClickRef),sf.base.EventHandler.add(i,"keydown",this.chart3DKeyDownRef),sf.base.EventHandler.add(document.body,"keydown",this.documentKeyHandler,this),sf.base.EventHandler.add(i,"keyup",this.chart3DKeyUpRef),r.resizeBound=r.chart3DResize.bind(this,r.dotnetrefCollection);var o=sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize";sf.base.EventHandler.add(window,o,r.resizeBound),this.long3DPressBound=this.chart3DLongPress.bind(this,t,e),this.touch3DObject=new sf.base.Touch(i,{tapHold:this.long3DPressBound,tapHoldThreshold:500})}},e.prototype.unwireEvents3D=function(e,t){var i=document.getElementById(e);if(i){this.dotnetref=t,r.dotnetrefCollection=r.dotnetrefCollection.filter((function(t){return t.id!==e}));
/*! Find the Events type */
var s=sf.base.Browser.isPointer?"pointerleave":"mouseleave";
/*! Bind the Event handler */if(i.removeEventListener("mousemove",this.chart3DMouseMoveRef),i.removeEventListener("touchmove",this.chart3DMouseMoveRef),sf.base.EventHandler.remove(i,sf.base.Browser.touchStartEvent,this.chart3DMouseDownRef),sf.base.EventHandler.remove(i,sf.base.Browser.touchEndEvent,this.chart3DMouseEndRef),sf.base.EventHandler.remove(i,s,this.chart3DMouseLeaveRef),sf.base.EventHandler.remove(i,"click",this.chart3DMouseClickRef),sf.base.EventHandler.remove(i,"keydown",this.chart3DKeyDownRef),sf.base.EventHandler.remove(document.body,"keydown",this.documentKeyHandler),sf.base.EventHandler.remove(i,"keyup",this.chart3DKeyUpRef),0==document.getElementsByClassName("e-chart").length){var o=sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize";sf.base.EventHandler.remove(window,o,r.resizeBound)}this.touch3DObject&&(this.touch3DObject.destroy(),this.touch3DObject=null)}},e.prototype.set3DMouseXY=function(e,t){if(r.getElement(this.svgId)){var i=r.getElement(this.svgId).getBoundingClientRect(),s=this.element.getBoundingClientRect();this.secondaryElementOffset.left=Math.max(i.left-s.left,0),this.secondaryElementOffset.top=Math.max(i.top-s.top,0),this.mouseY=t-s.top-this.secondaryElementOffset.top/this.scaleX,this.mouseX=e-s.left-this.secondaryElementOffset.left/this.scaleY}},e.prototype.chart3DOnMouseDown=function(e,t,i){this.dotnetref=e;var s,o,n,l=i.target,a=l.id,h=(sf.base.Browser.isDevice,this.element.getBoundingClientRect());"touchstart"===i.type?(this.isTouch=!0,s=(n=i).changedTouches[0].clientX,o=n.changedTouches[0].clientY,l=n.target):(this.isTouch="touch"===i.pointerType||"2"===i.pointerType||this.isTouch,s=i.clientX,o=i.clientY,l=i.target),this.set3DMouseXY(s,o),i.type.indexOf("touch")>-1&&this.options.enableRotation&&(this.pinchtarget=document.getElementById(t+"_pinch_target"),this.pinchtarget.style.left=this.mouseX+this.secondaryElementOffset.left+"px",this.pinchtarget.style.top=this.mouseY+this.secondaryElementOffset.top+"px",i.preventDefault());var d=r.getElement(this.svgId).getBoundingClientRect();return this.mouseDownX=this.previousMouseMoveX=s-h.left-Math.max(d.left-h.left,0),this.mouseDownY=this.previousMouseMoveY=o-h.top-Math.max(d.top-h.top,0),this.options.enableRotation&&(this.rotateActivate=!0,this.previousCoords={x:this.mouseDownX,y:this.mouseDownY}),this.userInteractionBase.toggleVisibility&&(a=i.target.id.indexOf("_chart_legend_g_")>-1?i.target.id.replace(/_g_/g,"_text_"):i.target.id),e.invokeMethodAsync("OnChart3DMouseDown",this.get3DEventArgs(i,a)),!1},e.prototype.chart3DMouseEnd=function(e,t,i){var s,o,n=this;this.dotnetref=e;var l="touchend"===i.type?i:null;if(s="touchend"===i.type?l.changedTouches[0].clientX:i.clientX,o="touchend"===i.type?l.changedTouches[0].clientY:i.clientY,this.isTouch=i.type.indexOf("touch")>-1||("touch"===i.pointerType||"2"===i.pointerType),this.set3DMouseXY(s,o),this.rotateActivate=!1,this.isTouch&&this.pinchtarget&&this.options.enableRotation&&setTimeout((function(){n.pinchtarget.style.left=n.pinchtarget.style.top="-100px"}),2e3),this.options.enableRotation&&this.isChartRotated&&this.selectedDataIndexes.length>0&&(this.isChartRotated=!1,r.redraw3DSelection(this.dataId)),this.tooltipBase.tooltipModule.enable){var a=r.get3DData(this,event);this.isTouch?(r.tooltip3D(this,event),"Move"===this.tooltipBase.tooltipModule.fadeOutMode&&(clearTimeout(this.timerId),this.timerId=+setTimeout((function(){r.removeTooltip3D(n.tooltipBase.tooltipModule.fadeOutDuration,n),r.removeBlurEffect(n)}),500)),this.startMove&&"Move"===this.tooltipBase.tooltipModule.fadeOutMode&&(r.removeTooltip3D(2e3,this),r.removeBlurEffect(this))):r.find3DData(a,this.tooltipBase.previousPoints[0])||"Click"!==this.tooltipBase.tooltipModule.fadeOutMode||(r.removeTooltip3D(0,this),r.removeBlurEffect(this))}return e.invokeMethodAsync("OnChart3DMouseUp",this.get3DEventArgs(i)),this.isTouch=!1,!1},e.prototype.chart3DOnMouseLeave=function(e,t,i){return this.dotnetref=e,this.isPointMouseDown=!1,this.rotateActivate=!1,this.options.enableRotation&&this.isChartRotated&&this.selectedDataIndexes.length>0&&(this.isChartRotated=!1,r.redraw3DSelection(this.dataId)),null!=this.tooltipBase.tooltipModule&&(r.removeTooltip3D(this.tooltipBase.tooltipModule.fadeOutDuration,this),r.removeBlurEffect(this)),!1},e.prototype.chart3DOnMouseMove=function(e,t,i){var s,o,n,l=i.target;"touchmove"===i.type?(this.isTouch=!0,s=(n=i).changedTouches[0].clientX,o=n.changedTouches[0].clientY):(this.isTouch="touch"===i.pointerType||"2"===i.pointerType||this.isTouch,s=i.clientX,o=i.clientY),r.getElement(this.svgId)&&(this.set3DMouseXY(s,o),this.isTouch&&this.pinchtarget&&this.options.enableRotation&&(this.pinchtarget.style.left=this.mouseX+this.secondaryElementOffset.left+"px",this.pinchtarget.style.top=this.mouseY+this.secondaryElementOffset.top+"px",i.preventDefault()),this.rotateActivate&&sf.svgbase.withInAreaBounds(this.mouseX,this.mouseY,this.userInteractionBase.seriesClipRect)?(this.tooltipBase.tooltipModule&&this.tooltip&&r.removeTooltip3D(0,this),(!this.isTouch||this.isTouch&&l.id.indexOf("pinch_target")>-1)&&r.rotateChart3D(this,e)):r.tooltip3DMousemovehandler(this,i),r.legendHighlight3D(this,l,i.type),"None"!=this.highlightMode&&r.highlightChart3D(this,l,i.type),sf.base.isNullOrUndefined(l)||"None"!=this.highlightMode||l.id.indexOf("legend")>0||!(this.highlightDataIndexes.length>0)||r.removeLegendHighlightStyles(this),"touchmove"===i.type&&(sf.base.Browser.isIos||sf.base.Browser.isIos7)&&i.preventDefault&&i.preventDefault(),e.invokeMethodAsync("OnChart3DMouseMove",this.get3DEventArgs(i)),this.isTouch=!1)},e.prototype.chart3DOnMouseClick=function(e,t,i){var s=i.target,o=window.sfBlazor.getCompInstance(this.dataId);if(!o.rotateActivate&&(r.calculateSelectedElements(o,s,i.type),s.id.indexOf("legend")>0&&!o.toggleVisibility))for(var n=[o.id+"_chart_legend_text_",o.id+"_chart_legend_shape_marker_",o.id+"_chart_legend_shape_",o.id+"_chart_legend_g_"],l=s.id,a=void 0,h=0,d=n;h<d.length;h++){var c=d[h];if(l.indexOf(c)>-1){a=parseInt(l.split(c)[1],10),r.legendSelection(o,a,s,i.type);break}}e.invokeMethodAsync("OnChart3DMouseClick",this.get3DEventArgs(i))},e.prototype.documentKeyHandler=function(e){e.altKey&&74===e.keyCode&&!sf.base.isNullOrUndefined(this.element)&&this.element.focus()},e.prototype.chart3DOnKeyDown=function(e,t,i){this.dotnetref=e;var s="";return("Space"===i.code||i.code.indexOf("Arrow")>-1)&&i.preventDefault(),this.tooltipBase.tooltipModule.enable&&("Tab"===i.code&&this.previousTargetId.indexOf("-series")>-1||"Escape"===i.code)&&(s="ESC"),"None"!==this.highlightMode&&"Tab"===i.code&&this.previousTargetId.indexOf("_chart_legend_")>-1&&(this.options.enableHighlight||this.options.enableLegendHighlight)&&r.removeLegendHighlightStyles(this),i.ctrlKey&&"p"===i.key&&(i.preventDefault(),s="CtrlP"),""!==s&&(this.chart3DKeyboardNavigations(i,i.target.id,s),e.invokeMethodAsync("OnChart3DKeyboardNavigations",s,i.target.id)),!1},e.prototype.chart3DOnKeyUp=function(e,t,i){this.dotnetref=e;var s,o="",n=i.target.id,l=i.target,a=r.getElement(this.element.id+"-chart-title"),h=r.getElement(this.element.id+"-svg-0-region-series-0-point-0"),d=r.getElement(this.element.id+"_chart_legend_translate_g"),c=r.getElement(this.element.id+"_chart_legend_pageup");(r.set3DSeriesTabIndex(),a&&a.setAttribute("class","e-chart-focused"),h)&&((u=h.getAttribute("class"))&&-1===u.indexOf("e-chart-focused")?u+=" e-chart-focused":u||(u="e-chart-focused"),h.setAttribute("class",u));if(d){var u,g=d.firstElementChild;(u=g.getAttribute("class"))&&-1===u.indexOf("e-chart-focused")?u+=" e-chart-focused":u||(u="e-chart-focused"),g.setAttribute("class",u)}if(c&&c.setAttribute("class","e-chart-focused"),"Tab"===i.code){if(""!==this.previousTargetId)if(this.previousTargetId.indexOf("-series-")>-1&&-1===n.indexOf("-series-")){var p=r.getElement(this.element.id+"-svg-0-region-series-"+this.currentSeriesIndex+"-point-"+this.currentPointIndex);this.setTabIndex(p,h),this.currentPointIndex=0,this.currentSeriesIndex=0}else this.previousTargetId.indexOf("_chart_legend_page")>-1&&-1===n.indexOf("_chart_legend_page")&&-1===n.indexOf("_chart_legend_g_")?this.setTabIndex(i.target,r.getElement(this.element.id+"_chart_legend_pageup")):this.previousTargetId.indexOf("_chart_legend_g_")>-1&&-1===n.indexOf("_chart_legend_g_")&&(s=r.getElement(this.element.id+"_chart_legend_translate_g"),this.setTabIndex(s.children[this.currentLegendIndex],s.firstElementChild));this.previousTargetId=n,n.indexOf("-series-")>-1&&(this.currentSeriesIndex=+n.split("-series-")[1].split("-point-")[0],l.removeAttribute("tabindex"),l.blur(),n=this.focusChild(l)),o="None"!==this.highlightMode||this.options.enableLegendHighlight||this.tooltipBase.tooltipModule.enable?"Tab":""}else if(i.code.indexOf("Arrow")>-1){if(i.preventDefault(),this.previousTargetId=n,n.indexOf("_chart_legend_page")>-1)"ArrowLeft"===i.code?(r.getElement(this.element.id+"_chart_legend_pagedown").removeAttribute("tabindex"),this.focusChild(r.getElement(this.element.id+"_chart_legend_pageup"))):"ArrowRight"===i.code&&(r.getElement(this.element.id+"_chart_legend_pageup").removeAttribute("tabindex"),this.focusChild(r.getElement(this.element.id+"_chart_legend_pagedown")));else if(n.indexOf("_chart_legend_")>-1){var f=l.parentElement.children;f[this.currentLegendIndex].removeAttribute("tabindex"),this.currentLegendIndex+="ArrowUp"===i.code||"ArrowRight"===i.code?1:-1,this.currentLegendIndex=this.getActualIndex(this.currentLegendIndex,f.length);var m=f[this.currentLegendIndex];this.focusChild(m),n=m.children[1].id,o="None"!==this.highlightMode||this.options.enableLegendHighlight?"ArrowMove":""}else if(n.indexOf("-series-")>-1){var v=i.target;if(l.removeAttribute("tabindex"),l.blur(),"ArrowRight"===i.code||"ArrowLeft"===i.code?(this.currentSeriesIndex=this.currentSeriesIndex+("ArrowRight"===i.code?1:-1),this.currentSeriesIndex=this.getActualIndex(this.currentSeriesIndex,this.visible3DSeries.length)):this.currentPointIndex+="ArrowUp"===i.code?1:-1,n.indexOf("-point-")>-1){this.currentPointIndex=this.getActualIndex(this.currentPointIndex,this.visible3DSeries[this.currentSeriesIndex]?this.visible3DSeries[this.currentSeriesIndex].points.length:1);for(var b=document.querySelectorAll('[id*="svg-0-region-series-'+this.currentSeriesIndex+"-point-"+this.currentPointIndex+'"]'),x=0;x<b.length;x++)b[x].id.split("-point-")[1].split("-")[0]===this.currentPointIndex.toString()&&(v=b[x])}n=this.focusChild(v),o=this.tooltipBase.tooltipModule.enable||"None"!==this.highlightMode||this.options.enableLegendHighlight?"ArrowMove":""}}else"Enter"!==i.code&&"Space"!==i.code||!(n.indexOf("_chart_legend_")>-1||n.indexOf("-point-")>-1)||(n=n.indexOf("_chart_legend_page")>-1?n:n.indexOf("_chart_legend_")>-1?l.id.replace(/_g_/g,"_text_"):n,o="Enter");return""!==o&&(this.chart3DKeyboardNavigations(i,n,o),e.invokeMethodAsync("OnChart3DKeyboardNavigations",o,n)),!1},e.prototype.chart3DLongPress=function(e,t,i){this.dotnetref=e,this.startMove=!0;var s=i&&i.originalEvent.changedTouches?i.originalEvent.changedTouches[0].clientX:0,o=i&&i.originalEvent.changedTouches?i.originalEvent.changedTouches[0].clientY:0;this.set3DMouseXY(s,o);this.mouseX,this.mouseY;return!1},e.prototype.getVisible3DSeies=function(){this.visible3DSeries=[];for(var e,t=function(t){var s=i.chart3DSeries[t],o=document.getElementById(i.element.id+"_Chart3DSeriesClipRect_"+t),n=sf.base.isNullOrUndefined(o)?null:o.getAttribute("data-point").split(/(?![^(]*\)),/);e={name:n[0],type:n[1],index:parseInt(n[2]),xAxis:i.chart3DAxes.filter((function(e){return e.name==n[3]}))[0],yAxis:i.chart3DAxes.filter((function(e){return e.name==n[4]}))[0],columnFacet:n[5],tooltipFormat:n[6],points:sf.base.isNullOrUndefined(s)||""==s.chartPoints?[]:JSON.parse(s.chartPoints),visible:!0,clipRect:s.clipRect,enableTooltip:"true"==n[7],dataLabelFormat:n[8],isRectSeries:n[1].indexOf("Bar")>-1,interior:n[9],opacity:parseFloat(n[10]),pointColorMapping:n[11]},i.visible3DSeries.push(e)},i=this,s=0;s<this.chart3DSeries.length;s++)t(s);(!this.options.enableHighlight||this.options.enableLegendHighlight&&"None"===this.userInteractionBase.highlightMode)&&this.tooltipBase.tooltipModule&&this.tooltipBase.tooltipModule.enable&&this.tooltipSeriesStyles()},e.prototype.tooltipSeriesStyles=function(){if(!this.tooltipStyleAdded){var e=document.createElement("style");e.setAttribute("id",this.element.id+"_ej2_chart_tooltip"),e.innerText+=" ."+this.element.id+"_ej2_tooltipDeselected { opacity:0.2;} ",document.body.appendChild(e),this.tooltipStyleAdded=!0}},e.prototype.selectionHighlight3DOptions=function(e){this.toggleVisibility=e.toggleVisibility,this.highlightMode=e.highlightMode,this.selectionMode=e.selectionMode,this.highlightColor=e.highlightColor,this.highlightPattern=e.highlightPattern,this.selectionPattern=e.selectionPattern,this.isMultiSelect=e.isMultiSelect,this.oldMode=this.currentMode,this.options.enableRotation=e.enableRotation},e.prototype.get3DEventArgs=function(e,t){void 0===t&&(t=null);var i=e.changedTouches?e.changedTouches[0].clientX:e.clientX,s=e.changedTouches?e.changedTouches[0].clientY:e.clientY;this.set3DMouseXY(i,s);var o=e.touches,n=[];if(e.type.indexOf("touch")>-1)for(var r=0,l=o.length;r<l;r++)n.push({pageX:o[r].clientX,pageY:o[r].clientY,pointerId:e.pointerId||0});return{type:e.type,clientX:e.clientX,clientY:e.clientY,mouseX:this.mouseX,mouseY:this.mouseY,pointerType:e.pointerType,target:t||e.target.id,changedTouches:{clientX:e.changedTouches?e.changedTouches[0].clientX:0,clientY:e.changedTouches?e.changedTouches[0].clientY:0},touches:n,pointerId:e.pointerId}},e.prototype.chart3DKeyboardNavigations=function(e,t,i){switch(this.isLegendClicked=!1,i){case"Tab":case"ArrowMove":if((this.options.enableHighlight&&"None"!==this.highlightMode||this.options.enableLegendHighlight)&&0==this.selectedDataIndexes.length&&r.removeLegendHighlightStyles(this),t.indexOf("-point-")>-1){if(document.activeElement){var s=document.activeElement.getBoundingClientRect(),o=s.left+s.width/2,n=s.top,l=window.scrollX+o,a=window.scrollY+n;this.set3DMouseXY(l,a)}if(this.options.enableHighlight&&(r.highlightChart3D(this,document.getElementById(t),"mousemove"),r.completeSelection3D(this)),this.tooltipBase.tooltipModule.enable){var h=parseInt(t.split("-series-")[1].split("-point-")[0]),d=parseInt(t.split("-point-")[1].split("-")[0]);if(this.visible3DSeries[h]){var c={series:this.visible3DSeries[h],point:this.visible3DSeries[h].points[d]};if(c.series&&c.series.visible){var u=void 0,g=document.getElementById(t);g&&(u="ZLight"===g.getAttribute("name")?r.reverseZLight(g.getAttribute("fill")):"XLight"===g.getAttribute("name")?r.reverseXLight(g.getAttribute("fill")):g.getAttribute("fill")),c.point.interior=sf.base.isNullOrUndefined(c.point.interior)?u:c.point.interior,c.point.index=sf.base.isNullOrUndefined(c.point.index)?d:c.point.index}r.getTooltip3DElement(this),this.tooltipBase.currentPoints=[],r.push3DData(c,this)&&r.trigger3DTooltipRender(c,!0,r.getTooltipText(c,this),r.findHeader(c,this),this)}}}else this.tooltipBase.tooltipModule.enable&&(r.removeTooltip3D(1,this),r.removeBlurEffect(this));if(this.options.enableHighlight&&"None"!==this.highlightMode||this.options.enableLegendHighlight){t=t.indexOf("_chart_legend_g_")>-1?t.replace(/_g_/g,"_text_"):t;for(var p=[(v=this.element.id+"_chart_legend")+"_text_",v+"_shape_marker_",v+"_shape_"],f=0;f<p.length;f++){var m=p[f];if(t.indexOf(m)>-1){document.getElementById(t).setAttribute("class",""),r.legendSelection(this,parseInt(t.split(m)[1],10),document.getElementById(t),"mousemove");break}}}break;case"Enter":case"Space":if(t.indexOf("_chart_legend_")>-1){if(this.isLegendClicked=!0,this.focusChild(document.getElementById(t).parentElement),!this.toggleVisibility){t=t.indexOf("_chart_legend_g_")>-1?t.replace(/_g_/g,"_text_"):t;var v;for(p=[(v=this.element.id+"_chart_legend")+"_text_",v+"_shape_marker_",v+"_shape_"],f=0;f<p.length;f++){m=p[f];if(t.indexOf(m)>-1){document.getElementById(t).setAttribute("class",""),r.legendSelection(this,parseInt(t.split(m)[1],10),document.getElementById(t),"click");break}}}}else r.calculateSelectedElements(this,document.getElementById(t),"click");break;case"ESC":r.removeTooltip3D(1,this),r.removeBlurEffect(this)}},e.prototype.focusChild=function(e){e.setAttribute("tabindex","0");var t=e.getAttribute("class");return e.setAttribute("tabindex","0"),t&&-1===t.indexOf("e-chart-focused")?t="e-chart-focused "+t:t||(t="e-chart-focused"),e.setAttribute("class",t),e.focus(),e.id},e.prototype.setTabIndex=function(e,t){e&&e.removeAttribute("tabindex"),t&&t.setAttribute("tabindex","0")},e.prototype.getActualIndex=function(e,t){return e>t-1?0:e<0?t-1:e},e}(),t=function(){this.currentPoints=[],this.previousPoints=[],this.valueX=0,this.valueY=0,this.tooltipTempList=[]},i=function(){},s=function(e,t,i,s,o,n){this.width=e,this.height=t,this.left=i,this.top=s,this.right=o,this.bottom=n},o=function(e,t){this.x=e,this.y=t},n=function(e,t){this.series=e,this.point=t},r={initialize3D:function(t,i,s,o,n,r,l,a,h){var d=new e(t,i.id,i,s,o,n,r,l,a,h);d&&(d.render3D(),d.getVisible3DSeies())},dotnetrefCollection:[],destroy3D:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.destroy3D()},get3DParentElementBoundsById:function(e){var t=document.getElementById(e);if(t){var i=document.getElementById(e+"_svg");i&&(i.style.display="none"),t.style.width="100%",t.style.height="100%";var s=t.getBoundingClientRect(),o=s.width||t.clientWidth||t.offsetWidth,n=s.height||t.clientHeight||t.offsetHeight;return i&&(i.style.display=""),{width:o,height:n,left:s.left,top:s.top,right:s.right,bottom:s.bottom}}return{width:0,height:0,left:0,top:0,right:0,bottom:0}},get3DElementBoundsById:function(e,t){void 0===t&&(t=!0),t&&(this.id=e);var i=document.getElementById(e);if(i){var s=i.getBoundingClientRect();return{width:i.clientWidth||i.offsetWidth,height:i.clientHeight||i.offsetHeight,left:s.left,top:s.top,right:s.right,bottom:s.bottom}}return{width:0,height:0,left:0,top:0,right:0,bottom:0}},get3DBrowserDeviceInfo:function(){return{browserName:sf.base.Browser.info.name,isPointer:sf.base.Browser.isPointer,isDevice:sf.base.Browser.isDevice,isTouch:sf.base.Browser.isTouch,isIos:sf.base.Browser.isIos||sf.base.Browser.isIos7}},set3DSvgDimensions:function(e,t,i){e.setAttribute("width",t),e.setAttribute("height",i)},getAllCharacters:function(){for(var e=[],t=33;t<591;t++)e.push(String.fromCharCode(t));return e},get3DCharCollectionSize:function(e){for(var t=[],i=this.getAllCharacters(),s=i.length,o=e.length,n=0;n<o;n++)for(var r=e[n].split("_"),l=r[0],a=r[1],h=r[2],d=0;d<s;d++)t.push(this.measureText(i[d],l,a,h).Width+"");return JSON.stringify(t)},get3DRefreshElementBoundsById:function(e){var t=document.getElementById(e);if(t){for(var i=document.getElementsByClassName("e-chart"),s=i.length,o=0;o<s;o++){if((r=i[o]).id.indexOf("_stockChart_")<0)r.querySelector("[id*=_svg]").style.display="none"}t.style.width="100%",t.style.height="100%";var n=t.getBoundingClientRect();for(o=0;o<s;o++){var r;if((r=i[o]).id.indexOf("_stockChart_")<0)r.querySelector("[id*=_svg]").style.display=""}return{width:n.width||t.clientWidth||t.offsetWidth,height:n.height||t.clientHeight||t.offsetHeight,left:n.left,top:n.top,right:n.right,bottom:n.bottom}}return{width:0,height:0,left:0,top:0,right:0,bottom:0}},get3DCharSizeByFontKeys:function(e){for(var t,i={},s=e.length,o=[],n=0;n<s;n++)o=e[n].split("_"),t=this.measureText(o[0],o[1],o[2],o[3]),i[e[n]]={X:t.Width,Y:t.Height};return JSON.stringify(i)},focusTarget3D:function(e){var t,i;if(e.indexOf("_chart_legend_")>-1){i=(t=this.getElement(e).parentElement).getAttribute("class");for(var s=void 0,o=0,n=[this.id+"_chart_legend_text_",this.id+"_chart_legend_shape_marker_",this.id+"_chart_legend_shape_",this.id+"_chart_legend_g_"];o<n.length;o++){var r=n[o];if(e.indexOf(r)>-1){s=parseInt(e.split(r)[1],10);break}}this.setTabIndex(this.getElement(this.id+"_chart_legend_translate_g").firstElementChild,this.getElement(this.id+"-svg-"+s+"-region-series-"+s+"-point-0")),t.setAttribute("tabindex","0"),i&&-1===i.indexOf("e-chart-focused")?i+=" e-chart-focused":i||(i="e-chart-focused")}else t=this.getElement(e),i="e-chart-focused";t.setAttribute("tabindex","0"),t.setAttribute("class",i),t.focus()},getElement:function(e){return document.getElementById(e)},setTabIndex:function(e,t){e&&e.removeAttribute("tabindex"),t&&t.setAttribute("tabindex","0")},measureText:function(e,t,i,s){var o=document.getElementById("sfchartmeasuretext");return null===o&&(o=sf.base.createElement("text",{id:"sfchartmeasuretext"}),document.body.appendChild(o))," "===e&&(e="&nbsp;"),o.innerHTML=e,o.style.position="fixed",o.style.fontSize="100px",o.style.fontWeight=t,o.style.fontStyle=i,o.style.fontFamily=s,o.style.visibility="hidden",o.style.top="-100",o.style.left="0",o.style.whiteSpace="nowrap",o.style.lineHeight="normal",{Width:o.clientWidth,Height:o.clientHeight}},resizeBound:{},resize:{},chart3DResize:function(e,t){var i=this;return this.resize&&clearTimeout(this.resize),this.isDisposed||(this.resize=setTimeout((function(){for(var t,s,o,n=e.length,r={},l=0;l<n;l++)t=e[l].dotnetref,s=e[l].id,(o=document.getElementById(s+"_svg")).style.display="none";for(l=0;l<n;l++)t=e[l].dotnetref,s=e[l].id,o=document.getElementById(s),r[s]={Dotnetref:t,Width:o.clientWidth||o.offsetWidth,Height:o.clientHeight||o.offsetHeight};for(var a in r)t=r[a].Dotnetref,o=document.getElementById(a+"_svg"),t.invokeMethodAsync("OnChart3DResize",JSON.stringify({Width:r[a].Width,Height:r[a].Height})),o.style.display="";clearTimeout(i.resize)}),500)),!1},get3DDatalabelTemplateSize:function(e){for(var t,i=[],s=e.length,o=0;o<s;o++)t=this.get3DElementBoundsById(e[o],!1),i.push({X:t.width,Y:t.height});return JSON.stringify(i)},linear3D:function(e,t,i,s){return-i*Math.cos(e/s*(Math.PI/2))+i+t},doInitial3DAnimation:function(e){for(var t,i,s,o=0;o<e.length;o++)if(!this.rotateActivate)for(var n=0;n<e[o].pointIndex.length;n++)e[o].dataLabelVisible&&(t=this.getElement(this.id+"-svg-series-"+e[o].seriesIndex+"-point-"+n+"-data-label"),i=this.getElement(this.id+"-svg-data-label-series-"+e[o].seriesIndex+"-point-"+n),s=this.getElement(this.id+"-series-"+e[o].seriesIndex+"-data-label-"+n)),this.doRect3DAnimation(document.getElementById(e[o].clipPathId),e[o].duration,e[o].delay,e[o].pointX[n],e[o].pointY[n],e[o].pointWidth[n],e[o].pointHeight[n],e[o].isInvertedAxis,t,i,s)},doRect3DAnimation:function(e,t,i,s,o,n,r,l,a,h,d){var c,u=this.linear3D;sf.base.isNullOrUndefined(e)||(e.setAttribute("visibility","hidden"),a&&(a.style.visibility="hidden"),h&&(h.style.visibility="hidden"),d&&(d.style.visibility="hidden"),new sf.base.Animation({}).animate(e,{duration:t,delay:i,progress:function(t){t.timeStamp>=t.delay&&(e.setAttribute("visibility","visible"),l?(n=n||1,c=u(t.timeStamp-t.delay,0,n,t.duration),e.setAttribute("transform","translate("+s+" "+o+") scale("+c/n+", 1) translate("+-s+" "+-o+")")):(r=r||1,c=u(t.timeStamp-t.delay,0,r,t.duration),e.setAttribute("transform","translate("+s+" "+o+") scale(1,"+c/r+") translate("+-s+" "+-o+")")))},end:function(){e.setAttribute("transform","translate(0,0)"),a&&(a.style.visibility="visible"),h&&(h.style.visibility="visible"),d&&(d.style.visibility="visible")}}))},stopAnimation:function(e){this.stopTimer(e)},stopTimer:function(e){window.clearInterval(e)},rotateChart3D:function(e,t){if(null==e.mouseMoveThreshold||(new Date).getTime()-e.mouseMoveThreshold>e.eventInterval){e.mouseMoveThreshold=(new Date).getTime(),e.isChartRotated=!0;var i=e.previousCoords.x-e.mouseX,s=e.previousCoords.y-e.mouseY;(i||s)&&(e.userInteractionBase.tilt-=s,e.userInteractionBase.rotation+=i,e.previousCoords.y=e.mouseY,e.previousCoords.x=e.mouseX,e.isRemove=!1,e.isTouch&&e.pinchtarget&&(e.pinchtarget.style.left=e.mouseX+e.secondaryElementOffset.left+"px",e.pinchtarget.style.top=e.mouseY+e.secondaryElementOffset.top+"px"),t.invokeMethodAsync("Chart3DRotate",e.userInteractionBase.tilt,e.userInteractionBase.rotation))}},getTooltip3DElement:function(e){e.tooltipBase.header=null===e.tooltipBase.tooltipModule.header?"<b>${series.name}</b>":e.tooltipBase.tooltipModule.header,e.tooltipBase.formattedText=[]},tooltip3DMousemovehandler:function(e,t){e.mouseX=e.mouseX/e.scaleX,e.mouseY=e.mouseY/e.scaleY,e.rotateActivate||e.isTouch&&!e.startMove||!e.tooltipBase.tooltipModule.enable||!sf.svgbase.withInAreaBounds(e.mouseX,e.mouseY,e.userInteractionBase.seriesClipRect)||this.tooltip3D(e,t)},tooltip3D:function(e,t){this.getTooltip3DElement(e),!sf.base.isNullOrUndefined(e.visible3DSeries)&&e.isChartMove?this.render3DSeriesTooltip(e,!0,t):e.isRemove&&"Move"===e.tooltipBase.tooltipModule.fadeOutMode&&(this.removeTooltip3D(e.tooltipBase.tooltipModule.fadeOutDuration,e),this.removeBlurEffect(e),e.isRemove=!1)},get3DData:function(e,t){var i,s,o,n,r=null,l=null,a=e.mouseX,h=e.mouseY,d=e.userInteractionBase.seriesClipRect,c=t.target;if(c&&a>d.x&&a<d.x+d.width&&h>d.y&&h<d.y+d.height){var u=c.nodeName;"path"!==u&&"shape"!==u&&"text"!==u||!(c.id.indexOf("region")>1||c.id.indexOf("data-label")>1&&c.id.indexOf("series")>1)||(i=c.id.match(/(\d+)/g),s=parseInt(i[i.length-1].toString(),10),o=parseInt(i[i.length-2].toString(),10),sf.base.isNullOrUndefined(o)||(l=e.visible3DSeries[o]),c&&(n="ZLight"===c.getAttribute("name")?this.reverseZLight(c.getAttribute("fill")):"XLight"===c.getAttribute("name")?this.reverseXLight(c.getAttribute("fill")):c.getAttribute("fill")))}return l&&l.visible&&((r=l.points[s]).interior=sf.base.isNullOrUndefined(r.interior)?n:r.interior,r.index=sf.base.isNullOrUndefined(r.index)?s:r.index,l.type.indexOf("Stacking")>-1&&(r.percentage=parseFloat(c.getAttribute("aria-label").split(",")[1]))),{point:r,series:l}},find3DData:function(e,t){return e.point&&(!t||t.point!==e.point||t.point===e.point)},push3DData:function(e,t){return!!e.series.enableTooltip&&(null!=t&&t.tooltipBase.currentPoints.push(e),!0)},parseTemplate:function(e,t,i,s){for(var o,n,r=0,l=Object.keys(e);r<l.length;r++){var a=l[r];o=new RegExp("${point."+a+"}","gm"),i=i.replace(o.source,this.formatPointValue(e,a,"${point.x}"===o.source,"${point.y}"===o.source,t,s))}for(var h=0,d=Object.keys(t);h<d.length;h++){a=d[h];o=new RegExp("${series."+a+"}","gm"),n=t[a],i.indexOf(o.source)>-1&&(i=i.replace(o.source,n))}return i},getGlobalizedNumber:function(e,t){for(var i=e.split(""),s=Object.keys(t),o=0,n=e.length;o<n;o++)for(var r=0,l=s.length;r<l;r++)if(i[o]==s[r]){i.splice(o,1,t[s[r]]);break}return i.join("")},getGlobalizedDate:function(e,t){return Object.keys(t).forEach((function(i){e=e.replaceAll?e.replaceAll(i,t[i]):e.replace(i,t[i])})),e},convertDateAndTime:function(e){return new Date(e.getTime()+60*e.getTimezoneOffset()*1e3)},formatPointValue:function(e,t,i,s,o,n){var r,l,a,h=i?o.xAxis:o.yAxis,d=h.format?h.format:h.dateFormat,c=h.labelFormat;if("DateTime"==h.valueType&&i)r=(new sf.base.Internationalization).getDateFormat({format:d||"MM/dd/yyyy",type:this.firstToLowerCase("DateTime")})(this.convertDateAndTime(new Date(e.xValue))),r=this.getGlobalizedDate(r,n.dateValuePairs);else if("DateTimeCategory"===h.valueType&&i){var u=h.isUniversalDateTime?this.convertDateAndTime(new Date(e[t])):new Date(e[t]);r=(new sf.base.Internationalization).getDateFormat({format:d||c||"MM/dd/yyyy HH:mm:ss tt",type:this.firstToLowerCase("DateTime")})(u),r=this.getGlobalizedDate(r,n.dateValuePairs)}else if("Category"!==h.valueType&&i)r=(l=c&&null!==c.match("{value}"))?c.replace("{value}",this.formatAxisValue(e[t],l,c,void 0,n)):this.formatAxisValue(e[t],l,c,void 0,n);else if(s&&!sf.base.isNullOrUndefined(e[t])){l=c&&null!==c.match("{value}");var g=o.dataLabelFormat?parseInt(o.dataLabelFormat.substring(1)):"Logarithmic"===h.valueType?10:2;a=this.formatAxisValue(e[t],l,c,g,n),a=this.getGlobalizedNumber(a,n.numberValuePairs),r=l?c.replace("{value}",a):a}else r=this.getGlobalizedNumber(e[t].toString(),n.numberValuePairs);return r},firstToLowerCase:function(e){return e.substr(0,1).toLowerCase()+e.substr(1)},formatAxisValue:function(e,t,i,s,o){void 0===s&&(s=2);var n=Number(e);return(new sf.base.Internationalization).getNumberFormat({format:t?"":i,useGrouping:o.userInteractionBase.useGrouping,minimumFractionDigits:2,maximumFractionDigits:s>20?20:s})(n)},findHeader:function(e,t){var i=t.tooltipBase.header;return sf.base.isNullOrUndefined(i)?"":""!==(i=this.parseTemplate(e.point,e.series,i,t)).replace(/<b>/g,"").replace(/<\/b>/g,"").trim()?i:""},getFormat:function(e,t){if(t.tooltipFormat)return t.tooltipFormat;if(!t.tooltipFormat&&e.tooltipBase.tooltipModule.format)return e.tooltipBase.tooltipModule.format;return"${point.x} : <b>${point.y}</b>"},getTooltipText:function(e,t){return this.parseTemplate(e.point,e.series,this.getFormat(t,e.series),t)},render3DSeriesTooltip:function(e,t,i){var s=this.get3DData(e,i);e.tooltipBase.currentPoints=[],!this.find3DData(s,e.tooltipBase.previousPoints.length>0?e.tooltipBase.previousPoints[0]:null)||(!e.tooltipBase.previousPoints[0]||e.tooltipBase.previousPoints[0].point.index===s.point.index&&e.tooltipBase.previousPoints[0].series.index===s.series.index&&e.isRemove)&&e.tooltipBase.previousPoints[0]?!s.point&&e.isRemove&&"Move"===e.tooltipBase.tooltipModule.fadeOutMode&&(this.removeTooltip3D(e.tooltipBase.tooltipModule.fadeOutDuration,e),this.removeBlurEffect(e),e.isRemove=!1):(this.stopAnimation(e.tooltipBase.toolTipInterval),this.push3DData(s,e)&&this.trigger3DTooltipRender(s,t,this.getTooltipText(s,e),this.findHeader(s,e),e))},trigger3DTooltipRender:function(e,t,i,s,o){o.tooltipBase.argsData={data:{pointX:e.point.x?e.point.x.toString():"",pointY:e.point.y?e.point.y.toString():"",seriesIndex:e.series.index,seriesName:e.series.name,pointIndex:e.point.index,pointText:e.point.text},headerText:s,point:e.point,series:{},text:i},o.tooltipBase.tooltipEventCalled?o.dotnetref.invokeMethodAsync("Chart3DTooltipEventTriggered",o.tooltipBase.argsData):this.series3DTooltip(o,e,t)},series3DTooltip:function(e,t,i){if(null!=t){e.tooltipBase.header=e.tooltipBase.argsData.headerText,e.tooltipBase.formattedText=e.tooltipBase.formattedText.concat(e.tooltipBase.argsData.text);var s=new o(0,0);if(null!=e.tooltipBase.tooltipModule.template&&e.tooltipBase.currentPoints.length>0){e.tooltipBase.tooltipTempList=[];var n=t.point,r={x:sf.base.isNullOrUndefined(n.x)?"":this.formatPointValue(n,"x",!0,!1,t.series,e),y:sf.base.isNullOrUndefined(n.y)?"":this.formatPointValue(n,"y",!1,!0,t.series,e),text:n.text.toString(),pointX:n.x?n.x.toString():"",pointY:n.y?n.y.toString():"",seriesIndex:t.series.index,seriesName:t.series.name,pointIndex:t.point.index,pointText:t.point.text};e.tooltipBase.tooltipTempList.push(r);var l=this.getSymbolLocation(t,e),a=t.series.isRectSeries&&t.point&&parseInt(n.y?n.y.toString():"")<0,h=e.userInteractionBase.isInverted&&a;sf.base.isNullOrUndefined(e.tooltipBase.tooltipElementSize)&&(e.tooltipBase.tooltipElementSize=this.get3DTemplateSize(e.element.id+"_tooltip"));var d=sf.base.isNullOrUndefined(e.tooltipBase.tooltipElementSize.width)?0:e.tooltipBase.tooltipElementSize.width,c=sf.base.isNullOrUndefined(e.tooltipBase.tooltipElementSize.height)?0:e.tooltipBase.tooltipElementSize.height,u=this.get3DTemplateLocation(e.userInteractionBase.seriesClipRect,l,d,c,0,s,h,a),g=40;u.x=u.x>e.userInteractionBase.seriesClipRect.width?e.userInteractionBase.seriesClipRect.width-g:u.x,e.dotnetref.invokeMethodAsync("Chart3DSetTooltipTemplateElementSizeAsync",u.x,u.y,e.tooltipBase.tooltipTempList),this.updatePrevious3DPoint(e,[])}else{var p=e.userInteractionBase.chartBorderWidth;g=3;this.create3DTooltipRenderer(e,i,this.getSymbolLocation(t,e),s,t.point,e.tooltipBase.tooltipModule.enableMarker?["Circle"]:[],0,new sf.svgbase.Rect(p,p,e.userInteractionBase.availableSize.width-g-2*p,e.userInteractionBase.availableSize.height-g-2*p),null,this.getTemplateText(t,e),"")}this.blurEffect(e.visible3DSeries,t.series,e)}e.isRemove=!0},getSymbolLocation:function(e,t){var i,s,n=document.getElementById(t.element.id+"_svg").getBoundingClientRect();if(i="Cylinder"===e.series.columnFacet?document.querySelectorAll('[id*="'+t.element.id+"-svg-"+(-1===e.series.type.indexOf("Column")?"0":"1")+"-region-series-"+e.series.index+"-point-"+e.point.index+'"]'):document.querySelectorAll('[id*="'+t.element.id+"-svg-"+(-1===e.series.type.indexOf("Column")?"5":"2")+"-region-series-"+e.series.index+"-point-"+e.point.index+'"]')){if(1===i.length)s=i[0].getBoundingClientRect();else for(var r=0;r<i.length;r++){var l=i[r];if(-1!==l.id.indexOf("-"+e.point.index+"-back-front")||-1!==l.id.indexOf("-"+e.point.index+"-front-back")){s=l.getBoundingClientRect();break}}s||0===i.length||(s=i[0].getBoundingClientRect())}return new o(0!==t.tooltipBase.tooltipModule.location.x?t.tooltipBase.tooltipModule.location.x:s.left-n.left+s.width/2,0!==t.tooltipBase.tooltipModule.location.y?t.tooltipBase.tooltipModule.location.y:s.top-n.top+s.height/2)},findPalette:function(e){for(var t=[],i=0,s=e.tooltipBase.currentPoints;i<s.length;i++){var o=s[i];t.push(this.findColor(o,o.series))}return t},findColor:function(e,t){return t.isRectSeries||""!=e.point.interior?e.point.interior:t.interior},updatePrevious3DPoint:function(e,t){t&&t.length>0&&(e.tooltipBase.currentPoints=e.tooltipBase.currentPoints.concat(t)),e.tooltipBase.previousPoints=[],e.tooltipBase.previousPoints=e.tooltipBase.previousPoints.concat(e.tooltipBase.currentPoints)},render3DTooltip:function(e,t,i){var s=document.getElementById(t+"_svg"),o=e,n=i;!(s&&parseInt(s.getAttribute("opacity"),10)>0)&&!sf.base.isNullOrUndefined(n)?(n.tooltip=new sf.svgbase.Tooltip(o),n.tooltip.enableRTL=o.enableRTL,n.tooltip.appendTo("#"+t)):sf.base.isNullOrUndefined(n.tooltip)||(n.tooltip.location=new sf.svgbase.TooltipLocation(o.location.x,o.location.y),n.tooltip.content=o.content,n.tooltip.header=o.header,n.tooltip.offset=o.offset,n.tooltip.palette=o.palette,n.tooltip.shapes=o.shapes,n.tooltip.data=o.data,n.tooltip.template=o.template,n.tooltip.textStyle.color=o.textStyle.color||n.tooltip.textStyle.color,n.tooltip.textStyle.fontFamily=o.textStyle.fontFamily||n.tooltip.textStyle.fontFamily,n.tooltip.textStyle.fontStyle=o.textStyle.fontStyle||n.tooltip.textStyle.fontStyle,n.tooltip.textStyle.fontWeight=o.textStyle.fontWeight||n.tooltip.textStyle.fontWeight,n.tooltip.textStyle.opacity=o.textStyle.opacity||n.tooltip.textStyle.opacity,n.tooltip.textStyle.size=o.textStyle.size||n.tooltip.textStyle.size,n.tooltip.isNegative=o.isNegative,n.tooltip.clipBounds=new sf.svgbase.TooltipLocation(o.clipBounds.x,o.clipBounds.y),n.tooltip.arrowPadding=o.arrowPadding,n.tooltip.dataBind())},create3DTooltipRenderer:function(e,t,i,s,n,r,l,a,h,d,c){var u,g=e.tooltipBase.currentPoints[0].series.isRectSeries,p=e.tooltipBase.tooltipModule.border;t&&null!=i&&(u=new sf.svgbase.Tooltip({opacity:e.tooltipBase.tooltipModule.opacity,header:e.tooltipBase.header,content:e.tooltipBase.formattedText,fill:e.tooltipBase.tooltipModule.fill,border:p,enableAnimation:e.tooltipBase.tooltipModule.enableAnimation,location:null!=i?new o(i.x+e.secondaryElementOffset.left,i.y+e.secondaryElementOffset.top):null,shared:!1,crosshair:!1,shapes:r,clipBounds:s,areaBounds:new sf.svgbase.Rect(a.x+e.secondaryElementOffset.left,a.y+e.secondaryElementOffset.top,a.width,a.height),palette:this.findPalette(e),controlName:"Chart",controlInstance:e,template:c||e.tooltipBase.tooltipModule.template,data:d,theme:e.userInteractionBase.theme,offset:l,textStyle:e.tooltipBase.tooltipModule.textStyle,isNegative:g&&Number(n.y)<0,inverted:e.userInteractionBase.isInverted&&g,arrowPadding:e.tooltipBase.formattedText.length>1?0:7,availableSize:e.userInteractionBase.availableSize,duration:e.tooltipBase.tooltipDuration,isCanvas:!1,rx:4,ry:4,isTextWrap:e.tooltipBase.tooltipModule.enableTextWrap,enableRTL:e.userInteractionBase.enableRTL})),null!=u&&(this.updatePrevious3DPoint(e,h),this.render3DTooltip(u,e.element.id+"_tooltip",e))},getTemplateText:function(e,t){if(this.template){var i=sf.base.extend({},e.point);return i.x=this.formatPointValue(e.point,"x",!0,!1,e.series,t),i.y=this.formatPointValue(e.point,"y",!1,!0,e.series,t),i}return e.point},blurEffect:function(e,t,i){if(!i.options.enableHighlight||i.options.enableLegendHighlight&&"None"===i.userInteractionBase.highlightMode){for(var s=[],o=function(e){if(e.visible&&e.index!==t.index)document.querySelectorAll('[id*="region-series-'+e.index+'"]').forEach((function(t){var i=t.id.match(/(\d+)/g);parseInt(i[i.length-2].toString(),10)===e.index&&s.push(t)}));else if(e.visible)for(var o=document.querySelectorAll('[id*="region-series-'+e.index+'"]'),r=0;r<o.length;r++){var l=o[r],a=l.getAttribute("class")||"";a.indexOf(i.element.id+"_ej2_tooltipDeselected")>-1&&l.setAttribute("class",a.replace(i.element.id+"_ej2_tooltipDeselected","")),n.stopElementAnimation(l,e)}},n=this,r=0,l=e;r<l.length;r++){o(l[r])}for(var a=0;a<s.length;a++)if(s[a]){var h=s[a].getAttribute("class")||"";-1===(h+=""!==h?" ":"").indexOf("_selection_")&&-1===h.indexOf(i.element.id+"_ej2_tooltipDeselected")&&s[a].setAttribute("class",h+i.element.id+"_ej2_tooltipDeselected")}}},removeBlurEffect:function(e){if(!e.options.enableHighlight||e.options.enableLegendHighlight&&"None"===e.userInteractionBase.highlightMode)for(var t=document.getElementsByClassName(e.element.id+"_ej2_tooltipDeselected");t.length>0;){var i=t[0],s=i.getAttribute("class")||"";if(s.indexOf(e.element.id+"_ej2_tooltipDeselected")>-1){i.setAttribute("class",s.replace(e.element.id+"_ej2_tooltipDeselected",""));var o=parseFloat(i.id.split("-series-")[1].split("-point-")[0]);this.highlightAnimation(i,e.visible3DSeries[o],700,.2)}}},stopElementAnimation:function(e,t){var i=parseFloat(t.opacity.toString());e.getAttribute("e-animate")&&sf.base.Animation.stop(e),e.setAttribute("opacity",i.toString())},highlightAnimation:function(e,t,i,s){var o=parseFloat(t.opacity.toString());o&&new sf.base.Animation({}).animate(e,{duration:i,progress:function(t){e.style.animation="";var i=t.timeStamp/t.duration,n=s+(o-s)*i;e.setAttribute("opacity",n.toString())},end:function(){e.setAttribute("opacity",o.toString())}})},removeTooltip3D:function(e,t){var i=this,s=this.getElement(t.element.id+"_tooltip");this.stopAnimation(t.tooltipBase.toolTipInterval),s&&null==t.tooltipBase.tooltipModule.template&&t.tooltipBase.previousPoints.length>0?t.tooltipBase.toolTipInterval=+setTimeout((function(){t.tooltipBase.tooltipModule&&i.fadeOut3D(t.dataId)}),e):s&&null!=t.tooltipBase.tooltipModule.template&&t.tooltipBase.previousPoints.length>0&&(t.tooltipBase.toolTipInterval=+setTimeout((function(){null!=t.tooltipBase.tooltipModule.template&&(t.dotnetref.invokeMethodAsync("Chart3DRemoveTemplateTooltip"),t.tooltipBase.valueX=0,t.tooltipBase.valueY=0,t.tooltipBase.currentPoints=[],t.tooltipBase.previousPoints=[])}),e))},fadeOut3D:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||!sf.base.isNullOrUndefined(t)&&sf.base.isNullOrUndefined(t.tooltip)||(this.removeTooltipCommentElement(t),t.tooltipBase.valueX=0,t.tooltipBase.valueY=0,t.tooltipBase.currentPoints=[],t.tooltipBase.previousPoints=[],t.tooltip.fadeOut())},removeTooltipCommentElement:function(e){var t=e.tooltip.element?e.tooltip.element.id:null,i=t?document.getElementById(t):null;if(i&&!e.isRemoveCommentElement&&i.childNodes.length>1){for(var s=i.childNodes,o=[],n=0;n<s.length;n++)(s[n].nodeName.match("#comment")||s[n].nodeName.match("#text"))&&o.push(s[n]);for(var r=0,l=o;r<l.length;r++){var a=l[r];sf.base.remove(a),e.isRemoveCommentElement=!0}}},get3DTemplateLocation:function(e,t,i,s,n,r,l,a){var h=new o(t.x,t.y),d=i+10,c=s+10,u=r.x,g=r.y,p=e.x,f=e.y;return l?(((h=new o(h.x+u+n,h.y+g-s/2)).x+d+12>p+e.width||a)&&(h.x=(t.x>e.width?e.width:t.x)+u-n-(d+12)),h.x<p&&(h.x=(t.x<0?0:t.x)+u+n),h.y<=f&&(h.y=f),h.y+c>=f+e.height&&(h.y-=h.y+c-(f+e.height))):(((h=new o(h.x+u-i/2,h.y+g-s-12-n)).y<f||a)&&(h.y=(t.y<0?0:t.y)+g+n),h.y+c+12>f+e.height&&(h.y=(t.y>e.height?e.height:t.y)+g-s-12-n),h.x<p&&(h.x=p),h.x+d>p+e.width&&(h.x-=h.x+d-(p+e.width))),{x:h.x,y:h.y}},get3DTemplateSize:function(e){var t=this.getElement(e);return t?{width:t.offsetWidth,height:t.offsetHeight}:null},set3DTooltipArgsData:function(e,t,i){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||(s.tooltipBase.formattedText=[],s.tooltipBase.argsData.headerText=t,s.tooltipBase.argsData.text=i,this.series3DTooltip(s,s.tooltipBase.currentPoints[0],!0))},indexFinder:function(e){var t=["NaN","NaN"];return e.indexOf("-point-")>-1?t=e.split("-series-")[1].split("-point-"):e.indexOf("-border-")>-1?t[0]=e.split("-border-")[1]:e.indexOf("-series-")>-1?t[0]=e.split("-series-")[1]:e.indexOf("_chart_legend_shape_")>-1&&((t=e.split("_chart_legend_shape_"))[0]=t[1]),new n(parseInt(t[0],10),parseInt(t[1],10))},toEquals:function(e,t,i,s){return(e.series===t.series||"Cluster"===s.currentMode&&!i)&&(i||e.point===t.point)},addOrRemoveIndex:function(e,t,i,s){for(var o=0;o<e.length;o++)this.toEquals(e[o],t,i.isSeriesMode,i)&&(e.splice(o,1),o--);s&&e.push(t)},addSvgClass:function(e,t){var i=e.getAttribute("class")||"";-1===(i+=""!==i?" ":"").indexOf(t)&&e.setAttribute("class",i+t)},getSeriesElements:function(e){var t=[];e.visible&&document.querySelectorAll('[id*="region-series-'+e.index+'"]').forEach((function(e){t.push(e)}));return t},getElementByIndex:function(e){var t=[];return document.querySelectorAll('[id*="-region-series-'+e.series+"-point-"+e.point+'"]').forEach((function(i){var s=i.id.match(/(\d+)/g);parseInt(s[s.length-1].toString(),10)===e.point&&t.push(i)})),t},generateStyle:function(e,t){return e?t+"_series_"+e.index:"undefined"},getClusterElements:function(e,t){for(var i=[],s=0,o=e.visible3DSeries;s<o.length;s++){var r=o[s];if(r.visible){t=new n(r.index,t.point);for(var l=this.getElementByIndex(t),a=0;a<l.length;a++)i.push(l[a])}}return i},findElements:function(e,t,i){return e.isSeriesMode?this.getSeriesElements(t):"Cluster"===e.currentMode?this.getClusterElements(e,i):this.getElementByIndex(i)},hexToValue:function(e){var t;return!0===/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/.test(e)?(t=/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/.exec(e))?{red:parseInt(t[1],10),green:parseInt(t[2],10),blue:parseInt(t[3],10),alpha:t[4]}:null:(t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e))?{red:parseInt(t[1],16),green:parseInt(t[2],16),blue:parseInt(t[3],16)}:null},hexColor:function(e){var t=e.red,i=e.green,s=e.blue;if(e.alpha)return"rgba("+t.toString()+","+i.toString()+","+s.toString()+","+e.alpha+")";var o=[t.toString(16),i.toString(16),s.toString(16)];return o.forEach((function(e,t){1===e.length&&(o[t]="0"+e)})),"#"+o.join("").toUpperCase()},applyZLight:function(e){var t=this.hexToValue(e);return t.red=parseInt((.9*t.red).toString(),10),t.green=parseInt((.9*t.green).toString(),10),t.blue=parseInt((.9*t.blue).toString(),10),this.hexColor(t)},applyXLight:function(e){var t=this.hexToValue(e);return t.red=parseInt((.7*t.red).toString(),10),t.green=parseInt((.7*t.green).toString(),10),t.blue=parseInt((.7*t.blue).toString(),10),this.hexColor(t)},reverseXLight:function(e){var t=this.hexToValue(e);return t.red=Math.min(255,Math.round(t.red/.7)),t.green=Math.min(255,Math.round(t.green/.7)),t.blue=Math.min(255,Math.round(t.blue/.7)),this.hexColor(t)},reverseZLight:function(e){var t=this.hexToValue(e);return t.red=Math.min(255,Math.round(t.red/.9)),t.green=Math.min(255,Math.round(t.green/.9)),t.blue=Math.min(255,Math.round(t.blue/.9)),this.hexColor(t)},getSelectionClass:function(e,t){return this.generateStyle(t.visible3DSeries[this.indexFinder(e).series],t.styleId)},removeSvgClass:function(e,t){var i=e.getAttribute("class")||"";i.indexOf(t)>-1&&e.setAttribute("class",i.replace(t,""))},removeStyles:function(e,t){for(var i=0,s=e;i<s.length;i++){var o=s[i];if(o&&(this.removeSvgClass(o,this.getSelectionClass(o.id,t)),"None"===t.highlightPattern&&""!==t.highlightColor&&!sf.base.isNullOrUndefined(t.highlightColor)&&"transparent"!==t.highlightColor)){var n=t.visible3DSeries[this.indexFinder(o.id).series].interior;"ZLight"===o.getAttribute("name")&&(n=this.applyZLight(n)),"XLight"===o.getAttribute("name")&&(n=this.applyXLight(n)),o.setAttribute("fill",n)}}},removeMultiSelectElements:function(e,t,i,s){for(var o,n=0;n<t.length;n++)o=s[t[n].series],(e.isSeriesMode&&!this.toEquals(t[n],i,e.isSeriesMode,e)||"Cluster"===e.currentMode&&!this.toEquals(t[n],i,!1,e)||!e.isSeriesMode&&this.toEquals(t[n],i,!0,e)&&!this.toEquals(t[n],i,!1,e))&&(this.removeStyles(this.findElements(e,o,t[n]),e),t.splice(n,1),n--)},applyStyles:function(e,t){for(var i=0,s=e;i<s.length;i++){var o=s[i];if(o){if(this.removeSvgClass(o.parentNode,t.unSelected),this.removeSvgClass(o,t.unSelected),"fill"===t.visible3DSeries[0].pointColorMapping){var n=this.getSelectionClass(o.id,t),r=n.indexOf("highlight")>-1?parseInt(n.split(t.element.id+"_ej2_chart_highlight_series_")[1],10):parseInt(n.split(t.element.id+"_ej2_chart_selection_series_")[1],10),l=t.styleId.indexOf("highlight")>0?t.highlightPattern:t.selectionPattern,a=void 0;if((n.indexOf("highlight")>-1||n.indexOf("selection")>-1)&&(a=document.getElementById(t.element.id+"_"+l+"_Selection_"+r)),-1===o.id.indexOf("legend")&&-1===o.id.indexOf("Group")&&null!=a)for(var h=1;h<a.children.length;h++)a.children[h].setAttribute("fill",o.getAttribute("fill")),a.children[h].setAttribute("stroke",o.getAttribute("fill"))}if(this.addSvgClass(o,this.getSelectionClass(o.id,t)),t.tooltipBase.tooltipModule.enable&&this.getSelectionClass(o.id,t).indexOf("highlight")>0){r=parseFloat(o.id.split("-series-")[1].split("-point-")[0]);this.stopElementAnimation(o,t.visible3DSeries[r])}t.styleId.indexOf("highlight")>0&&""!==t.highlightColor&&!sf.base.isNullOrUndefined(t.highlightColor)&&"None"===t.highlightPattern&&"transparent"!==t.highlightColor&&o.setAttribute("fill",t.highlightColor)}}},selection:function(e,t,i){i=i.filter((function(e){return null!==e})),e.isMultiSelect||-1!==e.styleId.indexOf("highlight")||"None"===e.selectionMode||this.removeMultiSelectElements(e,e.selectedDataIndexes,t,e.visible3DSeries);var s=t.series;if(!sf.base.isNullOrUndefined(i[0])){e.visible3DSeries[s].isRectSeries&&i[0].id&&document.getElementById(i[0].id+"_Symbol")&&i.push(this.getElement(i[0].id+"_Symbol"));var o=void 0,n=i[0]&&(i[0].getAttribute("class")||"");i[0].parentNode&&i[0].parentNode.getAttribute("class");i[0]&&n.indexOf(this.getSelectionClass(i[0].id,e))>-1?this.removeStyles(i,e):(e.previousSelectedEle="None"!==e.highlightMode||e.options.enableLegendHighlight?i:[],this.applyStyles(i,e),o=!0),e.styleId.indexOf("highlight")>0&&("None"!==e.highlightMode||e.options.enableLegendHighlight)?this.addOrRemoveIndex(e.highlightDataIndexes,t,e,o):this.addOrRemoveIndex(e.selectedDataIndexes,t,e,o)}},selectionComplete:function(e,t,i){var s,o,n,r,l,a=[];if("Cluster"===i)for(var h=0,d=e.visible3DSeries;h<d.length;h++){if((g=d[h]).visible)for(var c=0;c<e.selectedDataIndexes.length;c++)o=e.isMultiSelect?e.selectedDataIndexes[c].point:t.point,n=g.index,s=g.points,isNaN(o)||(r=s[o].yValue,l=s[o].xValue,"Category"===e.chart3DAxes.filter((function(e){return"PrimaryXAxis"==e.name}))[0].valueType?l=s[o].x.toLocaleString():"DateTime"===e.chart3DAxes.filter((function(e){return"PrimaryXAxis"==e.name}))[0].valueType&&(l=new Date(s[o].xValue)),a.push({x:l,y:r,seriesIndex:n,pointIndex:o}))}else if("Series"===i)if(e.isMultiSelect)for(c=0;c<e.selectedDataIndexes.length;c++)n=e.selectedDataIndexes[c].series,e.selectedDataIndexes.length>0&&a.push({seriesIndex:n});else n=e.selectedDataIndexes.length>0?e.selectedDataIndexes[0].series:e.highlightDataIndexes&&e.highlightDataIndexes.length>0?e.highlightDataIndexes[0].series:0,(e.selectedDataIndexes.length>0||e.highlightDataIndexes&&e.highlightDataIndexes.length>0)&&a.push({seriesIndex:n});else if("Point"===i){var u=[];u=e.styleId.indexOf("highlight")>-1?e.highlightDataIndexes:e.selectedDataIndexes;for(c=0;c<u.length;c++){var g;o=u[c].point,n=u[c].series,s=(g=e.visible3DSeries[n]).points,isNaN(o)||(l=s[o].xValue,r=s[o].yValue,"Category"===e.chart3DAxes.filter((function(e){return"PrimaryXAxis"==e.name}))[0].valueType?l=s[o].x.toLocaleString():"DateTime"===e.chart3DAxes.filter((function(e){return"PrimaryXAxis"==e.name}))[0].valueType&&(l=new Date(s[o].xValue)),a.push({x:l,y:r,seriesIndex:n,pointIndex:o}))}}e.dotnetref.invokeMethodAsync("Chart3DSelectionChange",a)},clusterSelection:function(e,t){this.selection(e,t,this.getClusterElements(e,new n(t.series,t.point)))},checkVisibility:function(e,t){if(!e)return!1;for(var i=!1,s=[],o=0,n=e;o<n.length;o++){var r=n[o];-1===s.indexOf(r.series)&&s.push(r.series)}for(var l=0,a=s;l<a.length;l++){r=a[l];if(t&&t.visible3DSeries[r].visible){i=!0;break}}return i},checkSelectionElements:function(e,t,i,s,o,n,r){void 0===n&&(n=0),void 0===r&&(r="#D3D3D3");var l,a,h,d=e.isSeriesMode?t||[t]:t;"None"===e.selectionMode||"None"===e.highlightMode&&!e.options.enableLegendHighlight||(d=t);for(var c=t,u=0;u<d.length;u++)l=d[u].getAttribute("class")||"",a=d[u].parentNode.getAttribute("class")||"","None"===e.selectionMode||"None"===e.highlightMode&&!e.options.enableLegendHighlight||(i=l.indexOf("selection")>0||l.indexOf("highlight")>0?l:i),-1===l.indexOf(i)&&-1===a.indexOf(i)&&s?this.addSvgClass(d[u],e.unSelected):(c=d[u],-1!==l.indexOf(e.unSelected)&&e.tooltipBase.tooltipModule.enable&&i.indexOf("highlight")>0&&this.highlightAnimation(d[u],e.visible3DSeries[n],700,.3),this.removeSvgClass(d[u],e.unSelected),this.removeSvgClass(d[u].parentNode,e.unSelected));(h=this.getElement(e.element.id+"_chart_legend_shape_"+n))&&(h.hasAttribute("class")&&(this.removeSvgClass(h,h.getAttribute("class")),sf.base.isNullOrUndefined(e.highlightColor&&""!==e.highlightColor)||e.options.enableLegendHighlight||(h.setAttribute("stroke",r),"None"===e.highlightPattern&&h.setAttribute("fill",r))),c.length>0?(l=c[0].getAttribute("class"),a=c[0].parentNode.getAttribute("class")||""):c&&(l=c.getAttribute("class")||"",a=c.parentNode.getAttribute("class")||""),-1===l.indexOf(i)&&-1===a.indexOf(i)&&s?(this.addSvgClass(h,"None"===e.highlightMode&&e.options.enableLegendHighlight?i:e.unSelected),this.removeSvgClass(h,i),""===e.highlightColor||sf.base.isNullOrUndefined(e.highlightColor)||(h.setAttribute("stroke",e.visible3DSeries[n].interior),"None"===e.highlightPattern&&h.setAttribute("fill",e.visible3DSeries[n].interior))):(this.removeSvgClass(h,e.unSelected),sf.base.isNullOrUndefined(e.highlightColor)||""===e.highlightColor||(h.setAttribute("stroke",e.visible3DSeries[n].interior),"None"===e.highlightPattern&&h.setAttribute("fill",e.visible3DSeries[n].interior)),""===l&&""===a||"EJ2-Trackball"===l.trim()?this.removeSvgClass(h,i):(this.addSvgClass(h,i),i.indexOf("highlight")>0&&""!==e.highlightColor&&"transparent"!==e.highlightColor&&!sf.base.isNullOrUndefined(e.highlightColor)&&(h.setAttribute("stroke",e.highlightColor),e.styleId.indexOf("highlight")>0&&"None"===e.highlightPattern&&h.setAttribute("fill",e.highlightColor)))))},selectionBlurEffect:function(e,t,i,s){void 0===s&&(s=!1);for(var o=this.checkVisibility(e.highlightDataIndexes,e)||this.checkVisibility(e.selectedDataIndexes,e),n=function(i){var n=i.index,l=e.visible3DSeries[i.index].interior,a=[];if(i.visible){var h=document.querySelectorAll('[id*="region-series-'+i.index+'"]');h.length>0&&(h.forEach((function(e){a.push(e)})),r.checkSelectionElements(e,a,r.generateStyle(i,e.styleId),o,s,n,l),sf.base.isNullOrUndefined(r.getElement(t+"SymbolGroup"+i.index))||r.checkSelectionElements(e,a,r.generateStyle(i,e.styleId),o,s,n,l))}},r=this,l=0,a=i;l<a.length;l++){n(a[l])}},removeSelection:function(e,t,i,s,o){if(i.length>0){for(var r=[],l=0;l<i.length;l++)r.push(i[l]);this.removeStyles(r,e),e.isSeriesMode=!0,this.addOrRemoveIndex(e.selectedDataIndexes,new n(t,NaN),e);for(var a=0,h=e.visible3DSeries;a<h.length;a++){var d=h[a];if(s=this.generateStyle(d,e.styleId),document.querySelectorAll("."+s).length>0){for(var c=0,u=r;c<u.length;c++){var g=u[c];this.checkSelectionElements(e,g,s,!0,!0,t,"")}o=!1;break}}o&&(this.isSeriesMode="Series"===e.selectionMode,this.selectionBlurEffect(e,e.element.id,e.visible3DSeries,null))}},performSelection:function(e,t,i){switch(t.isSeriesMode="Series"===t.currentMode,t.currentMode){case"Series":this.selection(t,e,this.getSeriesElements(t.visible3DSeries[e.series])),this.selectionComplete(t,e,t.currentMode),this.selectionBlurEffect(t,t.element.id,t.visible3DSeries,!1);break;case"Point":!isNaN(e.point)&&i&&(this.selection(t,e,this.getElementByIndex(e)),this.selectionComplete(t,e,t.currentMode),this.selectionBlurEffect(t,t.element.id,t.visible3DSeries,!1));break;case"Cluster":isNaN(e.point)||(this.clusterSelection(t,e),this.selectionComplete(t,e,t.currentMode),this.selectionBlurEffect(t,t.element.id,t.visible3DSeries,!1))}},isAlreadySelected:function(e,t,i,s){if("click"===i?(e.currentMode=e.selectionMode,e.styleId=e.element.id+"_ej2_chart_selection"):"mousemove"!==i&&"pointermove"!==i||(e.currentMode=e.highlightMode,e.highlightDataIndexes=[],e.styleId=e.element.id+"_ej2_chart_highlight"),"None"!==e.highlightMode&&"None"===e.selectionMode&&"click"===i)return!1;if(("None"!==e.highlightMode||e.options.enableLegendHighlight)&&e.previousSelectedEle&&e.previousSelectedEle[0]){var o=void 0,n=t.nodeName;t.parentNode&&(o=("path"===n||"shape"===n)&&t.id.indexOf("region")>1);for(var r=function(t){var n;e.previousSelectedEle[t].hasAttribute("class")&&(e.previousSelectedEle[t].getAttribute("class").indexOf("highlight")>-1&&(o||"click"===i)?(e.previousSelectedEle[t].classList.forEach((function(e){e.indexOf("selection")>-1&&(n=e)})),e.previousSelectedEle[t].removeAttribute("class"),n&&l.addSvgClass(e.previousSelectedEle[t],n),e.previousSelectedEle[t].classList.remove(e.styleId+"_series_"+s.series),""===e.highlightColor||sf.base.isNullOrUndefined(e.highlightColor)||"None"!==e.highlightPattern||e.previousSelectedEle[t].setAttribute("fill",e.visible3DSeries[l.indexFinder(e.previousSelectedEle[t].id).series].interior),l.addOrRemoveIndex(e.highlightDataIndexes,l.indexFinder(e.previousSelectedEle[t].id),e)):!o&&e.previousSelectedEle[t].getAttribute("class").indexOf("highlight")>-1&&l.performSelection(l.indexFinder(e.previousSelectedEle[t].id),e,e.previousSelectedEle[t]))},l=this,a=0;a<e.previousSelectedEle.length;a++)r(a)}return!0},legendSelection:function(e,t,i,s){if("mousemove"===s){if(i.id.indexOf("text")>1&&(i=this.getElement(i.id.replace("text","shape"))),i.id.indexOf("marker")>1&&(i=this.getElement(i.id.replace("_marker",""))),i.id.indexOf("g")>1&&(i=this.getElement(i.id.replace("_g_","_shape_"))),i.hasAttribute("class")&&(i.getAttribute("class").indexOf("highlight")>-1||i.getAttribute("class").indexOf("selection")>-1))return;e.currentMode=e.highlightMode}else"click"===s&&(i.id.indexOf("text")>1&&(i=this.getElement(i.id.replace("text","shape"))),i.id.indexOf("g")>1&&(i=this.getElement(i.id.replace("_g_","_shape_"))));var o=this.indexFinder(i.id);if(this.isAlreadySelected(e,i,s,o)){var r=this.generateStyle(e.visible3DSeries[t],e.styleId),l=document.querySelectorAll("."+r);e.isSeriesMode="Series"===e.currentMode;if(l.length>0)this.removeSelection(e,t,l,r,!0);else{for(var a=0,h=e.visible3DSeries;a<h.length;a++){var d=h[a];d.index===t||e.isMultiSelect||(r=this.generateStyle(e.visible3DSeries[d.index],e.styleId),l=document.querySelectorAll("."+r),this.removeSelection(e,t,l,r,!0))}var c=[];(c="Point"===e.userInteractionBase.legendMode?this.getElementByIndex(o):this.getSeriesElements(e.visible3DSeries[t])).length>0&&(this.checkSelectionElements(e,c,r,!1,!0,t,""),e.isSeriesMode=!0,this.selection(e,new n(o.series,NaN),c),e.isSeriesMode="Series"===e.selectionMode,this.selectionBlurEffect(e,e.element.id,e.visible3DSeries,!0))}}},calculateSelectedElements:function(e,t,i){if(!sf.base.isNullOrUndefined(t)&&!("None"===e.selectionMode&&"None"===e.highlightMode||t.id&&-1===t.id.indexOf(e.element.id+"-"))&&("mousemove"!==i&&"pointermove"!==i||!t.hasAttribute("class")||!(t.getAttribute("class").indexOf("highlight")>-1||t.getAttribute("class").indexOf("selection")>-1))&&(this.isAlreadySelected(e,t,i,this.indexFinder(t.id)),t.id&&t.id.indexOf("-series-")>-1&&-1===t.id.indexOf("_Text_"))){this.performSelection(this.indexFinder(t.id),e,t)}},removeLegendHighlightStyles:function(e){var t;e.highlightDataIndexes=[];for(var i=0;i<e.visible3DSeries.length;i++)if(t=document.getElementsByClassName(this.generateStyle(e.visible3DSeries[i],e.styleId)),0===e.selectedDataIndexes.length){for(t=document.getElementsByClassName(this.generateStyle(e.visible3DSeries[i],e.styleId));t.length>0;){if((o=t[0])&&(this.removeSvgClass(o,o.getAttribute("class")),"None"===e.highlightPattern&&""!==e.highlightColor&&!sf.base.isNullOrUndefined(e.highlightColor)&&"transparent"!==e.highlightColor)){var s=e.visible3DSeries[i].interior;"ZLight"===o.getAttribute("name")&&(s=this.applyZLight(s)),"XLight"===o.getAttribute("name")&&(s=this.applyXLight(s)),-1!==o.id.indexOf("_chart_legend_shape")&&o.getAttribute("stroke")&&o.setAttribute("stroke",s),o.setAttribute("fill",s)}}for(t=document.getElementsByClassName(e.unSelected);t.length>0;){(o=t[0])&&(this.removeSvgClass(o,o.getAttribute("class")),e.tooltipBase.tooltipModule.enable&&this.generateStyle(e.visible3DSeries[i],e.styleId).indexOf("highlight")>-1&&this.highlightAnimation(o,e.visible3DSeries[i],700,.3))}}else for(t=document.getElementsByClassName(this.generateStyle(e.visible3DSeries[i],e.styleId));t.length>0;){var o;(o=t[0])&&(this.removeSvgClass(o,o.getAttribute("class")),this.addSvgClass(o,e.unSelected))}},highlightChart3D:function(e,t,i){if(e.rotateActivate||"None"===e.highlightMode&&!e.options.enableLegendHighlight);else if(!sf.base.isNullOrUndefined(t)){if(t.id.indexOf("_legend_text")>1&&(t=this.getElement(t.id.replace("text","shape"))),t.hasAttribute("class")&&(t.getAttribute("class").indexOf("highlight")>-1||t.getAttribute("class").indexOf("selection")>-1))return;this.calculateSelectedElements(e,t,i),e.highlightDataIndexes&&e.highlightDataIndexes.length>0&&-1===t.id.indexOf("_chart_legend_")&&-1===t.id.indexOf("-series-")&&this.removeLegendHighlightStyles(e)}},select3DDataIndex:function(e,t){var i=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(i))for(var s=0,o=t;s<o.length;s++){var n=o[s],r=this.getElementByIndex(n)[0];this.isAlreadySelected(i,r,"click",n),this.performSelection(n,i,r)}},redraw3DSelection:function(e){var t=window.sfBlazor.getCompInstance(e);if(t){t.isSeriesMode="Series"==t.oldMode;var i=t.selectedDataIndexes,s=t.highlightDataIndexes;t.styleId.indexOf("highlight")>-1&&t.highlightDataIndexes.length>0?(t.highlightDataIndexes=[],this.remove3DSelectedElements(e),i=s):(t.selectedDataIndexes=[],this.remove3DSelectedElements(e)),this.selectionBlurEffect(t,t.element.id,t.visible3DSeries,!0),this.select3DDataIndex(t.dataId,i)}},remove3DSelectedElements:function(e){var t=window.sfBlazor.getCompInstance(e);if(t)for(var i,s=0,o=t.visible3DSeries;s<o.length;s++){var n=o[s];n.visible&&(i=this.getSeriesElements(n),this.removeStyles(i,t))}},legendHighlight3D:function(e,t,i){if(!sf.base.isNullOrUndefined(t)&&t.id.indexOf("legend")>0&&!e.isTouch&&!sf.base.Browser.isDevice&&("None"!==e.highlightMode||e.options.enableLegendHighlight))for(var s=[e.id+"_chart_legend_text_",e.id+"_chart_legend_shape_marker_",e.id+"_chart_legend_shape_",e.id+"_chart_legend_g_"],o=t.id,n=void 0,r=0,l=s;r<l.length;r++){var a=l[r];if(o.indexOf(a)>-1){n=parseInt(o.split(a)[1],10),this.legendSelection(e,n,t,i);break}}},set3DHighlightSelectionOptions:function(e,t){var i=window.sfBlazor.getCompInstance(e);i&&i.selectionHighlight3DOptions(t)},set3DAttribute:function(e,t,i){var s=this.getElement(e);s&&s.setAttribute(t,i)},set3DUserInteractionBase:function(e,t,i,s,o,n){var r=window.sfBlazor.getCompInstance(e);r&&(r.userInteractionBase=t,r.chart3DAxes=i,r.chart3DSeries=s,r.tooltipBase.tooltipModule=t.tooltip,r.tooltipBase.tooltipModule.template=t.templateString?t.templateString:r.tooltipBase.tooltipModule.template,r.tooltipBase.tooltipDuration=0!=r.tooltipBase.tooltipModule.duration?r.tooltipBase.tooltipModule.duration:300,r.dateValuePairs=o,r.numberValuePairs=n,r.selectionHighlight3DOptions(t),r.isChartMove=!1,setTimeout((function(){r.getVisible3DSeies(),r.isChartMove=!0}),200))},completeSelection3D:function(e){"None"!==e.selectionMode&&(e.currentMode=e.selectionMode)},set3DSeriesTabIndex:function(){var e=document.querySelectorAll('[id*="0-region-series-0-point-0"]');e&&e.length>0&&e.forEach((function(t){t!=e[e.length-1]&&t.removeAttribute("tabindex")}))}};return r}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{})})();