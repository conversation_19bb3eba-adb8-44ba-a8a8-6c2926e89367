/*!*  filename: sf-numerictextbox.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[42],{"./bundles/sf-numerictextbox.js":function(e,t,n){"use strict";n.r(t);n("./modules/sf-numerictextbox.js")},"./modules/sf-numerictextbox.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.NumericTextBox=function(){"use strict";var e=new RegExp("^(-)?(\\d*)$"),t=0,n=null,s=function(){function s(e,t,n,s,i){window.sfBlazor=window.sfBlazor,this.keyCharCodeHelper=function(e){return this.element.value.substring(0,this.element.selectionStart)+String.fromCharCode(e.which)+this.element.value.substring(this.element.selectionEnd)},this.keyHelper=function(e){return this.element.value.substring(0,this.element.selectionStart)+e.key+this.element.value.substring(this.element.selectionEnd)},this.dataId=e,this.wrapperElement=t,this.element=n,this.options=i,window.sfBlazor.setCompInstance(this),this.dotNetRef=s}return s.prototype.initialize=function(){this.spinButtonEvents(),sf.base.EventHandler.add(this.element,"focus",this.focusHandler,this),sf.base.EventHandler.add(this.element,"blur",this.focusOutHandler,this),sf.base.EventHandler.add(this.element,"keypress",this.keyPressHandler,this),sf.base.EventHandler.add(this.element,"keydown",this.keyDownHandler,this),sf.base.EventHandler.add(this.element,"paste",this.pasteHandler,this),sf.base.Browser.isDevice&&sf.base.EventHandler.add(document,"scroll",this.scrollHandler)},s.prototype.clearInvalid=function(e){this.element.value=e,n=null},s.prototype.keyPressHandler=function(e){if(!this.options.enabled||this.options.readonly)return!0;var t=e.keyCode;if(0===e.which||e.metaKey||e.ctrlKey||8===t||13===t)return!0;var n=this.options.decimalSeparator,s=String.fromCharCode(e.which),i="NumpadDecimal"===e.code&&this.keyCharCodeHelper(e)!==n;i&&(s=n);var o=this.element.value;if(o=o.substring(0,this.element.selectionStart)+s+o.substring(this.element.selectionEnd),this.numericRegex().test(o)){if(i){var l=this.element.selectionStart+1;this.element.value=o,this.element.setSelectionRange(l,l),e.preventDefault(),e.stopPropagation()}return!0}return e.preventDefault(),e.stopPropagation(),!1},s.prototype.pasteHandler=function(e){if(this.options.enabled&&!this.options.readonly){var t=e.clipboardData.getData("text/plain");this.numericRegex().test(t.trim())?this.dotNetRef.invokeMethodAsync("InvokePasteHandler",t):(t=t.replace(/[^-0-9.,]/g,""),n&&n==t?e.preventDefault():(this.dotNetRef.invokeMethodAsync("InvokePasteHandler",t),n=t))}},s.prototype.preventHandler=function(){var e=this,t=!!navigator.platform&&/iPad|iPhone|iPod|MacIntel/.test(navigator.platform);setTimeout((function(){if(e.element.selectionStart>0){var n=e.element.selectionStart,s=e.element.selectionStart-1,i=e.element.value.split(""),o=e.options.decimalSeparator.charCodeAt(0);" "===e.element.value[s]&&e.element.selectionStart>0&&!t?(sf.base.isNullOrUndefined(e.prevVal)?e.element.value=e.element.value.trim():0!==s?e.element.value=e.prevVal:0===s&&(e.element.value=e.element.value.trim()),e.element.setSelectionRange(s,s)):isNaN(parseFloat(e.element.value[e.element.selectionStart-1]))&&45!==e.element.value[e.element.selectionStart-1].charCodeAt(0)?(i.indexOf(e.element.value[e.element.selectionStart-1])!==i.lastIndexOf(e.element.value[e.element.selectionStart-1])&&e.element.value[e.element.selectionStart-1].charCodeAt(0)===o||e.element.value[e.element.selectionStart-1].charCodeAt(0)!==o)&&(e.element.value=e.element.value.substring(0,s)+e.element.value.substring(n,e.element.value.length),e.element.setSelectionRange(s,s),isNaN(parseFloat(e.element.value[e.element.selectionStart-1]))&&e.element.selectionStart>0&&e.element.value.length&&e.preventHandler()):isNaN(parseFloat(e.element.value[e.element.selectionStart-2]))&&e.element.selectionStart>1&&45!==e.element.value[e.element.selectionStart-2].charCodeAt(0)&&(i.indexOf(e.element.value[e.element.selectionStart-2])!==i.lastIndexOf(e.element.value[e.element.selectionStart-2])&&e.element.value[e.element.selectionStart-2].charCodeAt(0)===o||e.element.value[e.element.selectionStart-2].charCodeAt(0)!==o)&&(e.element.setSelectionRange(s,s),e.nextEle=e.element.value[e.element.selectionStart],e.cursorPosChanged=!0,e.preventHandler()),!0===e.cursorPosChanged&&e.element.value[e.element.selectionStart]===e.nextEle&&isNaN(parseFloat(e.element.value[e.element.selectionStart-1]))&&(e.element.setSelectionRange(e.element.selectionStart+1,e.element.selectionStart+1),e.cursorPosChanged=!1,e.nextEle=null),""===e.element.value.trim()&&e.element.setSelectionRange(0,0),e.element.selectionStart>0&&45===e.element.value[e.element.selectionStart-1].charCodeAt(0)&&e.element.selectionStart>1&&(sf.base.isNullOrUndefined(e.prevVal)?e.element.value=e.element.value:e.element.value=e.prevVal,e.element.setSelectionRange(e.element.selectionStart,e.element.selectionStart)),e.prevVal=e.element.value}}))},s.prototype.keyDownHandler=function(e){var t=!!navigator.platform&&/iPad|iPhone|iPod|MacIntel/.test(navigator.platform);this.options.readonly||(sf.base.Browser.isDevice&&38!==e.keyCode&&40!==e.keyCode&&8!==e.keyCode&&(this.numericRegex().test(this.keyCharCodeHelper(e))||this.numericRegex().test(this.keyHelper(e))||(t&&sf.base.Browser.isDevice?this.preventHandler():e.preventDefault())),!t&&sf.base.Browser.isDevice&&this.preventHandler(),38===e.keyCode?(e.preventDefault(),this.dotNetRef.invokeMethodAsync("ServerAction","increment",e,this.element.value)):40===e.keyCode&&(e.preventDefault(),this.dotNetRef.invokeMethodAsync("ServerAction","decrement",e,this.element.value)))},s.prototype.numericRegex=function(){var t=this.options.decimalSeparator,n="*";return"."===t&&(t="\\"+t),0===this.options.decimals&&this.options.validateDecimalOnType?e:(this.options.decimals&&this.options.validateDecimalOnType&&(n="{0,"+this.options.decimals+"}"),new RegExp("^(-)?(((\\d+("+t+"\\d"+n+")?)|("+t+"\\d"+n+")))?$"))},s.prototype.mouseWheel=function(e){var t;e.preventDefault();var n=e;n.wheelDelta?t=n.wheelDelta/120:n.detail&&(t=-n.detail/3),t>0?this.dotNetRef.invokeMethodAsync("ServerAction","increment",e,this.element.value):t<0&&this.dotNetRef.invokeMethodAsync("ServerAction","decrement",e,this.element.value)},s.prototype.focusHandler=function(e){this.isFocused=!0,this.options.enabled&&!this.options.readonly&&(sf.base.Browser.isDevice||sf.base.EventHandler.add(this.element,"mousewheel DOMMouseScroll",this.mouseWheel,this))},s.prototype.focusOutHandler=function(e){this.isFocused=!1,e.preventDefault(),sf.base.Browser.isDevice||sf.base.EventHandler.remove(this.element,"mousewheel DOMMouseScroll",this.mouseWheel)},s.prototype.mouseDownOnSpinner=function(e){var t=this;if(this.options.enabled&&!this.options.readonly){this.isFocused&&(this.isPrevFocused=!0,e.cancelable&&e.preventDefault());var n=e.currentTarget,s=n.classList.contains("e-spin-up")?"increment":"decrement";sf.base.EventHandler.add(n,"mouseleave",this.mouseUpClick,this),this.timeOut=setInterval((function(){t.isCalled=!0,t.dotNetRef.invokeMethodAsync("ServerAction",s,e,t.element.value)}),150),sf.base.EventHandler.add(document,"mouseup",this.mouseUpClick,this)}},s.prototype.mouseUpOnSpinner=function(e){if(this.options.enabled&&!this.options.readonly){if(this.isPrevFocused&&(this.element.focus(),!sf.base.Browser.isDevice&&e.cancelable&&(this.isPrevFocused=!1)),sf.base.Browser.isDevice||e.preventDefault(),!this.getElementData(e))return;var t=e.currentTarget,n=t.classList.contains("e-spin-up")?"increment":"decrement";sf.base.EventHandler.remove(t,"mouseleave",this.mouseUpClick),this.isCalled||this.dotNetRef.invokeMethodAsync("ServerAction",n,e,this.element.value),this.isCalled=!1,sf.base.EventHandler.remove(document,"mouseup",this.mouseUpClick)}},s.prototype.touchMoveOnSpinner=function(e){!this.options.enabled||this.options.readonly?document.elementFromPoint(e.clientX,e.clientY).classList.contains("e-input-group-icon")||clearInterval(this.timeOut):clearInterval(this.timeOut)},s.prototype.scrollHandler=function(e){clearInterval(this.timeOut)},s.prototype.getElementData=function(e){return!(e.which&&3===e.which||e.button&&2===e.button||!this.options.enabled||this.options.readonly)&&(clearInterval(this.timeOut),!0)},s.prototype.mouseUpClick=function(e){e.stopPropagation(),clearInterval(this.timeOut),this.isCalled=!1,sf.base.EventHandler.remove(this.spinUp,"mouseleave",this.mouseUpClick),sf.base.EventHandler.remove(this.spinDown,"mouseleave",this.mouseUpClick)},s.prototype.selectRange=function(e){var n=this;if(clearTimeout(t),sf.base.Browser.isDevice||"11.0"!==sf.base.Browser.info.version){var s=sf.base.Browser.isDevice&&sf.base.Browser.isIos?600:30;t=setTimeout((function(){"number"!==n.element.type&&n.element.setSelectionRange(0,e.length)}),s)}else this.element.setSelectionRange(0,e.length)},s.prototype.isDevice=function(){return sf.base.Browser.isDevice},s.prototype.spinButtonEvents=function(){this.spinDown=this.wrapperElement?this.wrapperElement.querySelector(".e-spin-down"):null,this.spinUp=this.wrapperElement?this.wrapperElement.querySelector(".e-spin-up"):null,this.spinDown&&this.spinUp&&(this.unBindSpinButton(),this.bindSpinButton())},s.prototype.bindSpinButton=function(){sf.base.EventHandler.add(this.spinUp,sf.base.Browser.touchStartEvent,this.mouseDownOnSpinner,this),sf.base.EventHandler.add(this.spinDown,sf.base.Browser.touchStartEvent,this.mouseDownOnSpinner,this),sf.base.EventHandler.add(this.spinUp,sf.base.Browser.touchEndEvent,this.mouseUpOnSpinner,this),sf.base.EventHandler.add(this.spinDown,sf.base.Browser.touchEndEvent,this.mouseUpOnSpinner,this),sf.base.EventHandler.add(this.spinUp,sf.base.Browser.touchMoveEvent,this.touchMoveOnSpinner,this),sf.base.EventHandler.add(this.spinDown,sf.base.Browser.touchMoveEvent,this.touchMoveOnSpinner,this)},s.prototype.unBindSpinButton=function(){sf.base.EventHandler.remove(this.spinUp,sf.base.Browser.touchStartEvent,this.mouseDownOnSpinner),sf.base.EventHandler.remove(this.spinDown,sf.base.Browser.touchStartEvent,this.mouseDownOnSpinner),sf.base.EventHandler.remove(this.spinUp,sf.base.Browser.touchEndEvent,this.mouseUpOnSpinner),sf.base.EventHandler.remove(this.spinDown,sf.base.Browser.touchEndEvent,this.mouseUpOnSpinner),sf.base.EventHandler.remove(this.spinUp,sf.base.Browser.touchMoveEvent,this.touchMoveOnSpinner),sf.base.EventHandler.remove(this.spinDown,sf.base.Browser.touchMoveEvent,this.touchMoveOnSpinner)},s.prototype.destroy=function(){sf.base.EventHandler.remove(this.element,"focus",this.focusHandler),sf.base.EventHandler.remove(this.element,"blur",this.focusOutHandler),sf.base.EventHandler.remove(this.element,"keypress",this.keyPressHandler),sf.base.EventHandler.remove(this.element,"keydown",this.keyDownHandler),sf.base.EventHandler.remove(this.element,"paste",this.pasteHandler),sf.base.Browser.isDevice&&sf.base.EventHandler.remove(document,"scroll",this.scrollHandler)},s}();return{initialize:function(e,t,n,i,o){n&&e&&new s(e,t,n,i,o).initialize()},selectRange:function(e,t){var n=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(n)||n.selectRange(t)},propertyChanges:function(e,t){var n=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(n)||(n.options=t)},focusIn:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.element.focus()},focusOut:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.element.blur()},spinButtonEvents:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.spinButtonEvents()},clearInvalid:function(e,t){var n=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(n)||n.clearInvalid(t)},destroy:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.destroy()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfnumerictextbox');})})();