/*!*  filename: sf-filemanager.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[26],{"./bundles/sf-filemanager.js":function(e,t,r){"use strict";r.r(t);r("./modules/sf-filemanager.js")},"./modules/sf-filemanager.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.FileManager=function(){"use strict";var e="e-blur",t=function(){function t(e,t,r,i){this.element=t,this.properties=i,this.dataId=e,this.treeviewEle=t.querySelector(".e-navigation .e-treeview"),this.dotnetRef=r,this.isMac=navigator.platform.toUpperCase().indexOf("MAC")>=0,sf.base.isNullOrUndefined(this.element)||(this.ctrlId=this.element.id,window.sfBlazor.setCompInstance(this)),this.bindKeyboardEvent(),this.bindTreeViewKeyBoardEvent(),this.wireEvents()}return t.prototype.fmDialogOpen=function(){this.dotnetRef.invokeMethodAsync("FileManagerShowDialog")},t.prototype.unWireMobileEvents=function(){sf.base.isNullOrUndefined(this.clickObj)||this.clickObj.destroy()},t.prototype.wireMobileEvents=function(){var e,t=this;e="Details"===this.properties.view?this.element.querySelector(".e-view-container .e-grid .e-gridcontent"):this.element.querySelector(".e-view-container .e-large-icons"),sf.base.isNullOrUndefined(e)||(this.clickObj=new sf.base.Touch(e,{tapHold:function(e){var r,i;i="Details"===t.properties.view?(r=sf.base.closest(e.originalEvent.target,".e-row"))?r.getAttribute("data-rowindex"):null:(r=sf.base.closest(e.originalEvent.target,"li.e-list-item"))?r.getAttribute("data-uid"):null,null!=r&&null!=i&&t.dotnetRef.invokeMethodAsync("ChangeMobileMultiSelection",i)}}))},t.prototype.wireEvents=function(){var e=this.element.querySelector("#"+this.properties.id+"_grid");e&&sf.base.EventHandler.add(e,"dblclick",this.gridBlur,this);var t=this.element.querySelector("#"+this.properties.id+"_largeicons");if(this.properties.virtualization&&t){var r=t.getElementsByTagName("ul")[0];sf.base.EventHandler.add(r,"scroll",this.onVirtualScroll,this)}},t.prototype.unWireEvents=function(){var e=this.element.querySelector("#"+this.properties.id+"_grid");e&&sf.base.EventHandler.remove(e,"dblclick",this.gridBlur);var t=this.element.querySelector("#"+this.properties.id+"_largeicons");if(this.properties.virtualization&&t){var r=t.getElementsByTagName("ul")[0];sf.base.EventHandler.remove(r,"scroll",this.onVirtualScroll)}},t.prototype.adjustHeight=function(){var e=sf.base.select("#"+this.element.id+"_toolbar",this.element),t=e?e.offsetHeight:0,r=this.element.querySelector(".e-address").offsetHeight,i=this.element.clientHeight-t-r;return sf.base.formatUnit(this.element.clientHeight-t)+" "+sf.base.formatUnit(i)},t.prototype.createDragObj=function(e){var t,r;"LargeIcons"===this.properties.view?(t=this.element.querySelector(".e-large-icons"),r=".e-large-icon"):(t=this.element.querySelector(".e-grid.e-control"),r=".e-row"),this.properties.draggable||e?(this.dragObj&&this.dragObj.destroy(),t&&this.dragObjProperty(this.dragObj,t,".e-fullrow")):this.properties.draggable||this.dragObj.destroy(),this.treeviewEle?(this.treeDragObj&&this.treeDragObj.destroy(),this.dragObjProperty(this.treeDragObj,this.treeviewEle,r)):this.properties.draggable||this.treeDragObj.destroy()},t.prototype.dragObjProperty=function(e,t,r){var i=this;new sf.base.Draggable(t,{cursorAt:{left:44,top:18},enableTailMode:!0,enableTapHold:!0,dragArea:this.element,dragTarget:r,drag:this.draggingHandler.bind(this),dragStart:function(e){i.dragStartHandler(e)},dragStop:this.dragStopHandler.bind(this),enableAutoScroll:!0,helper:this.dragHelper.bind(this)})},t.prototype.dragHelper=function(e){var t=e.sender.target;return this.getModule(t),"largeiconsview"===this.activeModule||"navigationpane"===this.activeModule?this.dragLi=sf.base.closest(t,".e-list-item"):"detailsview"===this.activeModule&&(this.dragLi=sf.base.closest(t,"tr.e-row"),"false"==this.dragLi.getAttribute("aria-selected")&&t.click()),this.dragLi?(this.createVirtualDragElement(),this.virtualDragElement):null},t.prototype.createVirtualDragElement=function(){this.updateViewElement(),this.updateDragValues(),this.cloneIcon=sf.base.createElement("div",{className:"e-fe-icon "+this.dragType}),this.cloneName=sf.base.createElement("div",{className:"e-fe-name",innerHTML:this.dragName});var e=sf.base.createElement("div",{className:"e-fe-content"});if(e.appendChild(this.cloneIcon),e.appendChild(this.cloneName),this.dragCount>1){var t=sf.base.createElement("span",{className:"e-fe-count",innerHTML:this.dragCount.toString(10)});e.appendChild(t)}var r=sf.base.createElement("div",{className:"e-fe-clone"});r.appendChild(e),this.virtualDragElement=r,this.element.appendChild(this.virtualDragElement)},t.prototype.getModule=function(e){e&&(sf.base.closest(e,".e-row")?this.activeModule="detailsview":sf.base.closest(e,".e-large-icon")?this.activeModule="largeiconsview":this.activeModule="navigationpane")},t.prototype.getXYValue=function(e,t){var r,i=e.changedTouches;if(!(r="X"===t?i?i[0].clientX:e.clientX:i?i[0].clientY:e.clientY)&&"focus"===e.type&&e.target){var s=e.target.getBoundingClientRect();r=s?"X"===t?s.left:s.top:null}return Math.ceil(r)},t.prototype.removeDestroyElement=function(){this.dragObj.intDestroy(this.dragStartArgs.event),this.dragCancel()},t.prototype.TriggerDragEvent=function(e){e&&this.removeDestroyElement()},t.prototype.UpdateGridFocus=function(e){if("Details"==this.properties.view){this.updateViewElement();var t=this.viewElem.querySelectorAll(".e-focused"),r=this.viewElem.querySelector('[data-rowindex="'+e.toString()+'"]');if(t)for(var i=0;i<t.length;i++)sf.base.closest(t[i],"tr").getAttribute("data-rowindex")!=e.toString()&&(sf.base.closest(t[i],"tr").removeAttribute("tabindex"),sf.base.removeClass([t[i]],["e-focus","e-focused"]));sf.base.isNullOrUndefined(r)||(this.viewElem.setAttribute("tabindex","-1"),r.setAttribute("tabindex","0"),sf.base.addClass([r],["e-focus","e-focused"]))}},t.prototype.TriggerDragStartEvent=function(e){if(e)this.removeDestroyElement();else{this.dragStartArgs.bindEvents(this.dragStartArgs.dragElement);var t=this.dragStartArgs;t.cancel=!1,this.getModule(this.dragStartArgs.target);var r=this.element.querySelector(".e-navigation .e-treeview ul li").getAttribute("data-uid");(!this.properties.draggable||"navigationpane"===this.activeModule&&sf.base.closest(this.dragStartArgs.target,"li").getAttribute("data-uid")===r)&&(t.cancel=!0),this.removeBlur(),t.cancel?this.removeDestroyElement():t.cancel||(this.updateViewElement(),this.blurActive(),this.updateDragValues())}},t.prototype.dragStartHandler=function(e){this.dragStartArgs=e,window.sfBlazor.setCompInstance(this),this.UpdateXY(e),"largeiconsview"==this.activeModule?this.dotnetRef.invokeMethodAsync("DragStartCall",this.dragLeft,this.dragTop,e.target.closest("li").getAttribute("data-uid")):this.dotnetRef.invokeMethodAsync("DragStartCall",this.dragLeft,this.dragTop,null)},t.prototype.blurActive=function(){for(var t=0,r=this.viewElem.querySelectorAll(".e-active");t<r.length;)r[t].classList.add(e),t++},t.prototype.updateViewElement=function(){"LargeIcons"===this.properties.view?this.viewElem=this.element.querySelector(".e-large-icons"):this.viewElem=this.element.querySelector(".e-gridcontent")},t.prototype.getIconClass=function(e){return e.querySelector(".e-list-img")?"e-fe-image":e.querySelector(".e-list-icon").classList[1]},t.prototype.updateDragValues=function(){var e;"largeiconsview"===this.activeModule?(e=this.viewElem.querySelectorAll(".e-active"),this.dragName=e.length>0?e[0].querySelector(".e-list-text").textContent:"",this.dragType=e.length>0?this.getIconClass(e[0]):""):"detailsview"===this.activeModule?null!=(e=this.viewElem.querySelectorAll('tr[aria-selected="true"]'))&&e.length>0&&(this.dragName=e.length>0?e[0].querySelector(".e-fe-text").textContent:"",this.dragType=e.length>0?e[0].querySelector(".e-fe-icon").classList[1]:""):"navigationpane"===this.activeModule&&(this.dragName=this.dragLi.querySelector(".e-list-text").textContent,this.dragType="e-fe-folder"),null!=e&&(this.dragCount=e.length)},t.prototype.getTargetModule=function(e){e&&(sf.base.closest(e,".e-gridcontent")?this.targetModule="detailsview":sf.base.closest(e,".e-large-icons")?this.targetModule="largeiconsview":e.classList.contains("e-fullrow")||e.classList.contains("e-icon-expandable")?this.targetModule="navigationpane":sf.base.closest(e,".e-address-list-item")?this.targetModule="breadcrumbbar":this.targetModule="")},t.prototype.draggingHandler=function(t){var r=!1;this.updateDragValues(),this.cloneIcon.setAttribute("class","e-fe-icon "+this.dragType),this.cloneName.innerHTML=this.dragName;var i=null;this.blurActive(),this.getTargetModule(t.target),this.removeDropTarget(),this.removeBlur("hover"),"navigationpane"===this.targetModule?((i=sf.base.closest(t.target,"li")).classList.add("e-hover","e-fe-drop-folder"),r=!0):"detailsview"===this.targetModule?((i=sf.base.closest(t.target,"tr"))&&i.querySelector(".e-fe-folder")&&!i.classList.contains(e)?i.classList.add("e-fe-drop-folder"):!i||i.querySelector(".e-fe-folder")||i.classList.contains(e)||i.classList.add("e-fe-drop-file"),r=!0):"largeiconsview"===this.targetModule?((i=sf.base.closest(t.target,"li"))&&i.querySelector(".e-fe-folder")&&!i.classList.contains(e)&&i.classList.add("e-hover","e-fe-drop-folder"),r=!0):"breadcrumbbar"===this.targetModule&&(r=!0),this.element.classList.remove("e-fe-drop","e-no-drop"),this.element.classList.add(r?"e-fe-drop":"e-no-drop"),this.UpdateXY(t),this.dotnetRef.invokeMethodAsync("DraggingCall",this.dragLeft,this.dragTop)},t.prototype.UpdateXY=function(e){this.dragLeft=this.getXYValue(e.event,"X"),this.dragTop=this.getXYValue(e.event,"Y")},t.prototype.dragStopHandler=function(e){var t,r,i=!1;if(e.cancel=!1,this.removeDropTarget(),this.element.classList.remove("e-fe-drop","e-no-drop"),this.dragCancel(),this.getTargetModule(e.target),""===this.targetModule&&e.target&&e.target.classList.contains("e-view-container")&&(i=!0),this.removeBlur("hover"),"largeiconsview"!==this.targetModule&&"navigationpane"!==this.targetModule&&"breadcrumbbar"!==this.targetModule||!e.target)"detailsview"===this.targetModule&&e.target&&(r=(t=sf.base.closest(e.target,"tr"))?t.getAttribute("data-rowindex"):null);else{t=sf.base.closest(e.target,"li");var s="breadcrumbbar"===this.targetModule?"data-utext":"data-uid";r=t?t.getAttribute(s):null}null==r&&(i=!0);var a=this.treeviewEle?this.dragLi.getAttribute("data-uid"):null;"detailsview"===this.activeModule&&(a=this.dragLi.getAttribute("data-rowindex")),this.UpdateXY(e),this.dotnetRef.invokeMethodAsync("DragStopCall",a,r,this.targetModule,this.activeModule,i,this.dragLeft,this.dragTop),this.dragCount=0,this.dragName="",this.dragType=""},t.prototype.dragCancel=function(){this.removeBlur();var e=sf.base.select(".e-fe-clone",this.element);e&&sf.base.detach(e)},t.prototype.gridBlur=function(e){var t=e.target;"TD"===t.tagName&&t.blur()},t.prototype.removeItemClass=function(e){for(var t=this.element.querySelectorAll("."+e),r=0;r<t.length;r++)t[r].classList.remove(e)},t.prototype.removeDropTarget=function(){this.removeItemClass("e-fe-drop-folder"),this.removeItemClass("e-fe-drop-file")},t.prototype.removeBlur=function(t){for(var r=t?this.element.querySelectorAll(".e-hover"):this.element.querySelectorAll("."+e),i=0;i<r.length;)r[i].classList.remove(t?"e-hover":e),i++},t.prototype.bindTreeViewKeyBoardEvent=function(){var e=this.element.querySelector(".e-navigation"),t={altEnter:"alt+enter",esc:"escape",del:"delete",ctrlX:this.isMac?"cmd+x":"ctrl+x",ctrlC:this.isMac?"cmd+c":"ctrl+c",ctrlV:this.isMac?"cmd+v":"ctrl+v",f2:"f2",shiftF10:"shift+f10"};if(e)new sf.base.KeyboardEvents(e,{keyAction:this.treeviewKeydownHandler.bind(this),keyConfigs:t,eventName:"keydown"})},t.prototype.bindKeyboardEvent=function(){if("Details"===this.properties.view){var e={altEnter:"alt+enter",altN:"alt+n",esc:"escape",tab:"tab",moveDown:"downarrow",ctrlShift1:"ctrl+shift+1",ctrlShift2:"ctrl+shift+2",ctrlEnd:"ctrl+end",ctrlHome:"ctrl+home",ctrlDown:"ctrl+downarrow",ctrlLeft:"ctrl+leftarrow",ctrlRight:"ctrl+rightarrow",shiftEnd:"shift+end",shiftHome:"shift+home",shiftDown:"shift+downarrow",shiftUp:"shift+uparrow",ctrlUp:"ctrl+uparrow",csEnd:"ctrl+shift+end",csHome:"ctrl+shift+home",csDown:"ctrl+shift+downarrow",csUp:"ctrl+shift+uparrow",space:"space",ctrlSpace:"ctrl+space",shiftSpace:"shift+space",csSpace:"ctrl+shift+space",end:"end",home:"home",moveUp:"uparrow",del:"delete",ctrlX:this.isMac?"cmd+x":"ctrl+x",ctrlC:this.isMac?"cmd+c":"ctrl+c",ctrlV:this.isMac?"cmd+v":"ctrl+v",ctrlShiftN:"ctrl+shift+n",shiftdel:"shift+delete",ctrlD:"ctrl+d",f2:"f2",ctrlA:this.isMac?"cmd+a":"ctrl+a",enter:"enter"},t=this.element.querySelector("#"+this.properties.id+"_grid");t&&this.bindKeyboardEvents(e,t)}else if("LargeIcons"===this.properties.view){e={altN:"alt+n",f5:"f5",ctrlShift1:"ctrl+shift+1",ctrlShift2:"ctrl+shift+2",ctrlU:"ctrl+u",end:"end",home:"home",tab:"tab",moveDown:"downarrow",moveLeft:"leftarrow",moveRight:"rightarrow",moveUp:"uparrow",ctrlEnd:"ctrl+end",ctrlHome:"ctrl+home",ctrlDown:"ctrl+downarrow",ctrlLeft:"ctrl+leftarrow",ctrlRight:"ctrl+rightarrow",ctrlUp:"ctrl+uparrow",shiftEnd:"shift+end",shiftHome:"shift+home",shiftDown:"shift+downarrow",shiftLeft:"shift+leftarrow",shiftRight:"shift+rightarrow",shiftUp:"shift+uparrow",csEnd:"ctrl+shift+end",csHome:"ctrl+shift+home",csDown:"ctrl+shift+downarrow",csLeft:"ctrl+shift+leftarrow",csRight:"ctrl+shift+rightarrow",csUp:"ctrl+shift+uparrow",space:"space",ctrlSpace:"ctrl+space",shiftSpace:"shift+space",csSpace:"ctrl+shift+space",ctrlA:this.isMac?"cmd+a":"ctrl+a",enter:"enter",altEnter:"alt+enter",esc:"escape",del:"delete",ctrlX:this.isMac?"cmd+x":"ctrl+x",ctrlC:this.isMac?"cmd+c":"ctrl+c",ctrlV:this.isMac?"cmd+v":"ctrl+v",f2:"f2",shiftdel:"shift+delete",back:"backspace",ctrlD:"ctrl+d"};var r=this.element.querySelector("#"+this.properties.id+"_largeicons");r&&this.bindKeyboardEvents(e,r)}},t.prototype.bindKeyboardEvents=function(e,t){this.keyboardModule=new sf.base.KeyboardEvents(t,{keyAction:this.keyupHandler.bind(this),keyConfigs:e,eventName:"keyup"}),this.keyboardDownModule=new sf.base.KeyboardEvents(t,{keyAction:this.keydownHandler.bind(this),keyConfigs:e,eventName:"keydown"})},t.prototype.getRowValue=function(){var e=this.element.querySelector("#"+this.element.id+"_largeicons").querySelectorAll(".e-list-item"),t=1;if(e)for(var r=0,i=e.length-1;r<i&&e[r].getBoundingClientRect().top===e[r+1].getBoundingClientRect().top;r++)t++;return t},t.prototype.onVirtualScroll=function(){var e=this.element.querySelector("#"+this.element.id+"_largeicons");this.scrollPosition=sf.base.isNullOrUndefined(this.scrollPosition)?0:this.scrollPosition;var t=e.getElementsByTagName("ul")[0],r=t.scrollTop<=0?0:t.scrollTop;if(this.virtualChildEleTop=this.listItemHeight*Math.floor(r/this.listItemHeight),this.virtualChildEleTop!==parseFloat(this.virtualChildEle.style.top))if(this.virtualChildEle.style.top=this.virtualChildEleTop+"px",r>this.scrollPosition){var i=Math.round(this.virtualChildEleTop/this.listItemHeight-this.listDiff);this.dotnetRef.invokeMethodAsync("OnVirtualScroll",i,!0)}else{i=Math.round(this.listDiff-this.virtualChildEleTop/this.listItemHeight);this.dotnetRef.invokeMethodAsync("OnVirtualScroll",i,!1)}this.listDiff=Math.round(this.virtualChildEleTop/this.listItemHeight),this.scrollPosition=r},t.prototype.calculateItemCount=function(e){var t=this.element.querySelector("#"+this.element.id+"_largeicons"),r=t.getElementsByTagName("ul")[0],i=r.clientWidth-17,s=t.querySelectorAll(".e-list-item"),a=parseInt(window.getComputedStyle(s[0]).getPropertyValue("margin-right"),10)+parseInt(window.getComputedStyle(s[0]).getPropertyValue("margin-left"),10),o=parseInt(window.getComputedStyle(s[0]).getPropertyValue("margin-top"),10)+parseInt(window.getComputedStyle(s[0]).getPropertyValue("margin-bottom"),10),l=Math.floor(parseFloat(sf.base.formatUnit(i))/(s[0].offsetWidth+a));this.listItemHeight=s[0].getBoundingClientRect().height+o,this.columnItemCount=Math.round(parseFloat(sf.base.formatUnit(t.clientHeight))/s[0].offsetHeight)+1;var n=l*this.columnItemCount;this.lastRowCount=e%l?e%l:l;var c=e<l?this.listItemHeight:Math.round(e/l)*this.listItemHeight;return t.querySelector(".e-virtual-parent").style.height=c+"px",this.virtualChildEle=t.querySelector(".e-virtual-child"),this.virtualChildEle.style.top="0px",this.virtualChildEleTop=0,this.listDiff=0,r.scrollTop=0,n+" "+l+" "+this.lastRowCount},t.prototype.keyupHandler=function(e){e.preventDefault();var t=0;"LargeIcons"===this.properties.view&&(t=this.getRowValue());var r=null;switch(e.action){case"altN":r="NewFolder";break;case"f5":r="Refresh";break;case"ctrlShift1":r="DetailsView";break;case"ctrlShift2":r="LargeIconsView";break;case"ctrlU":this.element.querySelector("#"+this.element.id+"_tb_upload")&&(r="Upload");break;case"altEnter":r="Details";break;case"del":case"shiftdel":r="Delete";break;case"enter":r="Open";break;case"ctrlC":r="Copy";break;case"ctrlV":r="Paste";break;case"ctrlX":r="Cut";break;case"ctrlD":r="Download";break;case"f2":r="Rename";break;case"ctrlA":r="SelectAll";break;case"home":r="Home";break;case"end":r="End";break;case"moveDown":r="MoveDown_"+t.toString();break;case"moveLeft":r="MoveLeft";break;case"moveRight":r="MoveRight";break;case"moveUp":r="MoveUp_"+t.toString();break;case"esc":r="Esc";break;case"ctrlLeft":r="ControlLeft";break;case"ctrlRight":r="ControlRight";break;case"ctrlEnd":r="ControlEnd";break;case"ctrlHome":r="ControlHome";break;case"shiftHome":r="ShiftHome";break;case"shiftEnd":r="ShiftEnd";break;case"shiftLeft":r="ShiftLeft";break;case"shiftRight":r="ShiftRight";break;case"csHome":r="ControlShiftHome";break;case"csEnd":r="ControlShiftEnd";break;case"csLeft":r="ControlShiftLeft";break;case"csRight":r="ControlShiftRight";break;case"ctrlUp":r="ControlUp_"+t.toString();break;case"shiftUp":r="ShiftUp_"+t.toString();break;case"csUp":r="ControlShiftUp_"+t.toString();break;case"ctrlDown":r="ControlDown_"+t.toString();break;case"shiftDown":r="ShiftDown_"+t.toString();break;case"csDown":r="ControlShiftDown_"+t.toString();break;case"space":r="Space";break;case"csSpace":r="ControlShiftSpace";break;case"shiftSpace":r="ShiftSpace";break;case"ctrlSpace":r="ControlSpace";break;case"tab":r="Tab"}r&&this.dotnetRef.invokeMethodAsync("PerformKeyboardAction",r)},t.prototype.treeviewKeydownHandler=function(e){if(e.preventDefault(),null==this.element.querySelector(".e-dialog.e-popup-open")){var t=null;switch(e.action){case"altEnter":t="Details";break;case"esc":t="Esc";break;case"del":t="Delete";break;case"ctrlC":t="Copy";break;case"ctrlV":t="Paste";break;case"ctrlX":t="Cut";break;case"shiftF10":t="Download";break;case"f2":t="Rename"}t&&this.dotnetRef.invokeMethodAsync("PerformTreeViewKeyboardAction",t)}},t.prototype.keydownHandler=function(e){if(null==this.element.querySelector(".e-dialog.e-popup-open"))switch(e.action){case"end":case"home":case"space":case"ctrlSpace":case"shiftSpace":case"csSpace":case"ctrlA":case"enter":case"altEnter":case"ctrlEnd":case"shiftEnd":case"csEnd":case"ctrlHome":case"shiftHome":case"csHome":case"ctrlDown":case"shiftDown":case"csDown":case"ctrlLeft":case"shiftLeft":case"csLeft":case"esc":case"del":case"shiftdel":case"ctrlC":case"ctrlV":case"ctrlX":case"f2":case"moveDown":case"moveUp":case"ctrlD":case"altN":case"f5":case"ctrlShift1":case"ctrlShift2":case"ctrlU":e.preventDefault()}},t}();return{initialize:function(e,r,i,s,a){new t(e,r,i,s);var o=window.sfBlazor.getCompInstance(e),l=r.getElementsByClassName("e-content");return sf.base.isNullOrUndefined(l[0])||"flex"!=l[0].style.display||l[0].style.removeProperty("display"),!sf.base.isNullOrUndefined(o)&&s.draggable&&o.createDragObj(),o.isMobile=a,o.adjustHeight()},dragStartActionContinue:function(e,t){var r=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(r)||r.TriggerDragStartEvent(t)},wireMobileEvents:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||(t.wireMobileEvents(),t.createDragObj(!0))},dragActionContinue:function(e,t){var r=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(r)||r.TriggerDragEvent(t)},focusItem:function(e,t){var r=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(r)&&"LargeIcons"===r.properties.view){var i=r.element.querySelector('.e-list-item.e-large-icon[data-uid="'+t+'"]');i&&i.focus()}},getItemCount:function(e,t){var r=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(r))return r.calculateItemCount(t)},updateProperties:function(e,t){var r=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(r))return r.properties=t,r.unWireEvents(),r.wireEvents(),r.isMobile&&(r.unWireMobileEvents(),r.wireMobileEvents()),r.adjustHeight()},uploadOpen:function(e,t,r){var i=window.sfBlazor.getCompInstance(e),s=(sf.base.isNullOrUndefined(r)?i.element:document.querySelector(r)).querySelector("#"+t);s&&!sf.base.Browser.isIos?s.click():i.fmDialogOpen()},triggerBlur:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||"TD"!==document.activeElement.tagName||document.activeElement.blur()},updateFocus:function(e,t){var r=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(r)||r.UpdateGridFocus(t)},updateView:function(e,t){var r=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(r)||(r.properties.view=t,r.keyboardDownModule.destroy(),r.keyboardModule.destroy(),r.bindKeyboardEvent(),r.isMobile&&(r.unWireMobileEvents(),r.wireMobileEvents()),"LargeIcons"===t&&r.properties.virtualization&&r.wireEvents()),r.properties.draggable&&r.createDragObj()},updateGridRow:function(e,t){if(e){for(var r=e.querySelectorAll('tr.e-row[tabindex="0"]'),i=0;i<r.length;i++)r[i].removeAttribute("tabindex");var s=e.querySelector('[data-rowindex="'+t+'"]');s&&(s.setAttribute("tabindex","0"),s.focus())}},focusToolbar:function(e,t){var r=window.sfBlazor.getCompInstance(e),i=r.element.querySelector(t);!sf.base.isNullOrUndefined(r)&&i&&i.children[0].focus()},saveFile:function(e,t,r){var i=window.sfBlazor.getCompInstance(r),s={action:"download",path:e.path,names:e.names,data:e.data,customData:e.customData},a=sf.base.createElement("form",{id:i.element.id+"_downloadForm",attrs:{action:t,method:"post",name:"downloadForm",download:""}}),o=sf.base.createElement("input",{id:i.element.id+"_hiddenForm",attrs:{name:"downloadInput",value:JSON.stringify(s),type:"hidden"}});a.appendChild(o),document.body.appendChild(a),document.forms.namedItem("downloadForm").submit(),document.body.removeChild(a)},download:function(e,t){var r=new Blob([t],{type:"application/octet-stream"}),i=document.createElement("a");i.href=URL.createObjectURL(r),i.download=e,i.style.display="none",document.body.appendChild(i),i.click(),document.body.removeChild(i)},getTargetElement:function(e,t,r){var i,s,a=document.elementFromPoint(t,r),o=sf.base.closest(a,'li[role="treeitem"]');if(a)if(a.classList.contains("e-yscroll")&&a.classList.contains("e-content")||a.classList.contains("e-list-parent")&&a.classList.contains("e-ul")||a.classList.contains("e-view-container")||a.classList.contains("e-large-icons")||a.classList.contains("e-virtual-parent")||sf.base.closest(a,".e-empty.e-view-container"))s={IsFile:!1,RowIndex:null,IsFolder:!1,IsLayout:!0,IsTree:!1};else if(o){s={IsFile:!1,RowIndex:null,TargetID:o.getAttribute("data-uid"),IsFolder:!0,IsLayout:!1,IsTree:!0}}else{if("Details"===e)s={IsFile:l=!(i=sf.base.closest(a,"tr")).querySelector(".e-fe-grid-icon .e-fe-icon").classList.contains("e-fe-folder"),RowIndex:parseInt(i.getAttribute("data-rowindex"),10),IsFolder:!l,IsLayout:!1,IsTree:!1};else if("LargeIcons"===e){var l,n=(i=sf.base.closest(a,"li")).querySelector(".e-list-icon");s={IsFile:l=!n||!n.classList.contains("e-fe-folder"),RowIndex:null,TargetID:i.getAttribute("data-uid"),IsFolder:!l,IsLayout:!1,IsTree:!1}}}else s={IsFile:!1,RowIndex:null,IsFolder:!1,IsLayout:!1,IsTree:!1};return s},removeRefElement:function(e){var t=window.sfBlazor.getCompInstance(e),r=sf.base.select(".e-dlg-ref-element",t.element);sf.base.detach(r.parentElement)}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sffilemanager');})})();