/*!*  filename: sf-grid.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[29],{"./bundles/sf-grid.js":function(e,t,n){"use strict";n.r(t);n("./modules/sf-grid.js")},"./modules/sf-grid.js":function(e,t){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Grid=function(){"use strict";function e(e,t,n,r){null!=e&&("Left"===r&&(n?e.style.right=t+"px":e.style.left=t+"px"),"Right"===r&&(n?e.style.left=t+"px":e.style.right=t+"px"))}var t,r,i=null;function o(){if(null!==i)return i;var e,t=document.createElement("div");return t.style.cssText="width:100px;height: 100px;overflow: scroll;position: absolute;top: -9999px;",document.body.appendChild(t),e=t.offsetWidth-t.clientWidth|0,document.body.removeChild(t),i=e}function s(e){return l(e,"previous")+l(e,"next")}function l(e,t){for(var n=e[t+"ElementSibling"],r=0,i=["e-gridheader","e-gridfooter","e-groupdroparea","e-gridpager","e-toolbar"];n;)i.some((function(e){return n.classList.contains(e)}))&&(r+=n.offsetHeight),n=n[t+"ElementSibling"];return r}function a(e,t,n){for(var r=e;r&&!(n?r.id===t:r.classList.contains(t));)r=r.parentElement;return r}function d(e,t){for(var n=-1,r=0,i=t.length;r<i;r++)if(t[r].isEqualNode(e)){n=r;break}return n}function u(e,t){for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n;return-1}function h(e){var t={};return t.x=sf.base.isNullOrUndefined(e.clientX)?e.changedTouches[0].clientX:e.clientX,t.y=sf.base.isNullOrUndefined(e.clientY)?e.changedTouches[0].clientY:e.clientY,t}function p(e,t){for(var n=[],r=0,i=e.length;r<i;r++){var o=t(e[r],r);sf.base.isNullOrUndefined(o)||n.push(o)}return n}function c(e){return e.options.enableVirtualization&&e.options.groupCount>0&&(e.options.offline||""===e.options.url)}function f(e,t){for(var n=[].slice.call(e.querySelectorAll(t)),r=0;r<n.length;r++)sf.base.remove(n[r])}function g(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];for(var i=0,o=e.length;i<o;i++)t?(sf.base.classList(e[i],n.slice(),[]),e[i].setAttribute("aria-selected","true")):(sf.base.classList(e[i],[],n.slice()),e[i].removeAttribute("aria-selected"))}!function(e){e.timer=null}(t||(t={}));var m=function(){function e(e){this.previousValues={top:0,left:0},this.oneTimeReady=!0,this.infiniteScrollTop=0,this.infiniteDataRequested=!1,this.infiniteScollDirection="",this.maxPage=0,this.infiniteInitialRender=!0,this.rowElements=[],this.isLazyChildLoad=!1,this.currentRowIndex=0,this.parent=e,this.addEventListener(),this.setHeight(),this.setPadding()}return e.prototype.setHeight=function(){var e=0,t=this.parent.element.querySelector(".e-content");if(this.parent.options.frozenRows&&"auto"!==this.parent.options.height&&!this.parent.options.height.match(/%/g)){var n=this.parent.element.querySelector(".e-headercontent").querySelector("tbody");e=n?n.offsetHeight:0,t.style.height=sf.base.formatUnit(parseInt(this.parent.options.height)-e)}},e.prototype.removeUnwantedScroll=function(e,t){void 0===t&&(t=0);var n=!1,r=this.parent.content.offsetWidth>=this.parent.getContentTable().offsetWidth,i=0!=this.parent.options.frozenRows&&0==this.parent.options.frozenColumns,o=this.parent.element.querySelector(".e-movablescrollbar");o&&o.offsetHeight;0==this.parent.options.frozenColumns||sf.base.isNullOrUndefined(this.getMovableContent())||(n=this.getMovableContent().offsetWidth>=this.getMovableContentTable().offsetWidth+t-2||0==this.getMovableContent().offsetWidth);var s=this.parent.content.scrollWidth>this.parent.content.offsetWidth?this.parent.content.scrollHeight+17:this.parent.content.scrollHeight;return"Height"===e&&this.parent.content.offsetHeight>=s||!("Width"!==e||!(n||i&&r))&&("100%"==this.parent.options.height&&(this.frozenContentElement().style.borderBottom="none"),!0)},e.prototype.getMovableContent=function(){return this.parent.element.querySelector(".e-movablecontent")},e.prototype.getMovableContentTable=function(){return this.parent.element.querySelector(".e-movablecontent .e-table")},e.prototype.frozenContentElement=function(){return this.parent.element.querySelector(".e-frozencontent")},e.prototype.getScrollbar=function(){return this.parent.element.querySelector(".e-scrollbar")},e.prototype.setPadding=function(){var t=this.parent.element.querySelector(".e-gridheader");if(!sf.base.isNullOrUndefined(t)&&("auto"!=this.parent.options.height||"None"!=this.parent.options.frozenName||0!=this.parent.options.frozenColumns)){if(this.removeUnwantedScroll("Height"))return this.content.style.overflow=this.parent.options.frozenColumns&&this.parent.options.enableColumnVirtualization?"hidden auto":"auto",t.querySelector(".e-headercontent").style.borderRightWidth="",void(t.style.paddingRight="");var n=e.getScrollBarWidth()-this.getThreshold(),r=this.getCssProperties();this.parent.options.enableRtl?t.style["padding-right"]="":t.style["padding-left"]="";var i=this.parent.element.querySelector(".e-content");!this.parent.options.enableVirtualization&&"auto"==i.style.overflowY&&this.parent.options.frozenColumns>0&&"auto"==this.parent.options.height&&(n=0),t.style[r.padding]=n>0?n+"px":"0px",t.querySelector(".e-headercontent").style[r.border]=n>0?"1px":"0px";var o=this.parent.element.querySelector(".e-gridfooter");if(o){var s=o.querySelector(".e-summarycontent");this.parent.options.enableAdaptiveUI||(s.style[r.border]=n>0?"1px":"0px"),o.style[r.padding]=n>0?n+"px":"0px"}}},e.prototype.removePadding=function(e){var t=this.getCssProperties(e),n=this.parent.getHeaderContent();n.style[t.border]="",n.parentElement.style[t.padding]="";this.parent.getFooterContent()},e.prototype.refresh=function(){if("100%"===this.parent.options.height){var e=this.parent.element.querySelector(".e-gridcontent"),t=s(e);e.style.height="calc(100% - "+t+"px)"}},e.prototype.getThreshold=function(){return"mozilla"===sf.base.Browser.info.name?.5:1},e.prototype.addEventListener=function(){this.wireEvents()},e.prototype.onContentScroll=function(e){var t=this,n=e,r=n.classList.contains("e-headercontent");return function(e){if(null!==t.content.querySelector("tbody")&&!t.parent.options.isPreventScrollEvent){var i=e.target,o=i.scrollLeft;i.scrollWidth;t.updateFrozenShadow(i);var s=t.parent.getColumns().filter((function(e){return e.isFrozen&&"Right"===e.freeze}));t.content.scrollTop>0&&t.parent.options.frozenRows?t.parent.element.classList.add("e-top-shadow"):t.parent.element.classList.remove("e-top-shadow"),Math.round(i.scrollWidth-i.scrollLeft)===i.offsetWidth&&s.length>0?t.parent.element.classList.remove("e-right-shadow"):t.parent.element.classList.add("e-right-shadow");var l=i.classList.contains("e-summarycontent");if(t.parent.options.enableInfiniteScrolling&&!t.parent.options.isEdit&&(t.isLazyChildLoad||t.infiniteScrollHandler(i)),t.parent.options.groupCount>0&&t.parent.options.enableLazyLoading){var a=t.previousValues.top<i.scrollTop;t.lazyLoadInfiniteScrollHandler(i,a)}if(t.previousValues.left!==o){n.scrollLeft=o;var d=t.parent.element.querySelector(".e-summarycontent");d&&(d.scrollLeft=o),l&&(t.header.scrollLeft=o),t.previousValues.left=o}else t.previousValues.top=r?i.scrollTop:t.previousValues.top}}},e.prototype.onFreezeContentScroll=function(e){var t=this,n=e;return function(e){if(null!==t.content.querySelector("tbody")){var r=e.target.scrollTop;t.previousValues.top!==r&&(n.scrollTop=r,t.previousValues.top=r)}}},e.prototype.updateFrozenShadow=function(e){var t=this.parent.getColumns().filter((function(e){return e.isFrozen&&"Left"===e.freeze})),n=this.parent.getColumns().filter((function(e){return e.isFrozen&&"Right"===e.freeze}));0!==e.scrollLeft&&(this.parent.options.frozenColumns>0&&0==n.length||t.length>0)?this.parent.element.classList.add("e-left-shadow"):this.parent.element.classList.contains("e-left-shadow")&&this.parent.element.classList.remove("e-left-shadow")},e.prototype.onCustomScrollbar=function(e,t){var n,r=this,i=e,o=t;return function(e){if(null!==r.content.querySelector("tbody")){var t=e.target,s=t.scrollLeft;r.previousValues.left!==s&&(r.updateFrozenShadow(t),r.parent.options.aggregatesCount&&(n=r.parent.element.querySelector(".e-movablefootercontent")),i.scrollLeft=s,o.scrollLeft=s,n&&(n.scrollLeft=s),r.previousValues.left=s)}}},e.prototype.onWheelScroll=function(e){var t=this,n=e;return function(e){if(null!==t.content.querySelector("tbody")){var r=n.scrollTop+(1===e.deltaMode?30*e.deltaY:e.deltaY);t.previousValues.top!==r&&(e.preventDefault(),t.parent.getContent().querySelector(".e-frozencontent").scrollTop=r,n.scrollTop=r,t.previousValues.top=r)}}},e.prototype.onTouchScroll=function(e){var t=this,n=e;return function(e){if("mouse"!==e.pointerType){var r=t.parent.options.frozenColumns>0,i=t.getPointXY(e),o=n.scrollLeft+(t.pageXY.x-i.x),s=r?t.parent.getHeaderContent():t.parent.getHeaderContent().querySelector(".e-headercontent"),l=r?t.parent.getContent():t.parent.getContent().querySelector(".e-content");if(!(t.previousValues.left===o||o<0||s.scrollWidth-s.clientWidth<o)){if(event.cancelable&&e.preventDefault(),s.scrollLeft=o,l.scrollLeft=o,r)t.parent.element.querySelector(".e-movablescrollbar").scrollLeft=o;t.pageXY.x=i.x,t.previousValues.left=o}}}},e.prototype.setPageXY=function(){var e=this;return function(t){"mouse"!==t.pointerType&&(e.pageXY=e.getPointXY(t))}},e.prototype.getPointXY=function(e){var t={x:0,y:0};return e.touches&&e.touches.length?(t.x=e.touches[0].pageX,t.y=e.touches[0].pageY):(t.x=e.pageX,t.y=e.pageY),t},e.prototype.wireEvents=function(){var e=this;if(this.oneTimeReady){this.content=this.parent.getContent(),this.header=this.parent.getHeaderContent();var t=this.content.parentElement.querySelector(".e-movablescrollbar");if(this.parent.options.frozenColumns>0&&this.parent.options.enableColumnVirtualization?(sf.base.EventHandler.add(t,"scroll",this.onCustomScrollbar(this.content,this.header),this),sf.base.EventHandler.add(this.content,"scroll",this.onCustomScrollbar(t,this.header),this),sf.base.EventHandler.add(this.header,"scroll",this.onCustomScrollbar(t,this.content),this),sf.base.EventHandler.add(this.header,"touchstart pointerdown",this.setPageXY(),this),sf.base.EventHandler.add(this.content,"touchstart pointerdown",this.setPageXY(),this),sf.base.EventHandler.add(this.content,"touchmove pointermove",this.onTouchScroll(this.header),this)):(sf.base.EventHandler.add(this.content,"scroll",this.onContentScroll(this.header),this),sf.base.EventHandler.add(this.header,"scroll",this.onContentScroll(this.content),this)),this.parent.options.aggregatesCount){var n=this.parent.element.querySelector(".e-summarycontent");sf.base.isNullOrUndefined(n)||sf.base.EventHandler.add(n,"scroll",this.onContentScroll(this.content),this)}this.parent.options.enableStickyHeader&&this.addStickyListener(!0),this.refresh(),this.oneTimeReady=!1}var r,i,o,s,l,a=this.parent.getContent().querySelector(".e-table");s=function(){r=e.header.scrollLeft,i=a.scrollHeight,o=e.parent.getContent().clientHeight},l=function(){e.parent.options.enableVirtualization||(i<o&&sf.base.addClass(a.querySelectorAll("tr:last-child td"),"e-lastrowcell"),e.header.scrollLeft=e.previousValues.left,e.content.scrollLeft=e.previousValues.left,e.content.scrollTop=e.previousValues.top),e.parent.options.enableColumnVirtualization||(e.content.scrollLeft=r),e.parent.options.frozenColumns&&e.parent.getHeaderContent()&&(e.parent.getHeaderContent().scrollLeft=e.parent.getContent().scrollLeft)},requestAnimationFrame((function(){try{l(null,s())}catch(e){l(e)}}))},e.prototype.getCssProperties=function(e){var t={},n=sf.base.isNullOrUndefined(e)?this.parent.options.enableRtl:e;return t.border=n?"borderLeftWidth":"borderRightWidth",t.padding=n?"paddingLeft":"paddingRight",t},e.prototype.getScrollableParent=function(e){if(null===e)return null;var t=sf.base.isNullOrUndefined(e.tagName)?e.scrollingElement:e,n=document.defaultView.getComputedStyle(t,null).overflowY;return t.scrollHeight>t.clientHeight&&"hidden"!==n&&"visible"!==n||"HTML"===e.tagName||"ARTICLE"===e.tagName?e:this.getScrollableParent(e.parentNode)},e.prototype.addStickyListener=function(e){this.gridParentElement=this.getScrollableParent(this.parent.element.parentElement),e&&(this.gridParentElement?sf.base.EventHandler.add("HTML"===this.gridParentElement.tagName||"ARTICLE"===this.gridParentElement.tagName?document:this.gridParentElement,"scroll",this.makeStickyHeader,this):sf.base.EventHandler.remove(this.gridParentElement,"scroll",this.makeStickyHeader))},e.prototype.makeStickyHeader=function(){if(this.parent.options.enableStickyHeader&&this.parent.element&&!sf.base.isNullOrUndefined(this.gridParentElement)&&this.parent.getContent()){var e=this.parent.getContent().parentElement.getBoundingClientRect();if(e){var t=this.parent.getHeaderContent().parentElement,n=this.parent.element.querySelector(".e-toolbar"),r=this.parent.element.querySelector(".e-groupdroparea"),i=this.parent.element.querySelector(".e-ccdlg"),o=this.parent.element.querySelector(".e-cc-toolbar"),s=t.offsetHeight+(n?n.offsetHeight:0)+(r?r.offsetHeight:0),l=this.gridParentElement.getBoundingClientRect().top,a=e.top-(l<0?0:l),d=e.left;if(a<s&&e.bottom>0){t.classList.add("e-sticky");var u=0;r&&(this.setSticky(r,u,e.width,d,!0),u+=r.getBoundingClientRect().height-2),n&&(this.setSticky(n,u,e.width,d,!0),u+=n.getBoundingClientRect().height-2),this.setSticky(t,u,e.width,d,!0)}else if(t.classList.contains("e-sticky")&&this.setSticky(t,null,null,null,!1),r&&this.setSticky(r,null,null,null,!1),n&&this.setSticky(n,null,null,null,!1),i){var h=sf.popups.calculateRelativeBasedPosition(o,i).top+o.getBoundingClientRect().height,p=i.style.display;i.style.display="block";var c=i.getBoundingClientRect(),f=o.getBoundingClientRect().left-e.left-c.width+o.clientWidth+2;i.style.display=p,this.setSticky(i,h,c.width,f,!1)}}}},e.prototype.setSticky=function(e,t,n,r,i){i?e.classList.add("e-sticky"):e.classList.remove("e-sticky"),e.style.width=null!=n?n+"px":"",e.style.top=null!=t?t-2+"px":"",e.style.left=null!==r?parseInt(e.style.left,10)!==r?r+"px":e.style.left:""},e.prototype.infiniteScrollHandler=function(e){var t=this,n=e.scrollLeft!==this.previousValues.left,r=e.classList.contains("e-content"),i="chrome"===sf.base.Browser.info.name?200:100;if(r&&!n){var o=e.scrollHeight-e.scrollTop,s=Math.round(o),l=o<e.clientHeight?Math.ceil(o):Math.floor(o);this.rowElements=this.parent.getDataRows(),l>e.clientHeight&&(l-=1);var a=l===e.clientHeight||s===e.clientHeight,d=this.parent.options.allowGrouping&&!this.parent.options.enableLazyLoading&&this.parent.options.groupCount>0&&this.parent.options.currentPage>=1&&!this.infiniteInitialRender;a&&!this.infiniteDataRequested&&!this.infiniteInitialRender&&(this.parent.options.currentPage<=this.maxPage-1||this.parent.options.totalItemCount!=this.rowElements.length&&!this.infiniteInitialRender&&!this.parent.options.infiniteCacheMode)?(setTimeout((function(){t.parent.dotNetRef.invokeMethodAsync("LoadInfiniteData",{requestType:"InfiniteScrolling"},a,!1,!1,null,null,0)}),i),this.infiniteScrollTop=this.calculateScrollPosition("down"),this.infiniteScollDirection="down",this.infiniteDataRequested=!0):0===e.scrollTop&&(1!==this.parent.options.currentPage&&this.parent.options.currentPage>1||d)&&this.parent.options.infiniteCacheMode&&(setTimeout((function(){t.parent.dotNetRef.invokeMethodAsync("LoadInfiniteData",{requestType:"InfiniteScrolling"},a,!0,!1,null,null,0)}),i),this.infiniteScrollTop=this.calculateScrollPosition("up"),this.infiniteScollDirection="up"),this.infiniteInitialRender=!1}},e.prototype.lazyLoadInfiniteScrollHandler=function(e,t){var n,r,i=this,o=[].slice.call(this.parent.getContent().getElementsByClassName("e-lazyload-middle-down")),s=[].slice.call(this.parent.getContent().getElementsByClassName("e-lazyload-last-down")),l=!1,a=!1,d=0,u=this.currentRowIndex,h="chrome"===sf.base.Browser.info.name?200:100;if(t&&o.length>0){var p=this.findRowElementsInGrid(o);l=p.rowEntered,d=p.rowIndex,n=p.row,l&&(this.currentRowIndex=d)}if(!t&&s.length>0)for(var c=0;c<s.length;c++){var f=s[parseInt(c.toString(),10)].getBoundingClientRect().top,g=s[c].rowIndex,m=this.parent.getContent().parentElement.scrollHeight;if(this.isLazyChildLoad=!1,f>0&&f<m){a=!0,g=(r=s[parseInt(c.toString(),10)]).rowIndex,a&&(this.currentRowIndex=g);break}}if((t&&l||!t&&a)&&this.currentRowIndex!=u&&!this.isLazyChildLoad){var v=sf.base.isNullOrUndefined(n)?null:n.getAttribute("data-uid"),b=sf.base.isNullOrUndefined(r)?null:r.getAttribute("data-uid"),y=l||a;setTimeout((function(){i.parent.dotNetRef.invokeMethodAsync("LoadInfiniteData",{requestType:"InfiniteScrolling"},!1,!1,y,v,b,d)}),h),this.isLazyChildLoad=!0}},e.prototype.findRowElementsInGrid=function(e){for(var t,n=!1,r=0,i=0;i<e.length;i++)r=e[parseInt(i.toString(),10)].rowIndex,this.isRowEnteredInGrid(r)&&(n=!0,t=e[i]);return{rowEntered:n,rowIndex:r,row:t}},e.prototype.isRowEnteredInGrid=function(e){var t=this.parent.getContent().scrollTop/this.parent.getRowHeight();return e<t+this.parent.getContent().offsetHeight/this.parent.getRowHeight()&&e>t},e.prototype.calculateScrollPosition=function(e){var t=0,n=this.parent.getContent();if("down"===e)if(this.parent.options.allowGrouping&&!sf.base.isNullOrUndefined(this.parent.options.initGroupingField)&&this.parent.options.initGroupingField.length>0&&!this.parent.options.infiniteCacheMode){t+=this.parent.getContentTable().querySelectorAll("tr:not(.e-row)").length*this.parent.getRowHeight()}else{var r=this.parent.options.pageSize*(this.parent.options.infiniteMaxBlocks-1);r=0==r?this.parent.options.pageSize:r;for(var i=0,s=0;i<n.offsetHeight;)i=++s*this.parent.getRowHeight();t+=(r-(s-=1.5))*this.parent.getRowHeight()}else"up"===e&&(t+=this.parent.options.pageSize*this.parent.getRowHeight()+o()+10);return t},e.prototype.resetInfniniteScrollPositions=function(){var e=this,t=this.parent.getContent();if(this.infiniteInitialRender&&(this.maxPage=Math.ceil(this.parent.options.totalItemCount/this.parent.options.pageSize)),["Refresh","Filtering","ClearFiltering","Sorting","sorting","Searching","Grouping","UnGrouping","Reorder","refresh","filtering","searching","grouping","ungrouping","reorder","GroupExpandCollapse",null].some((function(t){return e.parent.options.requestType===t}))&&(this.infiniteInitialRender=!0,t.scrollTop=0,this.infiniteScollDirection=""),"Delete"==this.parent.options.requestType){var n=t.scrollTop+t.offsetHeight>=t.scrollHeight;this.infiniteInitialRender=!!n||this.infiniteInitialRender}"down"!==this.infiniteScollDirection&&"up"!==this.infiniteScollDirection||(this.infiniteDataRequested=!1,(this.parent.options.currentPage<=this.maxPage-1||0===t.scrollTop)&&this.parent.options.infiniteCacheMode&&"InfiniteScrolling"===this.parent.options.requestType&&(t.scrollTop=this.infiniteScrollTop)),this.isLazyChildLoad&&(this.isLazyChildLoad=!1)},e.prototype.infiniteOnDataReady=function(){var e=this;this.maxPage=Math.ceil(this.parent.options.totalItemCount/this.parent.options.pageSize),this.parent.options.allowGrouping&&!this.parent.options.enableLazyLoading&&this.parent.options.groupCount>0&&(this.maxPage=this.maxPage+1),["Refresh","Filtering","Sorting","sorting","Searching","Grouping","UnGrouping","Reorder","RowDragAndDrop","refresh","filtering","searching","grouping","ungrouping","reorder","GroupExpandCollapse",null].some((function(t){return e.parent.options.requestType===t}))&&(this.infiniteDataRequested=!1)},e.prototype.destroy=function(){var e=this.parent.element;if(e&&(e.querySelector(".e-gridheader")||e.querySelector(".e-gridcontent"))&&(this.parent.options.enableStickyHeader&&this.addStickyListener(!1),this.removePadding(),sf.base.EventHandler.remove(this.parent.getContent(),"scroll",this.onContentScroll),sf.base.EventHandler.remove(this.header,"scroll",this.onContentScroll),this.parent.options.aggregatesCount)){var t=this.parent.element.querySelector(".e-summarycontent");sf.base.isNullOrUndefined(t)||sf.base.EventHandler.remove(t,"scroll",this.onContentScroll)}},e.getScrollBarWidth=function(){return o()},e}(),v=function(){function e(e){this.parent=e}return e.prototype.refreshFreeze=function(e){if("textwrap"===e.case||"refreshHeight"===e.case){var t=void 0,n=void 0,r=void 0,i=this.getFrozenHeader(),o=this.getMovableHeader(),s=this.parent.getContent(),l=this.parent.options.wrapMode;"textwrap"===e.case?("Both"===l||e.isModeChg?(t=i.querySelectorAll("tr"),n=o.querySelectorAll("tr"),"LeftRight"==this.parent.options.frozenName&&(r=this.parent.element.querySelectorAll(".e-frozenheader")[1].querySelectorAll("tr"))):(t=i.querySelector("Content"===l?"tbody":"thead").querySelectorAll("tr"),n=o.querySelector("Content"===l?"tbody":"thead").querySelectorAll("tr")),this.parent.getHeaderContent().querySelectorAll(".e-stackedheadercell").length||this.setWrapHeight(t,n,e.isModeChg,!1,!1,r),this.refreshStackedHdrHgt()):"refreshHeight"===e.case&&("LeftRight"===this.parent.options.frozenName&&(r=s.querySelector(".e-frozen-right-content").querySelectorAll("tr")),this.setWrapHeight(s.querySelector(".e-frozencontent").querySelectorAll("tr"),s.querySelector(".e-movablecontent").querySelectorAll("tr"),e.isModeChg,!1,!1,r),this.parent.getHeaderContent().querySelectorAll(".e-stackedheadercell").length||("LeftRight"==this.parent.options.frozenName&&(r=this.parent.element.querySelectorAll(".e-frozenheader")[1].querySelectorAll("tr")),this.setWrapHeight(i.querySelectorAll("tr"),o.querySelectorAll("tr"),e.isModeChg,!1,!1,r)))}},e.prototype.updateResizeHandler=function(){for(var e=[].slice.call(this.parent.getHeaderContent().querySelectorAll(".e-rhandler")),t=0;t<e.length;t++)e[t].style.height=e[t].parentElement.offsetHeight+"px"},e.prototype.setWrapHeight=function(e,t,n,r,i,o){for(var s,l,a,d,u=this.parent.options.allowTextWrap,h=this.parent.options.wrapMode,p=this.parent.getHeaderContent().querySelector("thead"),c=this.parent.getHeaderContent().querySelector("tbody"),f=[],g=[],m=[],v=0,b=e.length;v<b;v++)sf.base.isNullOrUndefined(e[v])||sf.base.isNullOrUndefined(t[v])||(o&&(m[v]=o[v].getBoundingClientRect().height),f[v]=e[v].getBoundingClientRect().height,g[v]=t[v].getBoundingClientRect().height);for(v=0,b=e.length;v<b;v++)(n&&("Header"===h&&r||"Content"===h&&p.contains(e[v])||"Header"===h&&c.contains(e[v]))||i)&&(o[v]&&(o[v].style.height=null),e[v].style.height=null,t[v].style.height=null),s=f[v],l=g[v],a=m[v]?m[v]:0,0!=this.parent.options.rowHeight?d=this.parent.options.rowHeight:(d=Math.max(s,l,a),t[v].style.height=d+"px",e[v].style.height=d+"px"),o&&(o[v].style.height=d+"px");u&&this.setFrozenHeight()},e.prototype.setFrozenHeight=function(e){if(!this.parent.options.isPreventScrollEvent){var t=this.parent.element.querySelector(".e-content"),n=this.parent.element.querySelectorAll(".e-frozenscrollbar"),r=this.parent.element.querySelector(".e-movablescrollbar"),i=this.parent.element.querySelector(".e-movablechild"),s=this.parent.element.querySelector(".e-content");if(this.parent.options.frozenColumns>0&&"100%"===this.parent.options.height&&this.parent.options.enableColumnVirtualization){var l=this.parent.element.querySelector(".e-movablescrollbar").offsetHeight;s.style.height="calc(100% - "+l+"px)"}if(sf.base.isNullOrUndefined(t)||(e=Array.from(t.getElementsByClassName("e-table")[0].querySelectorAll("col")).filter((function(e){return"width: auto"==e.getAttribute("style")})).length>0&&e?e:0),r&&"100%"!==this.parent.options.height&&"auto"!==this.parent.options.height){var a=parseInt(this.parent.options.height.split?this.parent.options.height.split("px")[0]:this.parent.options.height,10)-r.offsetHeight;s.style.height=a+"px"}if((this.parent.options.enableVirtualization&&this.parent.options.enableColumnVirtualization||this.parent.options.enableColumnVirtualization)&&n){for(var d=this.parent.options.columns,u=0,h=0;h<d.length;h++)d[h].visible&&!sf.base.isNullOrUndefined(d[h].width)&&(u+=parseFloat(d[h].width.split?d[h].width.split("px")[0]:d[h].width.toString()));i.style.width=u+o()+"px"}else sf.base.isNullOrUndefined(r)||sf.base.isNullOrUndefined(i)||(r.style.width=t.offsetWidth+"px",i.style.width=t.getElementsByClassName("e-table")[0].offsetWidth+e+o()+"px");sf.base.isNullOrUndefined(t)||this.parent.scrollModule.setPadding()}},e.prototype.updateStackedFrozenHeight=function(e,t){for(var n,r,i=0,o=e.length;i<o;i++)n=this.getRowSpan(e[i]),r=this.getRowSpan(t[i]),n.min>1?this.updateStackedHdrRowHgt(i,n.max,e[i],t):r.min>1&&this.updateStackedHdrRowHgt(i,r.max,t[i],e)},e.prototype.refreshStackedHdrHgt=function(){var e,t,n,r=this.getFrozenHeader().querySelectorAll(".e-columnheader"),i=this.getMovableHeader().querySelectorAll(".e-columnheader");"LeftRight"==this.parent.options.frozenName?(t=this.parent.element.querySelectorAll(".e-frozenheader")[0].querySelectorAll(".e-columnheader"),e=this.parent.element.querySelectorAll(".e-frozenheader")[1].querySelectorAll(".e-columnheader"),(n=i.length>t.length?i.length>e.length?i:e:t.length>e.length?t:e)==e?(this.updateStackedFrozenHeight(t,n),this.updateStackedFrozenHeight(e,i)):n==t?(this.updateStackedFrozenHeight(t,i),this.updateStackedFrozenHeight(e,n)):(this.updateStackedFrozenHeight(t,n),this.updateStackedFrozenHeight(e,i))):this.updateStackedFrozenHeight(r,i),this.parent.options.allowResizing&&this.updateResizeHandler()},e.prototype.getRowSpan=function(e){for(var t,n,r,i=0,o=e.childElementCount;i<o;i++)0===i&&(n=e.children[0].rowSpan),t=e.children[i].rowSpan,n=Math.min(t,n),r=Math.max(t,n);return{min:n,max:r}},e.prototype.updateStackedHdrRowHgt=function(e,t,n,r){for(var i=0,o=0;o<t;o++)i+=r[e+o].style.height?parseInt(r[e+o].style.height,10):r[e+o].offsetHeight;n.style.height=i+"px"},e.prototype.getFrozenHeader=function(){return this.frozenHeader},e.prototype.getMovableHeader=function(){return this.movableHeader},e.prototype.refreshRowHeight=function(){this.parent.options.rowHeight},e.prototype.clearWrapHeight=function(){var e=function(e,t){for(var n=0,r=e.length;n<r;n++)sf.base.isNullOrUndefined(e[n])||sf.base.isNullOrUndefined(t[n])||(e[n].style.height=null,t[n].style.height=null)};this.parent.options.frozenColumns&&((this.parent.options.frozenRows||"Both"==this.parent.options.wrapMode||"Header"==this.parent.options.wrapMode)&&e(this.parent.element.querySelector(".e-frozenheader").querySelectorAll("tr"),this.parent.element.querySelector(".e-movableheader").querySelectorAll("tr")),e(this.parent.element.querySelector(".e-frozencontent").querySelectorAll("tr"),this.parent.element.querySelector(".e-movablecontent").querySelectorAll("tr"))),this.parent.options.frozenRows&&0==this.parent.options.frozenColumns&&e(this.parent.element.querySelector(".e-headercontent").querySelectorAll("tr"),this.parent.element.querySelector(".e-content").querySelectorAll("tr"))},e}(),b=function(){function e(e){this.parent=e}return e.prototype.setMinwidthBycalculation=function(e){var t=0,n=this.parent.getColumns().filter((function(e){return sf.base.isNullOrUndefined(e.width)||"auto"===e.width||""===e.width}));if(n.length){t=sf.base.isNullOrUndefined(this.parent.options.width)||"auto"===this.parent.options.width||"100%"===this.parent.options.width?this.parent.element.getBoundingClientRect().width-e:("string"==typeof this.parent.options.width?parseInt(this.parent.options.width,10):this.parent.options.width)-e;for(var r=0,i=0,o=0,s=n;o<s.length;o++){var l=s[o];r+=sf.base.isNullOrUndefined(l.minWidth)?0:"string"==typeof l.minWidth?parseInt(l.minWidth,10):l.minWidth}for(var a={},d=0;d<n.length;d++)0===e&&this.parent.options.allowResizing&&this.isWidthUndefined()&&d!==n.length-1&&this.setUndefinedColumnWidth(n),0!==e&&t<r?(a[n[d].field]=n[d].minWidth+"px",i+=parseInt(n[d].minWidth.toString(),10)):0!==e&&t>r&&(a[n[d].field]="",i+=0);this.parent.dotNetRef.invokeMethodAsync("SetMinWidth",a),this.parent.options.frozenColumns&&this.parent.freezeModule.setFrozenHeight(i)}},e.prototype.setUndefinedColumnWidth=function(e){for(var t=0;t<e.length;t++)t!==e.length-1&&(e[t].width=200,this.setWidth(200,this.parent.getColumnIndexByField(e[t].field)))},e.prototype.setColumnWidth=function(e,t,n,r,i){if(void 0===r&&(r=!0),void 0===i&&(i=!1),!(this.parent.getColumns(i).length<1)){var o;o=this.parent.options.enableColumnVirtualization?this.parent.options.virtualizedColumns.findIndex((function(t){return t.uid==e.uid}))+this.parent.getIndentCount():sf.base.isNullOrUndefined(t)?this.parent.getNormalizedColumnIndex(e.uid):t;var s=this.getWidth(e),l=this.getTableWidth(this.parent.getColumns(i));null!==s&&(this.setWidth(s,o),"auto"!==this.parent.options.width&&-1===this.parent.options.width.toString().indexOf("%")&&this.setMinwidthBycalculation(l),this.parent.options.enableColumnVirtualization&&"resize"===n?(this.parent.options.virtualizedColumns.filter((function(t){return t.uid==e.uid}))[0].width=s,this.parent.virtualContentModule.refreshOffsets(),this.parent.virtualContentModule.setVirtualHeight()):this.parent.options.enableColumnVirtualization&&i&&(this.parent.options.columns.filter((function(t){return t.uid==e.uid}))[0].width=s,this.parent.virtualContentModule.refreshOffsets()),(this.parent.options.allowResizing&&"resize"===n||this.parent.options.frozenColumns&&this.parent.options.allowResizing)&&this.setWidthToTable(null,!1,"resize"),r&&(s.toString().indexOf("px")>0&&(s=s.toString().replace("px","")),this.parent.dotNetRef.invokeMethodAsync("ColumnWidthChanged",{index:o,width:s,columnUid:e.uid})))}},e.prototype.setWidth=function(e,t,n){var r=this;if("string"==typeof e&&-1!==e.indexOf("%")&&(!Boolean(window.chrome)||!Boolean(window.chrome.webstore))&&this.parent.options.allowGrouping){var i=this.parent.element.offsetWidth;e=parseInt(e,10)/100*i}var o,s,l=this.parent.getHeaderTable(),d=this.parent.getContentTable(),u=sf.base.formatUnit(e),h=this.parent.options.frozenColumns,p=this.parent.getHeaderContent().querySelector(".e-movableheader"),c=this.parent.getContent().querySelector(".e-movablecontent"),f=0;if(0!=this.parent.options.frozenRightCount||0!=this.parent.options.frozenLeftCount){this.parent.updateColumnLevelFrozen();var g=this.parent.options.enableColumnVirtualization?t:this.parent.frozenColumnModel.findIndex((function(e){return e.uid==r.parent.columnModel[t].uid}));if("Left"==this.parent.options.frozenName&&(v=g<this.parent.options.frozenLeftCount?l:p),"Right"==this.parent.options.frozenName)v=g>=(f=this.parent.options.columns.length-this.parent.options.frozenRightCount)?l:p;else if("LeftRight"==this.parent.options.frozenName){f=this.parent.options.columns.length-this.parent.options.frozenRightCount-this.parent.options.frozenLeftCount;var m=this.parent.getHeaderContent().querySelector(".e-frozen-right-header");v=g<this.parent.options.frozenLeftCount?l:g<this.parent.options.frozenLeftCount+f?p:m}if(!(o=this.getColumnLevelFrozenColgroup(g,this.parent.options.frozenLeftCount,f,v)))return}else o=h&&t>=h&&p&&p.querySelector("colgroup")?p.querySelector("colgroup").children[t-h]:l.querySelector("colgroup").children[t];if(o&&!n?o.style.width=u:o&&n&&(o.style.width=" "),0!=this.parent.options.frozenRightCount||0!=this.parent.options.frozenLeftCount){var v;g=this.parent.options.enableColumnVirtualization?t:this.parent.frozenColumnModel.findIndex((function(e){return e.uid==r.parent.columnModel[t].uid}));if("Left"==this.parent.options.frozenName&&(v=g<this.parent.options.frozenLeftCount?d:c),"Right"==this.parent.options.frozenName&&(v=g>=(f=this.parent.options.columns.length-this.parent.options.frozenRightCount)?d:c),"LeftRight"==this.parent.options.frozenName){var b=this.parent.getContent().querySelector(".e-frozen-right-content");v=g<this.parent.options.frozenLeftCount?d:g<this.parent.options.frozenLeftCount+f?c:b}s=this.getColumnLevelFrozenColgroup(g,this.parent.options.frozenLeftCount,f,v)}else s=d.querySelector("colgroup").children[t];if(s&&!n?s.style.width=u:s&&n&&(s.style.width=" "),0!=this.parent.options.aggregatesCount){var y=void 0,C=this.parent.getFooterContent().querySelector("colgroup");y=sf.base.isNullOrUndefined(C)?null:C.children[t],s&&y&&!n?y.style.width=u:s&&y&&n&&(y.style.width=" ")}for(var w=this.parent.element.querySelectorAll(".e-table.e-inline-edit"),S=[],x=0;x<w.length;x++)if(a(w[x],"e-grid").id===this.parent.element.id)for(var z=0;z<w[x].querySelector("colgroup").children.length;z++)S.push(w[x].querySelector("colgroup").children[z]);w.length&&S.length&&(S[t].style.width=u),0==this.parent.options.frozenColumns||this.parent.options.enableColumnVirtualization||this.parent.freezeModule.setFrozenHeight()},e.prototype.getColumnLevelFrozenColgroup=function(e,t,n,r){if(!r||!r.querySelector("colgroup"))return null;var i=this.parent.options.enableColumnVirtualization?this.parent.options.virtualizedColumns:this.parent.frozenColumnModel,o=[].slice.call(r.querySelector("colgroup").children);return"Left"===i[e].freeze&&i[e].isFrozen?o[e]:"Right"===i[e].freeze&&i[e].isFrozen?o[e-(t+n)]:o[e-t]},e.prototype.isWidthUndefined=function(){var e=this.parent.getColumns().filter((function(e){return sf.base.isNullOrUndefined(e.width)&&sf.base.isNullOrUndefined(e.minWidth)})).length;return this.parent.getColumns().length===e},e.prototype.getWidth=function(e){if(!e.width)return null;var t=parseInt(e.width.toString(),10);return e.minWidth&&t<parseInt(e.minWidth.toString(),10)?e.minWidth:e.maxWidth&&t>parseInt(e.maxWidth.toString(),10)?e.maxWidth:e.width},e.prototype.getTableWidth=function(e){for(var t=0,n=0,r=e;n<r.length;n++){var i=r[n],o=this.getWidth(i);"auto"===i.width&&(o=0),!1!==i.visible&&null!==o&&(t+=parseInt(o.toString(),10))}return t},e.prototype.calcMovableOrFreezeColWidth=function(e){var t=0!=this.parent.frozenColumnModel.length?this.parent.frozenColumnModel.slice():this.parent.getColumns().slice(),n=0;if(!this.parent.options.frozenLeftColumnsCount&&!this.parent.options.frozenRightColumnsCount){for(var r=0;r<t.length;r++)t[r].index<this.parent.options.actualFrozenColumns&&!t[r].isFrozen&&n++;this.parent.options.actualFrozenColumns=n}var i=this.parent.options.frozenLeftColumnsCount||this.parent.options.actualFrozenColumns,o=t.length-this.parent.options.frozenColumns;return"movable"===e?(this.parent.options.frozenRightColumnsCount&&t.splice(i+o,t.length),i&&t.splice(0,i)):"freeze-left"===e?t.splice(i,t.length):"freeze-right"===e&&t.splice(0,i+o),sf.base.formatUnit(this.getTableWidth(t))},e.prototype.setWidthToFrozenLeftTable=function(e){var t=sf.base.isNullOrUndefined(e)?this.calcMovableOrFreezeColWidth("freeze-left"):e;t=this.parent.getContent().querySelector(".e-frozen-left-content").classList.contains("e-frozenborderdisabled")?"0":t,this.parent.getHeaderTable().style.width=t,this.parent.getContentTable().style.width=t,this.parent.resizeModule.leftFrozenTableWidth=t,this.parent.getFooterContent()&&!sf.base.isNullOrUndefined(this.parent.getFooterContent().querySelector(".e-frozen-left-footercontent"))&&(this.parent.getFooterContent().querySelector(".e-frozen-left-footercontent").style.width=t)},e.prototype.setWidthToFrozenRightTable=function(e){var t=sf.base.isNullOrUndefined(e)?this.calcMovableOrFreezeColWidth("freeze-right"):e;t=this.parent.getContent().querySelector(".e-frozen-right-content").classList.contains("e-frozenborderdisabled")?"0":t,this.parent.options.enableColumnVirtualization||(this.parent.getHeaderContent().querySelector(".e-frozen-right-header").querySelector(".e-table").style.width=t,this.parent.getContent().querySelector(".e-frozen-right-content").querySelector(".e-table").style.width=t,this.parent.resizeModule.rightFrozenTableWidth=t,this.parent.getFooterContent()&&!sf.base.isNullOrUndefined(this.parent.getFooterContent().querySelector(".e-frozen-right-footercontent"))&&(this.parent.getFooterContent().querySelector(".e-frozen-right-footercontent").style.width=t))},e.prototype.setWidthToMovableTable=function(e){var t="";if(sf.base.isNullOrUndefined(e)){var n=this.parent.getColumns().filter((function(e){return sf.base.isNullOrUndefined(e.width)})).length>=1,r=this.parent.getColumns().filter((function(e){return"auto"===e.width})).length>=1;"number"!=typeof this.parent.options.width||n||r?n||r||(t=this.calcMovableOrFreezeColWidth("movable")):t=sf.base.formatUnit(this.parent.options.width-parseInt(this.calcMovableOrFreezeColWidth("freeze").split("px")[0],10)-5)}else t=e;this.parent.getHeaderContent().querySelector(".e-movableheader").firstElementChild&&!this.parent.options.enableColumnVirtualization&&(this.parent.getHeaderContent().querySelector(".e-movableheader").firstElementChild.style.width=t),this.parent.getFooterContent()&&this.parent.getFooterContent().querySelector(".e-movablefootercontent").firstElementChild&&!this.parent.options.enableColumnVirtualization&&(this.parent.getFooterContent().querySelector(".e-movablefootercontent").firstElementChild.style.width=t),this.parent.options.enableColumnVirtualization||(this.parent.getContent().querySelector(".e-movablecontent").firstElementChild.style.width=t,this.parent.resizeModule.tableWidth=t)},e.prototype.setWidthToFrozenEditTable=function(){var e=this.calcMovableOrFreezeColWidth("freeze");this.parent.element.querySelectorAll(".e-table.e-inline-edit")[0].style.width=e},e.prototype.setWidthToMovableEditTable=function(){var e=this.calcMovableOrFreezeColWidth("movable");this.parent.element.querySelectorAll(".e-table.e-inline-edit")[1].style.width=e},e.prototype.setPersistedWidth=function(e){this.parent.options.frozenColumns?0!=this.parent.options.frozenRightColumnsCount||0!=this.parent.options.frozenLeftColumnsCount?(0!=this.parent.options.frozenLeftColumnsCount&&this.setWidthToFrozenLeftTable(e.leftFrozenTableWidth),0!=this.parent.options.frozenRightColumnsCount&&this.setWidthToFrozenRightTable(e.rightFrozenTableWidth),this.setWidthToMovableTable(e.tableWidth)):(this.setWidthToFrozenLeftTable(e.leftFrozenTableWidth),this.setWidthToMovableTable(e.tableWidth)):(this.parent.getHeaderTable().style.width=e.tableWidth,this.parent.getContentTable().style.width=e.tableWidth,0!=this.parent.options.aggregatesCount&&(this.parent.getFooterContent().querySelector(".e-table").style.width=e.tableWidth))},e.prototype.setWidthToTable=function(e,t,n){var r,i;void 0===e&&(e=null),void 0===t&&(t=!1),void 0===n&&(n=""),r=this.parent.options.enableColumnVirtualization&&"resize"===n?sf.base.formatUnit(this.getTableWidth(this.parent.options.virtualizedColumns)):sf.base.formatUnit(this.getTableWidth(null!=e?e:this.parent.getColumns())),i=this.parent.getColumns().filter((function(e){return!0===e.autoFit})),(!this.parent.options.frozenColumns||this.parent.options.frozenColumns&&i.length>0||this.parent.options.frozenColumns&&this.parent.options.enableColumnVirtualization&&"resize"===n)&&(this.parent.options.hasDetailTemplate&&this.setWidth("30",0),t&&(r=""),this.parent.resizeModule.tableWidth=r,this.parent.getHeaderTable().style.width=r,this.parent.getContentTable().style.width=r,0==this.parent.options.aggregatesCount||sf.base.isNullOrUndefined(this.parent.getFooterContent().querySelector(".e-table"))||(this.parent.getFooterContent().querySelector(".e-table").style.width=r));var o=this.parent.element.querySelector(".e-table.e-inline-edit");o&&this.parent.options.frozenColumns?(this.setWidthToFrozenEditTable(),this.setWidthToMovableEditTable()):o&&(o.style.width=r)},e}(),y=function(){function e(e){var t=this;this.dragStart=function(e){var n=t.parent;document.body.classList.add("e-prevent-select");var r=n.element.querySelector(".e-gridpopup");r&&(r.style.display="none"),t.parent.reorderModule.dragStart({target:e.target,column:t.column,event:e.event}),t.parent.groupModule.columnDragStart({target:e.target,column:t.column,event:e.event}),e.bindEvents(e.dragElement)},this.drag=function(e){var n=t.parent,r=e.target;if(r){var i=sf.base.closest(r,".e-grid"),o=sf.base.closest(r,".e-gridcontent"),s=sf.base.closest(r,".e-gridpager"),l=t.parent.element.querySelector(".e-cloneproperties");if((!i||i.getAttribute("id")!==n.element.getAttribute("id")||o&&i.getAttribute("id")==sf.base.closest(o,".e-grid").getAttribute("id")||s)&&(sf.base.classList(l,["e-notallowedcur"],["e-defaultcur"]),n.options.allowReordering&&(n.options.enableColumnVirtualization?(n.element.querySelector(".e-reorderuparrow-virtual").style.display="none",n.element.querySelector(".e-reorderdownarrow-virtual").style.display="none"):(n.element.querySelector(".e-reorderuparrow").style.display="none",n.element.querySelector(".e-reorderdownarrow").style.display="none")),!n.options.groupReordering))return;n.options.allowReordering&&t.parent.reorderModule.drag({target:e.target,column:t.column,event:e.event}),n.options.allowGrouping&&t.parent.groupModule.columnDrag({target:e.target})}},this.dragStop=function(e){var n,r=t.parent;document.body.classList.remove("e-prevent-select");var i=r.element.querySelector(".e-gridpopup");i&&(i.style.display="none"),(!a(e.target,"e-headercell")&&!a(e.target,"e-groupdroparea")||!r.options.allowReordering&&a(e.target,"e-headercell")||!e.helper.getAttribute("e-mappinguid")&&a(e.target,"e-groupdroparea"))&&(sf.base.remove(e.helper),n=!0),r.options.allowReordering&&t.parent.reorderModule.dragStop({target:e.target,event:e.event,column:t.column,cancel:n})},this.drop=function(e){var n=t.parent,r=(e.droppedElement.getAttribute("e-mappinguid"),sf.base.closest(e.target,".e-grid"));sf.base.remove(e.droppedElement),r&&r.getAttribute("id")!==n.element.getAttribute("id")||!n.options.allowReordering&&!n.options.allowGrouping||(n.options.allowReordering&&t.parent.reorderModule.headerDrop({target:e.target}),n.options.allowGrouping&&n.options.showDropArea&&t.parent.groupModule.columnDrop({target:e.target,droppedElement:e.droppedElement}))},this.helper=function(e){var n=t.parent,r=e.sender.target,i=sf.base.closest(r,".e-headercell:not(.e-stackedHeaderCell)");if(i){var o=(i.querySelector(".e-headercelldiv")||i.querySelector(".e-stackedheadercelldiv")).getAttribute("e-mappinguid"),s=n.getColumnByUid(o);if(!sf.base.isNullOrUndefined(s)&&!s.allowGrouping&&!s.allowReordering)return!1}var l=a(r,"e-headercell");if(n.getContent().classList.contains("e-freezeline-moving")||!n.options.allowReordering&&!n.options.allowGrouping||!sf.base.isNullOrUndefined(l)&&l.querySelectorAll(".e-checkselectall").length>0)return!1;var d=sf.base.createElement("div",{className:"e-cloneproperties e-dragclone e-headerclone"}),u=r.classList.contains("e-headercell")?r:l;if(!u||!n.options.allowReordering&&u.classList.contains("e-stackedheadercell"))return!1;var h,p=u.offsetHeight,c=u.querySelector(".e-headercelldiv")||u.querySelector(".e-stackedheadercelldiv");if(c){if(h=u.querySelector(".e-stackedheadercelldiv")?n.getStackedHeaderColumnByHeaderText(c.innerText.trim(),n.options.columns):n.getColumnByUid(c.getAttribute("e-mappinguid")),t.column=h,t.column.lockColumn)return!1;d.setAttribute("e-mappinguid",c.getAttribute("e-mappinguid"))}return d.innerText=c?sf.base.isNullOrUndefined(h.headerText)?h.field:h.headerText:u.innerText,d.style.width=u.offsetWidth+"px",d.style.height=u.offsetHeight+"px",d.style.lineHeight=(p-6).toString()+"px",n.element.appendChild(d),d},this.parent=e,(this.parent.options.allowGrouping||this.parent.options.allowReordering)&&(this.initializeHeaderDrag(),this.initializeHeaderDrop())}return e.prototype.initializeHeaderDrag=function(){var e=this.parent;if(this.parent.options.allowReordering||this.parent.options.allowGrouping&&this.parent.options.showDropArea)for(var t=[].slice.call(e.getHeaderContent().querySelectorAll(".e-columnheader")),n=0,r=t.length;n<r;n++)this.draggable=new sf.base.Draggable(t[n],{dragTarget:".e-headercell",distance:5,helper:this.helper,dragStart:this.dragStart,drag:this.drag,dragStop:this.dragStop,abort:".e-rhandler",isPreventSelect:!1})},e.prototype.initializeHeaderDrop=function(){var e=this.parent;this.droppable=new sf.base.Droppable(e.getHeaderContent(),{accept:".e-dragclone",drop:this.drop})},e.prototype.destroy=function(){sf.base.isNullOrUndefined(this.draggable)||this.draggable.destroy(),sf.base.isNullOrUndefined(this.droppable)||this.droppable.destroy()},e}(),C=function(){function e(e){var t=this;this.drop=function(e){var n=a(e.target,"e-grid"),r=e.droppedElement.parentElement,i=!sf.base.isNullOrUndefined(n)&&!sf.base.isNullOrUndefined(r);t.parent.options.allowRowDragAndDrop&&t.parent.options.rowDropTarget?i&&n.id!==r.id&&0==t.parent.options.groupCount&&t.parent.rowDragAndDropModule.columnDrop({target:e.target,droppedElement:e.droppedElement,mouseEvent:e.event}):t.parent.groupModule.columnDrop({target:e.target,droppedElement:e.droppedElement}),sf.base.remove(e.droppedElement)},this.parent=e,this.parent.options.allowGrouping&&this.initializeContentDrop()}return e.prototype.initializeContentDrop=function(){var e=this.parent;new sf.base.Droppable(e.getContent(),{accept:".e-dragclone",drop:this.drop})},e}(),w=function(){function e(e){this.parent=e,e.options.allowReordering&&this.createReorderElement()}return e.prototype.chkDropPosition=function(e,t){var n=this.parent.getColumnByUid(t.firstElementChild.getAttribute("e-mappinguid")),r=!n||!n.lockColumn;return(e.parentElement.isEqualNode(t.parentElement)||this.parent.options.frozenColumns&&Array.prototype.indexOf.call(sf.base.closest(e,"thead").children,e.parentElement)===Array.prototype.indexOf.call(sf.base.closest(t,"thead").children,t.parentElement))&&this.targetParentContainerIndex(e,t)>-1&&r},e.prototype.chkDropAllCols=function(e,t){for(var n,r=this.getHeaderCells();!n&&r.length>0;)n=e!==r.pop()&&this.targetParentContainerIndex(e,t)>-1;return n},e.prototype.findColParent=function(e,t,n){n=n;for(var r=0,i=t.length;r<i;r++){if(e===t[r])return!0;if(t[r].columns){var o=n.length;if(n.push(t[r]),this.findColParent(e,t[r].columns,n))return!0;n.splice(o,n.length-o)}}return!1},e.prototype.getColumnsModel=function(e){for(var t=[],n=[],r=0,i=e.length;r<i;r++)t.push(e[r]),e[r].columns&&(n=n.concat(e[r].columns));return n.length&&(t=t.concat(this.getColumnsModel(n))),t},e.prototype.headerDrop=function(e){var t=this.parent,n=this.element.querySelector(".e-headercelldiv")||this.element.querySelector(".e-stackedheadercelldiv");this.parent.options.enableColumnVirtualization&&(n=this.draggedHeader.querySelector(".e-headercelldiv")||this.draggedHeader.querySelector(".e-stackedheadercelldiv"));var r=n.getAttribute("e-mappinguid"),i=t.getColumnByUid(r);if(sf.base.closest(e.target,"th")&&(sf.base.isNullOrUndefined(i)||i.allowReordering&&!i.lockColumn)){var o=sf.base.closest(e.target,".e-headercell"),s=(o.querySelector(".e-headercelldiv")||o.querySelector(".e-stackedheadercelldiv")).getAttribute("e-mappinguid");if(!sf.base.isNullOrUndefined(s)){var l=t.getColumnByUid(s);if(sf.base.isNullOrUndefined(l)||!l.allowReordering||l.lockColumn)return}if(o&&this.chkDropPosition(this.element,o)&&this.chkDropAllCols(this.element,o))if(this.parent.options.enableColumnVirtualization){var a=this.parent.options.columns,u=this.draggedHeader.querySelector(".e-headercelldiv").getAttribute("e-mappinguid"),h=this.parent.getColumns(!1,!0).filter((function(e){return e.uid===u})),p=null,c=h[0],f=o.querySelector(".e-headercelldiv").getAttribute("e-mappinguid");a.some((function(e,t){return e.uid===f&&(p=t,e.uid===f)}));sf.base.isNullOrUndefined(p)||this.moveColumns(p,c)}else{var g=this.targetParentContainerIndex(this.element,o),m=this.element.firstElementChild.getAttribute("e-mappinguid");if(this.destElement=o,m)this.moveColumns(g,this.parent.getColumnByUid(m));else{var v=this.getHeaderCells(),b=d(this.element,v),y=(a=this.getColumnsModel(this.parent.options.columns))[b];this.moveColumns(g,y)}}}},e.prototype.isActionPrevent=function(e){return t=e.element,n=t.querySelector("#"+t.id+"EditConfirm"),t.querySelectorAll(".e-updatedtd").length&&(!n||n.classList.contains("e-popup-close"));var t,n},e.prototype.moveColumns=function(e,t,n,r){var i=this.parent;if(!this.isActionPrevent(i)){var o=this.getColParent(t,this.parent.options.columns),s=o?o.columns:this.parent.options.columns,l=u(t,s);if(this.parent.options.frozenColumns&&o&&!n){for(var a=0;a<s.length;a++)if(s[a].field===t.field){l=a;break}var d=this.parent.getColumnByUid(this.destElement.firstElementChild.getAttribute("e-mappinguid"));if(d){for(a=0;a<s.length;a++)if(s[a].field===d.field){e=a;break}}else for(a=0;a<s.length;a++)s[a].headerText===this.destElement.innerText.trim()&&(e=a)}i.options.allowReordering&&l!==e&&-1!==l&&-1!==e&&(s.splice(e,0,s.splice(l,1)[0]),i.getColumns(!0),!1!==r&&setTimeout((function(){i.dotNetRef.invokeMethodAsync("ColumnReordered",{requestType:"reorder",fromIndex:e,toIndex:l,toColumnUid:t.uid})}),10))}},e.prototype.targetParentContainerIndex=function(e,t){var n=this.getHeaderCells();"None"!==this.parent.options.frozenName&&this.parent.updateColumnLevelFrozen();var r=this.parent.options.columns,i="None"==this.parent.options.frozenName?this.getColumnsModel(r):this.parent.frozenColumnModel.slice(),o=this.getColParent(i[d(e,n)],r);return r=o?o.columns:r,u(i[d(t,n)],r)},e.prototype.getHeaderCells=function(){return[].slice.call(this.parent.element.getElementsByClassName("e-headercell"))},e.prototype.getColParent=function(e,t){var n=[];return this.findColParent(e,t,n),n[n.length-1]},e.prototype.reorderSingleColumn=function(e,t){var n=this.parent.getColumnByField(e),r=this.parent.getColumnByField(t);if((sf.base.isNullOrUndefined(n)||n.allowReordering&&!n.lockColumn)&&(sf.base.isNullOrUndefined(r)||r.allowReordering&&!n.lockColumn)){var i=this.parent.getColumnByField(t),o=this.getColParent(i,this.parent.options.columns),s=u(i,o?o.columns:this.parent.options.columns);s>-1&&this.moveColumns(s,this.parent.getColumnByField(e),!0)}},e.prototype.reorderMultipleColumns=function(e,t){var n=this.parent.getColumnIndexByField(t),r=this.parent.getColumnByField(t);if(!(n<0)&&(sf.base.isNullOrUndefined(r)||r.allowReordering&&!r.lockColumn)){for(var i=0;i<e.length;i++){var o=this.parent.getColumnByField(e[i]);if(!sf.base.isNullOrUndefined(o)&&(!o.allowReordering||o.lockColumn))return}for(i=0;i<e.length;i++){o=this.parent.getColumnByIndex(n);var s=this.getColParent(o,this.parent.options.columns),l=u(o,s?s.columns:this.parent.options.columns);l>-1&&this.moveColumns(l,this.parent.getColumnByField(e[i]),!0,!1),this.parent.getColumnIndexByField(e[i+1])>=l&&n++}var a=this.parent.getColumns();this.parent.dotNetRef.invokeMethodAsync("ColumnReordered",{fromColumnUid:e.map((function(e){return a.filter((function(t){return t.field===e}))[0].uid})),toColumnUid:r.uid,isMultipleReorder:!0,requestType:"reorder",type:"actionBegin"})}},e.prototype.moveTargetColumn=function(e,t){t>-1&&this.moveColumns(t,e,!0)},e.prototype.reorderSingleColumnByTarget=function(e,t){var n=this.parent.getColumnByField(e);this.moveTargetColumn(n,t)},e.prototype.reorderMultipleColumnByTarget=function(e,t){for(var n=0;n<e.length;n++)this.reorderSingleColumnByTarget(e[n],t)},e.prototype.reorderColumns=function(e,t){"string"==typeof e?(this.reorderSingleColumn(e,t),this.fromCol=e):(this.reorderMultipleColumns(e,t),this.fromCol=e[0])},e.prototype.reorderColumnByIndex=function(e,t){var n=this.parent.getColumnByIndex(e);this.moveTargetColumn(n,t)},e.prototype.reorderColumnByTargetIndex=function(e,t){"string"==typeof e?this.reorderSingleColumnByTarget(e,t):this.reorderMultipleColumnByTarget(e,t)},e.prototype.createReorderElement=function(){var e=this.parent.element.querySelector(".e-headercontent");this.upArrow=e.appendChild(sf.base.createElement("div",{className:"e-icons e-icon-reorderuparrow e-reorderuparrow",attrs:{style:"display:none"}})),this.downArrow=e.appendChild(sf.base.createElement("div",{className:"e-icons e-icon-reorderdownarrow e-reorderdownarrow",attrs:{style:"display:none"}})),this.parent.options.enableColumnVirtualization&&(this.upArrow.classList.replace("e-reorderuparrow","e-reorderuparrow-virtual"),this.downArrow.classList.replace("e-reorderdownarrow","e-reorderdownarrow-virtual"))},e.prototype.destroy=function(){this.parent.element;this.upArrow&&sf.base.remove(this.upArrow),this.downArrow&&sf.base.remove(this.downArrow)},e.prototype.keyPressHandler=function(e){this.parent;e.action},e.prototype.drag=function(e){var t=this.parent,n=e.target,r=t.element.querySelector(".e-cloneproperties");if(e.column.allowReordering&&!e.column.lockColumn){var i=sf.base.closest(n,".e-headercell:not(.e-stackedHeaderCell)"),o=this.x>h(e.event).x+t.getContent().firstElementChild.scrollLeft;if(sf.base.removeClass(t.element.querySelector(".e-headercontent").querySelectorAll(".e-reorderindicate"),["e-reorderindicate"]),this.setDisplay("none"),this.stopTimer(),sf.base.classList(r,["e-defaultcur"],["e-notallowedcur"]),this.updateScrollPostion(e.event),i&&!i.isEqualNode(this.element)){n=i;var s=(i.querySelector(".e-headercelldiv")||i.querySelector(".e-stackedheadercelldiv")).getAttribute("e-mappinguid"),l=t.getColumnByUid(s);this.chkDropPosition(this.element,n)&&this.chkDropAllCols(this.element,n)&&l.allowReordering?(this.updateArrowPosition(n,o),sf.base.classList(n,["e-allowDrop","e-reorderindicate"],[])):t.options.allowGrouping&&a(e.target,"e-groupdroparea")||sf.base.classList(r,["e-notallowedcur"],["e-defaultcur"])}}else sf.base.classList(r,["e-notallowedcur"],["e-defaultcur"])},e.prototype.updateScrollPostion=function(e){var t=this,n=(this.parent.options.frozenColumns,h(e).x),r=this.parent.element.getBoundingClientRect(),i=r.left,o=r.right,s=this.parent.getContent();"None"!==this.parent.options.frozenName?this.updateFrozenScrollPosition(n,r):n>i&&n<i+35?this.timer=window.setInterval((function(){t.setScrollLeft(s,!0)}),50):n<o&&n>o-35&&(this.timer=window.setInterval((function(){t.setScrollLeft(s,!1)}),50))},e.prototype.updateFrozenScrollPosition=function(e,t){var n=this,r=this.parent.getContent().querySelector(".e-movablecontent"),i=this.parent.element.querySelector(".e-movableheader").getBoundingClientRect(),o=this.parent.options.frozenLeftCount||this.parent.options.actualFrozenColumns,s=this.parent.options.frozenRightCount?i.right:t.right,l=o?i.left:t.left;e>l&&e<l+35?this.timer=window.setInterval((function(){n.setScrollLeft(r,!0)}),50):e<s&&e>s-35&&(this.timer=window.setInterval((function(){n.setScrollLeft(r,!1)}),50))},e.prototype.setScrollLeft=function(e,t){var n=e.scrollLeft;e.scrollLeft=e.scrollLeft+(t?-5:5),n!==e.scrollLeft&&this.setDisplay("none")},e.prototype.stopTimer=function(){window.clearInterval(this.timer)},e.prototype.updateArrowPosition=function(e,t){var n=e.getBoundingClientRect(),r=this.parent.element.getBoundingClientRect();t&&n.left<r.left||!t&&n.right>r.right||(this.upArrow.style.top=n.top+n.height-r.top-7+"px",this.downArrow.style.top=n.top-r.top-2+"px",this.upArrow.style.left=this.downArrow.style.left=(t?n.left:n.right)-r.left-4+"px",this.parent.options.enableColumnVirtualization&&(this.upArrow.style.left=this.downArrow.style.left=Number(this.upArrow.style.left.replace("px",""))+this.parent.getContent().scrollLeft+"px"),this.setDisplay(""))},e.prototype.dragStart=function(e){var t=this.parent;document.body.classList.add("e-prevent-select");var n=e.target;this.element=n.classList.contains("e-headercell")?n:a(n,"e-headercell"),sf.base.isNullOrUndefined(this.element)&&(this.element=e.event.target.classList.contains("e-headercell")?e.event.target:a(e.event.target,"e-headercell")),this.draggedHeader=this.element.cloneNode(!0),e.column.allowReordering&&!e.column.lockColumn&&(this.x=h(e.event).x+t.getContent().firstElementChild.scrollLeft)},e.prototype.dragStop=function(e){var t=this.parent;this.parent.options.allowGrouping&&"touchend"===e.event.type&&sf.base.EventHandler.remove(window,"touchmove",this.parent.groupModule.preventTouchOnWindow),document.body.classList.remove("e-prevent-select"),this.setDisplay("none"),this.stopTimer(),e.cancel,sf.base.removeClass(t.element.querySelector(".e-headercontent").querySelectorAll(".e-reorderindicate"),["e-reorderindicate"])},e.prototype.setDisplay=function(e){this.upArrow.style.display=e,this.downArrow.style.display=e},e.prototype.getModuleName=function(){return"reorder"},e}(),S="e-rhandler",x="e-rsuppress",z="e-ricon",I="e-rhelper",R="th.e-headercell",A="e-rcursor",E=function(){function t(e){this.tapped=!1,this.isDblClk=!0,this.parent=e,this.widthService=new b(this.parent)}return t.prototype.autoFitColumns=function(e){var t=!!this.parent.options.enableColumnVirtualization,n=[];n=null==e||e.length<=0?this.parent.options.frozenColumns>0?this.parent.autofitFrozenColumns(!0):this.parent.getColumns(t).map((function(e){return e.field||e.uid})):"string"==typeof e?[e]:e,this.findColumn(n),this.parent.options.frozenColumns>0&&this.widthService.setWidthToTable()},t.prototype.autoFit=function(){var e;(e=this.parent.options.frozenColumns||this.parent.options.actualFrozenColumns>0?this.parent.autofitFrozenColumns():this.parent.getColumns().filter((function(e){return!0===e.autoFit})).map((function(e){return e.field||e.uid}))).length>0&&this.autoFitColumns(e)},t.prototype.resizeColumn=function(e,t,n){var r,i,o,s=this.parent,l=0,a=0,d=!!this.parent.options.enableColumnVirtualization,u=n||this.parent.getUidByColumnField(e,d);u=u||e;var h,p,c,f=this.parent.getNormalizedColumnIndex(u,d),g=this.parent.getColumnIndexByField(e,d);g=-1===g?this.parent.getColumnIndexByUid(e,d):g;var m=s.options.frozenColumns;if(sf.base.isNullOrUndefined(s.getFooterContent())||(o=s.getFooterContent().querySelector(".e-table")),r=s.getHeaderTable(),i=s.getContentTable(),h=r.querySelector('[e-mappinguid="'+u+'"]').parentElement.cloneNode(!0),m){var v=h.getAttribute("aria-colindex");p=i.querySelectorAll('[aria-colindex="'+v+'"]'),o&&(c=o.querySelectorAll("td:nth-child("+(f+1)+")"))}else p=i.querySelectorAll("td:nth-child("+(f+1)+"):not(.e-groupcaption)"),o&&(c=o.querySelectorAll("td:nth-child("+(f+1)+"):not(.e-groupcaption)"));var b=r.querySelector("tr").querySelectorAll(".e-grouptopleftcell");if(b.length>0)for(var y=0;y<b.length;y++)a+=b[y].offsetWidth;var C=i.querySelector(".e-detailrowcollapse")||i.querySelector(".e-detailrowexpand");this.parent.options.hasDetailTemplate&&C&&(a+=C.offsetWidth);var w=[h],S=[],x=[];if(o)for(y=0;y<c.length;y++)x[y]=c[y].cloneNode(!0);for(y=0;y<p.length;y++)S[y]=p[y].cloneNode(!0);var z=this.createTable(r,w,"e-gridheader"),I=this.createTable(i,S,"e-gridcontent"),R=null;x.length&&(R=this.createTable(o,x,"e-gridfooter"));var A=s.getColumns(d)[g].width=sf.base.formatUnit(Math.max(z,I,R));if(this.widthService.setColumnWidth(s.getColumns(d)[g]),!1===s.getColumns(d).some((function(e){return(null===e.width||void 0===e.width||e.width.length<=0)&&e.visible}))){var E=s.getColumns(d);for(y=0;y<E.length;y++)E[y].visible&&(l+=parseFloat(E[y].width))}var N=l+a;l>0&&!s.options.frozenColumns&&(this.parent.options.hasDetailTemplate&&this.widthService.setWidth("30",0),r.style.width=sf.base.formatUnit(N),i.style.width=sf.base.formatUnit(N),sf.base.isNullOrUndefined(o)||(o.style.width=sf.base.formatUnit(N)));var q=r.offsetWidth;s.getContent().scrollWidth>q?(r.classList.add("e-tableborder"),i.classList.add("e-tableborder")):(r.classList.remove("e-tableborder"),i.classList.remove("e-tableborder")),sf.base.isNullOrUndefined(o)||o.classList.add("e-tableborder"),this.parent.freezeModule.refreshRowHeight(),A.toString().indexOf("px")>0&&(A=A.replace("px","")),this.parent.options.isResizedGrid=!0,this.parent.dotNetRef.invokeMethodAsync("ColumnWidthChanged",{width:A,columnUid:u})},t.prototype.destroy=function(){var e=this.parent.element;e&&(e.querySelector(".e-gridheader")||e.querySelector(".e-gridcontent"))&&(this.widthService=null,this.unwireEvents())},t.prototype.getModuleName=function(){return"resize"},t.prototype.findColumn=function(e){for(var t=function(t){var r=e[t],i=!!n.parent.options.enableColumnVirtualization,o=n.parent.getColumnIndexByField(r,i);o=-1===o?n.parent.getColumnIndexByUid(r,i):o;var s=n.parent.getColumns(i)[o];o>-1&&!sf.base.isNullOrUndefined(s)&&!0===s.visible&&(n.parent.options.allowGrouping&&!sf.base.isNullOrUndefined(n.parent.options.initGroupingField)&&n.parent.options.initGroupingField.some((function(e){return e==s.field}))&&!n.parent.options.showGroupedColumn||n.resizeColumn(r,o))},n=this,r=0;r<e.length;r++)t(r)},t.prototype.createTable=function(e,t,n){var r=sf.base.createElement("div");r.className=this.parent.element.className,r.style.cssText="display: inline-block;visibility:hidden;position:absolute";var i=sf.base.createElement("div");i.className=n;var o=sf.base.createElement("table");o.className=e.className,o.classList.add("e-resizetable"),o.style.cssText="table-layout: auto;width: auto";for(var s=sf.base.createElement("tr"),l=0;l<t.length;l++){var a=s.cloneNode();a.className=e.querySelector("tr").className,a.appendChild(t[l]),o.appendChild(a)}i.appendChild(o),r.appendChild(i),document.body.appendChild(r);var d=o.getBoundingClientRect().width;return document.body.removeChild(r),Math.ceil(d)},t.prototype.render=function(){this.unwireEvents(),this.wireEvents(),this.setHandlerHeight()},t.prototype.refreshHeight=function(){for(var e=this.getResizeHandlers(),t=0;t<e.length;t++)e[t].parentElement.offsetHeight>0&&(e[t].style.height=e[t].parentElement.offsetHeight+"px");this.setHandlerHeight()},t.prototype.wireEvents=function(){sf.base.EventHandler.add(this.parent.getHeaderContent(),sf.base.Browser.touchStartEvent,this.resizeStart,this),sf.base.EventHandler.add(this.parent.getHeaderContent(),"dblclick",this.callAutoFit,this)},t.prototype.unwireEvents=function(){sf.base.EventHandler.remove(this.parent.getHeaderContent(),sf.base.Browser.touchStartEvent,this.resizeStart),sf.base.EventHandler.remove(this.parent.getHeaderContent(),"dblclick",this.callAutoFit)},t.prototype.getResizeHandlers=function(){return this.parent.options.frozenColumns?[].slice.call(this.parent.getHeaderContent().querySelectorAll("."+S)):[].slice.call(this.parent.getHeaderContent().querySelector(".e-table").querySelectorAll("."+S))},t.prototype.setHandlerHeight=function(){for(var e=[].slice.call(this.parent.getHeaderContent().querySelector(".e-table").querySelectorAll("."+x)),t=0;t<e.length;t++)e[t].style.height=e[t].parentElement.offsetHeight+"px"},t.prototype.callAutoFit=function(e){if(e.target.classList.contains("e-rhandler")){var t=this.getTargetColumn(e);if(t.columns)return;this.resizeColumn(t.field,this.parent.getNormalizedColumnIndex(t.uid),t.uid),sf.base.closest(e.target,R).classList.add("e-resized")}},t.prototype.resizeStart=function(e){if(e.target.classList.contains("e-rhandler")){var t=[],n={};if(!this.helper){if(0===this.getScrollBarWidth()){if(this.parent.options.allowGrouping)for(var r=0;r<this.parent.options.groupCount;r++)this.widthService.setWidth("30px",r);for(var i=0,o=this.refreshColumnWidth();i<o.length;i++){var s=o[i];this.widthService.setColumnWidth(s,null,null,!1),n={width:""==s.width?0:s.width,columnUid:s.uid},t.push(n)}this.widthService.setWidthToTable()}this.refreshStackedColumnWidth(),this.element=e.target,this.parentElementWidth=this.parent.element.getBoundingClientRect().width,this.appendHelper(),this.column=this.getTargetColumn(e),this.pageX=this.getPointX(e),"Right"===this.column.freeze?this.parent.options.enableRtl?this.minMove=(this.column.minWidth?parseFloat(this.column.minWidth.toString()):0)-parseFloat(sf.base.isNullOrUndefined(this.column.width)?"":this.column.width.toString()):this.minMove=parseFloat(sf.base.isNullOrUndefined(this.column.width)?"":this.column.width.toString())-(this.column.minWidth?parseFloat(this.column.minWidth.toString()):0):this.parent.options.enableRtl?this.minMove=parseFloat(this.column.width.toString())-(this.column.minWidth?parseFloat(this.column.minWidth.toString()):0):this.minMove=(this.column.minWidth?parseFloat(this.column.minWidth.toString()):0)-parseFloat(sf.base.isNullOrUndefined(this.column.width)?"":this.column.width.toString()),this.minMove+=this.pageX}sf.base.Browser.isDevice&&!this.helper.classList.contains(z)?(this.helper.classList.add(z),sf.base.EventHandler.add(document,sf.base.Browser.touchStartEvent,this.removeHelper,this),sf.base.EventHandler.add(this.helper,sf.base.Browser.touchStartEvent,this.resizeStart,this)):(sf.base.EventHandler.add(document,sf.base.Browser.touchEndEvent,this.resizeEnd,this),this.parent.dotNetRef.invokeMethodAsync("ResizeStarted",{columnUid:this.column.uid,columnList:t})),this.parent.options.enableVirtualization&&this.parent.options.frozenColumns&&(this.parent.element.querySelector(".e-virtualtable").style.position="")}},t.prototype.preventResizeAction=function(e){e?this.cancelResizeAction():(sf.base.EventHandler.add(this.parent.element,sf.base.Browser.touchMoveEvent,this.resizing,this),this.updateCursor("add"))},t.prototype.cancelResizeAction=function(e){e&&(sf.base.EventHandler.remove(this.parent.element,sf.base.Browser.touchMoveEvent,this.resizing),sf.base.EventHandler.remove(document,sf.base.Browser.touchEndEvent,this.resizeEnd),this.updateCursor("remove")),sf.base.Browser.isDevice&&(sf.base.EventHandler.remove(document,sf.base.Browser.touchStartEvent,this.removeHelper),sf.base.EventHandler.remove(this.helper,sf.base.Browser.touchStartEvent,this.resizeStart)),sf.base.detach(this.helper),this.refresh()},t.prototype.getWidth=function(e,t,n){return t&&e<t?t:n&&e>n?n:e},t.prototype.updateResizeEleHeight=function(){for(var e=[].slice.call(this.parent.getHeaderContent().querySelectorAll(".e-rhandler")),t=0;t<e.length;t++)e[t].style.height=e[t].parentElement.offsetHeight+"px"},t.prototype.getColData=function(e,t){return{width:parseFloat(sf.base.isNullOrUndefined(this.widthService.getWidth(e))||"auto"===this.widthService.getWidth(e)?"0":this.widthService.getWidth(e).toString())+t,minWidth:e.minWidth?parseFloat(e.minWidth.toString()):null,maxWidth:e.maxWidth?parseFloat(e.maxWidth.toString()):null}},t.prototype.resizing=function(e){if(!sf.base.isNullOrUndefined(this.column)){var t=0;sf.base.isNullOrUndefined(this.column)&&(t=a(this.element,"th").offsetWidth),this.parent.options.allowTextWrap&&(this.updateResizeEleHeight(),this.setHelperHeight(),this.parent.scrollModule.refresh());var n=this.getPointX(e),r=this.parent.options.enableRtl?-(n-this.pageX):n-this.pageX;"Right"===this.column.freeze&&this.column.isFrozen&&(r=this.parent.options.enableRtl?n-this.pageX:this.pageX-n);var i=this.getColData(this.column,r);i.width||(i.width=sf.base.closest(this.element,"th").offsetWidth);var o=this.getWidth(i.width,i.minWidth,i.maxWidth);if(("Right"!==this.column.freeze&&(!this.parent.options.enableRtl&&this.minMove>=n-10||this.parent.options.enableRtl&&this.minMove<=n+10)||"Right"==this.column.freeze&&(this.parent.options.enableRtl&&this.minMove>=n-10||!this.parent.options.enableRtl&&this.minMove<=n+10))&&(o=this.column.minWidth?parseFloat(this.column.minWidth.toString()):10,this.pageX=n=this.minMove),o!==parseFloat(sf.base.isNullOrUndefined(this.column.width)||"auto"===this.column.width?t.toString():this.column.width.toString())){this.pageX=n,this.column.width=sf.base.formatUnit(o);var s=[this.column],l=[this.column];this.column.columns&&(s=this.getSubColumns(this.column,[]),s=this.calulateColumnsWidth(s,!1,r),l=this.calulateColumnsWidth(s,!0,r));for(var d=0,u=l;d<u.length;d++){var h=u[d];this.widthService.setColumnWidth(h,null,"resize")}this.updateHelper()}this.refreshResizeFrozenColumns(),this.isDblClk=!1}},t.prototype.refreshResizeFrozenColumns=function(){var t=this,n=this.parent.options.enableColumnVirtualization?this.getTranslateX():0;if("Left"===this.column.freeze&&this.column.isFrozen||0==this.parent.options.frozenLeftColumnsCount&&0==this.parent.options.frozenRightColumnsCount&&this.column.index<this.parent.options.frozenColumns){var r=30*this.parent.getIndentCount(),i=this.parent.getColumns(!0).filter((function(e){return"Left"===e.freeze&&e.isFrozen||0==t.parent.options.frozenLeftCount&&0==t.parent.options.frozenRightCount&&e.index<t.parent.options.frozenColumns}));this.frozenHeaderRefresh("Left");for(var o=0;o<i.length;o++){if(i[parseInt(o.toString(),10)].index>this.column.index){(this.parent.options.frozenRows?[].slice.call(this.parent.getHeaderContent().querySelectorAll('td[data-colindex="'+o+'"]')).concat([].slice.call(this.parent.getContent().querySelectorAll('td[data-colindex="'+o+'"]'))):[].slice.call(this.parent.getContent().querySelectorAll('td[data-colindex="'+o+'"]'))).filter((function(i){e(i,r-n,t.parent.options.enableRtl,"Left")})),this.parent.options.enableColumnVirtualization&&(i[parseInt(o.toString(),10)].valueX=r)}i[parseInt(o.toString(),10)].visible&&(i[parseInt(o.toString(),10)].translateLeftRightValue=r,r+=parseFloat(i[parseInt(o.toString(),10)].width.toString()))}this.refreshResizeFixedCols("Left")}if("Right"===this.column.freeze&&this.column.isFrozen){var s=0;i=this.parent.getColumns(!0);this.frozenHeaderRefresh("Right");var l=i.filter((function(e){return"Right"===e.freeze&&e.isFrozen}));for(o=i.length-1;o>=i.length-l.length;o--){(this.parent.options.frozenRows?[].slice.call(this.parent.getHeaderContent().querySelectorAll('td[data-colindex="'+o+'"]')).concat([].slice.call(this.parent.getContent().querySelectorAll('td[data-colindex="'+o+'"]'))):[].slice.call(this.parent.getContent().querySelectorAll('td[data-colindex="'+o+'"]'))).filter((function(r){e(r,s+n,t.parent.options.enableRtl,"Right")})),this.parent.options.enableColumnVirtualization&&(i[parseInt(o.toString(),10)].valueX=s),i[parseInt(o.toString(),10)].visible&&(i[parseInt(o.toString(),10)].translateLeftRightValue=s,s+=parseFloat(i[parseInt(o.toString(),10)].width.toString()))}this.refreshResizeFixedCols("Right")}this.column.isFrozen&&"Fixed"===this.column.freeze&&(this.refreshResizeFixedCols("Left"),this.refreshResizeFixedCols("Right"),this.frozenHeaderRefresh("Left"),this.frozenHeaderRefresh("Right"))},t.prototype.frozenHeaderRefresh=function(t){var n=this,r=this.parent.options.enableColumnVirtualization?this.getTranslateX():0;if("Left"===t)for(var i=[].slice.call(this.parent.getHeaderContent().querySelector("thead").querySelectorAll("tr")),o=0;o<i.length;o++)for(var s=[].slice.call(i[parseInt(o.toString(),10)].querySelectorAll(".e-leftfreeze,.e-fixedfreeze")),l=function(t){var i=s[parseInt(t.toString(),10)];if(i.classList.contains("e-rowdragheader")||i.classList.contains("e-dragheadercell")||i.classList.contains("e-grouptopleftcell"))return"continue";var o=a.getParticularColumn(i),l=a.parent.getColumns(!0),d=0,u=[];if(a.parent.options.aggregatesCount&&a.parent.getFooterContent()){if(a.parent.getFooterContent().querySelectorAll(".e-summaryrow").length)[].slice.call(a.parent.getFooterContent().querySelectorAll(".e-summaryrow")).filter((function(e){u.push(e.querySelector('[e-mappinguid="'+o.uid+'"]'))}));u=u.concat([].slice.call(a.parent.getFooterContent().querySelectorAll('[e-mappinguid="'+o.uid+'"]')))}if(i.classList.contains("e-fixedfreeze")){a.parent.getFrozenLeftColumns().length&&(d=30*a.parent.getIndentCount());for(var h=0;h<l.length;h++)if(o.index>l[parseInt(h.toString(),10)].index){if(o.uid===l[parseInt(h.toString(),10)].uid)break;"Left"!==l[parseInt(h.toString(),10)].freeze&&"Fixed"!==l[parseInt(h.toString(),10)].freeze||!l[parseInt(h.toString(),10)].isFrozen||l[parseInt(h.toString(),10)].visible&&(d+=parseInt(l[parseInt(h.toString(),10)].width.toString(),10))}u&&u.length&&u.filter((function(t){e(t,d-r,n.parent.options.enableRtl,"Left")})),e(i,(0===d?d:d-1)-r,a.parent.options.enableRtl,"Left")}else if(d=30*a.parent.getIndentCount(),0===o.index)u&&u.length&&u.filter((function(t){e(t,d-r,n.parent.options.enableRtl,"Left")})),e(i,d-r,a.parent.options.enableRtl,"Left"),a.parent.options.enableColumnVirtualization&&(o.valueX=d);else{for(var p=0;p<l.length&&!(o.index<l[parseInt(p.toString(),10)].index||o.uid===l[parseInt(p.toString(),10)].uid);p++)l[parseInt(p.toString(),10)].visible&&(d+=parseInt(l[parseInt(p.toString(),10)].width.toString(),10));u&&u.length&&u.filter((function(t){e(t,d-r,n.parent.options.enableRtl,"Left")})),e(i,d-r,a.parent.options.enableRtl,"Left"),a.parent.options.enableColumnVirtualization&&(o.valueX=d)}},a=this,d=0;d<s.length;d++)l(d);if("Right"===t)for(i=[].slice.call(this.parent.getHeaderContent().querySelector("thead").querySelectorAll("tr")),o=0;o<i.length;o++){var u=[].slice.call(i[parseInt(o.toString(),10)].querySelectorAll(".e-rightfreeze, .e-fixedfreeze")),h=function(t){var i=u[parseInt(t.toString(),10)],o=p.getParticularColumn(i),s=p.parent.getColumns(!0),l=0,a=[];if(p.parent.options.aggregatesCount&&p.parent.getFooterContent()){if(p.parent.getContent().querySelectorAll(".e-summaryrow").length)[].slice.call(p.parent.getContent().querySelectorAll(".e-summaryrow")).filter((function(e){a.push(e.querySelector('[e-mappinguid="'+o.uid+'"]'))}));a=a.concat([].slice.call(p.parent.getFooterContent().querySelectorAll('[e-mappinguid="'+o.uid+'"]')))}if(i.classList.contains("e-fixedfreeze")){l=0;for(var d=s.length-1;d>=0;d--)o.index<s[parseInt(d.toString(),10)].index&&("Right"!==s[parseInt(d.toString(),10)].freeze&&"Fixed"!==s[parseInt(d.toString(),10)].freeze||!s[parseInt(d.toString(),10)].isFrozen||s[parseInt(d.toString(),10)].visible&&(l+=parseFloat(s[parseInt(d.toString(),10)].width.toString())));a.length&&a.filter((function(t){e(t,l+r,n.parent.options.enableRtl,"Right")})),e(i,l+r,p.parent.options.enableRtl,"Right")}else{l=0;for(var h=s.length-1;h>=0&&!(o.index>s[parseInt(h.toString(),10)].index||o.uid===s[parseInt(h.toString(),10)].uid);h--)s[parseInt(h.toString(),10)].visible&&(l+=parseInt(s[parseInt(h.toString(),10)].width.toString(),10));a.length&&a.filter((function(t){e(t,l+r,n.parent.options.enableRtl,"Right")})),e(i,l+r,p.parent.options.enableRtl,"Right"),p.parent.options.enableColumnVirtualization&&(o.valueX=l)}},p=this;for(d=u.length-1;d>=0;d--)h(d)}},t.prototype.refreshResizeFixedCols=function(t){for(var n=this.parent.getColumns(!0),r=this.parent.options.enableColumnVirtualization?this.getTranslateX():0,i=[].slice.call(this.parent.getHeaderContent().querySelector("tbody").querySelectorAll(".e-fixedfreeze")).concat([].slice.call(this.parent.getContent().querySelectorAll(".e-fixedfreeze"))),o=0;o<i.length;o++){var s=i[parseInt(o.toString(),10)],l=void 0;if(s.classList.contains("e-summarycell")){var a=s.getAttribute("e-mappinguid");l=this.parent.getColumnByUid(a)}else{var d=parseInt(s.getAttribute("data-colindex"),10);l=n[parseInt(d.toString(),10)]}var u=0;if("Left"===t){this.parent.getFrozenLeftColumns().length&&(u=30*this.parent.getIndentCount());for(var h=0;h<n.length;h++)if(l.index>n[parseInt(h.toString(),10)].index){if(l.uid===n[parseInt(h.toString(),10)].uid)break;"Left"!==n[parseInt(h.toString(),10)].freeze&&"Fixed"!==n[parseInt(h.toString(),10)].freeze||!n[parseInt(h.toString(),10)].isFrozen||n[parseInt(h.toString(),10)].visible&&(u+=parseFloat(n[parseInt(h.toString(),10)].width.toString()))}e(s,(0===u?u:u-1)-r,this.parent.options.enableRtl,"Left")}if("Right"===t){u=0;for(h=n.length-1;h>=0&&l.uid!==n[parseInt(h.toString(),10)].uid;h--)"Right"!==n[parseInt(h.toString(),10)].freeze&&"Fixed"!==n[parseInt(h.toString(),10)].freeze||!n[parseInt(h.toString(),10)].isFrozen||n[parseInt(h.toString(),10)].visible&&(u+=parseFloat(n[parseInt(h.toString(),10)].width.toString()));e(s,u+r,this.parent.options.enableRtl,"Right")}}},t.prototype.getParticularColumn=function(e){var t=e.classList.contains("e-filterbarcell")?e.getAttribute("e-mappinguid"):e.querySelector("[e-mappinguid]").getAttribute("e-mappinguid");return this.parent.getColumnByUid(t)},t.prototype.getTranslateX=function(){if(!this.parent.options.enableColumnVirtualization)return 0;var e=this.parent.getContent().getElementsByClassName("e-virtualtable")[0],t=e.style.transform.indexOf("(")+1,n=e.style.transform.indexOf("p");return parseInt(e.style.transform.slice(t,n))},t.prototype.calulateColumnsWidth=function(e,t,n){for(var r=[],i=0,o=e;i<o.length;i++){for(var s=o[i],l=0,a=0;a<e.length;a++)l+=parseFloat(e[a].width.toString());var d=this.getColData(s,parseFloat(s.width)*n/l),u=this.getWidth(d.width,d.minWidth,d.maxWidth);u!==parseFloat(s.width.toString())&&(t&&(s.width=sf.base.formatUnit(u<1?1:u)),r.push(s))}return r},t.prototype.getSubColumns=function(e,t){for(var n=0,r=e.columns;n<r.length;n++){var i=r[n];!1!==i.visible&&i.allowResizing&&(i.columns?this.getSubColumns(i,t):t.push(i))}return t},t.prototype.resizeEnd=function(e){if(this.helper){sf.base.EventHandler.remove(this.parent.element,sf.base.Browser.touchMoveEvent,this.resizing),sf.base.EventHandler.remove(document,sf.base.Browser.touchEndEvent,this.resizeEnd),this.updateCursor("remove"),sf.base.detach(this.helper);this.parent.getContent();sf.base.closest(this.element,".e-headercell").classList.add("e-resized"),a(this.element,"e-frozenheader")?this.isFrozenColResized=!0:this.isFrozenColResized=!1,this.parent.options.frozenColumns&&(this.parent.freezeModule.refreshRowHeight(),this.parent.freezeModule.setFrozenHeight()),this.parent.options.allowTextWrap&&this.updateResizeEleHeight();var t=this.column.width.toString();t=t.replace("px",""),this.parent.dotNetRef.invokeMethodAsync("ColumnWidthChanged",{width:t,columnUid:this.column.uid,allowStopEvent:!0,tableWidth:this.tableWidth,leftFrozenTableWidth:this.leftFrozenTableWidth,rightFrozenTableWidth:this.rightFrozenTableWidth}),this.parent.options.enableColumnVirtualization||this.parent.updateColumnWidth(this.parent.columnModel),this.refresh(),this.doubleTapEvent(e),this.isDblClk=!0}},t.prototype.getPointX=function(e){return e.touches&&e.touches.length?e.touches[0].pageX:e.pageX},t.prototype.refreshColumnWidth=function(){for(var e=this.parent.getColumns(),t=0,n=[].slice.apply(this.parent.getHeaderContent().querySelectorAll("th.e-headercell"));t<n.length;t++)for(var r=n[t],i=0,o=e;i<o.length;i++){var s=o[i];if(r.querySelector("[e-mappinguid]")&&r.querySelector("[e-mappinguid]").getAttribute("e-mappinguid")===s.uid&&s.visible){s.width=r.getBoundingClientRect().width?r.getBoundingClientRect().width:s.width;break}!s.visible&&"string"==typeof s.width&&s.width.includes("px")&&(s.width=Number(s.width.replace("px","")))}return e},t.prototype.refreshStackedColumnWidth=function(){for(var e=0,t=this.parent.getStackedColumns(this.parent.options.columns);e<t.length;e++){var n=t[e];n.width=this.getStackedWidth(n,0)}},t.prototype.getStackedWidth=function(e,t){for(var n=0,r=e.columns;n<r.length;n++){var i=r[n];!1!==i.visible&&(i.columns?this.getStackedWidth(i,t):t+=i.width)}return t},t.prototype.getTargetColumn=function(e){var t=sf.base.closest(e.target,R),n=(t=t.querySelector(".e-headercelldiv")||t.querySelector(".e-stackedheadercelldiv")).getAttribute("e-mappinguid");return this.parent.getColumnByUid(n)},t.prototype.updateCursor=function(e){var t=[].slice.call(this.parent.getHeaderContent().querySelectorAll("th"));t.push(this.parent.element);for(var n=0,r=t;n<r.length;n++){r[n].classList[e](A)}},t.prototype.refresh=function(){this.column=null,this.pageX=null,this.element=null,this.helper=null},t.prototype.appendHelper=function(){this.helper=sf.base.createElement("div",{className:I}),this.parent.element.appendChild(this.helper),this.setHelperHeight()},t.prototype.setHelperHeight=function(){for(var e=this.parent.getContent().offsetHeight-(this.parent.options.frozenColumns?0:this.getScrollBarWidth()),t=sf.base.closest(this.element,R),n=[].slice.call(this.parent.getHeaderContent().querySelectorAll("tr")),r=n.indexOf(t.parentElement);r<n.length;r++)e+=n[r].offsetHeight;var i=this.calcPos(t);a(t,"e-frozen-right-header")?i.left+=this.parent.options.enableRtl?t.offsetWidth-2:-1:i.left+=this.parent.options.enableRtl?-1:t.offsetWidth-2,this.helper.style.cssText="height: "+e+"px; top: "+i.top+"px; left:"+Math.floor(i.left)+"px;"},t.prototype.getScrollBarWidth=function(e){var t=this.parent.getContent();return t.scrollHeight>t.clientHeight&&e||t.scrollWidth>t.clientWidth?o():0},t.prototype.removeHelper=function(e){var t=e.target.classList;t.contains(S)||t.contains(z)||!this.helper||(sf.base.EventHandler.remove(document,sf.base.Browser.touchStartEvent,this.removeHelper),sf.base.EventHandler.remove(this.helper,sf.base.Browser.touchStartEvent,this.resizeStart),sf.base.detach(this.helper),this.refresh())},t.prototype.updateHelper=function(){var e=sf.base.closest(this.element,R),t=Math.floor(this.calcPos(e).left+(this.parent.options.enableRtl?-1:e.offsetWidth-2));if(a(e,"e-frozen-right-header")&&(t=Math.floor(this.calcPos(e).left+(this.parent.options.enableRtl?e.offsetWidth-2:-1))),t>this.parentElementWidth&&(t=this.parentElementWidth-2),this.parent.options.frozenColumns){var n=sf.base.closest(e,".e-table").offsetLeft;t<n&&(t=n)}this.helper.style.left=t+"px"},t.prototype.calcPos=function(e){for(var t={top:0,left:0},n=e.getBoundingClientRect(),r=e.ownerDocument,i=a(e,"e-grid")||r.documentElement;i&&(i===r.body||i===r.documentElement)&&"static"===i.style.position;)i=i.parentNode;return i&&i!==e&&1===i.nodeType&&(t=i.getBoundingClientRect()),{top:n.top-t.top,left:n.left-t.left}},t.prototype.doubleTapEvent=function(e){this.getUserAgent()&&this.isDblClk&&(this.tapped?(clearTimeout(this.tapped),this.callAutoFit(e),this.tapped=null):this.tapped=setTimeout(this.timeoutHandler(),300))},t.prototype.getUserAgent=function(){var e=sf.base.Browser.userAgent.toLowerCase();return/iphone|ipod|ipad/.test(e)},t.prototype.timeoutHandler=function(){this.tapped=null},t}(),N=function(){function e(e){var t=this;this.visualElement=sf.base.createElement("div",{className:"e-cloneproperties e-dragclone e-gdclone",styles:"line-height:23px",attrs:{action:"grouping"}}),this.helper=function(e){var n=t.parent,r=e.sender.target,i=r.classList.contains("e-groupheadercell")?r:a(r,"e-groupheadercell");return!(!i||!r.classList.contains("e-drag")&&t.parent.options.groupReordering)&&(t.column=n.getColumnByField(i.firstElementChild.getAttribute("ej-mappingname")),t.visualElement.textContent=i.textContent,t.visualElement.style.width=i.offsetWidth+2+"px",t.visualElement.style.height=i.offsetHeight+2+"px",t.visualElement.setAttribute("e-mappinguid",t.column.uid),n.element.appendChild(t.visualElement),t.visualElement)},this.dragStart=function(e){t.parent.element.classList.add("e-ungroupdrag"),document.body.classList.add("e-prevent-select"),e.bindEvents(e.dragElement)},this.drag=function(e){var n=e.target,r=t.parent.element.querySelector(".e-cloneproperties");t.parent.options.groupReordering||(sf.base.classList(r,["e-defaultcur"],["e-notallowedcur"]),a(n,"e-gridcontent")||a(n,"e-headercell")||sf.base.classList(r,["e-notallowedcur"],["e-defaultcur"]))},this.dragStop=function(e){document.body.classList.remove("e-prevent-select"),t.parent.element.classList.remove("e-ungroupdrag"),!(a(e.target,"e-gridcontent")||a(e.target,"e-gridheader"))&&sf.base.remove(e.helper)},this.preventTouchOnWindow=function(e){e.preventDefault()},this.drop=function(e){var n=t.parent,r=n.getColumnByUid(e.droppedElement.getAttribute("e-mappinguid"));n.element.querySelector(".e-groupdroparea").classList.remove("e-hover"),sf.base.remove(e.droppedElement),n.options.allowGrouping&&sf.base.EventHandler.remove(window,"touchmove",t.preventTouchOnWindow),t.parent.element.querySelector(".e-groupdroparea").removeAttribute("aria-dropeffect"),t.parent.element.querySelector("[aria-grabbed=true]").setAttribute("aria-grabbed","false"),sf.base.isNullOrUndefined(r)||!1===r.allowGrouping||a(n.getColumnHeaderByUid(r.uid),"e-grid").getAttribute("id")!==n.element.getAttribute("id")||n.dotNetRef.invokeMethodAsync("GroupColumn",r.field,"Group")},this.parent=e,this.parent.options.allowGrouping&&this.parent.options.showDropArea&&this.initDragAndDrop()}return e.prototype.columnDrag=function(e){this.parent.options.groupReordering;var t=this.parent,n=t.element.querySelector(".e-cloneproperties"),r=t.getColumnByUid(n.getAttribute("e-mappinguid"));a(e.target,"e-groupdroparea")&&sf.base.classList(n,["e-defaultcur"],["e-notallowedcur"]),sf.base.EventHandler.add(window,"touchmove",this.preventTouchOnWindow),(a(e.target,"e-groupdroparea")||t.options.allowReordering&&a(e.target,"e-headercell"))&&(r.allowGrouping||!a(e.target,"e-groupdroparea"))||sf.base.classList(n,["e-notallowedcur"],["e-defaultcur"]),t.options.showDropArea&&(e.target.classList.contains("e-groupdroparea")?t.element.querySelector(".e-groupdroparea").classList.add("e-hover"):t.element.querySelector(".e-groupdroparea").classList.remove("e-hover"))},e.prototype.columnDragStart=function(e){if(!e.target.classList.contains("e-stackedheadercell")){var t=this.parent.element.querySelector(".e-groupdroparea");t&&t.setAttribute("aria-dropeffect","copy");var n=e.target.classList.contains("e-headercell")?e.target:a(e.target,"e-headercell");sf.base.isNullOrUndefined(n)&&(n=e.event.target.classList.contains("e-headercell")?e.event.target:a(e.event.target,"e-headercell")),n.setAttribute("aria-grabbed","true")}},e.prototype.columnDrop=function(e){var t=this.parent;if("grouping"===e.droppedElement.getAttribute("action")){var n=t.getColumnByUid(e.droppedElement.getAttribute("e-mappinguid"));if(sf.base.isNullOrUndefined(n)||!1===n.allowGrouping||a(t.getColumnHeaderByUid(n.uid),"e-grid").getAttribute("id")!==t.element.getAttribute("id"))return;t.dotNetRef.invokeMethodAsync("GroupColumn",n.field,"Ungroup")}},e.prototype.initDragAndDrop=function(){this.initializeGHeaderDrop(),this.initializeGHeaderDrag()},e.prototype.initializeGHeaderDrag=function(){var e=this.parent.element.querySelector(".e-groupdroparea");if(e)new sf.base.Draggable(e,{dragTarget:this.parent.options.groupReordering?".e-drag":".e-groupheadercell",distance:this.parent.options.groupReordering?-10:5,helper:this.helper,dragStart:this.dragStart,drag:this.drag,dragStop:this.dragStop,isPreventSelect:!1})},e.prototype.initializeGHeaderDrop=function(){this.parent;var e=this.parent.element.querySelector(".e-groupdroparea");if(e)new sf.base.Droppable(e,{accept:".e-dragclone",drop:this.drop})},e.prototype.getModuleName=function(){return"group"},e.prototype.getGHeaderCell=function(e){return this.element&&this.element.querySelector('[ej-mappingname="'+e+'"]')?this.element.querySelector('[ej-mappingname="'+e+'"]').parentElement:null},e}(),q=function(){function e(e){this.mediaCol=[],this.media={},this.mediaBindInstance={},this.mediaColVisibility={},this.parent=e}return e.prototype.renderColumnChooser=function(){var e=this.parent.element.querySelector("#"+this.parent.element.id+"_ccdlg");e.style.maxHeight="430px";var t=e.style.display;e.style.display="block";var n,r=this.parent.element.querySelector(".e-cc-toolbar");this.parent.getHeaderContent().parentElement.classList.contains("e-sticky")?(n=r.getBoundingClientRect(),e.classList.add("e-sticky")):n=sf.popups.calculateRelativeBasedPosition(r,e),e.style.display=t;var i,o=n.top+r.getBoundingClientRect().height;i=this.parent.options.enableRtl?this.parent.element.querySelector(".e-columnchooser-btn").offsetLeft:n.left-250+r.clientWidth+2,this.parent.dotNetRef.invokeMethodAsync("GetChooserPosition",i.toString(),o.toString())},e.prototype.setMediaColumns=function(e){var t=this.parent.getColumns();if(!sf.base.isNullOrUndefined(t)){for(var n=0;n<t.length;n++)""!==t[n].hideAtMedia&&(sf.base.isNullOrUndefined(t[n].visible)||t[n].visible||e)&&this.pushMediaColumn(t[n],n);this.parent.dotNetRef.invokeMethodAsync("SetMediaColumnVisibility",{mediaColVisibility:this.mediaColVisibility}),this.mediaColVisibility={}}},e.prototype.windowResized=function(){var e=this;setTimeout((function(){!sf.base.isNullOrUndefined(e.mediaColVisibility)&&Object.keys(e.mediaColVisibility).length>0&&(e.parent.dotNetRef.invokeMethodAsync("SetMediaColumnVisibility",{mediaColVisibility:e.mediaColVisibility,invokedByMedia:!0}),e.mediaColVisibility={})}),100)},e.prototype.pushMediaColumn=function(e,t){this.mediaCol.push(e),this.media[e.uid]=window.matchMedia(e.hideAtMedia),this.mediaQueryUpdate(t,this.media[e.uid]),this.mediaBindInstance[t]=this.mediaQueryUpdate.bind(this,t),this.media[e.uid].addListener(this.mediaBindInstance[t])},e.prototype.mediaQueryUpdate=function(e,t){var n=this.parent.getColumns()[e];this.mediaCol.some((function(e){return e.uid===n.uid}))&&(this.mediaColVisibility[n.uid]=t.matches)},e.prototype.updateMediaColumns=function(e){for(var t=Object.keys(e),n=function(e){var n=-1;r.mediaCol.some((function(r){return n++,r.uid===t[e]}))?r.mediaCol.splice(n,1):r.pushMediaColumn(r.parent.getColumnByUid(t[e]),r.parent.getColumnIndexByUid(t[e]))},r=this,i=0;i<t.length;i++)n(i)},e.prototype.removeMediaListener=function(){for(var e=0;e<this.mediaCol.length;e++)this.media[this.mediaCol[e].uid].removeListener(this.mediaBindInstance[this.mediaCol[e].index])},e}(),H=function(){function e(e){this.key=null,this.uid=null,this.parent=e}return e.prototype.renderColumnMenu=function(e,t,n){this.key=n,this.uid=e;var r=this.parent.getColumnHeaderByUid(e).querySelector(".e-columnmenu"),i=document.getElementsByClassName("e-"+this.parent.element.id+"-column-menu")[0];i.style.position="absolute";var o=i.getElementsByTagName("ul")[0];if(sf.base.isNullOrUndefined(o))return{Left:1,Top:1};var s={top:0,left:0};o.style.visibility="hidden",i.style.display="block";var l=o.getBoundingClientRect();o.classList.add("e-transparent"),o.style.visibility="",i.style.display="";var a=this.getHeaderCell(r);return this.parent.options.enableRtl?s=sf.popups.calculatePosition(a,"left","bottom"):(s=sf.popups.calculatePosition(a,"right","bottom")).left-=l.width,s.left-=pageXOffset,s.top-=pageYOffset,t&&(sf.base.EventHandler.add(o,"mouseover",this.appendFilter,this),sf.base.EventHandler.add(o,"keydown",this.appendFilter,this)),{Left:-1!==Math.sign(s.left)?Math.ceil(s.left):0,Top:Math.ceil(s.top)}},e.prototype.setPosition=function(){var e=document.getElementsByClassName("e-"+this.parent.element.id+"-column-menu")[0],t=sf.base.isNullOrUndefined(e)?null:e.getElementsByTagName("ul")[0];if(!(sf.base.isNullOrUndefined(t)||sf.base.isNullOrUndefined(this.uid)||sf.base.isNullOrUndefined(this.parent.getColumnHeaderByUid(this.uid))||sf.base.Browser.isDevice)){var n=this.parent.getColumnHeaderByUid(this.uid).querySelector(".e-columnmenu"),r=this.getHeaderCell(n).getBoundingClientRect(),i=r.left+pageXOffset,o=r.bottom+pageYOffset,s=t.getBoundingClientRect(),l=document.documentElement;r.bottom+s.height>l.clientHeight&&o-r.height-s.height>l.clientTop&&(o=o-r.height-s.height),r.left+s.width>l.clientWidth&&r.right-s.width>l.clientLeft&&(i=i+r.width-s.width),i=i-t.getBoundingClientRect().width+r.width,e.style.left=Math.ceil(i+1)+"px",e.style.top=Math.ceil(o+1)+"px"}},e.prototype.appendFilter=function(e){var t=this,n=!1;setTimeout((function(){sf.base.closest(e.target,"#"+t.key)&&(t.parent.element.querySelector(".e-filter-popup")||!t.parent.element.querySelector(".e-filter-popup"))||e.target.parentElement.id==t.key&&(t.parent.element.querySelector(".e-filter-popup")||!t.parent.element.querySelector(".e-filter-popup"))?n=!0:!sf.base.closest(e.target,"#"+t.key)&&t.parent.element.querySelector(".e-filter-popup")&&(n=!1),t.parent.dotNetRef.invokeMethodAsync("FilterMouseOverHandler",t.uid,n)}),10)},e.prototype.getHeaderCell=function(e){return sf.base.closest(e,"th.e-headercell")},e}(),L=function(){function e(e){this.parent=e}return e.prototype.filterPopupRender=function(e,t,n,r){var i=this.parent.element.querySelector("#"+e);if(!sf.base.isNullOrUndefined(i))if(r){sf.base.EventHandler.add(i,"mousedown",this.mouseDownHandler,this),i.style.maxHeight="excel"==n?"800px":"350px";var o=document.getElementsByClassName("e-"+this.parent.element.id+"-column-menu")[0].getElementsByTagName("ul")[0],s=sf.base.isNullOrUndefined(o.querySelector(".e-icon-filter"))?o.getElementsByClassName("e-menu-item e-focused")[0]:o.querySelector(".e-icon-filter").parentElement,l=this.parent.element.querySelector(".e-filter-popup"),d=this.parent.element.getBoundingClientRect(),u=s.getBoundingClientRect(),h=u.left-d.left,p=u.top-d.top,c=i.style.display;i.style.display="block",d.height<p?p=p-l.offsetHeight+u.height:d.height<p+l.offsetHeight&&(p=d.height-l.offsetHeight),window.innerHeight<l.offsetHeight+p+d.top&&(p=window.innerHeight-l.offsetHeight-d.top),h+=this.parent.options.enableRtl?-l.offsetWidth:u.width,d.width<=h+l.offsetWidth?h-=u.width+l.offsetWidth:h<0&&(h+=l.offsetWidth+u.width),i.style.display=c,this.parent.dotNetRef.invokeMethodAsync("GetFilterIconPosition",h.toString(),p.toString())}else{var f=[].slice.call(this.parent.element.querySelector(".e-headercontent").querySelectorAll("div[e-mappinguid="+t+"]"))[1];i.style.maxHeight="excel"==n?"800px":"350px";c=i.style.display;i.style.display="block";var g=sf.popups.calculateRelativeBasedPosition(f,i);this.parent.options.enableColumnVirtualization&&this.parent.virtualContentModule.virtualEle.filterTranslateX&&!a(f,"e-frozenheader")&&(g.left+=this.parent.virtualContentModule.virtualEle.filterTranslateX),i.style.display=c;h=g.left-250+f.clientWidth;var m=g.top+f.getBoundingClientRect().height-5;h<1?h=250+h-16:h-=4;var v=this.parent.element.offsetWidth;if(v-h<250)h-=250-(v-h);this.parent.dotNetRef.invokeMethodAsync("GetFilterIconPosition",h.toString(),m.toString())}},e.prototype.mouseDownHandler=function(e){(e&&sf.base.closest(e.target,".e-filter-popup")||e.currentTarget&&e.currentTarget.activeElement&&a(e.currentTarget.activeElement,"e-filter-popup")||a(e.target,"e-popup")||a(e.target,"e-popup-wrapper"))&&!sf.base.Browser.isDevice&&this.parent.dotNetRef.invokeMethodAsync("PreventColumnMenuClose",!0)},e}(),O=function(){function e(e){this.parent=e}return e.prototype.createTooltip=function(e,t){for(var r,i,s={},l=!1,d=function(){var n=u.parent.getContent(),d=e[h].fieldName,p=e[h].uid,c=e[h].message;if(d=d.replace(/[.]/g,"___"),u.parent.options.hasTemplateInEditSettings||sf.base.isNullOrUndefined(p))i=u.parent.element.querySelector("#"+d)||document.querySelector("#"+d);else if(document.querySelectorAll("[e-mappinguid="+p+"_Dialog]")[0])i=document.querySelectorAll("[e-mappinguid="+p+"_Dialog]")[0];else if((u.parent.options.enableVirtualization||u.parent.options.frozenRows||u.parent.options.enableInfiniteScrolling)&&u.parent.options.showAddNewRow){var f=u.parent.element.querySelectorAll("[e-mappinguid="+p+"].e-rowcell"),g=Array.from(f).filter((function(e){var t=a(e,"e-gridform");return t&&t.querySelector(".e-griderror")}));u.parent.options.frozenRows?a(g[0],"e-gridheader")?(l=!0,i=g[0]):l=!1:(u.parent.options.enableVirtualization||u.parent.options.enableInfiniteScrolling)&&a(g[0],"e-gridheader")?(i=g[0],l=!0):(i=u.parent.getContent().querySelectorAll("[e-mappinguid="+p+"]")[0],l=!1)}else for(var m=u.parent.getContent().querySelectorAll("[e-mappinguid="+p+"]"),v=0;v<m.length;v++)m[v].querySelector(".e-disabled")||(i=m[v]);if(sf.base.isNullOrUndefined(i)){var b=u.parent.columnModel.filter((function(e){return e.field.split(d).length>1}));sf.base.isNullOrUndefined(b)||0==b.length||(d=b[0].field.replace(/[.]/g,"___"),i=u.parent.getContent().querySelectorAll("[e-mappinguid="+p+"]")[0]||document.querySelectorAll("[e-mappinguid="+p+"_Dialog]")[0])}var y=!l&&(n.scrollHeight>n.clientHeight||n.scrollWidth>n.clientWidth),C="Dialog"!==u.parent.options.editMode,w=u.parent.options.enableAdaptiveUI?"_adaptive_dialogEdit_wrapper":"_dialogEdit_wrapper";if(!i)return{value:void 0};var S=sf.base.closest(i,".e-rowcell"),x=sf.base.closest(i,".e-row"),z=void 0,I=!1,R=Math.round(u.parent.getContent().clientHeight/u.parent.getRowHeight())-1,A=[].slice.call(u.parent.getContent().querySelectorAll(".e-row"));if(u.parent.options.enableVirtualization&&u.parent.options.allowGrouping&&u.parent.options.groupCount>0&&t&&(A=[].slice.call(u.parent.getContent().querySelectorAll("tr"))).pop(),"Batch"===u.parent.options.editMode&&R>1&&A.length>=R&&A[A.length-1].getAttribute("data-rowindex")===x.getAttribute("data-rowindex")&&(I=!0),C)if(u.parent.options.frozenRows,R>1&&A.length>=R&&("Bottom"===u.parent.options.newRowPosition&&t||!sf.base.isNullOrUndefined(S)&&S.classList.contains("e-lastrowcell")&&!x.classList.contains("e-addedrow"))||I)z=!0;else if(l&&!sf.base.isNullOrUndefined(x)){var E=x.getAttribute("data-rowindex");if(null!==E)parseInt(E,10)+1===u.parent.options.frozenRows&&(z=!0)}var N=C?l?u.parent.getHeaderTable():u.parent.getContentTable():document.querySelector("#"+u.parent.element.id+w).querySelector(".e-dlg-content"),q=N.getBoundingClientRect(),H=C?u.parent.element.getBoundingClientRect().left:q.left,L=sf.base.closest(i,"td"),O=L?L.getBoundingClientRect():i.parentElement.getBoundingClientRect(),T=p+"_Error",B=void 0;(B=u.parent.options.hasTemplateInEditSettings||sf.base.isNullOrUndefined(p)?u.parent.element.querySelector("#"+d+"_Error")||document.querySelector("#"+d+"_Error"):(u.parent.options.enableVirtualization||u.parent.options.frozenRows)&&u.parent.options.showAddNewRow&&l?u.parent.getHeaderContent().querySelectorAll("[e-mappinguid="+T+"]")[0]:u.parent.getContent().querySelectorAll("[e-mappinguid="+T+"]")[0]||document.querySelectorAll("[e-mappinguid="+T+"]")[0]).style.top=(l?O.top+O.height:O.bottom-q.top)+N.scrollTop+9+"px",B.style.left=O.left-H+N.scrollLeft+O.width/2+"px",B.style.maxWidth="auto",C&&q.left<H&&(B.style.left=parseInt(B.style.left,10)-q.left+H+"px");var U=void 0;(U=z?B.querySelector(".e-tip-bottom"):B.querySelector(".e-tip-top"),(u.parent.options.frozenColumns||u.parent.options.frozenRows)&&"Dialog"!==u.parent.options.editMode)&&(("Normal"===u.parent.options.editMode?sf.base.closest(i,".e-editcell"):sf.base.closest(i,".e-table")).style.position="relative",B.style.position="absolute");B.style.display="block",B.querySelector(".e-error").innerText=c,!z&&C&&n.getBoundingClientRect().bottom<O.bottom+O.height&&(n.scrollTop=n.scrollTop+B.offsetHeight+U.scrollHeight);var M=parseInt(document.defaultView.getComputedStyle(B,null).getPropertyValue("font-size"),10);if(B.getBoundingClientRect().width<O.width&&B.querySelector("label").getBoundingClientRect().height/(1.2*M)>=2&&"Dialog"!==u.parent.options.editMode&&(B.style.width=O.width-4+"px"),!u.parent.options.frozenColumns&&!u.parent.options.frozenRows||"Normal"!==u.parent.options.editMode&&"Batch"!==u.parent.options.editMode?B.style.left=parseInt(B.style.left,10)-B.offsetWidth/2+"px":B.style.left=L.offsetLeft+(L.offsetWidth/2-B.offsetWidth/2)+"px",C&&!y&&!u.parent.options.allowPaging||u.parent.options.frozenColumns||u.parent.options.frozenRows){u.parent.options.showAddNewRow||(n.style.position="static");var k=sf.popups.calculateRelativeBasedPosition(L,B);B.style.top=k.top+O.height+9+"px"}if(z){if(!y||u.parent.options.frozenColumns||"auto"===u.parent.options.height||u.parent.options.frozenRows||u.parent.options.enableVirtualization)B.style.bottom=O.height+9+"px";else{var D=n.scrollWidth>n.offsetWidth?o():0,F=-1===u.parent.options.height.toString().indexOf("%")?parseInt(u.parent.options.height,10):n.offsetHeight;B.style.bottom=F-n.querySelector("table").offsetHeight-D+O.height+9+"px"}B.style.top=null}r=z?"bottom":"top",d.includes("___")&&sf.base.isNullOrUndefined(p)&&(d=d.replace("___","."));var W=sf.base.isNullOrUndefined(p)?u.parent.getColumnByField(d).uid:p;s[W]="top: "+B.style.top+"; bottom: "+B.style.bottom+"; left: "+B.style.left+"; \n            max-width: "+B.style.maxWidth+"; width: "+B.style.width+"; text-align: center; position: "+B.style.position+";"},u=this,h=0;h<e.length;h++){var p=d();if("object"===n(p))return p.value}this.parent.dotNetRef.invokeMethodAsync("ShowValidationPopup",s,r)},e}(),T=function(){function e(e){this.copyContent="",this.isSelect=!1,this.parent=e,this.clipBoardTextArea=sf.base.createElement("textarea",{className:"e-clipboard",styles:"opacity: 0",attrs:{tabindex:"-1","aria-label":"clipboard","aria-hidden":"true"}}),this.parent.element.appendChild(this.clipBoardTextArea)}return e.prototype.pasteHandler=function(){var e=this,t=this.parent,n=sf.base.closest(document.activeElement,".e-rowcell");if(n&&t.options.allowEditing&&"Batch"===t.options.editMode&&"Cell"===t.options.selectionMode&&"Flow"!==t.options.cellSelectionMode&&(!n.classList.contains("e-editedbatchcell")||"Box"!==t.options.cellSelectionMode)){this.activeElement=document.activeElement,this.clipBoardTextArea.value="";var r=window.scrollX,i=window.scrollY;this.clipBoardTextArea.focus(),setTimeout((function(){e.activeElement.focus(),window.scrollTo(r,i);t.dotNetRef.invokeMethodAsync("InvokeCopyPasteAction",{clipboardText:e.clipBoardTextArea.value},"Paste")}),10)}},e.prototype.pasteAction=function(e,t,n,r){r||""==e||this.paste(e,t,n)},e.prototype.paste=function(e,t,n){var r,i,o,s=this.parent,l=n,a=t;if(s.options.allowEditing&&"Batch"===s.options.editMode&&"Cell"===s.options.selectionMode&&"Flow"!==s.options.cellSelectionMode){var d,u,h=e.split("\n"),p=s.getDataRows(),c=this.parent.options.frozenColumns;c&&(u=s.getMovableDataRows());for(var f=0;f<h.length;f++){if(d=h[f].split("\t"),l=n,f===h.length-1&&""===h[f]||sf.base.isUndefined(s.getRowByIndex(a))){l++;break}for(var g=0;g<d.length;g++){if(o=s.getCellFromIndex(a,l),c){var m=p[a],v=u[a];o=!!m.querySelector('[data-colindex="'+l+'"]')||v.querySelector('[data-colindex="'+l+'"]')}if(!o){l++;break}r=s.getColumnByIndex(l),i=d[g],!r.allowEditing||r.isPrimaryKey||r.template||s.dotNetRef.invokeMethodAsync("InvokePasteAction",{cellValue:i},a,l,r.field),l++}a++}}},e.prototype.pasteData=function(e,t,n,r,i){if(!i){this.parent.editModule&&this.parent.dotNetRef.invokeMethodAsync("UpdateCell",e,t,n);var o=this.parent.getCellFromIndex(e,r);o&&sf.base.classList(o,["e-focus","e-focused"],[])}},e.prototype.setCopyData=function(e){if(""===window.getSelection().toString()){this.clipBoardTextArea.value=this.copyContent="";var t=this.parent.getRows();if("Cell"!==this.parent.options.selectionMode){var n=this.parent.getSelectedRowIndexes(this.parent.options.enableVirtualization);if(e){for(var r=[],i=0;i<this.parent.getVisibleColumns().length;i++)r[i]=this.parent.getVisibleColumns()[i].headerText;this.getCopyData(r,!1,"\t",e),this.copyContent+="\n"}for(i=0;i<n.length;i++){i>0&&(this.copyContent+="\n"),1!=parseInt(t[0].getAttribute("aria-rowindex"))&&"Both"!==this.parent.options.selectionMode&&(n[i]=n[i]-parseInt(t[0].getAttribute("aria-rowindex"))+1);var o=[].slice.call(t[n[i]].querySelectorAll(".e-rowcell:not(.e-hide)"));this.getCopyData(o,!1,"\t",e)}}else{var s=this.checkBoxSelection();if(s.status){if(e){var l=[];for(i=0;i<s.colIndexes.length;i++)l.push(this.parent.getColumnHeaderByIndex(s.colIndexes[i]));this.getCopyData(l,!1,"\t",e),this.copyContent+="\n"}for(i=0;i<s.rowIndexes.length;i++){i>0&&(this.copyContent+="\n");o=[].slice.call(t[s.rowIndexes[i]].querySelectorAll(".e-cellselectionbackground"));this.getCopyData(o,!1,"\t",e)}}else this.getCopyData([].slice.call(this.parent.element.querySelectorAll(".e-cellselectionbackground")),!0,"\n",e)}this.parent.options.isClipboardEventBinded?this.parent.dotNetRef.invokeMethodAsync("InvokeCopyPasteAction",{clipboardText:this.copyContent},"Copy"):this.clipBoardData(!1,this.copyContent)}else if(a(document.activeElement,"e-grid")){var d=document.activeElement;document.execCommand("copy"),this.clipBoardTextArea.blur(),d.focus()}},e.prototype.clipBoardData=function(e,t){var n=document.activeElement;this.copyContent=""!=t?t:this.copyContent,e||(this.clipBoardTextArea.value=this.copyContent,sf.base.Browser.userAgent.match(/ipad|ipod|iphone/i)?this.clipBoardTextArea.setSelectionRange(0,this.clipBoardTextArea.value.length):this.clipBoardTextArea.select(),this.isSelect=!0,document.queryCommandSupported("copy")&&(document.execCommand("copy"),this.clipBoardTextArea.blur(),n.focus()),this.isSelect&&(window.getSelection().removeAllRanges(),this.isSelect=!1))},e.prototype.getCopyData=function(e,t,n,r){for(var i="string"!=typeof e[0],o=0;o<e.length;o++)r&&t&&(this.copyContent+=this.parent.getColumns()[parseInt(e[o].getAttribute("data-colindex"),10)].headerText+"\n"),i?e[o].classList.contains("e-hide")||(!e[o].classList.contains("e-gridchkbox")&&Object.keys(e[o].querySelectorAll(".e-check")).length?this.copyContent+=!0:!e[o].classList.contains("e-gridchkbox")&&Object.keys(e[o].querySelectorAll(".e-uncheck")).length?this.copyContent+=!1:this.copyContent+=e[o].innerText):this.copyContent+=e[o],o<e.length-1&&(this.copyContent+=n)},e.prototype.copy=function(e){document.queryCommandSupported("copy")&&this.setCopyData(e)},e.prototype.getSelectedRowCellIndexes=function(){var e,t=this.parent,n=[],r=t.getRows();t.options.frozenColumns&&(e=t.getMovableDataRows());for(var i=function(i){var o=r[i].querySelectorAll(".e-cellselectionbackground");if(t.options.frozenColumns&&!o.length&&(o=e[i].querySelectorAll(".e-cellselectionbackground")),o.length){var s=[];o.forEach((function(e){s.push(parseInt(e.getAttribute("data-colindex")))})),n.push({rowIndex:i,cellIndexes:s})}},o=0;o<r.length;o++)i(o);return n},e.prototype.checkBoxSelection=function(){var e,t={status:!1};if("Cell"===this.parent.options.selectionMode){e=this.getSelectedRowCellIndexes();var n=void 0,r=[],i=void 0;for(i=0;i<e.length&&(e[i].cellIndexes.length&&r.push(e[i].rowIndex),!e[i].cellIndexes.length||(n||(n=JSON.stringify(e[i].cellIndexes.sort())),n===JSON.stringify(e[i].cellIndexes.sort())));i++);r.sort((function(e,t){return e-t})),i===e.length&&(t={status:!0,rowIndexes:r,colIndexes:e[0].cellIndexes})}return t},e}(),B=function(){function e(e){this.parent=e,this.wireEvents()}return e.prototype.wireEvents=function(){sf.base.EventHandler.add(this.parent.getContent(),"scroll",this.scrollHandler,this),sf.base.EventHandler.add(this.parent.element,"mousemove",this.mouseMoveHandler,this),sf.base.EventHandler.add(this.parent.element,"mouseout",this.mouseMoveHandler,this),sf.base.EventHandler.add(this.parent.element,"keydown",this.onKeyPressed,this)},e.prototype.unWireevents=function(){sf.base.EventHandler.remove(this.parent.getContent(),"scroll",this.scrollHandler),sf.base.EventHandler.remove(this.parent.element,"mousemove",this.mouseMoveHandler),sf.base.EventHandler.remove(this.parent.element,"mouseout",this.mouseMoveHandler),sf.base.EventHandler.remove(this.parent.element,"keydown",this.onKeyPressed)},e.prototype.open=function(e){this.close(),this.ctrlId=sf.base.getUniqueID(this.parent.element.getAttribute("id")),sf.base.isNullOrUndefined(this.toolTipElement)&&(this.toolTipElement=sf.base.createElement("div",{className:"e-tooltip-wrap e-popup e-lib e-control e-popup-open",styles:'width: "auto", height: "auto", position: "absolute"',attrs:{role:"tooltip","aria-hidden":"false",id:this.ctrlId+"_content"}})),sf.base.attributes(e,{"aria-describedby":this.ctrlId+"_content","data-tooltip-id":this.ctrlId+"_content"}),this.renderToolTip(),this.setPosition(e)},e.prototype.renderToolTip=function(){var e=sf.base.createElement("div",{className:"e-tip-content"});e.innerHTML=this.content,this.toolTipElement.appendChild(e);var t=sf.base.createElement("div",{className:"e-arrow-tip e-tip-bottom",styles:"top: 99.9%"});t.appendChild(sf.base.createElement("div",{className:"e-arrow-tip-outer e-tip-bottom"})),t.appendChild(sf.base.createElement("div",{className:"e-arrow-tip-inner e-tip-bottom",styles:"top: -6px"})),this.toolTipElement.appendChild(t),document.body.appendChild(this.toolTipElement)},e.prototype.setPosition=function(e){var t={top:0,left:0},n=this.toolTipElement.querySelector(".e-arrow-tip"),r=sf.popups.calculatePosition(e,"Center","Top");t.top-=this.toolTipElement.offsetHeight+n.offsetHeight,t.left-=this.toolTipElement.offsetWidth/2,this.toolTipElement.style.top=r.top+t.top+"px",this.toolTipElement.style.left=r.left+t.left+"px";var i=a(this.parent.element,"e-dialog");i&&(this.toolTipElement.style.zIndex=(parseInt(i.style.zIndex,10)+1).toString())},e.prototype.close=function(){if(this.toolTipElement){var e=this.parent.element.querySelector('[aria-describedby="'+this.ctrlId+'_content"]');sf.base.isNullOrUndefined(e)?sf.base.isNullOrUndefined(this.parent.element.querySelector("form"))||(sf.base.isNullOrUndefined(document.getElementById(this.ctrlId+"_content"))||document.getElementById(this.ctrlId+"_content").remove(),this.toolTipElement=null):(e.removeAttribute("aria-describedby"),e.removeAttribute("data-tooltip-id"),this.toolTipElement=null),sf.base.isNullOrUndefined(document.getElementById(this.ctrlId+"_content"))||(document.getElementById(this.ctrlId+"_content").remove(),this.toolTipElement=null)}},e.prototype.getTooltipStatus=function(e){var t,n=this.parent.getHeaderTable(),r=this.parent.getContentTable(),i=this.createTable(n,"e-gridheader","header"),o=this.createTable(r,"e-gridcontent","content"),s=e.classList.contains("e-headercell")?i:o,l=e.classList.contains("e-headercell")?"th":"tr";return s.querySelector(l).className=e.className,s.querySelector(l).innerText=e.innerText,t=s.querySelector(l).getBoundingClientRect().width,document.body.removeChild(i),document.body.removeChild(o),e.firstElementChild?t>e.getBoundingClientRect().width||e.firstElementChild.offsetWidth<e.firstElementChild.scrollWidth:t>e.getBoundingClientRect().width||e.offsetWidth<e.scrollWidth},e.prototype.mouseMoveHandler=function(e){if(this.isEllipsisTooltip()){if(this.parent.options.allowTextWrap){var t=this.parent.options.wrapMode;if("Header"==t&&a(e.target,"e-gridheader")||"Content"==t&&a(e.target,"e-gridcontent")||"Both"==t)return}var n=a(e.target,"e-ellipsistooltip");this.prevElement===n&&"mouseout"!==e.type||this.close();var r=e.target.tagName;if(n&&"mouseout"!==e.type&&(!sf.base.Browser.isDevice||-1===["A","BUTTON","INPUT"].indexOf(r))){if(n.getAttribute("data-tooltip-id"))return;if(this.getTooltipStatus(n)){if(n.getElementsByClassName("e-headertext").length){var i=this.parent.getColumnByUid(n.querySelector(".e-headercelldiv").getAttribute("e-mappinguid"));this.content=sf.base.isNullOrUndefined(i.description)?n.getElementsByClassName("e-headertext")[0].innerText:i.description}else this.content=n.innerText;this.prevElement=n,this.open(n)}}}this.hoverFrozenRows(e)},e.prototype.hoverFrozenRows=function(e){if(this.parent.options.frozenColumns){var t=a(e.target,"e-row"),n=[].slice.call(this.parent.element.querySelectorAll(".e-frozenhover"));if(n.length&&"mouseout"===e.type)for(var r=0;r<n.length;r++)n[r].classList.remove("e-frozenhover");else if(t){var i=[].slice.call(this.parent.element.querySelectorAll('tr[data-rowindex="'+t.getAttribute("data-rowindex")+'"]'));if(i.splice(i.indexOf(t),1),"true"!=t.getAttribute("aria-selected"))for(r=0;r<i.length;r++)i[r].classList.add("e-frozenhover");else for(r=0;r<i.length;r++)i[r].classList.remove("e-frozenhover")}}},e.prototype.isEllipsisTooltip=function(){var e=this.parent.getColumns();if("EllipsisWithTooltip"===this.parent.options.clipMode)return!0;for(var t=0;t<e.length;t++)if("EllipsisWithTooltip"===e[t].clipMode)return!0;return!1},e.prototype.scrollHandler=function(){this.isEllipsisTooltip()&&this.close()},e.prototype.createTable=function(e,t,n){var r=sf.base.createElement("div");r.className=this.parent.element.className,r.style.cssText="display: inline-block;visibility:hidden;position:absolute";var i=sf.base.createElement("div");i.className=t;var o=sf.base.createElement("table");o.className=e.className,o.style.cssText="table-layout: auto;width: auto";var s="header"===n?"th":"td",l=sf.base.createElement("tr"),a=sf.base.createElement(s);return l.appendChild(a),o.appendChild(l),i.appendChild(o),r.appendChild(i),document.body.appendChild(r),r},e.prototype.onKeyPressed=function(e){"Tab"!==e.key&&"ShiftTab"!==e.key||this.close()},e.prototype.destroy=function(){this.close(),this.unWireevents()},e}(),U=function(){function e(e){var t=this;this.isOverflowBorder=!0,this.istargetGrid=!1,this.dataRowElements=[],this.showAddNewRowDisable=!1,this.helper=function(e){var n=t.parent,r=t.draggable.currentStateTarget;t.draggable.queryPositionInfo=function(e){return n.options.enableRtl&&sf.base.isNullOrUndefined(n.options.rowDropTarget)&&(e.left=this.position.left-(this.parentClientRect.left+this.borderWidth.left)-n.element.querySelector(".e-cloneproperties").clientWidth+n.element.querySelector(".e-rowdragdrop").clientWidth/2+"px"),e};var i=sf.base.createElement("div",{className:"e-cloneproperties e-draganddrop e-grid e-dragclone",styles:'height:"auto", z-index:2, width:'+n.element.offsetWidth}),o=sf.base.createElement("table",{styles:"width:"+n.element.offsetWidth}),s=sf.base.createElement("tbody");if((document.getElementsByClassName("e-griddragarea").length||n.options.rowDropTarget&&!e.sender.target.classList.contains("e-selectionbackground")&&"Single"!==n.options.selectionType||!n.options.rowDropTarget&&!a(r,"e-rowdragdrop")||n.options.rowDropTarget&&"Single"===n.options.selectionType&&null===r.parentElement.getAttribute("data-rowindex"))&&sf.base.isNullOrUndefined(a(r,"e-rowdragdrop")))return!1;n.options.rowDropTarget&&"Row"===n.options.selectionMode&&"Single"===n.options.selectionType&&null!==t.draggable.currentStateTarget.parentElement.getAttribute("data-rowindex")&&n.dotNetRef.invokeMethodAsync("SelectRow",parseInt(t.draggable.currentStateTarget.parentElement.getAttribute("data-rowindex"),10),!1,-1),t.startedRow=sf.base.closest(r,"tr").cloneNode(!0),sf.base.isNullOrUndefined(t.startedRow.querySelector(".e-rowcell.e-focus"))||sf.base.removeClass([t.startedRow.querySelector(".e-rowcell.e-focus")],["e-focus","e-focused"]);var l=n.getSelectedRows(),d=a(r,"e-detailrow"),u=l.filter((function(e){var t=a(e,"e-detailrow");return sf.base.isNullOrUndefined(t)||!sf.base.isNullOrUndefined(d)&&!sf.base.isNullOrUndefined(t)}));f(t.startedRow,".e-indentcell"),f(t.startedRow,".e-detailrowcollapse"),f(t.startedRow,".e-detailrowexpand"),t.removeCell(t.startedRow,"e-gridchkbox");var h=new RegExp("e-active","g");if(t.startedRow.innerHTML=t.startedRow.innerHTML.replace(h,""),s.appendChild(t.startedRow),!sf.base.isNullOrUndefined(u)&&u.length>1&&n.getSelectedRows().length>1&&t.startedRow.hasAttribute("aria-selected")){var p=sf.base.createElement("span",{className:"e-dropitemscount",innerHTML:""+u.length});i.appendChild(p)}var c=sf.base.closest(r,"tr").querySelector(".e-icon-rowdragicon");return c&&a(r,"e-rowdragdrop")&&c.classList.add("e-dragstartrow"),o.appendChild(s),i.appendChild(o),n.element.appendChild(i),i},this.dragStart=function(e){var n=t.parent;if(document.body.classList.add("e-prevent-select"),!document.getElementsByClassName("e-griddragarea").length){var r=t.parent.element.querySelector(".e-dropitemscount");t.parent.getSelectedRows().length>1&&r&&(r.style.left=t.parent.element.querySelector(".e-cloneproperties table").offsetWidth-5+"px");var i=parseInt(t.startedRow.getAttribute("data-rowindex"),10),o=sf.base.isNullOrUndefined(t.startedRow.getAttribute("data-uid"))?null:t.startedRow.getAttribute("data-uid");t.parent.dotNetRef.invokeMethodAsync("RowDragStartEvent",i,o),e.bindEvents(e.dragElement),t.dragStartData=t.rowData;var s=document.getElementById(n.options.rowDropTarget);n.options.rowDropTarget&&s&&s.blazor__instance&&"function"==typeof s.blazor__instance.getModuleName&&"grid"===s.blazor__instance.getModuleName()&&sf.base.isNullOrUndefined(n.element.querySelector(".e-dragstartrow"))&&s.blazor__instance.getContent().classList.add("e-allowRowDrop")}},this.drag=function(e){var n=t.parent;t.istargetGrid=!1,t.destinationGrid=t.parent,t.showAddNewRowDisable=!1;var r=t.parent.element.querySelector(".e-cloneproperties"),i=t.getElementFromPosition(r,e.event),o=a(r,"e-grid").parentElement,s=sf.base.isNullOrUndefined(o)?null:o.querySelector(".e-dragstartrow"),l=a(e.target,"e-grid");if(t.parent.options.rowDropTarget){var d=document.getElementById(n.options.rowDropTarget);sf.base.isNullOrUndefined(o)||sf.base.isNullOrUndefined(l)||o.id===l.id||(t.destinationGrid=sf.base.isNullOrUndefined(d)||sf.base.isNullOrUndefined(d.blazor__instance)?t.parent:d.blazor__instance),a(e.target,"e-grid")&&(t.istargetGrid=t.parent.options.rowDropTarget===a(e.target,"e-grid").id)}sf.base.isNullOrUndefined(l)||!l.blazor__instance.options.showAddNewRow||t.showAddNewRowDisable||(n.dotNetRef.invokeMethodAsync("DisableShowAddForm","RowDragStart",!1,t.destinationGrid.dotNetRef),t.showAddNewRowDisable=!0),sf.base.classList(r,["e-defaultcur"],["e-notallowedcur","e-movecur"]),t.isOverflowBorder=!0;var u=a(i,"e-grid")?sf.base.closest(e.target,"tr"):null,h=a(o,"e-detailrow"),p=a(l,"e-detailrow"),c=a(i,"e-row");if(e.target){if(t.stopTimer(),n.element.classList.add("e-rowdrag"),t.dragTarget=u&&a(i,"e-grid").id===r.parentElement.id?n.options.groupCount>0||t.parent.options.showAddNewRow?parseInt(u.getAttribute("data-rowindex"),10):u.rowIndex:parseInt(t.startedRow.getAttribute("data-rowindex"),10),!n.options.rowDropTarget||sf.base.isNullOrUndefined(l)||o.id===l.id||sf.base.isNullOrUndefined(s)||(t.dragTarget=u&&a(i,"e-grid").id!==r.parentElement.id?u.rowIndex:parseInt(t.startedRow.getAttribute("data-rowindex"),10)),n.options.rowDropTarget){var f=document.getElementById(n.options.rowDropTarget),g=a(e.target,"e-row");a(i,"e-gridcontent")?a(r.parentElement,"e-grid").id===a(i,"e-grid").id&&(sf.base.isNullOrUndefined(s)||!sf.base.isNullOrUndefined(c)&&"true"===c.getAttribute("aria-selected"))||sf.base.isNullOrUndefined(g)&&sf.base.isNullOrUndefined(l.querySelector(".e-emptyrow"))||!sf.base.isNullOrUndefined(f)&&f.classList.contains("e-grid")&&!sf.base.isNullOrUndefined(l)&&t.destinationGrid.element.id!==l.id||a(u,"e-showAddNewRow")?sf.base.classList(r,["e-notallowedcur"],["e-defaultcur"]):sf.base.classList(r,["e-defaultcur"],["e-notallowedcur"]):a(i,"e-droppable:not(.e-headercontent)")?sf.base.classList(r,["e-defaultcur"],["e-notallowedcur"]):(sf.base.isNullOrUndefined(f)||sf.base.isNullOrUndefined(i)||!a(i,"e-grid"))&&f.contains(i)||sf.base.classList(r,["e-notallowedcur"],["e-defaultcur"])}else{var m=a(i,"e-grid");m&&m.id===r.parentElement.id&&!sf.base.isNullOrUndefined(c)&&"false"===c.getAttribute("aria-selected")?sf.base.classList(r,["e-movecur"],["e-defaultcur"]):sf.base.classList(r,["e-notallowedcur"],["e-movecur"])}if(n.options.allowRowDragAndDrop||!n.options.rowDropTarget&&e.target.classList.contains("e-selectionbackground")){if(a(i,"e-grid")){var v=sf.base.isNullOrUndefined(o)?null:a(o,"e-treegrid");sf.base.isNullOrUndefined(s)||(sf.base.isNullOrUndefined(l)||o!==l)&&!v||(sf.base.isNullOrUndefined(u)||a(u,"e-content")||(t.removeTargetGridBorder(o.blazor__instance),t.removeTargetGridBorder(l.blazor__instance)),t.updateScrollPostion(e.event,i))}if(sf.base.isNullOrUndefined(document.querySelector(".e-lastrow-dragborder"))||document.querySelector(".e-lastrow-dragborder").remove(),a(u,"e-columnheader")){var b=a(u,"e-detailrow");if(!sf.base.isNullOrUndefined(b)&&b.getElementsByClassName("e-emptyrow"))return}var y=!sf.base.isNullOrUndefined(u)&&!u.classList.contains("e-emptyrow"),C=t.parent.options.frozenRows&&t.parent.options.frozenColumns&&!sf.base.isNullOrUndefined(u)&&parseInt(t.startedRow.getAttribute("data-rowindex"),10)!==parseInt(u.getAttribute("data-rowindex"),10);if(t.isOverflowBorder&&(parseInt(t.startedRow.getAttribute("data-rowindex"),10)!==t.dragTarget||C)&&y)t.moveDragRows(e,t.startedRow,u);else{var w=t.parent.getRows(),S=!sf.base.isNullOrUndefined(u)&&t.startedRow.getAttribute("data-uid")!==w[w.length-1].getAttribute("data-uid");if(u&&!sf.base.isNullOrUndefined(t.parent.getRowByIndex(w.length-1))&&t.parent.getRowByIndex(w.length-1).getAttribute("data-uid")===u.getAttribute("data-uid")&&S&&!n.options.groupCount){var x=sf.base.createElement("div",{className:"e-lastrow-dragborder"}),z=t.parent.getContent();if(!sf.base.isNullOrUndefined(z)){var I=z.scrollWidth>z.clientWidth&&z.scrollHeight<=z.clientHeight;x.style.width=I?t.parent.getContent().offsetWidth+"px":t.parent.getContent().offsetWidth-t.getScrollWidth()+"px",t.borderIndex=Number(a(u,"e-row").getAttribute("data-rowindex")),z.parentElement.querySelectorAll(".e-lastrow-dragborder").length||(z.classList.add("e-grid-relative"),z.parentElement.appendChild(x),x.style.bottom=t.parent.options.allowPaging?t.parent.element.querySelector(".e-pager").offsetHeight+t.getScrollWidth()+"px":t.getScrollWidth()+"px")}}else if(!sf.base.isNullOrUndefined(l)&&l.querySelector(".e-emptyrow")&&o.id!==l.id&&l.querySelector(".e-content").getElementsByClassName("e-emptyrow").length>0&&(0==l.blazor__instance.getRows().length||!sf.base.isNullOrUndefined(u)&&a(u,"e-detailrow")&&u.getElementsByClassName("e-emptyrow").length>0)&&(!sf.base.isNullOrUndefined(h)||sf.base.isNullOrUndefined(h)&&sf.base.isNullOrUndefined(p))){x=sf.base.createElement("div",{className:"e-lastrow-dragborder"}),z=l.querySelector(".e-content");if(!sf.base.isNullOrUndefined(z)){I=z.scrollWidth>z.clientWidth&&z.scrollHeight<=z.clientHeight;x.style.width=I?t.destinationGrid.getContent().offsetWidth+"px":t.destinationGrid.getContent().offsetWidth-t.getScrollWidth()+"px";var R=!sf.base.isNullOrUndefined(u)&&u.classList.contains("e-detailrow");z=R?u.getElementsByClassName("e-grid")[0].querySelector(".e-content"):z,x.style.position=R?"relative":x.style.position,z.parentElement.querySelectorAll(".e-lastrow-dragborder").length||(z.classList.add("e-grid-relative"),z.parentElement.appendChild(x),x.style.bottom=t.destinationGrid.options.allowPaging?t.destinationGrid.element.querySelector(".e-pager").offsetHeight+t.getScrollWidth()+"px":t.getScrollWidth()+"px")}}t.removeBorder(u)}n.options.groupCount&&(sf.base.isNullOrUndefined(u)||u.querySelector("td.e-groupcaption")||t.startedRow.getAttribute("caption-uid")===u.getAttribute("caption-uid")||!n.options.groupCount?t.addorRemoveDashedBorder(e,!1,t.dataRowElements):(t.addorRemoveDashedBorder(e,!1,t.dataRowElements),t.dataRowElements=t.parent.getDataRows().filter((function(e){return e.getAttribute("caption-uid")===u.getAttribute("caption-uid")})),t.addorRemoveDashedBorder(e,!0,t.dataRowElements))),t.isOverflowBorder||t.addorRemoveDashedBorder(e,!1,t.dataRowElements)}n.options.rowDropTarget&&t.istargetGrid&&o!==l&&(t.updateScrollPostion(e.event,i),t.moveDragRows(e,t.startedRow,u),sf.base.isNullOrUndefined(u)||t.lastRowBorderBetweenGrids(u,e.event))}},this.lastRowBorderBetweenGrids=function(e,n){var r=t.destinationGrid,i=r.getRows(),o=i.length>0&&!sf.base.isNullOrUndefined(e)&&r.getContent().querySelector("tr:last-child")===e,s=n.clientY-e.getBoundingClientRect().top,l=e.offsetHeight/2,d=Array.from(i).reduce((function(e,t){return e+t.offsetHeight}),0)>=r.getContent().clientHeight;if(i.length>0&&s>l&&d&&e&&!sf.base.isNullOrUndefined(r.getRowByIndex(i.length-1))&&r.getRowByIndex(i.length-1).getAttribute("data-uid")===e.getAttribute("data-uid")&&o&&!r.options.groupCount){var u=sf.base.createElement("div",{className:"e-lastrow-dragborder"}),h=r.getContent();if(!sf.base.isNullOrUndefined(h)){var p=h.scrollWidth>h.clientWidth&&h.scrollHeight<=h.clientHeight;u.style.width=p?h.offsetWidth+"px":h.offsetWidth-t.getScrollWidth()+"px",t.borderIndex=Number(a(e,"e-row").getAttribute("data-rowindex")),h.parentElement.querySelectorAll(".e-lastrow-dragborder").length||(h.classList.add("e-grid-relative"),h.parentElement.appendChild(u),u.style.bottom=r.options.allowPaging?r.element.querySelector(".e-pager").offsetHeight+t.getScrollWidth()+"px":t.getScrollWidth()+"px")}}},this.dragStop=function(e){document.body.classList.remove("e-prevent-select"),t.processDragStop(e)},this.emptyTargertInGrid=function(e){var n=t.parent,r=n.getContent().getElementsByClassName("e-dragborder"),i=n.getHeaderContent().getElementsByClassName("e-firstrow-dragborder"),o=n.getContent().parentElement.getElementsByClassName("e-lastrow-dragborder"),s=!e.classList.contains("e-rowdragdrop")&&!e.classList.contains("e-icon-rowdragicon"),l=t.destinationGrid.getRows().length>0&&!sf.base.isNullOrUndefined(n.element.querySelector(".e-dragstartrow"))&&!sf.base.isNullOrUndefined(r)&&!sf.base.isNullOrUndefined(i)&&0===r.length&&0===i.length&&sf.base.isNullOrUndefined(a(e,"e-rowcell"))&&s;return!(t.parent.options.rowDropTarget||l||t.isOverflowBorder||sf.base.isNullOrUndefined(i)||sf.base.isNullOrUndefined(o)||0!==i.length||0!==o.length)||l},this.processDragStop=function(e){var n=t.parent,r=t.getElementFromPosition(e.helper,e.event),i=r&&!r.classList.contains("e-dlg-overlay")?r:e.target;n.element.classList.remove("e-rowdrag");var o=document.getElementById(n.options.rowDropTarget),s=n.element.querySelector(".e-dragstartrow"),l=a(n.element.querySelector(".e-cloneproperties"),"e-grid").parentElement,d=a(e.target,"e-grid"),u=!sf.base.isNullOrUndefined(o)&&!o.classList.contains("e-grid")&&!sf.base.isNullOrUndefined(d)&&l.id!==d.id,h=!sf.base.isNullOrUndefined(d)&&!sf.base.isNullOrUndefined(d.querySelector(".e-emptyrow"));if(t.parent.options.allowRowDragAndDrop&&t.parent.options.rowDropTarget&&(!a(i,"e-grid")&&o.contains(e.target)||u&&(a(e.target,"e-row")||h))){var p=t.getElementXPath(i),c=i.id,f=parseInt(t.startedRow.getAttribute("data-rowindex"),10),g=i.getBoundingClientRect();n.dotNetRef.invokeMethodAsync("ReorderRows",f,0,"add",!1,p,c,g,null,!0,!1,null,null,!1)}if(n.options.rowDropTarget&&o&&o.blazor__instance&&"function"==typeof o.blazor__instance.getModuleName&&"grid"===o.blazor__instance.getModuleName()&&o.blazor__instance.getContent().classList.remove("e-allowRowDrop"),!a(i,"e-gridcontent")||i.classList.contains("e-lastrow-dragborder")||t.emptyTargertInGrid(i)||a(e.target,"e-columnheader"))return t.dragTarget=null,sf.base.remove(e.helper),t.stopTimer(),t.removeBorder(r),t.parent.options.rowDropTarget&&!sf.base.isNullOrUndefined(d)&&t.removeTargetGridBorder(d.blazor__instance),n.options.groupCount&&t.addorRemoveDashedBorder(e,!1,t.dataRowElements),s&&s.classList.remove("e-dragstartrow"),void(n.options.showAddNewRow&&t.showAddNewRowDisable&&(n.dotNetRef.invokeMethodAsync("DisableShowAddForm","RowDragStop",!0,null),t.showAddNewRowDisable=!1));if(t.parent.options.allowRowDragAndDrop){if(t.stopTimer(),t.parent.getContent().classList.remove("e-grid-relative"),t.removeBorder(r),n.options.groupCount&&t.addorRemoveDashedBorder(e,!1,t.dataRowElements),s&&t.parent.options.rowDropTarget&&!sf.base.isNullOrUndefined(d)&&l.id!==d.id)return;if(s&&!sf.base.isNullOrUndefined(d)&&l.id===d.id&&s.classList.remove("e-dragstartrow"),sf.base.isNullOrUndefined(s)&&l.querySelector(".e-selectionbackground"))return;var m=t.getElementXPath(e.target),v=i.id,b=parseInt(t.startedRow.getAttribute("data-rowindex"),10),y=sf.base.isNullOrUndefined(t.startedRow.getAttribute("data-uid"))?null:t.startedRow.getAttribute("data-uid"),C=sf.base.isNullOrUndefined(sf.base.closest(e.target,"tr"))?null:sf.base.closest(e.target,"tr").getAttribute("data-uid"),w=t.parent.options.enableVirtualization?b===t.dragTarget?t.dragTarget:b<t.borderIndex?t.borderIndex:t.borderIndex+1:t.parent.options.frozenRows?b<t.borderIndex?t.borderIndex:t.borderIndex+1:t.dragTarget;if(Number.isNaN(w)||sf.base.isNullOrUndefined(w))return void(n.options.showAddNewRow&&t.showAddNewRowDisable&&(n.dotNetRef.invokeMethodAsync("DisableShowAddForm","RowDragStop",!0,null),t.showAddNewRowDisable=!1));setTimeout((function(){n.dotNetRef.invokeMethodAsync("ReorderRows",b,w,"delete",!0,m,v,null,null,!1,!1,y,C,!1)}),10),t.dragTarget=null}n.options.showAddNewRow&&t.showAddNewRowDisable&&(n.dotNetRef.invokeMethodAsync("DisableShowAddForm","RowDragStop",!1,null),t.showAddNewRowDisable=!1)},this.removeCell=function(e,t){return[].slice.call(e.querySelectorAll("td")).filter((function(n){n.classList.contains(t)&&e.deleteCell(n.cellIndex)}))},this.drop=function(e){t.columnDrop({target:e.target,droppedElement:e.droppedElement,mouseEvent:e.event}),sf.base.remove(e.droppedElement)},this.parent=e,this.parent.options.allowRowDragAndDrop&&this.initializeDrag()}return e.prototype.stopTimer=function(){window.clearInterval(this.timer)},e.prototype.initializeDrag=function(){var e=this.parent;this.draggable=new sf.base.Draggable(e.getContent(),{dragTarget:".e-rowcelldrag, .e-rowdragdrop, .e-rowcell",distance:5,helper:this.helper,dragStart:this.dragStart,drag:this.drag,dragStop:this.dragStop,isPreventSelect:!1}),this.droppable=new sf.base.Droppable(e.getContent(),{accept:".e-dragclone",drop:this.drop})},e.prototype.updateScrollPostion=function(e,t){var n=this,r=h(e).y,i=this.destinationGrid.getContent().getBoundingClientRect(),o=this.destinationGrid.getRowHeight()-15,s=this.destinationGrid.getContent();if(i.top+o>=r){var l=-this.destinationGrid.getRowHeight();this.isOverflowBorder=!1,this.timer=window.setInterval((function(){n.setScrollDown(s,l,!0)}),200)}else if(i.top+this.destinationGrid.getContent().clientHeight-o-20<=r){var a=this.destinationGrid.getRowHeight();this.isOverflowBorder=!1,this.timer=window.setInterval((function(){n.setScrollDown(s,a,!0)}),200)}},e.prototype.setScrollDown=function(e,t,n){e.scrollTop=e.scrollTop+t},e.prototype.moveDragRows=function(e,t,n){var r=this.parent.element.querySelector(".e-cloneproperties"),i=sf.base.closest(e.target,"tr");if(a(i,"e-gridcontent")&&(!sf.base.isNullOrUndefined(r)&&a(r.parentElement,"e-grid").id===a(i,"e-grid").id||this.istargetGrid)){var o=i||this.startedRow;this.setBorder(o,e.event,t,n)}},e.prototype.setBorder=function(e,t,n,r){var i=this.parent.element;this.istargetGrid&&(i=this.destinationGrid.element);var o=this.parent.element.querySelector(".e-cloneproperties");this.parent.element.id!==this.destinationGrid.element.id&&(this.parent.element.getElementsByClassName("e-firstrow-dragborder").length>0||this.parent.element.getElementsByClassName("e-lastrow-dragborder").length>0)&&this.removeTargetGridBorder(this.parent),this.parent.options.groupCount?this.removeBorder(e):(this.removeFirstRowBorder(e),this.removeLastRowBorder(e));var s=this.parent.element.querySelector(".e-dragstartrow"),l=t.clientY-r.getBoundingClientRect().top,d=r.offsetHeight/2;if(!(sf.base.isNullOrUndefined(s)&&l>d)){if(!sf.base.isNullOrUndefined(r)){var u=a(r,"e-grid");if(!sf.base.isNullOrUndefined(u)&&u.id===a(n,"e-grid").parentElement.id&&(sf.base.isNullOrUndefined(u.querySelector(".e-dragstartrow"))||"true"===r.getAttribute("aria-selected")))return void this.removeBorder(e)}if(a(e,"e-gridcontent")&&(a(o.parentElement,"e-grid").id===a(e,"e-grid").id||this.istargetGrid)){sf.base.removeClass(i.querySelectorAll(".e-rowcell,.e-rowdragdrop,.e-detailrowcollapse, .e-detailrowexpand"),["e-dragborder"]);var h=[],p=parseInt(r.getAttribute("data-rowindex"),10),c=this.destinationGrid.getContentTable().querySelector("tr:last-child"),f=this.destinationGrid.element.querySelector(".e-showAddNewRow");if(r&&0===p&&sf.base.isNullOrUndefined(f)&&(this.parent.element.id!==this.destinationGrid.element.id&&l<d||this.parent.element.id===this.destinationGrid.element.id)){if(!r.classList.contains("e-emptyrow")&&r.classList.contains("e-row"))if(this.parent.options.groupCount&&!r.classList.contains("e-groupcaption"))e=r,h=[].slice.call(e.querySelectorAll(".e-groupcaption,.e-summarycell,.e-rowcell,.e-rowdragdrop,.e-detailrowcollapse, .e-detailrowexpand"));else{var m=sf.base.createElement("div",{className:"e-firstrow-dragborder"}),v=this.destinationGrid.getHeaderContent();if(v.classList.add("e-grid-relative"),!sf.base.isNullOrUndefined(this.destinationGrid.getContent())){var b=this.destinationGrid.getContent().scrollWidth>this.destinationGrid.getContent().clientWidth&&this.destinationGrid.getContent().scrollHeight<=this.destinationGrid.getContent().clientHeight;m.style.width=b?i.offsetWidth+"px":i.offsetWidth-this.getScrollWidth()+"px"}v.querySelectorAll(".e-firstrow-dragborder").length||v.appendChild(m)}if(this.borderIndex=-1,this.destinationGrid.element.getElementsByClassName("e-lastrow-dragborder").length>0)this.destinationGrid.element.getElementsByClassName("e-lastrow-dragborder")[0].remove();else if(document.getElementsByClassName("e-lastrow-dragborder").length>0){var y=document.getElementsByClassName("e-lastrow-dragborder")[0];sf.base.isNullOrUndefined(a(y,"e-grid"))||y.remove()}}else r&&parseInt(n.getAttribute("data-rowindex"),10)>p||this.parent.options.rowDropTarget&&this.parent.element.id!==this.destinationGrid.element.id?(this.parent.options.groupCount&&this.parent.options.enableVirtualization&&(p=this.parent.getDataRows().indexOf(r)),this.parent.options.groupCount&&!r.classList.contains("e-groupcaption")?(e=r,sf.base.isNullOrUndefined(e)||e.classList.contains("e-detailrow")||(h=[].slice.call(e.querySelectorAll(".e-rowcell,.e-rowdragdrop,.e-detailrowcollapse, .e-detailrowexpand")))):(e=!sf.base.isNullOrUndefined(this.parent.element.querySelector(".e-dragstartrow"))&&r===c&&l>d||this.destinationGrid.options.frozenRows?this.destinationGrid.getRowByIndex(p):this.destinationGrid.getRowByIndex(r.rowIndex-1),sf.base.isNullOrUndefined(e)||e.classList.contains("e-detailrow")||(h=[].slice.call(e.querySelectorAll(".e-rowcell,.e-rowdragdrop,.e-detailrowcollapse, .e-detailrowexpand"))))):sf.base.isNullOrUndefined(e)||e.classList.contains("e-detailrow")||o.classList.contains("e-notallowedcur")||(h=[].slice.call(e.querySelectorAll(".e-rowcell,.e-rowdragdrop,.e-detailrowcollapse, .e-detailrowexpand")));if(h.length>0&&(this.borderIndex=Number(a(h[0],"e-row").getAttribute("data-rowindex")),!this.parent.options.groupCount)){if(g(h,!0,"e-dragborder"),this.destinationGrid.element.getElementsByClassName("e-lastrow-dragborder").length>0)this.destinationGrid.element.getElementsByClassName("e-lastrow-dragborder")[0].remove();else if(document.getElementsByClassName("e-lastrow-dragborder").length>0){var C=document.getElementsByClassName("e-lastrow-dragborder")[0];sf.base.isNullOrUndefined(a(C,"e-grid"))||C.remove()}if(this.destinationGrid.element.getElementsByClassName("e-firstrow-dragborder").length>0)this.destinationGrid.element.getElementsByClassName("e-firstrow-dragborder")[0].remove();else if(document.getElementsByClassName("e-firstrow-dragborder").length>0){var w=document.getElementsByClassName("e-firstrow-dragborder")[0];sf.base.isNullOrUndefined(a(w,"e-grid"))||w.remove()}}}}},e.prototype.addorRemoveDashedBorder=function(e,t,n){if(!(n.length<=0)){var r,i,o=n[0],s=n[n.length-1];r=[].slice.call(o.querySelectorAll(".e-rowcell:not(.e-hide),.e-rowdragdrop,.e-detailrowcollapse, .e-detailrowexpand")),i=[].slice.call(s.querySelectorAll(".e-rowcell:not(.e-hide),.e-rowdragdrop,.e-detailrowcollapse, .e-detailrowexpand")),g(r,t,"e-dragtop"),g(i,t,"e-dragbottom"),this.updateDragClasses(t,n)}},e.prototype.updateDragClasses=function(e,t){for(var n=0;n<t.length;n++){var r=[];(r=[].slice.call(t[n].querySelectorAll(".e-rowcell:not(.e-hide),.e-rowdragdrop,.e-detailrowcollapse, .e-detailrowexpand"))).length&&(e?(r[0].classList.add("e-dragleft"),r[r.length-1].classList.add("e-dragright")):(r[0].classList.remove("e-dragleft"),r[r.length-1].classList.remove("e-dragright")))}},e.prototype.getScrollWidth=function(){var e=sf.base.isNullOrUndefined(this.destinationGrid)||this.destinationGrid.element.id===this.parent.element.id?this.parent.getContent():this.destinationGrid.getContent();return e.scrollWidth>e.offsetWidth?o():0},e.prototype.removeFirstRowBorder=function(e){this.destinationGrid.element.getElementsByClassName("e-firstrow-dragborder").length>0&&e&&0!==e.rowIndex&&this.destinationGrid.element.getElementsByClassName("e-firstrow-dragborder")[0].remove()},e.prototype.removeLastRowBorder=function(e){var t=e&&!sf.base.isNullOrUndefined(this.destinationGrid.getRowByIndex(this.destinationGrid.getRows().length-1))&&this.destinationGrid.getRowByIndex(this.destinationGrid.getRows().length-1).getAttribute("data-uid")!==e.getAttribute("data-uid");this.destinationGrid.element.getElementsByClassName("e-lastrow-dragborder").length>0&&e&&t&&this.destinationGrid.element.getElementsByClassName("e-lastrow-dragborder")[0].remove()},e.prototype.removeBorder=function(e){(this.removeFirstRowBorder(e),this.removeLastRowBorder(e),e=this.destinationGrid.getRows().filter((function(e){return e.querySelector("td.e-dragborder")}))[0])&&g([].slice.call(e.querySelectorAll(".e-dragborder")),!1,"e-dragborder")},e.prototype.getElementFromPosition=function(e,t){var n,r=h(t);return e.style.display="none",n=document.elementFromPoint(r.x,r.y),e.style.display="",n},e.prototype.getElementXPath=function(e){if(!e)return null;if(e.id)return"//[@id="+e.id+"]"+(""!=e.className?"."+e.className.toLowerCase():"");if("BODY"===e.tagName)return"/html/body";var t=[].slice.call(e.parentElement.childNodes).filter((function(t){return t.nodeName===e.nodeName})),n=t.indexOf(e);return this.getElementXPath(e.parentNode)+"/"+e.tagName.toLowerCase()+(""!=e.className?"."+e.className.toLowerCase():"")+(t.length>1?"["+(n+1)+"]":"")},e.prototype.getTargetIdx=function(e){return e?parseInt(e.getAttribute("data-rowindex"),10):0},e.prototype.columnDrop=function(e){var t=this.parent;if("grouping"!==e.droppedElement.getAttribute("action")){var n=sf.base.closest(e.target,"tr"),r=void 0;if("true"!==e.droppedElement.querySelector("tr").getAttribute("single-dragrow")&&e.droppedElement.parentElement.id===t.element.id||"true"===e.droppedElement.querySelector("tr").getAttribute("single-dragrow")&&e.droppedElement.parentElement.id!==t.element.id)return this.removeTargetGridBorder(this.parent),void this.removeTargetGridBorder(this.destinationGrid);e.droppedElement.parentElement.id!==t.element.id&&(r=e.droppedElement.parentElement.blazor__instance);var i=r.content.querySelector(".e-dragstartrow");if(r.element.id!==t.element.id&&r.options.rowDropTarget!==t.element.id)return void(sf.base.isNullOrUndefined(i)||(i.classList.remove("e-dragstartrow"),t.element.getElementsByClassName("e-lastrow-dragborder").length>0&&this.removeTargetGridBorder(t)));var o=this.getTargetIdx(n);if(!sf.base.isNullOrUndefined(n)){var s=e.mouseEvent.clientY-n.getBoundingClientRect().top,l=n.offsetHeight/2,d=t.getContentTable().querySelector("tr:last-child");e.target&&!d.classList.contains("e-emptyrow")&&d&&n==d&&s>l&&(o=parseInt(d.getAttribute("aria-rowindex"),10))}isNaN(o)&&(o=0),t.options.allowPaging&&(o=o+t.options.currentPage*t.options.pageSize-t.options.pageSize);var u=this.getElementXPath(e.target),h=e.target.id,p=e.target.getBoundingClientRect();this.removeTargetGridBorder(this.parent);var c=0,f=!1;if(t.options.tValue==r.options.tValue){if(!sf.base.isNullOrUndefined(i)){var g=a(i,"e-row"),m="false"===g.getAttribute("aria-selected");c=m?parseInt(g.getAttribute("data-rowindex"),10):0,f=m,i.classList.remove("e-dragstartrow")}t.dotNetRef.invokeMethodAsync("ReorderRows",c,o,"add",!1,u,h,p,r.dotNetRef,!1,!1,null,null,f),r.dotNetRef.invokeMethodAsync("ReorderRows",c,o,"delete",!1,u,h,p,null,!1,!1,null,null,f)}else sf.base.isNullOrUndefined(i)||i.classList.remove("e-dragstartrow"),r.dotNetRef.invokeMethodAsync("ReorderRows",c,o,"delete",!1,u,h,p,null,!1,!0,null,null,f)}},e.prototype.removeTargetGridBorder=function(e){if(!sf.base.isNullOrUndefined(e)){if(e.element.getElementsByClassName("e-firstrow-dragborder").length>0&&e.element.getElementsByClassName("e-firstrow-dragborder")[0].remove(),e.element.getElementsByClassName("e-lastrow-dragborder").length>0)e.element.getElementsByClassName("e-lastrow-dragborder")[0].remove();else if(document.getElementsByClassName("e-lastrow-dragborder").length>0){var t=document.getElementsByClassName("e-lastrow-dragborder")[0];sf.base.isNullOrUndefined(a(t,"e-grid"))||t.remove()}sf.base.removeClass(e.element.querySelectorAll(".e-rowcell.e-dragborder,.e-detailrowcollapse.e-dragborder, .e-rowdragdrop.e-dragborder, e-detailrowexpand.e-dragborder"),["e-dragborder"])}},e.prototype.destroy=function(){var e=this.parent.element;e&&(e.querySelector(".e-gridheader")||e.querySelector(".e-gridcontent"))&&(sf.base.isNullOrUndefined(this.draggable)||this.draggable.destroy(),sf.base.isNullOrUndefined(this.droppable)||this.droppable.destroy())},e}(),M=function(){function e(e){this.parent=e,this.addEventListener()}return e.prototype.addEventListener=function(){sf.base.EventHandler.add(this.parent.getContent().parentElement,"mousedown",this.mouseDownHandler,this),this.parent.options.allowDragSelection&&sf.base.EventHandler.add(this.parent.getContent().parentElement,"touchstart",this.mouseDownHandler,this)},e.prototype.removeEventListener=function(){sf.base.EventHandler.remove(this.parent.getContent().parentElement,"mousedown",this.mouseDownHandler),this.parent.options.allowDragSelection&&sf.base.EventHandler.remove(this.parent.getContent().parentElement,"touchstart",this.mouseDownHandler)},e.prototype.mouseDownHandler=function(e){var t,n=e.target,r=this.parent,i=a(n,"e-grid");if(!(i&&i.id!==r.element.id||a(n,"e-headercontent")&&!this.parent.options.frozenRows)){if((e.shiftKey||e.ctrlKey)&&e.preventDefault(),a(n,"e-rowcell")&&!e.shiftKey&&!e.ctrlKey){if(!(r.options.cellSelectionMode.indexOf("Box")>-1||(r.options.allowDragSelection&&!r.options.enableAutoFill&&r.options.cellSelectionMode.indexOf("Flow"))>-1)||this.isRowType()||this.isSingleSel()?this.isCellDrag=!1:(this.isCellDrag=!0,t=!0),(r.options.allowRowDragAndDrop||r.options.allowDragSelection&&!r.options.enableAutoFill)&&!r.options.isEdit){if(!r.options.allowDragSelection&&(!this.isRowType()||sf.base.closest(n,"td").classList.contains("e-selectionbackground"))||this.isSingleSel())return r.options.allowDragSelection&&document.body.classList.add("e-disableuserselect"),void(this.isDragged=!1);t=!0,this.element=sf.base.createElement("div",{className:"e-griddragarea"}),this.element.style.left="0px",this.element.style.top="0px",this.element.style.zIndex="10",r.getContent().appendChild(this.element)}t&&(this.isAutoFillSel=!1,this.enableDrag(e,!0))}this.updateStartEndCells(),(n.classList.contains("e-autofill")||n.classList.contains("e-xlsel"))&&(this.isCellDrag=!0,this.isAutoFillSel=!0,this.enableDrag(e),document.body.style.cursor="crosshair")}},e.prototype.mouseUpHandler=function(e){if(document.body.classList.remove("e-disableuserselect"),sf.base.isNullOrUndefined(this.element)||sf.base.isNullOrUndefined(this.element.parentNode)||sf.base.remove(this.element),this.isCellDrag||sf.base.isNullOrUndefined(this.prevStartDIndex)&&sf.base.isNullOrUndefined(this.prevEndIndex)?!this.parent.options.allowDragSelection||this.parent.options.enableAutoFill||!this.isDragged||this.isAutoFillSel||sf.base.isNullOrUndefined(this.startDragIndex)||sf.base.isNullOrUndefined(this.endDragIndex)||sf.base.isNullOrUndefined(this.endDragCellIndex)||(this.isDragged=!1,this.parent.dotNetRef.invokeMethodAsync("DragCellSelection",this.startDragIndex,this.startDragCellIndex,this.endDragIndex,this.endDragCellIndex,!1)):this.parent.dotNetRef.invokeMethodAsync("DragSelection",this.prevStartDIndex,this.prevEndIndex,!1),this.isDragged=!1,document.body.style.cursor="default",this.prevStartDIndex=void 0,this.prevEndIndex=void 0,this.startDragIndex=void 0,this.startDragCellIndex=void 0,this.endDragIndex=void 0,this.endDragCellIndex=void 0,"Batch"==this.parent.options.editMode&&this.parent.options.enableAutoFill){if(!sf.base.isNullOrUndefined(this.endRowIndex)&&!sf.base.isNullOrUndefined(this.endColIndex)&&!this.isAutoFillSel&&this.isInitialSelect){this.parent.dotNetRef.invokeMethodAsync("ClearSelection");var t=this.updateAutofillPosition(this.endColIndex,this.endRowIndex,!0);this.parent.dotNetRef.invokeMethodAsync("UpdateAutofillPositions",t,"UpdateAutofillBox"),this.assignCells(),this.selectCellByRow(),this.isInitialSelect=!1}if(this.isAutoFillSel){var n=this;this.assignCells(),setTimeout((function(){n.selectCellByRow()}),10),this.expandAFBorder(e,!0);var r=this.createBorder(this.startRowIndex,this.startColIndex,this.endRowIndex,this.endColIndex,!0);this.parent.dotNetRef.invokeMethodAsync("UpdateAutofillPositions",r,"UpdateAutofillBorder");t=this.updateAutofillPosition(this.endColIndex,this.endRowIndex,!0);this.parent.dotNetRef.invokeMethodAsync("UpdateAutofillPositions",t,"UpdateAutofillBox")}}sf.base.EventHandler.remove(this.parent.getContent(),"mousemove",this.mouseMoveHandler),this.parent.options.allowDragSelection&&sf.base.EventHandler.remove(this.parent.getContent(),"touchmove",this.mouseMoveHandler),this.parent.options.frozenRows&&(sf.base.EventHandler.remove(this.parent.getHeaderContent(),"mousemove",this.mouseMoveHandler),this.parent.options.allowDragSelection&&sf.base.EventHandler.remove(this.parent.getHeaderContent(),"touchmove",this.mouseMoveHandler)),sf.base.EventHandler.remove(document.body,"mouseup",this.mouseUpHandler),this.parent.options.allowDragSelection&&sf.base.EventHandler.remove(document.body,"touchend",this.mouseUpHandler)},e.prototype.enableDrag=function(e,t){var n=this.parent;if(t){var r=sf.base.closest(e.target,"tr");this.startDragIndex=parseInt(r.getAttribute("data-rowindex"),10),this.startDragCellIndex=parseInt(a(e.target,"e-rowcell").getAttribute("data-colindex"),10)}document.body.classList.add("e-disableuserselect");var i=n.element.getBoundingClientRect(),o=h(e);this.x=o.x-i.left,this.y=o.y-i.top,sf.base.EventHandler.add(n.getContent(),"mousemove",this.mouseMoveHandler,this),this.parent.options.allowDragSelection&&sf.base.EventHandler.add(n.getContent(),"touchmove",this.mouseMoveHandler,this),this.parent.options.frozenRows&&(sf.base.EventHandler.add(n.getHeaderContent(),"mousemove",this.mouseMoveHandler,this),this.parent.options.allowDragSelection&&sf.base.EventHandler.add(n.getHeaderContent(),"touchmove",this.mouseMoveHandler,this)),sf.base.EventHandler.add(document.body,"mouseup",this.mouseUpHandler,this),this.parent.options.allowDragSelection&&sf.base.EventHandler.add(document.body,"touchend",this.mouseUpHandler,this)},e.prototype.mouseMoveHandler=function(e){this.parent.options.allowDragSelection||"touchmove"==e.type||e.preventDefault();var t,n,r=this.parent.element.getBoundingClientRect(),i=this.x,o=this.y,s=h(e),l=s.x-r.left,d=s.y-r.top,u="touchmove"==e.type?e.touches[0].pageX:e.pageX,p="touchmove"==e.type?e.touches[0].pageY:e.pageY,c=p+2;if(n=sf.base.isNullOrUndefined(e.touches)||sf.base.isNullOrUndefined(document.elementFromPoint(e.touches[0].clientX,e.touches[0].clientY))?sf.base.closest(e.target,"tr"):sf.base.closest(document.elementFromPoint(e.touches[0].clientX,e.touches[0].clientY),"tr"),this.isDragged=!0,!this.isCellDrag||this.parent.options.allowDragSelection&&!this.parent.options.enableAutoFill&&this.isCellDrag){n||(n=sf.base.closest(document.elementFromPoint(this.parent.element.offsetLeft+2,sf.base.isNullOrUndefined(e.touches)?e.clientY:e.touches[0].clientY),"tr")),i>l&&(t=l,l=i,i=t),o>d&&(t=d,d=o,o=t,c=p-2);for(var f=[".e-gridheader",".e-groupdroparea",".e-toolbar"],g=0,m=0;m<f.length;m++){var v=this.parent.element.querySelector(f[m]);v&&(g+=v.offsetHeight)}var b=this.parent.element.querySelector(".e-yscroll"),y=0;b&&(y+=b.scrollTop);var C=this.parent.element.querySelector(".e-content");this.element.style.left=i+C.scrollLeft+"px",this.element.style.top=o-g+y+"px",this.element.style.width=l-i+"px",this.element.style.height=d-o+"px"}if(n&&!e.ctrlKey&&!e.shiftKey){var w=parseInt(n.getAttribute("data-rowindex"),10);if(this.isCellDrag||!sf.base.isNullOrUndefined(this.prevStartDIndex)&&this.prevStartDIndex==this.startDragIndex&&this.prevEndIndex==w){if(this.parent.options.allowDragSelection&&!this.parent.options.enableAutoFill&&this.isCellDrag){var S;R=!1;(S=sf.base.isNullOrUndefined(e.touches)||sf.base.isNullOrUndefined(document.elementFromPoint(e.touches[0].clientX,e.touches[0].clientY))?sf.base.closest(e.target,"td"):sf.base.closest(document.elementFromPoint(e.touches[0].clientX,e.touches[0].clientY),"td"))||(S=document.elementFromPoint(u,c)),S&&(sf.base.isNullOrUndefined(this.prevStartDIndex)&&(R=!0),this.prevStartDIndex=this.startDragIndex,this.endDragIndex=w,this.endDragCellIndex=parseInt(S.getAttribute("data-colindex"),10),this.isAutoFillSel||(!R||isNaN(w)||isNaN(parseInt(S.getAttribute("data-colindex"),10))?this.performDragCellSelection(this.startDragIndex,this.startDragCellIndex,w,parseInt(S.getAttribute("data-colindex"),10)):this.parent.dotNetRef.invokeMethodAsync("DragCellSelection",this.startDragIndex,this.startDragCellIndex,w,parseInt(S.getAttribute("data-colindex"),10),!0)))}else if("Batch"==this.parent.options.editMode&&this.parent.options.enableAutoFill&&this.startCell){var x=a(e.target,"e-rowcell");if(x&&!x.classList.contains("e-editedbatchcell"))if(this.startAFCell=this.startCell,this.endAFCell=x,this.endCell=x,this.isAutoFillSel)this.expandAFBorder(e,!1);else{this.assignCells();var z=this.createBorder(this.startRowIndex,this.startColIndex,this.endRowIndex,this.endColIndex,!0);this.parent.dotNetRef.invokeMethodAsync("UpdateAutofillPositions",z,"UpdateAutofillBorder"),this.isInitialSelect=!0}}}else{var I=-1,R=!1,A=this.parent.getSelectedRowIndexes();sf.base.isNullOrUndefined(this.prevStartDIndex)?(I=-1,R=!0):(w>=this.prevStartDIndex&&A.indexOf(w)>=0||this.prevStartDIndex>w&&A.indexOf(this.startDragIndex)>=0)&&(I=this.prevEndIndex),this.prevStartDIndex=this.startDragIndex,this.prevEndIndex=w,R&&!isNaN(w)?this.parent.dotNetRef.invokeMethodAsync("DragSelection",this.startDragIndex,w,!0):this.performDragSelection(this.startDragIndex,w,I)}}},e.prototype.performDragCellSelection=function(e,t,n,r){var i=e,o=n;e>n&&(i=n,o=e),this.selectCellsByRange(i,o,t,r)},e.prototype.selectCellsByRange=function(e,t,n,r){for(var i,o,s=this.parent.getHeaderContent().querySelectorAll("tr.e-row[data-uid]"),l=this.parent.getContent().querySelectorAll("tr.e-row[data-uid]"),a=Array.from(s).concat(Array.from(l)),d=0;d<a.length;d++){var u=[].slice.call(a[d].querySelectorAll(".e-rowcell"));a[d].removeAttribute("aria-selected"),g.apply(void 0,[u,!1].concat(["e-aria-selected","e-active"]))}var h=[];if(e>t){var p=e;e=t,t=p}if(n>r){var c=n;n=r,r=c}for(d=e;d<=t;d++){this.parent.options.cellSelectionMode.indexOf("Box")<0?(i=d===e?n:0,o=d===t?r:this.getLastColIndex(d)):(i=n,o=r);for(var f=i<o?i:o,m=i>o?i:o;f<=m;f++)h.push(this.getCellIndex(d,f));g.apply(void 0,[h,!0].concat(["e-aria-selected","e-active"]))}},e.prototype.getCellIndex=function(e,t){var n=this.parent.getHeaderContent().querySelectorAll("tr.e-row[data-uid]"),r=this.parent.getContent().querySelectorAll("tr.e-row[data-uid]"),i=Array.from(n).concat(Array.from(r));return i[e]&&i[e].querySelectorAll(".e-rowcell")[t]},e.prototype.getLastColIndex=function(e){var t=this.parent.getDataRows()[e].querySelectorAll("td.e-rowcell");return parseInt(t[t.length-1].getAttribute("data-colindex"),10)},e.prototype.updateAutofillPosition=function(e,t,n){void 0===n&&(n=!1);var r=this.parent.getRowByIndex(t).querySelector('[data-colindex="'+e+'"]'),i=[].slice.call(this.parent.element.querySelectorAll(".e-cellselectionbackground")),o="",s="",l="";if(i&&!n&&(r=i[i.length-1]),r&&r.offsetParent){var a=r.getBoundingClientRect(),d=r.offsetParent.getBoundingClientRect(),u=this.isLastCell(r)?4:0,h=this.isLastRow(r)?3:0;this.parent.options.enableRtl?s=d.right-a.right+a.width-4-u+"px":o=a.left-d.left+a.width-4-u+"px",l=a.top-d.top+a.height-5-h+"px"}return{Left:o,Right:s,Top:l}},e.prototype.createBorder=function(e,t,n,r,i){void 0===n&&(n=null),void 0===r&&(r=null),void 0===i&&(i=!1);var o,s,l,a=[].slice.call(this.parent.element.querySelectorAll(".e-cellselectionbackground")),d=this.parent.getRowByIndex(e),u=d.querySelector('[data-colindex="'+t+'"]'),h=[].slice.call(u.parentElement.querySelectorAll('[data-colindex="'+t+'"]')),p="",c="",f="";null!=n&&null!=r?(o=this.parent.getRowByIndex(n).querySelector('[data-colindex="'+r+'"]'),s=[].slice.call(o.parentElement.querySelectorAll('[data-colindex="'+r+'"]'))):(d,o=u,s=h),a&&!i&&(h=[].slice.call(a[0].parentElement.querySelectorAll('[data-colindex="'+(a[0].cellIndex-this.parent.getIndentCount())+'"]')),s=[].slice.call(a[a.length-1].parentElement.querySelectorAll('[data-colindex="'+(a[a.length-1].cellIndex-this.parent.getIndentCount())+'"]'))),this.startCell||(this.startCell=h[0]),this.endCells=s[0];var g=h[0],m=s[0],v=g.getBoundingClientRect(),b=m.getBoundingClientRect(),y=g.offsetParent.getBoundingClientRect(),C=this.isLastRow(m)?2:0,w=this.parent.options.frozenRows&&this.isFirstRow(g)?1.5:0,S=this.parent.options.frozenColumns&&this.isFirstCell(g)?1:0;return this.parent.options.enableRtl?(p=y.right-v.right-S+"px",f=v.right-b.left+S+1+"px"):(c=v.left-y.left-S+"px",f=b.right-v.left+S+1+"px"),l=v.top-y.top-w+"px","2px",{Right:p,Width:f,BorderWidth:"2px",Left:c,Height:b.top-v.top>0?b.top-y.top+b.height+1-(v.top-y.top)-C+w+"px":b.height+w-C+1+"px",Top:l}},e.prototype.expandAFBorder=function(e,t){for(var n=[].slice.call(this.parent.element.querySelectorAll(".e-cellselectionbackground")),r=parseInt(a(this.startCell,"e-row").getAttribute("data-rowindex"),10),i=parseInt(this.startCell.getAttribute("data-colindex"),10),o=parseInt(a(this.endCell,"e-row").getAttribute("data-rowindex"),10),s=parseInt(this.endCell.getAttribute("data-colindex"),10),l=parseInt(a(n[n.length-1],"e-row").getAttribute("data-rowindex"),10)-parseInt(a(n[0],"e-row").getAttribute("data-rowindex"),10),d=parseInt(a(n[0],"e-row").getAttribute("data-rowindex"),10),u=this.parent.getRowByIndex(d),h=0,p=0,c=u.cells.length;p<c;p++)u.cells[p].classList.contains("e-cellselectionbackground")&&h++;switch(h=(h-=1)>=0?h:0,!0){case!t&&this.endAFCell.classList.contains("e-cellselectionbackground")&&!!a(e.target,"e-rowcell"):this.startAFCell=this.parent.getCellFromIndex(r,i),this.endAFCell=this.parent.getCellFromIndex(r+l,i+h),this.drawAFBorders();break;case i+h<s&&s-i-h+1>o-r-l&&s-i-h+1>r-o:if(this.endAFCell=this.parent.getCellFromIndex(r+l,s),o=parseInt(a(this.endAFCell,"e-row").getAttribute("data-rowindex"),10),s=parseInt(this.endAFCell.getAttribute("data-colindex"),10),t){var f=parseInt(this.endCells.getAttribute("data-colindex"),10);for(p=r;p<=o;p++)for(var g=this.getAutoFillCells(p,i),m=0,v=f+1;v<=s;v++)m>h&&(m=0),this.updateValue(p,v,g[m]),m++}else this.drawAFBorders();break;case i>s&&i-s+1>o-r-l&&i-s+1>r-o:if(this.startAFCell=this.parent.getCellFromIndex(r,s),this.endAFCell=this.endCells,t)for(p=r;p<=r+l;p++){(g=this.getAutoFillCells(p,i)).reverse();for(m=0,v=this.startCellIndex-1;v>=s;v--)m>h&&(m=0),this.updateValue(p,v,g[m]),m++}else this.drawAFBorders();break;case r>o:if(this.startAFCell=this.parent.getCellFromIndex(o,i),this.endAFCell=this.endCells,t){var b=y=parseInt(this.endCells.parentElement.getAttribute("data-rowindex"),10);for(p=r-1;p>=o;p--){b===this.startIndex-1&&(b=y);g=this.getAutoFillCells(b,i),m=0;b--;for(v=this.startCellIndex;v<=this.startCellIndex+h;v++)this.updateValue(p,v,g[m]),m++}}else this.drawAFBorders();break;default:if(this.endAFCell=this.parent.getCellFromIndex(o,i+h),t){var y=parseInt(this.endCells.parentElement.getAttribute("data-rowindex"),10);for(b=this.startIndex,p=y+1;p<=o;p++){b===y+1&&(b=this.startIndex);g=this.getAutoFillCells(b,i);b++;m=0;for(var C=this.startCellIndex;C<=this.startCellIndex+h;C++)this.updateValue(p,C,g[m]),m++}}else this.drawAFBorders()}},e.prototype.drawAFBorders=function(){if(this.startCell){var e=this.startAFCell.getBoundingClientRect(),t=this.endAFCell.getBoundingClientRect(),n=t.top-e.top>0?1:0,r=t.top-e.top>=0&&(a(this.startAFCell,"e-movablecontent")||a(this.startAFCell,"e-frozencontent"))&&this.isFirstRow(this.startAFCell)?1.5:0,i=(a(this.startAFCell,"e-movablecontent")||a(this.startAFCell,"e-movableheader"))&&this.isFirstCell(this.startAFCell)?1:0,o=this.isLastRow(this.endAFCell)&&(a(this.endAFCell,"e-movablecontent")||a(this.endAFCell,"e-frozencontent"))?2:1,s=this.startAFCell.offsetParent.getBoundingClientRect(),l=this.parent.element.getBoundingClientRect(),d=this.startAFCell.offsetParent.parentElement.scrollTop,u=this.startAFCell.offsetParent.parentElement.scrollLeft,h=d-this.startAFCell.offsetTop,p=u-this.startAFCell.offsetLeft;h=h>0?Math.floor(h)-1:0,p=p>0?p:0;var c,f,g,m=e.left-l.left,v="",b="",y="",C="",w="",S="";if(this.parent.options.enableRtl){var x=a(this.startAFCell,"e-movablecontent")||a(this.startAFCell,"e-movableheader")?e.right-this.startAFCell.offsetParent.parentElement.getBoundingClientRect().width-l.left:0;b=l.right-t.right-2+t.width+"px",C=l.right-e.right-i+x-1+"px",w=t.left-l.left-.5+"px",S=parseInt(b,10)-parseInt(C,10)-i+1+"px"}else v=m-i+p-1+u+"px",y=t.left-l.left-2+t.width+u+"px",w=m+p-.5+u+"px",S=parseInt(y,10)-parseInt(v,10)-i+1+"px";g=f=e.top-s.top-r+"px";var z={BorderLeftAutofillLeft:v,BorderLeftAutofillTop:f,BorderLeftAutofillHeight:c=t.top-e.top>0?t.top-s.top+t.height+1-(e.top-s.top)+r-o-h+"px":t.height+r-o-h+"px",BorderLeftAutofillRight:b,BorderRightAutofillLeft:y,BorderRightAutofillHeight:parseInt(c,10)+"px",BorderRightAutofillRight:C,BorderRightAutofillTop:g,BorderTopAutofillLeft:w,BorderTopAutofillTop:g,BorderTopAutofillWidth:S,BorderBottomAutofillLeft:w,BorderBottomAutofillTop:parseFloat(f)+parseFloat(c)-n-1+"px",BorderBottomAutofillWidth:S};this.parent.dotNetRef.invokeMethodAsync("UpdateAutofillPositions",z,"UpdateAutofillPosition")}},e.prototype.updateValue=function(e,t,n){var r=this.parent.getColumnByIndex(t),i=parseInt(a(n,"e-row").getAttribute("data-rowindex"),10),o=this.parent.getColumnByIndex(n.cellIndex-this.parent.getIndentCount()),s=n.innerText;this.parent.dotNetRef.invokeMethodAsync("UpdateAutofillCell",e,r.field,o.field,i,s)},e.prototype.getAutoFillCells=function(e,t){return[].slice.call(this.parent.getDataRows()[e].querySelectorAll(".e-cellselectionbackground"))},e.prototype.updateStartEndCells=function(){var e=[].slice.call(this.parent.element.querySelectorAll(".e-cellselectionbackground"));this.startCell=e[0],this.endCell=e[e.length-1],this.startCell&&(this.startIndex=parseInt(this.startCell.parentElement.getAttribute("data-rowindex"),10),this.startCellIndex=parseInt(a(this.startCell,"e-rowcell").getAttribute("data-colindex"),10))},e.prototype.assignCells=function(){this.startRowIndex=parseInt(this.startAFCell.parentElement.getAttribute("data-rowindex"),10),this.endRowIndex=parseInt(this.endAFCell.parentElement.getAttribute("data-rowindex"),10),this.startColIndex=parseInt(this.startAFCell.getAttribute("data-colindex"),10),this.endColIndex=parseInt(this.endAFCell.getAttribute("data-colindex"),10),this.startRowIndex>this.endRowIndex&&(this.startRowIndex=this.endRowIndex,this.endRowIndex=parseInt(this.startAFCell.parentElement.getAttribute("data-rowindex"),10)),this.endColIndex<this.startColIndex&&(this.startColIndex=this.endColIndex,this.endColIndex=parseInt(this.startAFCell.getAttribute("data-colindex"),10))},e.prototype.selectCellByRow=function(){for(var e=this.startRowIndex;e<=this.endRowIndex;e++)for(var t=this.startColIndex;t<=this.endColIndex;t++)this.parent.dotNetRef.invokeMethodAsync("SelectCellByRow",e,t)},e.prototype.isLastCell=function(e){var t=[].slice.call(e.parentElement.querySelectorAll(".e-rowcell:not(.e-hide)"));return t[t.length-1]==e},e.prototype.isLastRow=function(e){var t=[].slice.call(sf.base.closest(e,"tbody").querySelectorAll(".e-row:not(.e-hiddenrow)"));return t[t.length-1]==e.parentElement},e.prototype.isFirstRow=function(e){var t=[].slice.call(sf.base.closest(e,"tbody").querySelectorAll(".e-row:not(.e-hiddenrow)"));return e.parentElement===t[0]},e.prototype.isFirstCell=function(e){return[].slice.call(e.parentElement.querySelectorAll(".e-rowcell:not(.e-hide)"))[0]===e},e.prototype.performDragSelection=function(e,t,n){var r=e,i=t;e>t&&(r=t,i=e),-1!=n&&this.clearSelectionExceptDragIndexes(r,i),this.selectRangeOfRows(r,i)},e.prototype.selectRangeOfRows=function(e,t){for(var n=this.parent.getHeaderContent().querySelectorAll("tr.e-row[data-uid]"),r=this.parent.getContent().querySelectorAll("tr.e-row[data-uid]"),i=Array.from(n).concat(Array.from(r)),o=function(e){var t=s.parent.options.enableVirtualization?i.filter((function(t){return parseInt(t.getAttribute("data-rowindex"),10)==e}))[0]:i[e];s.selectRow(t,!1)},s=this,l=e;l<=t;l++)o(l)},e.prototype.selectRow=function(e,t){if(!sf.base.isNullOrUndefined(e)){e.setAttribute("aria-selected","true");var n=[].slice.call(e.querySelectorAll(".e-rowcell"));this.parent.options.allowRowDragAndDrop&&this.parent.options.rowDropTarget&&(n=[].slice.call(e.querySelectorAll(".e-rowdragdrop")).concat(n)),g.apply(void 0,[n,!0].concat(["e-aria-selected","e-active"])),t||n[0].classList.contains("e-gridchkbox")&&n[0].querySelector(".e-frame").classList.replace("e-uncheck","e-check")}},e.prototype.clearSelectionByRow=function(e){var t=[].slice.call(e.querySelectorAll(".e-rowcell"));this.parent.options.allowRowDragAndDrop&&this.parent.options.rowDropTarget&&(t=[].slice.call(e.querySelectorAll(".e-rowdragdrop")).concat(t)),e.removeAttribute("aria-selected"),g.apply(void 0,[t,!1].concat(["e-aria-selected","e-active"])),t[0].classList.contains("e-gridchkbox")&&t[0].querySelector(".e-frame").classList.replace("e-check","e-uncheck")},e.prototype.clearSelectionExceptDragIndexes=function(e,t){for(var n=this.parent.getRows(),r=0;r<n.length;r++){var i=this.parent.options.enableVirtualization?parseInt(n[r].getAttribute("data-rowindex"),10):r;if(i<e||i>t){var o=this.parent.options.enableVirtualization?n.filter((function(e){return parseInt(e.getAttribute("data-rowindex"),10)==i}))[0]:n[i];this.clearSelectionByRow(o)}}},e.prototype.isRowType=function(){return"Row"===this.parent.options.selectionMode||"Both"===this.parent.options.selectionMode},e.prototype.isSingleSel=function(){return"Single"===this.parent.options.selectionType},e}(),k=function(){function e(e,t){var n=this;this.fromWheel=!1,this.touchMove=!1,this.options={},this.sentinelInfo={up:{check:function(e,t){var r=e.top-n.containerRect.top;return t.entered=r>=0,r+n.options.pageHeight/2>=0},axis:"Y"},down:{check:function(e,t){n.options.container.clientHeight;var r=e.bottom;return t.entered=e.bottom<=n.containerRect.bottom,r-n.containerRect.top-n.options.pageHeight/2<=n.options.pageHeight/2},axis:"Y"},right:{check:function(e,t){var r=e.right;return t.entered=r<n.containerRect.right,r-n.containerRect.width<=n.containerRect.right},axis:"X"},left:{check:function(e,t){var r=e.left;return t.entered=r>0,r+n.containerRect.width>=n.containerRect.left},axis:"X"}},this.element=e,this.options=t}return e.prototype.observe=function(e,t){var n=this;this.options.virtualScrollHandler=this.virtualScrollHandler(e,t),this.containerRect=this.options.container.getBoundingClientRect(),sf.base.EventHandler.add(this.options.container,"wheel",(function(){return n.fromWheel=!0}),this),sf.base.EventHandler.add(this.options.container,"scroll",this.options.virtualScrollHandler,this),sf.base.isNullOrUndefined(a(this.element,"e-gridcontent"))||sf.base.isNullOrUndefined(a(this.element,"e-gridcontent").querySelector(".e-movablescrollbar"))||sf.base.EventHandler.add(a(this.element,"e-gridcontent").querySelector(".e-movablescrollbar"),"scroll",this.options.virtualScrollHandler,this)},e.prototype.disconnect=function(){var e=this;this.containerRect=this.options.container.getBoundingClientRect(),sf.base.EventHandler.remove(this.options.container,"wheel",(function(){return e.fromWheel=!0})),sf.base.EventHandler.remove(this.options.container,"scroll",this.options.virtualScrollHandler),sf.base.isNullOrUndefined(a(this.element,"e-gridcontent"))||sf.base.isNullOrUndefined(a(this.element,"e-gridcontent").querySelector(".e-movablescrollbar"))||sf.base.EventHandler.remove(a(this.element,"e-gridcontent").querySelector(".e-movablescrollbar"),"scroll",this.options.virtualScrollHandler)},e.prototype.check=function(e){var t=this.sentinelInfo[e];return t.check(this.element.getBoundingClientRect(),t)},e.prototype.virtualScrollHandler=function(e,t){var n=this,r="chrome"===sf.base.Browser.info.name?200:100,i=0,o=0,s=sf.base.debounce(e,r),l=sf.base.debounce(e,50);return function(e){var r=e.target.scrollTop,a=e.target.scrollLeft,d=i<r?"down":"up",u=e.target.classList.contains("e-movablescrollbar"),h=!0,p=n.options.container.querySelectorAll(".e-masked-row").length>0;d=o===a?d:o<a?"right":"left",n.PreventAdjustTable=u||i==r&&a==o?"horizontal":d,u&&(d=o<a?"right":"left"),"down"==d?h=!!(p&&n.options.totalItems<100)||r-i<30:"up"==d?h=i-r<30:"right"==d?h=a-o<20:"left"==d&&(h=o-a<20),u||(i=r),o=a;var c=n.sentinelInfo[d];if(-1!==n.options.axes.indexOf(c.axis)){var f=n.check(d),g=n.options.container.querySelectorAll(".e-masked-row").length&&"X"!=c.axis,m=n.options.overscanCount>0&&"X"!=c.axis;if((c.entered||(sf.base.isNullOrUndefined(n.blazorActiveKey)||""==n.blazorActiveKey)&&!n.fromWheel&&g)&&t(n.element,c,h,d,{top:r,left:a},n.fromWheel,f),f||g||m){var v=s;"X"===c.axis&&(v=l),v({direction:d,sentinel:c,offset:{top:r,left:a},focusElement:document.activeElement,isWheelScroll:n.fromWheel})}g||(n.fromWheel=!1)}}},e.prototype.setPageHeight=function(e){this.options.pageHeight=e},e}(),D=function(){function e(e){var t=this;this.prevHeight=0,this.preStartIndex=0,this.preventEvent=!1,this.actions=["filtering","clearfiltering","searching","grouping","ungrouping","Filtering","ClearFiltering","Searching","Grouping","Ungrouping","UnGrouping"],this.offsets={},this.tmpOffsets={},this.offsetKeys=[],this.currentInfo={},this.nextRowToNavigate=0,this.startIndex=0,this.selectedCellNavigation=-1,this.selectedRowNavigation=-1,this.selectedRowIndex=-1,this.focusColumnIndex=-1,this.isScrollIntoview=!1,this.scrollInfo={},this.bindScrollEvent=function(){t.observer.observe((function(e){(t.parent.options.enableVirtualization||t.parent.options.enableColumnVirtualization)&&t.scrollListener(e)}),t.onEntered());var e=t.parent;if(e.options.enablePersistence&&e.scrollPosition){t.content.scrollTop=e.scrollPosition.top;var n={direction:"down",sentinel:t.observer.sentinelInfo.down,offset:e.scrollPosition,focusElement:e.element,isWheelScroll:t.observer.options.isWheelScroll};t.scrollListener(n),e.options.enableColumnVirtualization&&(t.content.scrollLeft=e.scrollPosition.left)}},this.parent=e,this.contentPanel=this.parent.element.querySelector(".e-gridcontent"),this.vHelper=new P(e),this.virtualEle=new W(e),this.addEventListener()}return e.prototype.getPanel=function(){return this.contentPanel},e.prototype.getTable=function(){return this.contentPanel.querySelector(".e-table")},e.prototype.renderTable=function(){this.header=this.parent.virtualHeaderModule,this.virtualEle.table=this.getTable(),this.virtualEle.content=this.content=this.getPanel().querySelector(".e-content"),this.virtualEle.renderWrapper(Number(this.parent.options.height)),this.virtualEle.renderPlaceHolder();var e={container:this.content,pageHeight:2*this.getBlockHeight(),debounceEvent:!0,axes:this.parent.options.enableColumnVirtualization?["X","Y"]:["Y"],totalItems:this.parent.options.totalItemCount,overscanCount:this.parent.options.overscanCount};this.observer=new k(this.virtualEle.wrapper,e),this.parent.dotNetRef.invokeMethodAsync("SetRowHeight",this.parent.getRowHeight())},e.prototype.addEventListener=function(){sf.base.EventHandler.add(this.parent.element,"keydown",this.keyDownHandler,this)},e.prototype.removeEventListener=function(){sf.base.isNullOrUndefined(this.observer)||this.observer.disconnect(),this.getPanel().firstElementChild.scrollTop=0,this.getPanel().firstElementChild.scrollLeft=0,sf.base.EventHandler.remove(this.parent.element,"keydown",this.keyDownHandler)},e.prototype.handleScrollEnd=function(e,t,n,r){n||r||this.parent.dotNetRef.invokeMethodAsync("VirtualRefresh",e,t,this.selectedRowIndex,this.isScrollIntoview,this.focusColumnIndex),r&&(this.isScrollByNavigation=!1)},e.prototype.ensurePageSize=function(){var e=this.parent.getRowHeight(),t=2*~~((this.parent.options.height.toString().indexOf("%")<0?this.content.getBoundingClientRect().height:this.parent.element.getBoundingClientRect().height)/e),n=this.parent.options.pageSize,r=n<t&&0==this.parent.options.overscanCount?t:n;this.parent.dotNetRef.invokeMethodAsync("SetPageSizeAndCIndex",{pageSize:r,startColumnIndex:this.startColIndex,endColumnIndex:this.endColIndex,VTableWidth:this.getColumnOffset(this.endColIndex)-this.getColumnOffset(this.startColIndex-1)+""}),this.parent.options.pageSize=r,this.observer.options.pageHeight=2*this.getBlockHeight()},e.prototype.scrollListener=function(e){var t=this;if(this.parent.options.enablePersistence&&(this.parent.scrollPosition=e.offset),this.preventEvent)this.preventEvent=!1;else{var n=e.sentinel,r=this.preStartIndex,i=this.parent.getColumnIndexesInView(),o=this.currentInfo=this.getInfoFromView(e.direction,n,e.offset),s=this.parent.options.enableColumnVirtualization&&this.parent.options.enableRtl?-1*this.getColumnOffset(o.columnIndexes[0]-1,!0):this.getColumnOffset(o.columnIndexes[0]-1,!0);if(this.parent.options.enableColumnVirtualization&&JSON.stringify(i)!==JSON.stringify(o.columnIndexes)){for(var l=this.parent.options.virtualizedColumns,a=0;a<l.length;a++)l[a].autoFit&&this.parent.resizeModule.autoFitColumns(l[a].field);var d=this.parent.options.enableColumnVirtualization&&this.parent.options.enableRtl?-1*this.getColumnOffset(this.startColIndex-1,!0):this.getColumnOffset(this.startColIndex-1,!0),u=this.parent.options.enableColumnVirtualization&&this.parent.options.enableRtl?-1*this.getColumnOffset(this.endColIndex,!0)+d+"":this.getColumnOffset(this.endColIndex,!0)-d+"",h=sf.base.isNullOrUndefined(o.endIndex)?0:(o.endIndex-this.parent.options.pageSize)*(this.parent.options.rowHeight?this.parent.options.rowHeight:this.parent.getRowHeight());this.movableTranslateY=sf.base.isNullOrUndefined(this.movableTranslateY)?0:this.movableTranslateY;var p={requestType:"virtualscroll",isHeaderNavigated:t.isHeaderNavigated,selectedRowNavigation:t.selectedRowNavigation,selectedCellNavigation:t.selectedCellNavigation,isScrollByFocus:t.isScrollByFocus,startColumnIndex:o.columnIndexes[0],endColumnIndex:o.columnIndexes[o.columnIndexes.length-1],axis:"X",VTablewidth:u,translateX:s,translateY:t.parent.options.enableVirtualMaskRow&&t.parent.options.enableVirtualization?t.parent.options.frozenColumns?t.movableTranslateY:t.translateMaskY+t.movableTranslateY:0};clearTimeout(this.scrollTimer),this.scrollTimer=setTimeout((function(){t.handleScrollEnd(p,h,e.isWheelScroll,!1)}),100),setTimeout((function(){t.parent.dotNetRef.invokeMethodAsync("RemoveValidationPopup"),e.isWheelScroll&&t.parent.dotNetRef.invokeMethodAsync("VirtualRefresh",p,h,t.selectedRowIndex,t.isScrollIntoview,t.focusColumnIndex),t.isScrollByFocus=!1}),0)}else"left"!==this.currentInfo.direction&&"right"!==this.currentInfo.direction||!this.parent.options.enableVirtualMaskRow||this.virtualEle.adjustTable(s,this.translateMaskY);if(this.parent.setColumnIndexesInView(this.parent.options.enableColumnVirtualization?o.columnIndexes:[]),this.isScrollByNavigation?(clearTimeout(this.scrollTimer),this.scrollTimer=setTimeout((function(){t.handleScrollEnd(null,0,null,!0)}),100)):this.nextRowToNavigate=0,this.preStartIndex!==r&&this.parent.options.enableVirtualization){this.parent.options.currentPage=o.currentPage;var c=t.isScrollIntoview,f=this.nextRowToNavigate;setTimeout((function(){t.parent.dotNetRef.invokeMethodAsync("VirtualRefresh",{requestType:"virtualscroll",nextRowToNavigate:f,virtualStartIndex:o.endIndex-t.parent.options.pageSize,virtualEndIndex:o.endIndex,axis:"Y",RHeight:t.parent.getRowHeight()},(o.endIndex-t.parent.options.pageSize)*(t.parent.options.rowHeight?t.parent.options.rowHeight:t.parent.getRowHeight()),t.selectedRowIndex,c,t.focusColumnIndex)}),0),t.isScrollIntoview=!1}else t.selectedRowIndex>=0&&this.preStartIndex===r&&t.isScrollIntoview&&("up"===this.currentInfo.direction||"down"===this.currentInfo.direction)&&(t.parent.dotNetRef.invokeMethodAsync("SelectRow",t.selectedRowIndex,t.isScrollIntoview,t.focusColumnIndex),t.selectedRowIndex=-1,t.isScrollIntoview=!1);this.prevInfo=o}},e.prototype.setColVTableWidthAndTranslate=function(e){if(this.parent.options.enableColumnVirtualization&&this.prevInfo&&JSON.stringify(this.currentInfo.columnIndexes)!==JSON.stringify(this.prevInfo.columnIndexes)||e&&e.refresh){var t=this.getColumnOffset(this.startColIndex-1),n=this.getColumnOffset(this.endColIndex)-t+"";if(0===this.parent.options.frozenColumns&&(this.header.virtualEle.setWrapperWidth(n),this.virtualEle.setWrapperWidth(n),this.parent.getContentTable().parentElement.style.width=n+"px"),this.header.virtualEle.adjustTable(this.movableTranslateX,0),this.parent.options.enableColumnVirtualization&&"X"===e.axis)if(this.parent.options.enableVirtualization){var r=this.virtualEle.extractTranslateY(this.virtualEle.wrapper.style.transform);this.virtualEle.adjustTable(this.movableTranslateX,parseInt(r.replace("px","")))}else this.virtualEle.adjustTable(this.movableTranslateX,0)}},e.prototype.refreshOnDataChange=function(){this.getPanel().firstElementChild.scrollTop=0,this.getPanel().firstElementChild.scrollLeft=0,this.parent.options.enableColumnVirtualization&&this.header.virtualEle.adjustTable(0,0),this.virtualEle.adjustTable(0,0),this.refreshOffsets(),this.refreshVirtualElement()},e.prototype.keyDownHandler=function(e){sf.base.isNullOrUndefined(this.observer)||(this.observer.blazorActiveKey="ArrowDown"===e.key||"ArrowUp"===e.key?e.key:"")},e.prototype.focusCell=function(e,t,n){var r=this.parent.getRowHeight(),i=this.parent.getContent(),s=e.getBoundingClientRect(),l=i.getBoundingClientRect();"MoveRightCell"==t?-1==this.selectedCellNavigation?this.selectedCellNavigation=Number(e.getAttribute("data-colindex")):this.selectedCellNavigation+1==Number(e.getAttribute("data-colindex"))&&this.selectedCellNavigation++:"MoveLeftCell"==t&&(-1==this.selectedCellNavigation?this.selectedCellNavigation=Number(e.getAttribute("data-colindex")):this.selectedCellNavigation-1==Number(e.getAttribute("data-colindex"))&&this.selectedCellNavigation--),"MoveDownCell"==t||"MoveUpCell"==t?e.focus({preventScroll:!0}):"MoveRightCell"!=t&&"MoveLeftCell"!=t||(sf.base.isNullOrUndefined(a(e,"e-row"))?this.isHeaderNavigated=!0:(this.isHeaderNavigated=!1,this.selectedRowNavigation=Number(a(e,"e-row").getAttribute("data-rowindex"))),this.isScrollByFocus=!0,e.focus()),"MoveDownCell"==t&&s.bottom>l.top+l.height-o()?(this.isScrollByNavigation=!0,i.scrollTop=i.scrollTop+r):"MoveUpCell"==t&&s.bottom<l.top+r&&(this.isScrollFromFocus=!0,this.isScrollByNavigation=!0,i.scrollTop=i.scrollTop-r),("AltW"==n||"CtrlHome"==n||"CtrlEnd"==n||"Home"==n||"End"==n||null==n&&null==t)&&e.focus()},e.prototype.getInfoFromView=function(e,t,n){var r={direction:e,sentinelInfo:t,offsets:n,startIndex:this.preStartIndex,endIndex:this.preEndIndex},i=this.parent.options.height.toString().indexOf("%")<0?this.content.getBoundingClientRect().height:this.parent.element.getBoundingClientRect().height;r.page=this.getPageFromTop(n.top+i,r),r.blockIndexes=this.vHelper.getBlockIndexes(r.page),r.columnIndexes="X"===t.axis?this.vHelper.getColumnIndexes():this.parent.getColumnIndexesInView();var o=this.parent.getRowHeight(),s=n.top/o,l=i/o,a=s+l,d=this.parent.options.pageSize/4,u=this.parent.options.groupCount?this.getVisibleGroupedRowCount():this.count;if("down"!==r.direction||this.isScrollFromFocus){if("up"===r.direction&&(r.startIndex&&r.endIndex||this.parent.options.enableVirtualMaskRow)){var h=Math.round((r.startIndex*o+d*o)/o);if(this.parent.options.enableVirtualMaskRow)if(l=Math.ceil(l),s<h||Math.ceil(s)>this.preStartIndex){var p=Math.ceil(s)>0?Math.ceil(s):0,c=u-this.parent.options.pageSize-this.parent.options.pageSize/2,f=u-this.parent.options.pageSize;r.startIndex=s<u&&c<=s&&!(f<=a&&a<=u)?p>0?p-this.parent.options.pageSize/2:0:p>0?p+this.parent.options.pageSize>u?u-this.parent.options.pageSize:p:0,r.startIndex=r.startIndex>0?r.startIndex-1:r.startIndex;v=r.startIndex+this.parent.options.pageSize;r.endIndex=r.startIndex<=0?this.parent.options.pageSize:v<u?v:u,r.startIndex=r.startIndex<0||this.parent.options.totalItemCount<=this.parent.options.pageSize?0:r.startIndex,r.currentPage=Math.ceil(r.startIndex/this.parent.options.pageSize),this.isScrollFromFocus=!1,this.preStartIndex=this.startIndex=r.startIndex,this.preEndIndex=r.endIndex}else this.isScrollFromFocus=!1,this.preStartIndex=this.preStartIndex,this.preEndIndex=this.preEndIndex;if(s<h&&!this.parent.options.enableVirtualMaskRow){var g=d>l?d:l+l/4;v=Math.round(s+g);r.endIndex=v<u?v:u;m=r.endIndex-this.parent.options.pageSize;r.startIndex=m>0?m:0,r.endIndex=m<0?this.parent.options.pageSize:r.endIndex,r.currentPage=Math.ceil(r.startIndex/this.parent.options.pageSize)}b=this.parent.getSelectedRowIndexes(!0);this.nextRowToNavigate=b.length>0?b[0]+1:-1}}else{var m=Math.round(a)-Math.round(d);if(this.parent.options.enableVirtualMaskRow)if(l=Math.ceil(l)-1,Math.ceil(s)-this.preStartIndex>=l){r.startIndex=Math.ceil(s)>=0?Math.ceil(s):0;var v=r.startIndex+this.parent.options.pageSize;r.endIndex=v<u?v:u,r.startIndex=v>=u?r.endIndex-this.parent.options.pageSize<0?0:r.endIndex-this.parent.options.pageSize:r.startIndex,r.currentPage=Math.ceil(r.endIndex/this.parent.options.pageSize),this.isScrollFromFocus=!1,this.preStartIndex=this.startIndex=r.startIndex,this.preEndIndex=r.endIndex}else this.isScrollFromFocus=!1,this.preStartIndex=this.preStartIndex,this.preEndIndex=this.preEndIndex;if(!this.parent.options.enableVirtualMaskRow&&sf.base.isNullOrUndefined(r.startIndex)||a>r.startIndex+Math.round(this.parent.options.pageSize/2+d)&&r.endIndex!==u){r.startIndex=m>=0?Math.round(m):0,r.startIndex=r.startIndex>s?Math.floor(s):r.startIndex;var v=r.startIndex+this.parent.options.pageSize;r.startIndex=v<a?Math.ceil(a)-this.parent.options.pageSize:r.startIndex,r.endIndex=v<u?v:u,r.startIndex=v>=u?r.endIndex-this.parent.options.pageSize>0?r.endIndex-this.parent.options.pageSize:0:r.startIndex,r.currentPage=Math.ceil(r.endIndex/this.parent.options.pageSize)}var b=this.parent.getSelectedRowIndexes(!0);this.nextRowToNavigate=b.length>0?b[b.length-1]-1:-1}return this.parent.options.enableVirtualMaskRow||(this.isScrollFromFocus=!1,this.preStartIndex=this.startIndex=r.startIndex,this.preEndIndex=r.endIndex),r},e.prototype.onDataReady=function(){var e=this,t=this;if(this.observer.options.totalItems=this.parent.options.totalItemCount,this.bindScrollEvent(),this.count=this.parent.options.totalItemCount,this.maxPage=Math.ceil(this.count/this.parent.options.pageSize),["Refresh","Filtering","ClearFiltering","Searching","Grouping","UnGrouping","Reorder","RowDragAndDrop","refresh","filtering","clearfiltering","searching","grouping","ungrouping","reorder","GroupExpandCollapse","InfiniteScrolling",null].some((function(t){return e.parent.options.requestType===t}))&&this.refreshOffsets(),this.setVirtualHeight(),this.parent.scrollModule.refresh(),this.resetScrollPosition(this.parent.options.requestType),this.setColVTableWidthAndTranslate(),(this.parent.options.enableVirtualMaskRow&&this.parent.options.enableVirtualization||this.parent.options.enableLazyLoading)&&"GroupExpandCollapse"!=this.parent.options.requestType){var n=this.parent.options.rowHeight,r=(this.currentInfo.endIndex-this.parent.options.pageSize)*(n||this.parent.getRowHeight());r="NaN"==r.toString()?0:r,this.parent.options.overscanCount>0&&this.currentInfo.startIndex>this.parent.options.overscanCount&&this.currentInfo.endIndex!=this.parent.options.totalItemCount?r-=(this.parent.options.pageSize+this.parent.options.overscanCount)*(n||this.parent.getRowHeight()):r-=this.parent.options.pageSize*(n||this.parent.getRowHeight()),0==this.count&&(r=0),sf.base.isNullOrUndefined(this.currentInfo.startIndex)||0==this.currentInfo.startIndex||sf.base.isNullOrUndefined(this.currentInfo.endIndex)||this.currentInfo.endIndex===this.parent.options.totalItemCount||(r-=20),(this.parent.options.overscanCount>0&&(this.parent.options.totalItemCount!==this.currentInfo.endIndex||"up"==this.currentInfo.direction)||0==this.parent.options.overscanCount)&&setTimeout((function(){t.translateMaskY=r,t.translateMaskX=sf.base.isNullOrUndefined(t.translateMaskX)?0:t.translateMaskX,0==t.parent.options.frozenColumns?t.virtualEle.wrapper.style.transform="translate("+t.translateMaskX+"px,"+r+"px)":t.virtualEle.wrapper.style.transform="translate(0px,"+r+"px)"}),0)}this.prevInfo=this.prevInfo?this.prevInfo:this.vHelper.getData()},e.prototype.setVirtualHeight=function(){var e=this.parent.options.columns.filter((function(e){return e.visible})),t=this.parent.options.enableColumnVirtualization?this.getColumnOffset(e.length-1)+"px":"100%",n=0,r=this.parent.options.totalItemCount*this.parent.getRowHeight(),i=this.parent.options.pageSize*this.parent.getRowHeight();this.parent.options.enableVirtualMaskRow?n=this.parent.options.frozenColumns&&this.parent.options.enableColumnVirtualization?r-2*i:r:this.parent.options.groupCount?n=this.parent.options.visibleGroupedRowsCount*this.parent.getRowHeight():this.parent.options.enableVirtualization&&(n=this.parent.options.frozenColumns&&this.parent.options.enableColumnVirtualization?r-i:r),this.virtualEle.setVirtualHeight(n,t),this.parent.options.enableColumnVirtualization&&this.header.virtualEle.setVirtualHeight(1,t)},e.prototype.getPageFromTop=function(e,t){var n=this,r=c(this.parent)?this.getGroupedTotalBlocks():this.getTotalBlocks(),i=0;this.offsets[r],this.prevHeight;return this.offsetKeys.some((function(o){var s=Number(o),l=e<=n.offsets[o]||s===r&&e>n.offsets[o];return l&&(t.block=s%2==0?1:0,i=Math.max(1,Math.min(n.vHelper.getPage(s),n.maxPage))),l})),i},e.prototype.getTranslateY=function(e,t,n,r){void 0===n&&((n={page:this.getPageFromTop(e+t,{})}).blockIndexes=this.vHelper.getBlockIndexes(n.page));var i=(n.blockIndexes[0]||1)-1,o=this.getOffset(i),s=this.getOffset(n.blockIndexes[n.blockIndexes.length-1]);r&&(n=this.prevInfo);var l=o>e?this.getOffset(i-1):s<e+t?this.getOffset(i+1):o,a=this.offsets[n.blockIndexes[n.blockIndexes.length-1]]-this.tmpOffsets[n.blockIndexes[0]];return l+a>this.offsets[c(this.parent)?this.getGroupedTotalBlocks():this.getTotalBlocks()]&&0==this.parent.options.groupCount&&(l-=l+a-this.offsets[this.getTotalBlocks()]),l},e.prototype.getOffset=function(e){return Math.min(0|this.offsets[e],0|this.offsets[this.maxBlock])},e.prototype.onEntered=function(){var e=this;return function(t,n,r,i,o,s,l){var a=e;e.observer.options.isWheelScroll=s,sf.base.Browser.isIE&&!s&&l&&e.preventEvent;var d="X"===n.axis,u=e.prevInfo.offsets?e.prevInfo.offsets.top:null,h=e.content.getBoundingClientRect().height,p=d?e.getColumnOffset(e.vHelper.getColumnIndexes()[0]-1,!0):e.getColumnOffset(e.prevInfo.columnIndexes[0]-1);p=e.parent.options.enableColumnVirtualization&&e.parent.options.enableRtl?-1*p:p;var c=e.getTranslateY(o.top,h,d&&u===o.top?e.prevInfo:void 0,!0);if(e.currentInfo&&e.currentInfo.startIndex&&d){var f=sf.base.isNullOrUndefined(e.currentInfo.endIndex)?0:e.currentInfo.endIndex;c=(f-e.parent.options.pageSize)*e.parent.getRowHeight()}if(e.movableTranslateX=e.vHelper.cOffsets[e.startColIndex-1]?e.vHelper.cOffsets[e.startColIndex-1]:p,e.movableTranslateX=e.parent.options.enableColumnVirtualization&&e.parent.options.enableRtl?-1*e.movableTranslateX:e.movableTranslateX,e.movableTranslateX=e.parent.options.enableColumnVirtualization&&e.parent.options.frozenColumns&&e.vHelper.mOffsets[e.startColIndex-1]?e.vHelper.mOffsets[e.startColIndex-1]:e.movableTranslateX,e.movableTranslateY=e.parent.options.pageSize*(e.parent.options.rowHeight>0?e.parent.options.rowHeight:e.parent.getRowHeight()),e.parent.options.enableVirtualMaskRow){if(e.offsets[e.maxBlock]>=c&&e.currentInfo.endIndex!=e.parent.options.totalItemCount)if("right"==i||"left"==i){var g=e.parent.getColumnIndexesInView(),m=e.getInfoFromView(i,n,o);JSON.stringify(g)!==JSON.stringify(m.columnIndexes)&&e.virtualEle.adjustTable(e.movableTranslateX,e.translateMaskY+e.movableTranslateY)}else{var v=e.parent.options.enableColumnVirtualization?e.parent.options.enableVirtualMaskRow?4:2:0;setTimeout((function(){var e=c;if(a.parent.options.enableVirtualMaskRow&&"down"==i)e=a.content.scrollTop-2*a.parent.options.pageSize*(0!=a.parent.options.rowHeight?a.parent.options.rowHeight:a.parent.getRowHeight());else if(a.parent.options.overscanCount>0&&"up"==i&&!sf.base.isNullOrUndefined(a.currentInfo.endIndex)){var t=(a.currentInfo.endIndex-a.parent.options.pageSize)*(0!=a.parent.options.rowHeight?a.parent.options.rowHeight:a.parent.getRowHeight());a.translateMaskY=c=t-(a.parent.options.overscanCount+a.parent.options.pageSize)*(0!=a.parent.options.rowHeight?a.parent.options.rowHeight:a.parent.getRowHeight())}a.virtualEle.adjustTable(p,Math.min(r?a.translateMaskY:"down"==i?e:c,a.offsets[a.maxBlock-v]))}),0)}}else 0==e.parent.options.overscanCount&&(e.currentInfo.endIndex==e.parent.options.totalItemCount&&"down"==i?(c=0!=e.parent.options.rowHeight?e.parent.options.rowHeight*e.currentInfo.startIndex:e.parent.getRowHeight()*e.currentInfo.startIndex,e.virtualEle.adjustTable(e.movableTranslateX,Math.min(c,e.offsets[e.maxBlock]))):d&&"up"!=i||"horizontal"==e.observer.PreventAdjustTable||e.virtualEle.adjustTable(e.movableTranslateX,Math.min(c,e.offsets[e.maxBlock])));d&&e.setColVTableWidthAndTranslate({refresh:!0,axis:"X"})}},e.prototype.getBlockSize=function(){return this.parent.options.pageSize>>1},e.prototype.getBlockHeight=function(){return this.getBlockSize()*this.parent.getRowHeight()},e.prototype.getGroupedTotalBlocks=function(){if(this.parent.options.enableLazyLoading&&this.parent.options.enableVirtualization)return this.getTotalBlocks();var e=this.getVisibleGroupedRowCount();return Math.floor(e/this.getBlockSize()<1?1:e/this.getBlockSize())},e.prototype.getVisibleGroupedRowCount=function(){return Number(this.virtualEle.placeholder.style.height.substring(0,this.virtualEle.placeholder.style.height.indexOf("p")))/this.parent.getRowHeight()},e.prototype.getTotalBlocks=function(){return Math.ceil(sf.base.isNullOrUndefined(this.count)?0:this.count/this.getBlockSize())},e.prototype.getColumnOffset=function(e,t){return 0|this.vHelper.cOffsets[e]},e.prototype.resetScrollPosition=function(e){var t=sf.base.isNullOrUndefined(e)?e:e.toLowerCase();if(this.actions.some((function(t){return t===e}))||this.parent.options.filterCount>0&&!sf.base.isNullOrUndefined(t)&&("save"==t||"delete"==t)||!sf.base.isNullOrUndefined(this.parent.options.initGroupingField)&&this.parent.options.initGroupingField.length>0&&!sf.base.isNullOrUndefined(t)&&("sorting"==t||"save"==t||"delete"==t)){var n=this.content;this.preventEvent=0!==n.scrollTop,n.scrollTop=0}},e.prototype.refreshOffsets=function(){this.parent;var e=0,t=this.getBlockSize(),n=c(this.parent)?this.getGroupedTotalBlocks():this.getTotalBlocks();this.prevHeight=this.offsets[n],this.maxBlock=n%2==0?n-2:n-1,this.offsets={};for(var r=[],i={},o=Array.apply(null,Array(n)).map((function(){return++e})),s=0;s<o.length;s++){var l=(i[o[s]]||[]).length,a=c(this.parent)?r.length%t:this.count%t,d=!c(this.parent)&&o[s]in i?l*this.parent.getRowHeight():a&&o[s]===n?a*this.parent.getRowHeight():this.getBlockHeight();this.offsets[o[s]]=(0|this.offsets[o[s]-1])+d,this.tmpOffsets[o[s]]=0|this.offsets[o[s]-1]}this.offsetKeys=Object.keys(this.offsets),this.parent.options.enableColumnVirtualization&&this.vHelper.refreshColOffsets()},e.prototype.updateTransform=function(e,t,n){var r=this;setTimeout((function(){r.translateMaskX=e,n&&(r.translateMaskY=t),r.virtualEle.adjustTable(e,r.translateMaskY)}),500)},e.prototype.refreshColumnIndexes=function(){this.vHelper.refreshColOffsets();var e=this.vHelper.getColumnIndexes();this.parent.setColumnIndexesInView(e),this.parent.dotNetRef.invokeMethodAsync("SetColumnIndexes",e[0],e[e.length-1])},e.prototype.refreshVirtualElement=function(){this.vHelper.refreshColOffsets(),this.setVirtualHeight()},e}(),F=function(){function e(e){this.parent=e,this.vHelper=new P(this.parent),this.virtualEle=new W(this.parent),this.headerPanel=this.parent.element.querySelector(".e-gridheader")}return e.prototype.getPanel=function(){return this.headerPanel},e.prototype.getTable=function(){return this.headerPanel.querySelector(".e-table")},e.prototype.renderTable=function(){this.vHelper.refreshColOffsets(),this.parent.setColumnIndexesInView(this.vHelper.getColumnIndexes(this.getPanel().querySelector(".e-headercontent"))),this.virtualEle.table=this.getTable(),this.virtualEle.content=this.getPanel().querySelector(".e-headercontent"),this.virtualEle.content.style.position="relative",this.virtualEle.renderWrapper(),this.virtualEle.renderPlaceHolder()},e}(),W=function(){function e(e){this.parent=e}return e.prototype.renderWrapper=function(e){this.wrapper=this.content.querySelector(".e-virtualtable"),this.wrapper.setAttribute("styles","min-height:"+sf.base.formatUnit(e)),this.content.querySelector(".e-gridcontent")&&(this.movableHeaderWrapper=this.content.querySelector(".e-virtualtable")),null!=this.content.querySelector(".e-gridcontent")&&(this.movableContentWrapper=this.content.querySelector(".e-virtualtable"))},e.prototype.renderPlaceHolder=function(){this.placeholder=this.content.lastElementChild},e.prototype.adjustTable=function(e,t){this.filterTranslateX=e,this.parent.options.enableColumnVirtualization&&!this.parent.options.enableVirtualization&&t>0&&(t=0);var n=[].slice.call(this.content.querySelectorAll(".e-leftfreeze,.e-rightfreeze,.e-fixedfreeze")),r=0,i=0;if(this.parent.getHeaderContent().querySelectorAll(".e-fixedfreeze").length&&(r=this.parent.leftrightColumnWidth("left"),i=this.parent.leftrightColumnWidth("right")),n.length)for(var o=0;o<n.length;o++){var s=n[parseInt(o.toString())],l=null;if(s.classList.contains("e-rowcell"))if(!sf.base.isNullOrUndefined(s.getAttribute("data-colindex"))&&s.querySelector("[e-mappinguid]")){var a=s.querySelector("[e-mappinguid]").getAttribute("e-mappinguid");l=this.parent.getColumnByUid(a)}else{var d=parseInt(s.getAttribute("data-colindex"));l=this.parent.getColumnByIndex(parseInt(d.toString()),!0)}else if(s.classList.contains("e-headercell")||s.classList.contains("e-filterbarcell")){a=s.classList.contains("e-filterbarcell")?s.getAttribute("e-mappinguid"):s.querySelector("[e-mappinguid]").getAttribute("e-mappinguid");l=this.parent.getColumnByUid(a)}"Left"===l.freeze?s.style.left=l.translateLeftRightValue-e+"px":"Right"===l.freeze?s.style.right=l.translateLeftRightValue+e+"px":"Fixed"===l.freeze&&(s.style.left=r-e+"px",s.style.right=i+e+"px")}t=this.parent.options.enableVirtualization?t:0,this.wrapper.style.transform="translate("+e+"px, "+t+"px)"},e.prototype.extractTranslateY=function(e){var t=e.match(/translate\([^,]+,([^)]+)\)/);return t&&t[1]?t[1].trim():"0"},e.prototype.setWrapperWidth=function(e,t){this.wrapper.style.width=e?e+"px":t?"100%":""},e.prototype.setVirtualHeight=function(e,t){this.parent.options.enableVirtualization&&(this.placeholder.style.height=e+"px"),this.placeholder.style.width=t},e}(),P=function(){function e(e){this.cOffsets={},this.mOffsets={},this.data={},this.groups={},this.parent=e}return e.prototype.getBlockIndexes=function(e){return[e+(e-1),2*e]},e.prototype.getPage=function(e){return e%2==0?e/2:(e+1)/2},e.prototype.getData=function(){return{page:this.parent.options.currentPage,blockIndexes:this.getBlockIndexes(this.parent.options.currentPage),direction:"down",columnIndexes:this.parent.getColumnIndexesInView()}},e.prototype.getColumnIndexes=function(e){var t=this;void 0===e&&(e=this.parent.getHeaderContent()),sf.base.isNullOrUndefined(this.parent.getContent())||(e=this.parent.getContent());for(var n=this.parent.getColumns().filter((function(e){return e.isFrozen&&"Left"===e.freeze})),r=this.parent.getColumns().filter((function(e){return e.isFrozen&&"Right"===e.freeze})),i=0;i<n.length;i++)parseInt(n[i].width);for(i=0;i<r.length;i++)parseInt(r[i].width);var o=[],s=this.parent.options.enableColumnVirtualization&&this.parent.options.enableRtl?-1*e.scrollLeft|0:0|e.scrollLeft,l=Object.keys(this.cOffsets),a=this.parent.options.needClientAction?this.parent.options.frozenColumns&&this.parent.options.enableColumnVirtualization?this.parent.getHeaderContent().offsetWidth:e.getBoundingClientRect().width:parseInt(this.parent.options.width);s=Math.min(this.cOffsets[l.length-1]-a,s);var d=sf.base.Browser.isDevice?2*a:a/2,u=s+a+(0===s?d:0);return l.some((function(e,n,r){var i=Number(e),l=t.cOffsets[e];return s-d<=l&&u+d>=l&&o.push(i),u+d<l})),o.length||this.parent.options.frozenColumns||(o=[],u+=this.cOffsets[this.getColumnIndexes()[0]],l.some((function(e,n,r){var i=Number(e),l=t.cOffsets[e];return s-d<=l&&u+d>=l&&o.push(i),u+d<l}))),1!=o.length||this.parent.options.frozenColumns?(this.parent.virtualContentModule.startColIndex=o[0],this.parent.virtualContentModule.endColIndex=o[o.length-1]):(this.parent.virtualContentModule.startColIndex=o[0],this.parent.virtualContentModule.endColIndex=o[0]+1,o.push(o[0]+1)),o},e.prototype.refreshColOffsets=function(){var e=0;this.cOffsets={},this.mOffsets={};var t=this.parent.options.groupCount,n=this.parent.options.columns;this.parent.options.enableColumnVirtualization&&(n=n.filter((function(e){return e.visible})));for(var r=n.length,i=Array.apply(null,Array(r)).map((function(){return e++})),o=0;o<i.length;o++)this.cOffsets[i[o]]=(0|this.cOffsets[i[o]-1])+(n[o].visible?parseInt(n[o].width,10):0);if(this.parent.options.frozenColumns){var s=0;if(this.parent.options.actualFrozenColumns){for(var l=0,a=0;a<n.length&&(!n[a].isFrozen&&n[a].index<this.parent.options.frozenColumns);a++)l++;u=n.slice(l,r)}else var d=n.filter((function(e){return e.isFrozen&&("Left"===e.freeze||"Right"===e.freeze)})),u=n.filter((function(e){return!d.some((function(t){return t===e}))}));for(var h=u.length,p=Array.apply(null,Array(h)).map((function(){return s++})),c=0;c<p.length;c++)p[c]=p[c]+t,this.mOffsets[p[c]]=(0|this.mOffsets[p[c]-1])+(u[c].visible?parseInt(u[c].width,10):0)}},e}(),_="th.e-headercell",V=".e-rowcell",G=".e-filterbarcell",X="e-frozen-helper",K="e-frozen-cursor",Y="data-colindex",j=function(){function e(e){this.currentBorderIndex=-1,this.originalBorderIndex=-1,this.visibleLtoROrderedColumns=[],this.visibleRtoLOrderedColumns=[],this.widthCollection=[],this.visibleFrozenColumns=[],this.visibleFrozenLeftColumns=[],this.domRtoLOrderedColumns=[],this.domLtoROrderedColumns=[],this.parent=e,this.parent.options.allowFreezeLineMoving&&this.wireEvents()}return e.prototype.wireEvents=function(){sf.base.EventHandler.add(this.parent.getContent(),sf.base.Browser.touchStartEvent,this.dragStart,this),sf.base.EventHandler.add(this.parent.getHeaderContent(),sf.base.Browser.touchStartEvent,this.dragStart,this)},e.prototype.unwireEvents=function(){sf.base.EventHandler.remove(this.parent.getContent(),sf.base.Browser.touchStartEvent,this.dragStart),sf.base.EventHandler.remove(this.parent.getHeaderContent(),sf.base.Browser.touchStartEvent,this.dragStart)},e.prototype.dragStart=function(e){if(e.target.classList.contains(K)){this.currentCursorElement=e.target,this.parent.getContent().classList.add("e-freezeline-moving"),this.isLeftCursorDragging=!!((this.currentCursorElement.classList.contains("e-frozen-left-cursor")||this.currentCursorElement.classList.contains("e-frozen-fixedleft-cursor")||this.currentCursorElement.classList.contains("e-frozen-fixedright-cursor"))&&!this.currentCursorElement.classList.contains("e-frozen-default-cursor")||this.currentCursorElement.classList.contains("e-frozen-default-cursor")&&this.currentCursorElement.classList.contains("e-frozen-right-cursor")),this.isLeftFixedCursorDragging=!!this.currentCursorElement.classList.contains("e-frozen-fixedleft-cursor"),this.isRightFixedCursorDragging=!!this.currentCursorElement.classList.contains("e-frozen-fixedright-cursor"),this.appendHelper();var t,n=sf.base.closest(this.currentCursorElement,_);if(sf.base.isNullOrUndefined(n))if(n=sf.base.closest(this.currentCursorElement,V),sf.base.isNullOrUndefined(n))t=sf.base.closest(this.currentCursorElement,G).getAttribute("e-mappinguid");else{var r=n.getAttribute(Y);t=this.parent.getHeaderContent().querySelector("["+Y+'="'+r+'"]').firstElementChild.getAttribute("e-mappinguid")}else t=n.firstElementChild.getAttribute("e-mappinguid");var i=this.parent.getColumns().filter((function(e){return e.uid==t}))[0].index;this.setColumnsForBorderUpdate(),sf.base.EventHandler.add(document,sf.base.Browser.touchEndEvent,this.dragEnd,this),this.parent.dotNetRef.invokeMethodAsync("InvokeFreezeLineMoving",{fromIndex:i})}},e.prototype.appendHelper=function(){this.helperElement=sf.base.createElement("div",{className:X}),this.setHelperHeight(),this.parent.element.appendChild(this.helperElement)},e.prototype.setHelperHeight=function(){var e=this.parent.getContent().offsetHeight,t=sf.base.closest(this.currentCursorElement,_);if(sf.base.isNullOrUndefined(t)){var n=sf.base.closest(this.currentCursorElement,V);if(sf.base.isNullOrUndefined(n)){var r=sf.base.closest(this.currentCursorElement,G).getAttribute("e-mappinguid");t=this.parent.getHeaderContent().querySelectorAll("[e-mappinguid="+r+"]")[0].parentElement}else{var i=n.getAttribute(Y);t=this.parent.getHeaderContent().querySelector("["+Y+'="'+i+'"]')}}for(var o=[].slice.call(this.parent.getHeaderContent().querySelectorAll("tr")),s=o.indexOf(t.parentElement);s<o.length;s++)e+=o[s].offsetHeight;this.parent.getContent().scrollWidth>this.parent.getContent().clientWidth&&(e-=16);var l=this.calcPos(t),a=this.calcPos(this.parent.getHeaderContent()).top;this.currentCursorElement.classList.contains("e-frozen-default-cursor")?l.left=this.isLeftCursorDragging?l.left:l.left+t.offsetWidth-2:t.classList.contains("e-rightfreeze")?l.left+=this.parent.options.enableRtl?t.offsetWidth-2:-1:l.left+=this.parent.options.enableRtl?-1:t.offsetWidth-2,this.helperElement.style.cssText="height: "+e+"px; top: "+a+"px; left:"+Math.floor(l.left)+"px;"},e.prototype.calcPos=function(e){for(var t={top:0,left:0,right:0},n=e.getBoundingClientRect(),r=e.ownerDocument,i=a(e,"e-grid")||r.documentElement;i&&(i===r.body||i===r.documentElement)&&"static"===i.style.position;)i=i.parentNode;return i&&i!==e&&1===i.nodeType&&(t=i.getBoundingClientRect()),{top:n.top-t.top,left:n.left-t.left,right:n.right-t.right}},e.prototype.preventFreezeLineMoving=function(e){e?this.cancelFreezeLineMoving():sf.base.EventHandler.add(this.parent.element,sf.base.Browser.touchMoveEvent,this.freezeLineDragging,this)},e.prototype.cancelFreezeLineMoving=function(){this.unwireEvents(),sf.base.detach(this.helperElement),this.setDefaultValue()},e.prototype.setDefaultValue=function(){this.parent.getContent().classList.remove("e-freezeline-moving"),this.addOrRemoveClasses("remove"),this.helperElement=null,this.currentBorderIndex=-1,this.currentCursorElement=null,this.borderCells=null,this.widthCollection=[]},e.prototype.freezeLineDragging=function(e){this.helperElement&&(this.scrollHorizontally(),this.updateHelper(e),this.updateBorder())},e.prototype.setColumnsForBorderUpdate=function(){var e=this,t=this.parent.getColumns(),n=t.filter((function(e){return"Left"==e.freeze&&e.isFrozen}));this.visibleFrozenLeftColumns=n.filter((function(e){return e.visible}));var r=t.filter((function(e){return"Right"==e.freeze&&e.isFrozen})),i=r.filter((function(e){return e.visible}));if(this.parent.options.actualFrozenColumns>0){var o=t.slice(0,this.parent.options.actualFrozenColumns);this.visibleFrozenColumns=o.filter((function(e){return e.visible}));var s=(l=t.filter((function(t){return t.index>e.parent.options.actualFrozenColumns-1&&(!t.isFrozen||t.isFrozen&&"Fixed"==t.freeze)}))).filter((function(e){return e.visible}));this.isLeftCursorDragging?(this.visibleLtoROrderedColumns=[].concat(this.visibleFrozenColumns,this.visibleFrozenLeftColumns,s,i),this.domLtoROrderedColumns=[].concat(o,n,l,r),this.widthCollection=this.pushColumnWidth(this.visibleLtoROrderedColumns,this.widthCollection)):(this.visibleRtoLOrderedColumns=[].concat(i.reverse(),s.reverse(),this.visibleFrozenLeftColumns.reverse(),this.visibleFrozenColumns.reverse()),this.domRtoLOrderedColumns=[].concat(r.reverse(),l.reverse(),n.reverse(),o.reverse()),this.widthCollection=this.pushColumnWidth(this.visibleRtoLOrderedColumns,this.widthCollection))}else{var l;s=(l=t.filter((function(e){return!e.isFrozen||e.isFrozen&&"Fixed"==e.freeze}))).filter((function(e){return e.visible}));this.isLeftCursorDragging?(this.domLtoROrderedColumns=[].concat(n,l,r),this.visibleLtoROrderedColumns=[].concat(this.visibleFrozenLeftColumns,s,i),this.widthCollection=this.pushColumnWidth(this.visibleLtoROrderedColumns,this.widthCollection)):(this.domRtoLOrderedColumns=[].concat(r.reverse(),l.reverse(),n.reverse()),this.visibleRtoLOrderedColumns=[].concat(i.reverse(),s.reverse(),this.visibleFrozenLeftColumns.reverse()),this.widthCollection=this.pushColumnWidth(this.visibleRtoLOrderedColumns,this.widthCollection))}},e.prototype.updateBorder=function(){var e;if(-1==this.currentBorderIndex)if(sf.base.isNullOrUndefined(a(this.currentCursorElement,"e-rowcell")))if(sf.base.isNullOrUndefined(a(this.currentCursorElement,"e-filterbarcell")))this.currentBorderIndex=Number(a(this.currentCursorElement,"e-headercell").getAttribute(Y));else{var t=a(this.currentCursorElement,"e-filterbarcell").getAttribute("e-mappinguid"),n=this.parent.getHeaderContent().querySelectorAll("[e-mappinguid="+t+"]")[0].parentElement;this.currentBorderIndex=Number(n.getAttribute(Y))}else this.currentBorderIndex=Number(a(this.currentCursorElement,"e-rowcell").getAttribute(Y));var r=this.isLeftFixedCursorDragging||this.isRightFixedCursorDragging?this.convertWidthToNumber(this.helperElement.style.left)+this.parent.getContent().scrollLeft:this.isLeftCursorDragging?this.convertWidthToNumber(this.helperElement.style.left):-this.calcPos(this.helperElement).right;if(r<this.widthCollection[0]/2)e=0,this.frozenDefaultBorderClass=this.isLeftCursorDragging?"e-frozen-left-border":"e-frozen-right-border";else if(r>this.widthCollection[0]/2&&r<this.widthCollection[0])e=0,this.frozenDefaultBorderClass=this.isLeftCursorDragging?"e-frozen-right-border":"e-frozen-left-border";else{this.frozenDefaultBorderClass="default";for(var i=0;i<this.widthCollection.length&&i+1!=this.widthCollection.length;i++)if(r>this.widthCollection[i]&&r<this.widthCollection[i+1]){var o=(this.widthCollection[i+1]-this.widthCollection[i])/2;e=r<this.widthCollection[i]+o?i:i+1;break}}if(!sf.base.isNullOrUndefined(e)&&this.currentBorderIndex!=e||"default"!=this.frozenDefaultBorderClass&&e==this.currentBorderIndex){this.addOrRemoveClasses("remove"),this.currentBorderIndex=e;var s=this.isLeftCursorDragging?this.visibleLtoROrderedColumns[this.currentBorderIndex].uid:this.visibleRtoLOrderedColumns[this.currentBorderIndex].uid,l=this.parent.getHeaderContent().querySelectorAll("[e-mappinguid="+s+"]");if(l.length)for(i=0;i<l.length;i++)l[i].classList.contains("e-headercelldiv")&&(this.borderHeaderCell=l[i].parentElement);if(this.parent.getHeaderContent().querySelector(".e-filterbar")){var d=this.parent.getHeaderContent().querySelectorAll(".e-filterbarcell");for(i=0;i<d.length;i++)d[i].getAttribute("e-mappinguid")==s&&(this.filterBarCell=d[i])}this.originalBorderIndex=Number(this.borderHeaderCell.getAttribute(Y)),this.borderCells=this.parent.getContent().querySelectorAll("["+Y+'="'+this.originalBorderIndex+'"]'),this.addOrRemoveClasses("add")}},e.prototype.addOrRemoveClasses=function(e){var t=this.isLeftCursorDragging?"e-frozen-right-border":"e-frozen-left-border";sf.base.isNullOrUndefined(this.borderCells)||("add"==e?(t="default"==this.frozenDefaultBorderClass?t:this.frozenDefaultBorderClass,sf.base.addClass(this.borderCells,t),this.borderHeaderCell.classList.add(t),sf.base.isNullOrUndefined(this.filterBarCell)||this.filterBarCell.classList.add(t)):"default"!=this.frozenDefaultBorderClass?(sf.base.removeClass(this.borderCells,["e-frozen-left-border","e-frozen-right-border"]),this.borderHeaderCell.classList.remove("e-frozen-right-border"),this.borderHeaderCell.classList.remove("e-frozen-left-border"),sf.base.isNullOrUndefined(this.filterBarCell)||(this.filterBarCell.classList.remove("e-frozen-right-border"),this.filterBarCell.classList.remove("e-frozen-left-border"))):(sf.base.removeClass(this.borderCells,t),this.borderHeaderCell.classList.remove(t),sf.base.isNullOrUndefined(this.filterBarCell)||this.filterBarCell.classList.remove(t)))},e.prototype.convertWidthToNumber=function(e){return Number(e.toString().replace("px",""))},e.prototype.pushColumnWidth=function(e,t){for(var n=0,r=0;r<e.length;r++){var i=this.convertWidthToNumber(e[r].width);t.push(n+i),n=t[t.length-1]}return t},e.prototype.scrollHorizontally=function(){if(0!=this.parent.options.frozenColumns){var e=this.parent.getContent();if(e.scrollWidth>e.clientWidth){this.parent.element.querySelector(".e-movablescrollbar"),this.parent.element.querySelector(".e-movablechild");e.scrollLeft=this.isLeftFixedCursorDragging||this.isRightFixedCursorDragging?e.scrollLeft:this.isLeftCursorDragging?0:e.offsetWidth}}},e.prototype.updateHelper=function(e){var t=this.getCurrentScreenX(e),n=this.calcPos(this.currentCursorElement).left,r=t-(this.parent.element.getBoundingClientRect().left+n),i=this.parent.getContent().offsetWidth,s=n+r,l=this.parent.getContent().querySelector(".e-movablecontent");if(l||(l=this.parent.getContent()),!this.isLeftCursorDragging||this.isLeftFixedCursorDragging||this.isRightFixedCursorDragging){if(!this.isLeftFixedCursorDragging&&!this.isRightFixedCursorDragging){var a=this.calcPos(l).left;s=(s=s<a?a+1:s)>i-o()?i-o()-1:s}}else{var d=this.calcPos(l).right;s=s>i+d?i+d-1:s}this.helperElement.style.left=s+"px",this.helperElement.style.zIndex="10"},e.prototype.getCurrentScreenX=function(e){return e.touches&&e.touches.length?e.touches[0].clientX:e.clientX},e.prototype.dragEnd=function(e){if(this.helperElement){sf.base.EventHandler.remove(this.parent.element,sf.base.Browser.touchMoveEvent,this.freezeLineDragging),sf.base.EventHandler.remove(document,sf.base.Browser.touchEndEvent,this.dragEnd);var t=this.getServerParams(),n=this.parent.options.actualFrozenColumns;if(this.parent.options.actualFrozenColumns>0&&!t.isFrozen)for(var r=0;r<t.frozenColumnsUidCollection.length;r++)for(var i=0;i<this.visibleFrozenColumns.length;i++)this.visibleFrozenColumns[i].uid==t.frozenColumnsUidCollection[r]&&n--;var o=!!(this.currentCursorElement.classList.contains("e-frozen-default-cursor")||this.isLeftCursorDragging&&"e-frozen-left-border"==this.frozenDefaultBorderClass||!this.isLeftCursorDragging&&"e-frozen-right-border"==this.frozenDefaultBorderClass);sf.base.detach(this.helperElement),this.setDefaultValue(),this.checkForServerCall(t.isFrozen,t.frozenColumnsUidCollection,t.endIndex,t.freezeDirection,t.freezeLineMovingDirection)&&this.parent.dotNetRef.invokeMethodAsync("InvokeFreezeLineMoved",{frozenColumnsUidCollection:t.frozenColumnsUidCollection,freezeDirection:t.freezeDirection,freezeLineMovingDirection:t.freezeLineMovingDirection,isFrozen:t.isFrozen,fromIndex:t.startIndex,toIndex:t.endIndex,frozenColumnsCount:n,hasGridStructureChanges:o})}},e.prototype.getServerParams=function(){var e,t,n,r=[],i=-1,o=-1;if(!sf.base.isNullOrUndefined(this.borderCells)){var s=this.convertWidthToNumber(this.helperElement.style.left),l=this.calcPos(this.currentCursorElement).left,d=sf.base.closest(this.currentCursorElement,V);if(sf.base.isNullOrUndefined(d)&&(d=sf.base.closest(this.currentCursorElement,_),sf.base.isNullOrUndefined(d))){var u=a(this.currentCursorElement,"e-filterbarcell").getAttribute("e-mappinguid");d=this.parent.getHeaderContent().querySelectorAll("[e-mappinguid="+u+"]")[0].parentElement}if(i=Number(d.getAttribute(Y)),o=Number(this.borderCells[0].getAttribute(Y)),this.isLeftCursorDragging)if(e="Left",(this.isLeftFixedCursorDragging||this.isRightFixedCursorDragging)&&(e="Fixed"),s>l){t=!this.isLeftFixedCursorDragging,n="Right";for(var h=0==this.parent.options.frozenColumns||0==this.parent.options.actualFrozenColumns&&0==this.visibleFrozenLeftColumns.length?0:i+1,p=this.currentCursorElement.classList.contains("e-frozen-default-cursor")&&"e-frozen-left-border"==this.frozenDefaultBorderClass?-1:o,c=h;c<=p;c++)this.domLtoROrderedColumns[c].visible&&r.push(this.domLtoROrderedColumns[c].uid)}else if(t=!!this.isLeftFixedCursorDragging,n="Left","e-frozen-left-border"!=this.frozenDefaultBorderClass)for(c=i;c>o;c--)this.domLtoROrderedColumns[c].visible&&r.push(this.domLtoROrderedColumns[c].uid);else for(c=i;-1!=c;c--)this.domLtoROrderedColumns[c].visible&&r.push(this.domLtoROrderedColumns[c].uid);else{var f=this.domRtoLOrderedColumns.reverse();if(s<l){if(t=!0,e="Right",n="Left","e-frozen-right-border"!=this.frozenDefaultBorderClass){i=this.currentCursorElement.classList.contains("e-frozen-default-cursor")?i+1:i;for(c=o;c<i;c++)f[c].visible&&r.push(f[c].uid)}}else if(t=!1,e="Left",n="Right","e-frozen-right-border"==this.frozenDefaultBorderClass)for(c=i;c<=o;c++)f[c].visible&&r.push(f[c].uid);else for(c=i;c<o;c++)f[c].visible&&r.push(f[c].uid)}}return{startIndex:i,endIndex:o,freezeDirection:e,isFrozen:t,freezeLineMovingDirection:n,frozenColumnsUidCollection:r}},e.prototype.checkForServerCall=function(e,t,n,r,i){var o=!0,s=this.parent.columnModel.slice(0,this.parent.options.actualFrozenColumns);if(s=(s=s.filter((function(e){return e.visible}))).concat(this.parent.columnModel.filter((function(e){return e.isFrozen&&e.visible}))),e)for(var l=0;l<this.parent.columnModel.length;l++)for(var a=0;a<t.length;a++)if(t[a]==this.parent.columnModel[l].uid){for(var d=!0,u=0;u<s.length;u++)s[u].uid==t[a]&&(d=!1);d&&s.push(this.parent.columnModel[l])}for(var h=0,p=0;p<s.length;p++)h+=+s[p].width;if(this.parent.element.offsetWidth<=h&&(o=!1),!sf.base.isNullOrUndefined(this.parent.getContent().querySelector(".e-movablecontent"))){var c=this.parent.getMovableDataRows()[0].getElementsByTagName("td"),f=this.parent.getFrozenDataRows()[0].getElementsByTagName("td"),g=n.toString();("Right"==i&&c[c.length-1].getAttribute("data-colindex")==g||"Left"==i&&c[0].getAttribute("data-colindex")==g||f[0].getAttribute("data-colindex")==g&&"Right"==r&&"Left"==i)&&(o=!1)}return o},e}(),J=function(){function e(e,n,r,i){var o=this;if(this.columnModel=[],this.frozenColumnModel=[],window.sfBlazor=window.sfBlazor,this.editedCellIndex=null,this.firstFocusableTemplateElement=null,this.lastFocusableTemplateElement=null,this.inViewIndexes=[],this.isRendered=!1,this.isGridFirstRender=!1,this.nColumnOffsets=[],this.getFrozenLeftColumns=function(){var e=[];return this.getColumns().filter((function(e){return e.isFrozen&&"Left"===e.freeze})).map((function(t){return e.push(t)})),e},this.getFrozenRightColumns=function(){var e=[];return this.getColumns().filter((function(e){return e.isFrozen&&"Right"===e.freeze})).map((function(t){return e.push(t)})),e},this.tapEvent=function(e){if(this.resizeModule.getUserAgent())if(t.timer){clearTimeout(t.timer),t.timer=null;var n=document.createEvent("MouseEvents");n.initEvent("dblclick",!0,!0),e.target.dispatchEvent(n)}else t.timer=setTimeout((function(){t.timer=null}),300)},this.element=n,!sf.base.isNullOrUndefined(this.element)){if(this.dotNetRef=i,this.dataId=e,this.options=r,this.header=this.element.querySelector(".e-headercontent"),this.content=this.element.querySelector(".e-gridcontent .e-content"),this.footer=this.element.querySelector(".e-summarycontent"),this.element.offsetWidth<=0)var s=setInterval((function(){o.element.offsetWidth>0&&(o.initModules(),clearInterval(s))}),500);else this.initModules();this.addScrollEvents(!0),sf.base.isNullOrUndefined(this.element)||(this.element.blazor__instance=this,window.sfBlazor.setCompInstance(this))}}return e.prototype.initModules=function(){this.scrollModule=new m(this),this.freezeModule=new v(this),this.headerDragDrop=new y(this),this.contentDragDrop=new C(this),this.reorderModule=new w(this),this.groupModule=new N(this),this.resizeModule=new E(this),this.frozenDragDropModule=new j(this),this.editModule=new O(this),this.columnChooserModule=new q(this),this.clipboardModule=new T(this),this.columnMenuModule=new H(this),this.filterModule=new L(this),this.virtualContentModule=new D(this),this.virtualHeaderModule=new F(this),this.toolTipModule=new B(this),this.rowDragAndDropModule=new U(this),this.selectionModule=new M(this),this.widthService=new b(this),this.isRendered=this.options.isPrerendered,this.keyModule=new sf.base.KeyboardEvents(this.element,{keyAction:this.keyActionHandler.bind(this),keyConfigs:Q,eventName:"keydown"}),this.options.enableColumnVirtualization&&this.virtualHeaderModule.renderTable(),(this.options.enableVirtualization||this.options.enableColumnVirtualization)&&this.virtualContentModule.renderTable(),this.options.allowResizing&&this.resizeModule.render(),this.options.isFreezeLineMoved&&this.freezeLineMovedAction(),this.options.needClientAction?this.clientActions():this.contentReady(),this.lastRowBorderCheck(),this.wireEvents(),this.options.enableColumnVirtualization||this.updateColumnWidth(this.options.columns)},e.prototype.getHeaderContent=function(){return this.header},e.prototype.getHeaderTable=function(){return this.header.querySelector(".e-table")},e.prototype.getContent=function(){return this.content},e.prototype.getContentTable=function(){return this.content.querySelector(".e-table")},e.prototype.getFooterContent=function(){return this.footer},e.prototype.getColumns=function(e,t){void 0===e&&(e=!1),this.columnModel=[];var n=this.options.enableColumnVirtualization&&e?this.options.virtualizedColumns:this.options.columns;return e&&this.options.frozenColumns>0?(n=this.getOrderedFrozenColumns(),this.columnModel=n):this.updateColumnModel(n),this.columnModel},e.prototype.getOrderedFrozenColumns=function(){var e=[],t=this.getColumns();return t.filter((function(e){return e.isFrozen&&"Left"===e.freeze})).map((function(t){return e.push(t)})),t.filter((function(e){return!e.isFrozen||e.isFrozen&&"Fixed"===e.freeze})).map((function(t){return e.push(t)})),t.filter((function(e){return e.isFrozen&&"Right"===e.freeze})).map((function(t){return e.push(t)})),e},e.prototype.autofitFrozenColumns=function(e){var t=[],n=this.getColumns();n.filter((function(t){return(e||t.autoFit)&&t.isFrozen&&"Left"===t.freeze})).map((function(e){return t.push(e.field||e.uid)})),n.filter((function(t){return(e||t.autoFit)&&!t.isFrozen})).map((function(e){return t.push(e.field||e.uid)})),n.filter((function(t){return(e||t.autoFit)&&t.isFrozen&&"Right"===t.freeze})).map((function(e){return t.push(e.field||e.uid)}));return t},e.prototype.freezeLineMovedAction=function(){this.options.isFreezeLineMoved=!1;var e=this.getContent().querySelector(".e-movablecontent");(sf.base.isNullOrUndefined(e)&&(e=this.getContent()),""!=e.querySelector("table").style.width)&&(0==this.options.frozenLeftColumnsCount&&0==this.options.frozenRightColumnsCount||this.updateColumnLevelFrozen(),new b(this).setWidthToTable());e.scrollLeft=0,sf.base.isNullOrUndefined(this.element.querySelector(".e-movablescrollbar"))||(this.element.querySelector(".e-movablescrollbar").scrollLeft=0)},e.prototype.addScrollEvents=function(e){if(this.options.showColumnMenu){for(var t=sf.popups.getScrollableParent(this.element),n=0;n<t.length;n++)t[n]instanceof HTMLElement&&(e?sf.base.EventHandler.add(t[n],"scroll",this.scrollHandler,this):sf.base.EventHandler.remove(t[n],"scroll",this.scrollHandler));e?sf.base.EventHandler.add(this.content,"scroll",this.scrollHandler,this):sf.base.EventHandler.remove(this.content,"scroll",this.scrollHandler)}},e.prototype.scrollHandler=function(e){if(!sf.base.isNullOrUndefined(this.element)&&!sf.base.isNullOrUndefined(this.element.blazor__instance))return this.columnMenuModule.setPosition()},e.prototype.updateColumnLevelFrozen=function(){var e=this.columnModel;this.options.enableColumnVirtualization&&(e=e.filter((function(e){return e.visible})));var t=[],n=[],r=[];if(0!=this.options.frozenRightCount||0!=this.options.frozenLeftCount||0!=this.options.frozenColumns){for(var i=0,o=e.length;i<o;i++){var s=e[i];"Left"===s.freeze&&s.isFrozen||s.index<this.options.frozenColumns?t.push(s):"Right"===s.freeze&&s.isFrozen?n.push(s):r.push(s)}this.frozenColumnModel=t.concat(r).concat(n)}},e.prototype.updateColumnModel=function(e){if(!sf.base.isNullOrUndefined(e))for(var t=0,n=e.length;t<n;t++)null!=e[t].columns&&e[t].columns.length>0?this.updateColumnModel(e[t].columns):this.columnModel.push(e[t])},e.prototype.updateColumnWidth=function(e){this.nColumnOffsets=[];var t=0;if(!this.options.enableColumnVirtualization)for(var n=0,r=e.length;n<r;n++)t=parseInt(t.toString())+(e[n].visible?parseInt(e[n].width):0),this.nColumnOffsets.push(t)},e.prototype.getColumnByIndex=function(e,t){var n;return void 0===t&&(t=!1),this.getColumns(t).some((function(t,r){return n=t,r===e})),n},e.prototype.getDataRows=function(){if(sf.base.isNullOrUndefined(this.getContentTable().querySelector("tbody")))return[];var e=[].slice.call(this.getContentTable().querySelector("tbody").children);if(this.options.frozenRows){var t=[].slice.call(this.getHeaderTable().querySelector("tbody").children);e=this.addMovableRows(t,e)}return this.generateDataRows(e)},e.prototype.addMovableRows=function(e,t){for(var n=0,r=t.length;n<r;n++)e.push(t[n]);return e},e.prototype.generateDataRows=function(e){for(var t=[],n=0,r=e.length;n<r;n++)e[n].classList.contains("e-row")&&!e[n].classList.contains("e-hiddenrow")&&t.push(e[n]);return t},e.prototype.getMovableDataRows=function(){var e=[].slice.call(this.getContent().querySelector("tbody").children);if(this.options.frozenRows){var t=[].slice.call(this.getHeaderContent().querySelector("tbody").children);e=this.addMovableRows(t,e)}return this.generateDataRows(e)},e.prototype.getFrozenDataRows=function(){var e=[].slice.call(this.getContent().querySelector(".e-frozencontent").querySelector("tbody").children);if(this.options.frozenRows){var t=[].slice.call(this.getHeaderContent().querySelector(".e-frozenheader").querySelector("tbody").children);e=this.addMovableRows(t,e)}return this.generateDataRows(e)},e.prototype.leftrightColumnWidth=function(e){var t="left"===e?this.getFrozenLeftColumns():"right"===e?this.getFrozenRightColumns():[],n=0;return t.filter((function(e){e.visible&&(n+=parseInt(e.width.toString(),10))})),n},e.prototype.getFrozenRightDataRows=function(){var e=[].slice.call(this.getContent().querySelector(".e-frozen-right-content").querySelector("tbody").children);if(this.options.frozenRows){var t=[].slice.call(this.getHeaderContent().querySelector(".e-frozenheader").querySelector("tbody").children);e=this.addMovableRows(t,e)}return this.generateDataRows(e)},e.prototype.getRowByIndex=function(e){return this.getDataRows()[e]},e.prototype.getCellFromIndex=function(e,t){return this.getDataRows()[e]&&this.getDataRows()[e].querySelectorAll(".e-rowcell")[t]},e.prototype.isMovableGrid=function(e,t){var n=this.getColumns(t);if(this.options.actualFrozenColumns>0)return e>=this.options.actualFrozenColumns;var r=[];return n.forEach((function(e){e.isFrozen&&r.push(e.index)})),-1==r.indexOf(e)},e.prototype.getColumnHeaderByIndex=function(e){return this.getHeaderTable().querySelectorAll(".e-headercell")[e]},e.prototype.getRows=function(){var e=[].slice.call(this.getContentTable().querySelectorAll("tr.e-row[data-uid]"));if(this.options.frozenRows){var t=[].slice.call(this.getHeaderContent().querySelectorAll("tr.e-row[data-uid]"));e=this.addMovableRows(t,e)}return e},e.prototype.getSelectedRows=function(){return this.getRows().filter((function(e){return"true"===e.getAttribute("aria-selected")}))},e.prototype.getSelectedRowIndexes=function(e){for(var t=[],n=this.getRows(),r=0;r<n.length;r++)if(n[r].hasAttribute("aria-selected")&&"true"===n[r].getAttribute("aria-selected")){var i=this.options.allowDragSelection&&this.options.enableVirtualization||e?parseInt(n[r].getAttribute("data-rowindex"),10):r;t.push(i)}return t},e.prototype.getVisibleColumns=function(){for(var e=[],t=0,n=this.columnModel;t<n.length;t++){var r=n[t];r.visible&&e.push(r)}return e},e.prototype.getColumnByField=function(e){return p(this.getColumns(),(function(t,n){if(t.field===e)return t}))[0]},e.prototype.getColumnIndexByField=function(e,t){void 0===t&&(t=!1);for(var n=this.getColumns(t),r=0;r<n.length;r++)if(n[r].field===e)return r;return-1},e.prototype.getColumnByUid=function(e){return p(this.getColumns().concat(this.getStackedColumns(this.options.columns)),(function(t,n){if(t.uid===e)return t}))[0]},e.prototype.getStackedColumns=function(e,t){void 0===t&&(t=[]);for(var n=0,r=e;n<r.length;n++){var i=r[n];i.columns&&(t.push(i),this.getStackedColumns(i.columns,t))}return t},e.prototype.getColumnIndexByUid=function(e,t){void 0===t&&(t=!1);var n=p(this.getColumns(t),(function(t,n){if(t.uid===e)return n}))[0];return sf.base.isNullOrUndefined(n)?-1:n},e.prototype.getColumnHeaderByUid=function(e){var t=this.getHeaderContent().querySelector("[e-mappinguid="+e+"]");return t?t.parentElement:null},e.prototype.getUidByColumnField=function(e,t){return void 0===t&&(t=!1),p(this.getColumns(t),(function(t,n){if(t.field===e)return t.uid}))[0]},e.prototype.getStackedHeaderColumnByHeaderText=function(e,t){for(var n=0;n<t.length;n++){var r=t[n];if(r.field===e||r.headerText===e){this.stackedColumn=r;break}r.columns&&this.getStackedHeaderColumnByHeaderText(e,r.columns)}return this.stackedColumn},e.prototype.getNormalizedColumnIndex=function(e,t){return void 0===t&&(t=!1),this.getColumnIndexByUid(e,t)+this.getIndentCount()},e.prototype.getIndentCount=function(){var e=0;return this.options.allowGrouping&&(e+=this.options.groupCount),this.options.hasDetailTemplate&&e++,this.options.allowRowDragAndDrop&&!this.options.hasDropTarget&&e++,e},e.prototype.isPercentageWidth=function(){for(var e=this.getVisibleColumns(),t=0,n=0,r=0;r<e.length;r++)sf.base.isNullOrUndefined(e[r].width)?n++:-1!==e[r].width.toString().indexOf("%")&&t++;return t===e.length&&!n},e.prototype.recalcIndentWidth=function(){if(!(this.options.isRenderedFromTreeGrid&&this.options.hasDetailTemplate&&this.options.allowRowDragAndDrop)&&this.isRendered&&this.getHeaderTable().querySelector(".e-emptycell")&&(this.options.groupCount||this.options.hasDetailTemplate||!this.options.allowRowDragAndDrop||!this.options.hasDropTarget)&&this.getContentTable()&&!this.getHeaderTable().querySelector(".e-emptycell").getAttribute("indentRefreshed")){var e=this.getHeaderTable().querySelector(".e-emptycell").parentElement.offsetWidth,t=e/30;if(t>=1&&(e=30/t),this.getHeaderTable().querySelector(".e-emptycell").setAttribute("indentRefreshed","true"),this.isPercentageWidth()){var n=e/30;n>=1&&(e=(e=30/n)>5?3.5:e),this.dotNetRef.invokeMethodAsync("SetIndentWidth",e+"%")}else this.dotNetRef.invokeMethodAsync("SetIndentWidth",e+"px")}},e.prototype.resetColumnWidth=function(){if(("auto"===this.options.width||"string"==typeof this.options.width)&&this.getColumns().filter((function(e){return(!e.width||"auto"===e.width)&&e.minWidth})).length>0){var e=this.widthService.getTableWidth(this.getColumns());this.widthService.setMinwidthBycalculation(e)}},e.prototype.contentReady=function(e,t){void 0===e&&(e=null);var n=document.getElementsByTagName("main")[0];if(!sf.base.isNullOrUndefined(n)&&n.parentElement.classList.contains("page")&&(this.element.style.width,1)&&(n.style.width="100%"),this.getColumns().some((function(e){return e.autoFit}))){var r=this;r.element.querySelector(".e-emptyrow")&&r.options.frozenColumns?setTimeout((function(){r.resizeModule.autoFit()}),100):this.resizeModule.autoFit()}this.options.allowResizing&&this.isGridFirstRender&&this.options.isColumnResized&&(new b(this).setWidthToTable(),this.isGridFirstRender=!1);!this.isGridFirstRender&&this.options.frozenColumns&&(this.options.enablePersistence||t)&&new b(this).setWidthToTable();this.options.isColumnReordered&&!sf.base.isNullOrUndefined(this.getContent().querySelector(".e-movablecontent"))&&""!=this.getContent().querySelector(".e-movablecontent").querySelector("table").style.width&&new b(this).setWidthToTable();if(this.options.frozenColumns&&this.options.enableColumnVirtualization&&(this.freezeModule.setFrozenHeight(),0!=this.options.aggregatesCount)){for(var i=this.element.querySelectorAll(".e-summaryrow"),o=0,s=0;s<i.length;s++)if(i[s].querySelectorAll(".e-templatecell").length>0){o=i[s].offsetHeight;break}for(s=0;s<i.length;s++)i[s].style.height=o+"px"}if((this.options.enableVirtualization||this.options.enableColumnVirtualization)&&this.virtualContentModule.onDataReady(),this.recalcIndentWidth(),this.resetColumnWidth(),this.lastRowBorderCheck(),"Paging"===e&&(a(document.activeElement,"e-grid")||this.element.focus()),this.options.enableStickyHeader){this.scrollModule.addStickyListener(!0);var l=this.element.querySelector(".e-groupdroparea");!sf.base.isNullOrUndefined(l)&&l.classList.contains("e-sticky")&&""===l.style.top&&l.classList.remove("e-sticky")}this.options.enableInfiniteScrolling&&(this.scrollModule.infiniteOnDataReady(),this.scrollModule.resetInfniniteScrollPositions()),sf.base.isNullOrUndefined(this.toolTipModule.toolTipElement)||this.toolTipModule.close(),this.getColumns().filter((function(e){return e.isFrozen&&"Right"===e.freeze})).length>0&&this.element.classList.add("e-right-shadow"),this.options.frozenRows>0&&(this.element.querySelector(".e-frozenrow-border").style.width=this.getContent().scrollHeight>this.getContent().offsetHeight?this.element.offsetWidth-17+"px":this.element.offsetWidth+"px")},e.prototype.lastRowBorderCheck=function(){this.options.enableVirtualization||this.getContent().querySelector(".e-table").scrollHeight<this.getContent().clientHeight&&this.dotNetRef.invokeMethodAsync("LastRowBorder",!0)},e.prototype.wireEvents=function(){sf.base.EventHandler.add(this.element,"mousedown",this.mouseDownHandler,this),sf.base.EventHandler.add(this.element,"focus",this.gridFocus,this),sf.base.EventHandler.add(document,"click",this.documentClickHandler,this),sf.base.EventHandler.add(this.element,"keydown",this.gridKeyDownHandler,this),sf.base.EventHandler.add(this.element,"keydown",this.keyDownHandler,this),sf.base.EventHandler.add(document.body,"keydown",this.documentKeyHandler,this),sf.base.EventHandler.add(this.getContent(),"touchstart",this.tapEvent,this),sf.base.EventHandler.add(window,"resize",this.windowResized,this),sf.base.EventHandler.add(this.element,"contextmenu",this.mouseDownHandler,this),this.options.allowEditing&&sf.base.EventHandler.add(this.element,"dblclick",this.doubleClickHandler,this)},e.prototype.unWireEvents=function(){sf.base.EventHandler.remove(this.element,"mousedown",this.mouseDownHandler),sf.base.EventHandler.remove(this.element,"focus",this.gridFocus),sf.base.EventHandler.remove(document,"click",this.documentClickHandler),sf.base.EventHandler.remove(this.element,"keydown",this.gridKeyDownHandler),sf.base.EventHandler.remove(this.element,"keydown",this.keyDownHandler),sf.base.EventHandler.remove(document.body,"keydown",this.documentKeyHandler),sf.base.EventHandler.remove(this.element,"dblclick",this.doubleClickHandler),sf.base.EventHandler.remove(this.getContent(),"touchstart",this.tapEvent),sf.base.EventHandler.remove(window,"resize",this.windowResized),sf.base.EventHandler.remove(this.element,"contextmenu",this.mouseDownHandler)},e.prototype.windowResized=function(){var e=this;setTimeout((function(){var t=e.element.querySelector(".e-content.e-yscroll");e.options.frozenColumns&&(e.options.width,1)&&e.options.allowTextWrap&&e.freezeModule.refreshRowHeight(),!sf.base.isNullOrUndefined(t)&&t.scrollHeight>t.clientHeight?e.element.querySelector(".e-gridheader").style.paddingRight=o()-e.scrollModule.getThreshold()+"px":e.scrollModule.setPadding();var n=e.element.querySelector(".e-gridcontent"),r=n.style.height.indexOf("- ")+2,i=n.style.height.indexOf("p"),l=sf.base.isNullOrUndefined(n)||""==n.style.height?0:parseInt(n.style.height.slice(r,i));(sf.base.isNullOrUndefined(n)||""==n.style.height?0:s(n))!=l&&e.scrollModule.refresh(),e.columnChooserModule.windowResized()}),100)},e.prototype.doubleClickHandler=function(e){"TD"==e.target.tagName&&e.target.blur(),this.toolTipModule.close()},e.prototype.setOptions=function(e,t){var n=sf.base.extend(t,{});if(this.options=e,!n.allowResizing&&e.allowResizing&&this.resizeModule.render(),(!n.allowGrouping&&e.allowGrouping||!n.allowReordering&&e.allowReordering||e.showDropArea)&&(this.headerDragDrop.initializeHeaderDrag(),this.headerDragDrop.initializeHeaderDrop(),this.groupModule.initializeGHeaderDrag(),this.groupModule.initializeGHeaderDrop()),!n.allowGrouping&&e.allowGrouping&&this.contentDragDrop.initializeContentDrop(),!n.allowRowDragAndDrop&&e.allowRowDragAndDrop?this.rowDragAndDropModule.initializeDrag():n.allowRowDragAndDrop&&!e.allowRowDragAndDrop&&this.rowDragAndDropModule.destroy(),this.isRendered||(this.isRendered=this.options.isPrerendered),n.groupCount!=e.groupCount){var r=this.getHeaderTable().querySelector(".e-emptycell");if(!r)return;r.removeAttribute("indentRefreshed")}},e.prototype.documentClickHandler=function(e){var t=a(e.target,"e-cc-toolbar"),n=a(e.target,"e-gridform"),r=a(e.target,"e-toolbar-item"),i=a(e.target,"e-rowcell");if(this.virtualContentModule.selectedCellNavigation=-1,a(e.target,"e-inline-edit")){var o=a(e.target,"e-rowcell");this.editedCellIndex=null==o?null:o.cellIndex}else sf.base.isNullOrUndefined(a(e.target,"e-popup-open"))&&(this.editedCellIndex=null);this.options.enableAdaptiveUI||this.targetIsFilterDialog(e)||e.target.classList.contains("e-cc-cancel")||e.target.classList.contains("e-choosercheck")||e.target.classList.contains("e-icon-filter")||t||!this.element.querySelectorAll(".e-filter-popup.e-popup-open").length&&!this.element.querySelectorAll(".e-ccdlg.e-popup-open").length||(null!=this.element.querySelector(".e-datetimepicker")&&this.element.querySelector(".e-datetimepicker").blur(),this.dotNetRef.invokeMethodAsync("FilterPopupClose")),!sf.base.isNullOrUndefined(n)||i||r||this.targetIsFilterDialog(e)||!this.element.querySelector(".e-gridform")||this.dotNetRef.invokeMethodAsync("UpdateChanges"),sf.base.Browser.isDevice||this.toolTipModule.close()},e.prototype.targetIsFilterDialog=function(e){var t=a(a(e.target,"e-filter-popup"),"e-popup-open"),n=a(a(e.target,"e-ddl"),"e-popup-open"),r=a(a(e.target,"e-selectall"),"e-ftrchk"),i=a(e.target,"e-chkcancel-icon"),o=a(e.target,"e-datepicker"),s=a(e.target,"e-timepicker"),l=a(e.target,"e-daterangepicker")||a(e.target,"e-zoomin"),d=e.target.classList.contains("e-check")||e.target.classList.contains("e-uncheck"),u=a(a(e.target,"e-ccdlg"),"e-popup-open"),h=a(a(e.target,"e-ddt"),"e-popup-open");return!!(t||n||r||i||o||s||l||d||u||h)},e.prototype.documentKeyHandler=function(e){if(e.altKey&&74===e.keyCode&&!sf.base.isNullOrUndefined(this.element)&&(this.element.focus(),this.dotNetRef.invokeMethodAsync("GridFocus",e)),e.altKey&&87===e.keyCode&&!sf.base.isNullOrUndefined(this.element)){var t=!sf.base.isNullOrUndefined(a(e.target,"e-pager"));this.dotNetRef.invokeMethodAsync("GridKeyDown",{key:e.key,code:e.code,ctrlKey:e.ctrlKey,shiftKey:e.shiftKey,altKey:e.altKey},!1,t,this.editedCellIndex,null,null,!1,!1)}},e.prototype.iterateTemplateElementsForward=function(e){for(var t=0;t<e.length;t++){var n=e[t];if(0==n.tabIndex){this.firstFocusableTemplateElement=n;break}if(!sf.base.isNullOrUndefined(n.children)&&0!=n.children.length){this.iterateTemplateElementsForward(n.children);break}}return this.firstFocusableTemplateElement},e.prototype.iterateTemplateElementsBackward=function(e){for(var t=e.length-1;t>=0;t--){var n=e[t];if(0==n.tabIndex){this.lastFocusableTemplateElement=n;break}if(!sf.base.isNullOrUndefined(n.children)&&0!=n.children.length){this.iterateTemplateElementsBackward(n.children);break}}return this.lastFocusableTemplateElement},e.prototype.keyDownHandler=function(e){var t,n,r=a(e.target,"e-grid"),i=e.target.tagName,o=!sf.base.isNullOrUndefined(a(e.target,"e-pager")),s=!1,l=!1;if(!(r&&r.id!==this.element.id||"Shift"==e.key||"Control"==e.key||"Meta"==e.key||"Alt"==e.key)&&("INPUT"!=i&&"TEXTAREA"!=i||"Delete"!=e.code)){if(!sf.base.isNullOrUndefined(r)&&!sf.base.isNullOrUndefined(r.querySelector(".e-templatecell"))&&!sf.base.isNullOrUndefined(a(e.target,"e-templatecell"))){var d=a(e.target,"e-rowcell");if(sf.base.isNullOrUndefined(d)||!d.firstElementChild)return;t=d.cellIndex,n=Number(a(e.target,"e-row").getAttribute("data-rowIndex"));var u=d.firstElementChild.children,h=this.iterateTemplateElementsForward(u),p=this.iterateTemplateElementsBackward(u),c=!e.shiftKey&&"Tab"==e.code,f=e.shiftKey&&"Tab"==e.code,g="Escape"==e.code;(e.target==h&&f||e.target==p&&c||g||null==h&&null==p&&!e.target.classList.contains("e-templatecell")&&(c||f))&&(l=!0)}e.target.classList.contains("e-searchinput")&&"Enter"==e.key&&(s=!0);var m=this.isPopUpOpened(e);if(!m||"Escape"!=e.key&&"Enter"!=e.key||"BODY"!=document.activeElement.tagName||e.target.focus(),"Escape"!=e.key||!m||"true"!==e.target.getAttribute("aria-expanded")){sf.base.isNullOrUndefined(a(e.target,"e-rowcell"))||(this.editedCellIndex=a(e.target,"e-rowcell").cellIndex==this.editedCellIndex?this.editedCellIndex:null);var v=!1;if(!sf.base.isNullOrUndefined(a(e.target,"e-batchrow"))&&m&&"Enter"==e.key&&e.target.classList.contains("e-multiselect")){var b=document.getElementsByClassName("e-popup-open");if(b.length>0)for(var y=0;y<b.length;y++){b[y].id===e.target.getAttribute("aria-owns")&&(v=!0);break}}this.dotNetRef.invokeMethodAsync("GridKeyDown",{key:e.key,code:e.code,ctrlKey:e.ctrlKey,shiftKey:e.shiftKey,altKey:e.altKey,metaKey:e.metaKey},s,o,this.editedCellIndex,n,t,l,v)}}},e.prototype.isPopUpOpened=function(e){var t=e.target.classList.contains("e-datepicker"),n=e.target.classList.contains("e-datetimepicker"),r=e.target.classList.contains("e-timepicker"),i=e.target.classList.contains("e-daterangepicker"),o=e.target.classList.contains("e-multiselect"),s=!sf.base.isNullOrUndefined(a(e.target,"e-ddl")),l=e.target.classList.contains("e-autocomplete"),d=e.target.classList.contains("e-combobox");return t||n||r||i||o||s||l||d},e.prototype.gridKeyDownHandler=function(e){var t=a(e.target,"e-filter-popup"),n=e.target.tagName;(this.toolTipModule.close(),!sf.base.isNullOrUndefined(t)&&t.classList.contains("e-popup-open")&&"Escape"!=e.key)&&(e.stopPropagation(),"Tab"!=e.key&&"shiftTab"!=e.key&&"Enter"!=e.key&&"shiftEnter"!=e.key||"INPUT"!=n&&"TEXTAREA"!=n||((C=document.createEvent("HTMLEvents")).initEvent("change",!1,!0),e.target.dispatchEvent(C)));var r=!1;if(this.options.hasTemplateInEditSettings&&a(e.target,"e-normaledit")){var i=this.element.querySelector(".e-normaledit").querySelectorAll("input"),o=this.element.querySelector(".e-normaledit").querySelectorAll("textarea"),s=Array.from(i).concat(Array.from(o));if(r=!0,!sf.base.isNullOrUndefined(s)){var l=s[0],d=s[s.length-1],u=e.shiftKey&&"Tab"===e.key,h=!e.shiftKey&&"Tab"===e.key;(document.activeElement===l&&u||document.activeElement===d&&h)&&(r=!1)}}if("Tab"==e.key){var p=this.element.querySelector(".e-normaledit");if(!sf.base.isNullOrUndefined(p)){var c=(g=e.target).classList.contains("e-autocomplete")||g.classList.contains("e-multiselect")||g.classList.contains("e-timepicker"),f=Array.from(p.querySelectorAll(".e-rowcell")).filter((function(e){return!(e.classList.contains("e-hide")||e.querySelector(".e-disabled"))}));if(c&&("Tab"==e.key&&!e.shiftKey&&f[f.length-1]!=a(g,"e-rowcell")||"Tab"==e.key&&e.shiftKey&&f[0]!=a(g,"e-rowcell")))return}}if(("Tab"==e.key||"Escape"==e.key||"shiftTab"==e.key||"Enter"==e.key||"shiftEnter"==e.key)&&("INPUT"==n||"TEXTAREA"==n||e.target.classList.contains("e-datepicker")||e.target.classList.contains("e-datetimepicker"))&&("Tab"!==e.key&&"shiftTab"!==e.key||!(this.options.showAddNewRow||this.element.querySelectorAll(".e-filterbar").length>0))||e.target.classList.contains("e-rowcell")&&"F2"==e.key){var g=e.target;"Tab"===e.key&&(g.classList.contains("e-datepicker")||g.classList.contains("e-datetimepicker"))||"Enter"===e.key&&g.classList.contains("e-autocomplete")||g.classList.contains("e-multiselect")||g.blur()}"Shift"!=e.key&&"Control"!=e.key&&"Alt"!=e.key||e.stopPropagation();var m=/(Mac)/i.test(navigator.platform);if(67===e.keyCode&&(e.ctrlKey||m&&e.metaKey)?this.clipboardModule.copy():72===e.keyCode&&(e.ctrlKey||m&&e.metaKey)&&e.shiftKey&&this.clipboardModule.copy(!0),86===e.keyCode&&(e.ctrlKey||m&&e.metaKey)&&!this.options.isEdit){var v=a(e.target,"e-rowcell");sf.base.isNullOrUndefined(v)||v.classList.contains("e-templatecell")||e.stopPropagation(),this.clipboardModule.pasteHandler()}var b,y=this.element.querySelectorAll(".e-normaledit");if(sf.base.isNullOrUndefined(y)||y.forEach((function(t){t.contains(e.target)&&(b=t)})),!a(e.target,"e-showAddNewRow")||!e.shiftKey){if(!sf.base.isNullOrUndefined(b)&&"Tab"==e.key){f=Array.from(b.querySelectorAll(".e-rowcell")).filter((function(e){return!(e.classList.contains("e-hide")||e.querySelector(".e-disabled"))}));if(r)return;(!e.shiftKey&&f[f.length-1]==a(e.target,"e-rowcell")||e.shiftKey&&f[0]==a(e.target,"e-rowcell"))&&(this.dotNetRef.invokeMethodAsync("EndEdit",{key:e.key,code:e.code,ctrlKey:e.ctrlKey,shiftKey:e.shiftKey,altKey:e.altKey}),e.preventDefault())}var C;if(this.element.querySelector(".e-batchrow"))if("Tab"==e.key||"shiftTab"==e.key||"Enter"==e.key||"shiftEnter"==e.key)if(e.preventDefault(),"INPUT"==n||"TEXTAREA"==n)(C=document.createEvent("HTMLEvents")).initEvent("change",!1,!0),e.target.dispatchEvent(C);if("Cell"==this.options.selectionMode&&"Batch"==this.options.editMode&&a(e.target,"e-gridform")){var w=a(a(e.target,"e-gridform"),"e-row").querySelectorAll(".e-rowcell:not(.e-hide)"),S=a(e.target,"e-rowcell");if(e.shiftKey||"Tab"!=e.key){if(e.shiftKey&&"Tab"==e.key)for(x=w.length-1;x>=0;x--){if(w[x].cellIndex==S.cellIndex){x>0&&(w[x-1].tabIndex=0);break}}}else for(var x=0;x<w.length;x++){if(w[x].cellIndex==S.cellIndex){x<w.length-1&&(w[x+1].tabIndex=0);break}}}}},e.prototype.mouseDownHandler=function(e){var t=a(e.target,"e-grid");if(!t||t.id===this.element.id)if(this.options.enableAdaptiveUI||this.targetIsFilterDialog(e)||!e.target.classList.contains("e-content")||!this.element.querySelectorAll(".e-filter-popup.e-popup-open").length&&!this.element.querySelectorAll(".e-ccdlg.e-popup-open").length||this.dotNetRef.invokeMethodAsync("FilterPopupClose"),(e.shiftKey||e.ctrlKey)&&e.preventDefault(),-1!==e.button&&2!==e.button&&a(e.target,"e-headercell")||2!==e.button&&a(e.target,"e-detailcell")||a(e.target,"e-detailrowexpand")||a(e.target,"e-detailrowcollapse")||e.target.classList.contains("e-headercontent")||sf.base.closest(e.target,".e-groupdroparea")||sf.base.closest(e.target,".e-gridpopup")||sf.base.closest(e.target,".e-summarycell")||sf.base.closest(e.target,".e-rhandler")||sf.base.closest(e.target,".e-filtermenudiv")||sf.base.closest(e.target,".e-filterbarcell")||sf.base.closest(e.target,".e-groupcaption"))this.dotNetRef.invokeMethodAsync("MouseDownHandler",null,null);else{var n=null,r=null,i=a(a(e.target,"e-gridform"),"e-grid");a(e.target,"e-editcell")||i&&i.id==t.id?n="Edit":a(e.target,"e-pager")?n="Pager":a(e.target,"e-headercontent")?(n="Header",r=a(e.target,"e-headercell")?a(e.target,"e-headercell").getAttribute("data-uid"):null):a(e.target,"e-content")&&(n="Content",r=a(e.target,"e-rowcell")?a(e.target,"e-rowcell").getAttribute("data-uid"):null),"Header"!=n&&"Content"!=n&&"Pager"!=n&&"Edit"!=n||this.dotNetRef.invokeMethodAsync("MouseDownHandler",n,r)}},e.prototype.gridFocus=function(e){!sf.base.isNullOrUndefined(this.element.querySelector(".e-gridform"))&&this.element.querySelector(".e-gridform").classList.contains("e-editing")||this.dotNetRef.invokeMethodAsync("GridFocus",e)},e.prototype.keyActionHandler=function(e){var t,n=this,r=e.target.tagName,i=!1;t=!sf.base.isNullOrUndefined(this.element.querySelector(".e-gridform"))&&this.element.querySelector(".e-gridform").classList.contains("e-editing")&&"SELECT"==r,("pageUp"===e.action||"pageDown"===e.action||"ctrlAltPageUp"===e.action||"ctrlAltPageDown"===e.action||"altPageUp"===e.action||"altPageDown"===e.action||"altDownArrow"===e.action&&!t||"ctrlPlusP"===e.action)&&e.preventDefault();var o=this.isPopUpOpened(e),s=a(e.target,"e-autocomplete");if(!sf.base.isNullOrUndefined(s)){var l=s.id+"_popup";if(!sf.base.isNullOrUndefined(document.getElementById(l)))return}if((!a(e.target,"e-unboundcelldiv")||"enter"!==e.action||!e.target.classList.contains("e-Savebutton"))&&(sf.base.isNullOrUndefined(this.element.querySelector("tr.e-showAddNewRow"))||!a(e.target,"e-filterbarcell")&&!e.target.classList.contains("e-searchinput"))){var d=this.element.querySelectorAll(".e-gridform");sf.base.isNullOrUndefined(d)||d.forEach((function(t){(t.classList.contains("e-editing")||t.classList.contains("e-adding")&&a(e.target,"e-showAddNewRow"))&&(i=!0)})),"enter"===e.action&&i&&"Batch"!==this.options.editMode&&(o&&"false"===e.target.getAttribute("aria-expanded")||!o)&&setTimeout((function(){e.target.blur(),n.dotNetRef.invokeMethodAsync("EndEdit",{key:e.key,code:e.code,ctrlKey:e.ctrlKey,shiftKey:e.shiftKey,altKey:e.altKey})}),40)}},e.prototype.destroy=function(e){this.unWireEvents(),this.frozenDragDropModule.unwireEvents(),e&&this.virtualContentModule.removeEventListener(),this.addScrollEvents(!1),this.toolTipModule.destroy(),this.keyModule.destroy(),this.columnChooserModule.removeMediaListener(),this.selectionModule.removeEventListener(),this.rowDragAndDropModule.destroy(),this.headerDragDrop.destroy(),this.scrollModule.destroy(),window.sfBlazor.disposeWindowsInstance(this.dataId)},e.prototype.getColumnIndexesInView=function(){return this.inViewIndexes},e.prototype.setColumnIndexesInView=function(e){this.inViewIndexes=e},e.prototype.getRowHeight=function(){return this.options.rowHeight?this.options.rowHeight:function(e){if(void 0!==r)return r;var t=sf.base.createElement("table",{className:"e-table",styles:"visibility: hidden"});t.innerHTML='<tr><td class="e-rowcell">A<td></tr>',e.appendChild(t);var n=t.querySelector("td").getBoundingClientRect();return e.removeChild(t),r=Math.ceil(n.height)}(this.element)},e.prototype.clientActions=function(){!this.options.enableVirtualization&&!this.options.enableColumnVirtualization||12!==this.options.pageSize&&"auto"!==this.options.width||this.virtualContentModule.ensurePageSize(),this.getColumns().some((function(e){return""!==e.hideAtMedia}))&&this.columnChooserModule.setMediaColumns()},e.prototype.print=function(){this.removeColGroup();var e=window.open("","print","height="+window.outerHeight+",width="+window.outerWidth+",tabbar=no");e.moveTo(0,0),e.resizeTo(screen.availWidth,screen.availHeight),sf.base.print(this.element,e)},e.prototype.removeColGroup=function(){var e=this.options.groupCount,t=this.element,n="#"+this.element.id;if(e){for(var r=t.querySelectorAll(".e-groupcaption"),i=r[e-1].getAttribute("colspan"),o=0;o<r.length;o++)r[o].setAttribute("colspan",i);var s=t.querySelectorAll("colgroup"+n+"colGroup"),l=t.querySelector(".e-content").querySelectorAll("colgroup");this.hideColGroup(s,e),this.hideColGroup(l,e)}},e.prototype.hideColGroup=function(e,t){for(var n=0;n<e.length;n++)for(var r=0;r<t;r++)e[n].children[r].style.display="none"},e.prototype.getModuleName=function(){return"grid"},e}(),Q={pageUp:"pageup",pageDown:"pagedown",ctrlAltPageUp:"ctrl+alt+pageup",ctrlAltPageDown:"ctrl+alt+pagedown",altPageUp:"alt+pageup",altPageDown:"alt+pagedown",altDownArrow:"alt+downarrow",altUpArrow:"alt+uparrow",ctrlDownArrow:"ctrl+downarrow",ctrlUpArrow:"ctrl+uparrow",ctrlPlusA:"ctrl+A",ctrlPlusP:"ctrl+P",ctrlPlusC:"ctrl+C",ctrlShiftPlusH:"ctrl+shift+H",enter:"enter"};return{initialize:function(e,t,n,r){sf.base.enableBlazorMode(),new J(e,t,n,r)},contentReady:function(e,t,n,r){var i=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(i.element)&&!sf.base.isNullOrUndefined(i.element.blazor__instance)){var o=i.element.blazor__instance;if(o.setOptions(t,o.options),o.options=t,o.scrollModule.setPadding(),o.contentReady(n,r),o.options.isColumnWidthChanged)new b(o).setWidthToTable();"100%"==t.height&&o.scrollModule.refresh()}},refreshPivotRowHeight:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t.element)||sf.base.isNullOrUndefined(t.element.blazor__instance)||t.element.blazor__instance.freezeModule.refreshFreeze({case:"textwrap"})},customFilterDialog:function(e,t,n){var r=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(r.element)&&!sf.base.isNullOrUndefined(r.element.blazor__instance)){var i=document.querySelector("#"+t);if(i.style.maxHeight="100%",i.style.border="1px",i.style.top="0px",n)r.element.querySelector(".e-sfcontextmenu").querySelector(".e-caret").style.paddingRight="8px"}},setCustomFilterDialogPadding:function(e,t){document.querySelector("#"+t).style.padding="16px"},searchClear:function(e,t,n){var r=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(r.element)&&!sf.base.isNullOrUndefined(r.element.blazor__instance)){var i=document.querySelector("#"+t);i.value=sf.base.isNullOrUndefined(n)?"":n,i.focus()}},updateTableWidth:function(e,t){var n=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(n.element)&&!sf.base.isNullOrUndefined(n.element.blazor__instance)){var r=n.element.blazor__instance;if(r.options.columns=t,r.options.allowResizing&&r.options.isResizedGrid){var i=new b(r),o=t.some((function(e){return""==e.width||null==e.width}));i.setWidthToTable(t,o)}}},preventResizeAction:function(e,t){var n=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(n.element)||sf.base.isNullOrUndefined(n.element.blazor__instance)||n.element.blazor__instance.resizeModule.preventResizeAction(t)},preventFreezeLineMoving:function(e,t){var n=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(n.element)||sf.base.isNullOrUndefined(n.element.blazor__instance)||n.element.blazor__instance.frozenDragDropModule.preventFreezeLineMoving(t)},freezeLineMovedActions:function(e,t){var n=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(n.element)&&!sf.base.isNullOrUndefined(n.element.blazor__instance)){var r=n.element.blazor__instance;r.options.actualFrozenColumns=t.actualFrozenColumns,r.options.columns=t.columns,r.options.frozenRightCount=t.frozenRightCount,r.options.frozenLeftCount=t.frozenLeftCount,r.options.frozenLeftColumnsCount=t.frozenLeftColumnsCount,r.options.frozenColumns=t.frozenColumns,r.options.isColumnReordered=t.isColumnReordered,r.freezeLineMovedAction(),r.freezeModule.setFrozenHeight()}},frozenHeight:function(e,t,n){var r=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(r.element)&&!sf.base.isNullOrUndefined(r.element.blazor__instance)){var i=r.element.blazor__instance;i.freezeModule.setFrozenHeight(),t.allowTextWrap&&(i.freezeModule.refreshRowHeight(),i.freezeModule.refreshFreeze({case:"textwrap"})),t.allowResizing&&i.freezeModule.updateResizeHandler()}},updateVirtualColumns:function(e,t){var n=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(n.element)&&!sf.base.isNullOrUndefined(n.element.blazor__instance)){var r=n.element.blazor__instance;if(r.options.virtualizedColumns=t,r.options.allowResizing&&0==r.options.frozenColumns&&""!=r.getContent().querySelector("table").style.width)new b(r).setWidthToTable(null,!1,"resize")}},updateOptions:function(e,t){var n=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(n.element)&&!sf.base.isNullOrUndefined(n.element.blazor__instance)){var r=n.element.blazor__instance;r.setOptions(t,r.options)}},virtualHeight:function(e,t,n){var r=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(r.element)&&!sf.base.isNullOrUndefined(r.element.blazor__instance)){var i=r.element.blazor__instance;i.options=t,i.options.totalItemCount=n,i.virtualContentModule.refreshOffsets(),i.virtualContentModule.setVirtualHeight()}},lazyGroupExpand:function(e,t){var n=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(n.element)&&!sf.base.isNullOrUndefined(n.element.blazor__instance)){var r=n.element.blazor__instance;r.setOptions(t,r.options),r.options=t,r.options.enableVirtualization&&r.virtualContentModule.onDataReady(),r.options.enableInfiniteScrolling&&(r.scrollModule.infiniteOnDataReady(),r.scrollModule.isLazyChidLoad=!1)}},viewRefresh:function(e,t){var n=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(n.element)&&!sf.base.isNullOrUndefined(n.element.blazor__instance)){var r=void 0;r=new b(n);var i=(t=t.filter((function(e){return e.visible}))).some((function(e){return""==e.width||null==e.width}));n.parent.getColumns(),n.options.frozenColumns&&n.parent.updateColumnLevelFrozen(),r.setWidthToTable(t,i)}},virtualDisconnect:function(e,t){var n=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(n.element)||sf.base.isNullOrUndefined(n.element.blazor__instance)||sf.base.isNullOrUndefined(n.element.blazor__instance.virtualContentModule)||(n.element.blazor__instance.options.enableVirtualization=t.enableVirtualization,n.element.blazor__instance.virtualContentModule.observer.disconnect())},reorderColumns:function(e,t,n){var r=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(r.element)||sf.base.isNullOrUndefined(r.element.blazor__instance)||r.element.blazor__instance.reorderModule.reorderColumns(t,n)},reorderColumnByIndex:function(e,t,n){var r=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(r.element)||sf.base.isNullOrUndefined(r.element.blazor__instance)||r.element.blazor__instance.reorderModule.reorderColumnByIndex(t,n)},reorderColumnByTargetIndex:function(e,t,n){var r=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(r.element)||sf.base.isNullOrUndefined(r.element.blazor__instance)||r.element.blazor__instance.reorderModule.reorderColumnByTargetIndex(t,n)},renderColumnChooser:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t.element)||sf.base.isNullOrUndefined(t.element.blazor__instance)||t.element.blazor__instance.columnChooserModule.renderColumnChooser()},renderColumnMenu:function(e,t,n,r){var i=window.sfBlazor.getCompInstance(e);return sf.base.isNullOrUndefined(i.element)||sf.base.isNullOrUndefined(i.element.blazor__instance)?{Left:1,Top:1}:i.element.blazor__instance.columnMenuModule.renderColumnMenu(t,n,r)},renderAdaptiveMenuItems:function(e){var t=window.sfBlazor.getCompInstance(e),n=document.getElementsByClassName("e-"+t.element.id+"-column-menu")[0],r=sf.base.isNullOrUndefined(n)?null:n.getElementsByTagName("ul")[0],i=document.getElementById(t.element.id+"_responsivetoolbaritems"),s=i.getBoundingClientRect(),l=s.left+pageXOffset,a=s.bottom+pageYOffset,d=r.getBoundingClientRect(),u=document.documentElement;s.bottom+d.height>u.clientHeight&&a-s.height-d.height>u.clientTop&&(a=a-s.height-d.height),s.left+d.width>u.clientWidth&&s.right-d.width>u.clientLeft&&(l=l+s.width-d.width),l="0"==i.getAttribute("data-index")?l-r.getBoundingClientRect().width+d.width:l-r.getBoundingClientRect().width+s.width-o(),n.style.left=Math.ceil(l+1)+"px",n.style.top=Math.ceil(a+1)+"px"},filterPopupRender:function(e,t,n,r,i){var o=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(o.element)||sf.base.isNullOrUndefined(o.element.blazor__instance)||o.element.blazor__instance.filterModule.filterPopupRender(t,n,r,i)},clientHeight:function(e){var t=window.sfBlazor.getCompInstance(e);return sf.base.isNullOrUndefined(t.element)||sf.base.isNullOrUndefined(t.element.blazor__instance)?0:Math.max(t.element.blazor__instance.content.clientHeight,window.innerHeight||0)},clientTransformUpdate:function(e,t,n,r){void 0===r&&(r=!1);var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i.element)||sf.base.isNullOrUndefined(i.element.blazor__instance)||i.element.blazor__instance.virtualContentModule.updateTransform(t,n,r)},autoFitColumns:function(e,t,n,r,i){void 0===i&&(i=!1);var o=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(o.element)&&!sf.base.isNullOrUndefined(o.element.blazor__instance)){var s=o.element.blazor__instance;if(r&&(s.options.columns=t,s.resizeModule.autoFitColumns(n)),i)new b(s).setPersistedWidth(t[0])}},autoFit:function(e){var t=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(t.element)&&!sf.base.isNullOrUndefined(t.element.blazor__instance)){var n=t.element.blazor__instance;n.resizeModule.autoFit(),n.scrollModule.setPadding()}},refreshColumnIndex:function(e,t){var n=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(n.element)&&!sf.base.isNullOrUndefined(n.element.blazor__instance)){var r=n.element.blazor__instance;r.options.columns=t,r.virtualContentModule.refreshColumnIndexes()}},focus:function(e,t,n,r,i){var o=window.sfBlazor.getCompInstance(e),s=o.element.querySelector('[data-uid="'+n+'"]'),l=s.classList.contains("e-recordplusexpand")||s.classList.contains("e-recordpluscollapse");if(!sf.base.isNullOrUndefined(o.element)&&!sf.base.isNullOrUndefined(o.element.blazor__instance)&&!sf.base.isNullOrUndefined(s)){var a=o.element.blazor__instance;!a.options.enableVirtualization||a.options.enableVirtualization&&!sf.base.isNullOrUndefined(r)&&("UpdateRecord"===r||"ScrollSelect"===r)||a.options.enableColumnVirtualization&&!sf.base.isNullOrUndefined(r)&&"ScrollSelect"===r||l?s.focus():a.virtualContentModule.focusCell(s,r,i)}},handleCheckBoxSelection:function(e){var t=window.sfBlazor.getCompInstance(e);if(t.element.querySelectorAll(".e-ftrchk.e-chkfocus").length>0)for(var n=t.element.querySelectorAll(".e-ftrchk.e-chkfocus"),r=a(document.activeElement,"e-ftrchk"),i=0;i<n.length;i++)(sf.base.isNullOrUndefined(r)||n[i].getAttribute("uid")!=r.getAttribute("uid")&&!r.classList.contains("e-selectall"))&&n[i].classList.remove("e-chkfocus")},focusFilterCheckBox:function(e,t,n){var r=window.sfBlazor.getCompInstance(e),i=r.element.querySelector(".e-selectall");n?(i.classList.contains("e-chkfocus")||a(i,"e-ftrchk").classList.add("e-chkfocus"),r.element.querySelector(".e-selectall").focus()):r.element.querySelector('[uid="'+t+'"]').querySelector(".e-chk-hidden").focus()},focusFilterDialogElements:function(e,t){var n=window.sfBlazor.getCompInstance(e),r=document.activeElement,i=n.element.querySelectorAll(".e-ftrchk"),o="Tab"==t||"ShiftTab"==t;if(n.element.querySelectorAll(".e-ftrchk.e-chkfocus").length>0)for(var s=n.element.querySelectorAll(".e-ftrchk.e-chkfocus"),l=0;l<s.length;l++)s[l].classList.remove("e-chkfocus");if(r.classList.contains("e-chk-hidden")&&o&&!sf.base.isNullOrUndefined(a(r,"e-ftrchk")))a(document.activeElement,"e-ftrchk").classList.add("e-chkfocus");else if("ArrowDown"!=t||sf.base.isNullOrUndefined(a(r,"e-searchbox")))if("ArrowDown"!=t&&"ArrowUp"!=t||!r.classList.contains("e-chk-hidden")){if(("ArrowDown"==t||"ArrowUp"==t)&&r.classList.contains("e-primary")&&r.classList.contains("e-flat")){!sf.base.isNullOrUndefined(n.element.querySelector(".e-flat:not(.e-primary)"))&&"ArrowDown"==t?n.element.querySelector(".e-flat:not(.e-primary)").focus():"ArrowUp"==t&&(i[i.length-1].classList.add("e-chkfocus"),i[i.length-1].querySelector(".e-chk-hidden").focus())}else if("ArrowUp"==t&&r.classList.contains("e-flat")&&!r.classList.contains("e-primary")){!sf.base.isNullOrUndefined(n.element.querySelector(".e-primary.e-flat"))&&n.element.querySelector(".e-primary.e-flat").focus()}}else{var d=Array.from(a(r,"e-checkboxlist").children),u=d.indexOf(a(document.activeElement,"e-ftrchk"));if(u<d.length-1&&"ArrowDown"==t)d[u+1].classList.add("e-chkfocus"),d[u+1].querySelector(".e-chk-hidden").focus();else if(u==d.length-1&&"ArrowDown"==t){!sf.base.isNullOrUndefined(n.element.querySelector(".e-primary.e-flat"))&&n.element.querySelector(".e-primary.e-flat").focus()}else if(u-1>=0&&"ArrowUp"==t)d[u-1].classList.add("e-chkfocus"),d[u-1].querySelector(".e-chk-hidden").focus();else if("ArrowUp"==t&&i[0].querySelector(".e-chk-hidden")==r){!sf.base.isNullOrUndefined(n.element.querySelector(".e-searchinput.e-input"))&&n.element.querySelector(".e-searchinput.e-input").focus()}}else n.element.querySelector(".e-ftrchk").classList.add("e-chkfocus"),n.element.querySelector(".e-ftrchk").querySelector(".e-chk-hidden").focus()},blurActiveElement:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t.element)||sf.base.isNullOrUndefined(t.element.blazor__instance)||sf.base.isNullOrUndefined(a(document.activeElement,"e-grid"))||document.activeElement.blur()},iterateElements:function(e,t,n){for(var r=e.children.length-1;r>=0;r--){if(e.children[r].classList.contains("sf-grid")&&!n){var i=sf.base.isNullOrUndefined(t.querySelector(".e-pagercontainer")),o=sf.base.isNullOrUndefined(t.querySelector(".e-lastpage"));return void(i?t.querySelectorAll(".e-rowcell:not(.e-hide)")[t.querySelectorAll(".e-rowcell:not(.e-hide)").length-1].focus():i||o?!i&&o&&t.querySelectorAll(".e-numericitem")&&t.querySelectorAll(".e-numericitem")[t.querySelectorAll(".e-numericitem").length-1].focus():t.querySelector(".e-lastpage").focus())}if(0==e.children[r].tabIndex)return void e.children[r].focus();if(!sf.base.isNullOrUndefined(e.children[r].children)&&0!=e.children[r].children.length&&(this.iterateElements(e.children[r],t,n),!document.activeElement.classList.contains("e-detailcell")))return}},focusDetailTemplateElements:function(e,t,n){var r=window.sfBlazor.getCompInstance(e);if(!(sf.base.isNullOrUndefined(r.element)&&sf.base.isNullOrUndefined(r.element.blazor__instance)&&sf.base.isNullOrUndefined(a(document.activeElement,"e-grid")))){var i=document.activeElement.querySelector(".e-grid"),o=sf.base.isNullOrUndefined(i);if("Tab"==t)document.activeElement.blur();else if("ShiftTab"==t){var s=a(document.activeElement,"e-detailcell").firstElementChild;if(0!=s.children.length&&n&&this.iterateElements(s,i,o),document.activeElement.classList.contains("e-detailcell")){var l=document.activeElement.parentElement.previousElementSibling.querySelectorAll(".e-rowcell:not(.e-hide)");return void l[l.length-1].focus()}}}},updateFilterBarCell:function(e,t,n){var r=window.sfBlazor.getCompInstance(e).element.querySelector(".e-filterbar");sf.base.isNullOrUndefined(r)||t.forEach((function(e,t){var i=r.querySelectorAll("#"+e+"_filterBarcell");i.length>0&&(i[0].value=n[t])}))},focusFilterBar:function(e,t,n,r){var i=window.sfBlazor.getCompInstance(e),o=i.element.querySelector(".e-filterbar");if(!sf.base.isNullOrUndefined(i.element)&&!sf.base.isNullOrUndefined(i.element.blazor__instance)&&!sf.base.isNullOrUndefined(o))if("Tab"==t){if(document.activeElement.classList.contains("e-headercell")){var s=o.querySelector(".e-textbox.e-input:not([disabled])");if(n){var l=o.querySelector(".e-fltrinputdiv").querySelector("input");sf.base.isNullOrUndefined(l)?o.querySelector(".e-fltrinputdiv").children[0].focus():l.focus()}else sf.base.isNullOrUndefined(s)||s.focus()}}else if("ShiftTab"==t){if(document.activeElement.classList.contains("e-rowcell")||document.activeElement.classList.contains("e-recordplusexpand")){s=o.querySelectorAll(".e-textbox.e-input:not([disabled])");if(n){var a=o.querySelectorAll(".e-fltrinputdiv"),d=a[a.length-1].querySelector("input");sf.base.isNullOrUndefined(d)?a[a.length-1].children[0].focus():d.focus()}else sf.base.isNullOrUndefined(s)||s[s.length-1].focus()}}else if("ArrowUp"==t||"ArrowDown"==t)if(document.activeElement.classList.contains("e-groupcaption")||document.activeElement.classList.contains("e-recordplusexpand"))o.querySelector(".e-textbox.e-input:not([disabled])").focus();else if(n){l=o.querySelectorAll(".e-fltrinputdiv")[-1==r?document.activeElement.getAttribute("data-colIndex"):r].querySelector("input");sf.base.isNullOrUndefined(l)?o.querySelectorAll(".e-fltrinputdiv")[-1==r?document.activeElement.getAttribute("data-colIndex"):r].children[0].focus():l.focus()}else o.querySelectorAll(".e-input")[-1==r?document.activeElement.getAttribute("data-colIndex"):r].focus()},focusAddForm:function(e,t){var n=window.sfBlazor.getCompInstance(e),r=n.element.querySelector(".e-showAddNewRow");if(!sf.base.isNullOrUndefined(n.element)&&!sf.base.isNullOrUndefined(n.element.blazor__instance)&&!sf.base.isNullOrUndefined(r))if("Tab"==t){if(document.activeElement.classList.contains("e-headercell")){var i=r.querySelector(".e-input:not([disabled])");sf.base.isNullOrUndefined(i)||i.focus()}}else if("ShiftTab"==t){if(document.activeElement.classList.contains("e-rowcell")||document.activeElement.classList.contains("e-recordplusexpand")||document.activeElement.classList.contains("e-recordpluscollapse")){var o=r.querySelectorAll(".e-input:not([disabled])"),s=r.querySelectorAll(".e-rowcell:not(.e-hide)"),l=[];s.forEach((function(e){var t=e.querySelector(".e-input:not([disabled])");t&&Array.from(o).some((function(e){return e===t}))&&l.push(t)})),sf.base.isNullOrUndefined(s)||l[l.length-1].focus()}}else if("ArrowUp"==t||"ArrowDown"==t)if(document.activeElement.classList.contains("e-groupcaption")||document.activeElement.classList.contains("e-recordplusexpand")||document.activeElement.classList.contains("e-recordpluscollapse")){i=r.querySelector(".e-input:not([disabled])");sf.base.isNullOrUndefined(i)||i.focus()}else{var a=document.activeElement.getAttribute("data-colIndex"),d=0;null!==a&&(d=parseInt(a,10)),r.querySelectorAll(".e-input")[d].focus()}},focusFirstGroupHeader:function(e){var t=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(t.element)&&!sf.base.isNullOrUndefined(t.element.blazor__instance)){var n=t.element.querySelector(".e-groupheadercell");sf.base.isNullOrUndefined(n)||n.focus()}},focusExcelInput:function(e,t){var n=window.sfBlazor.getCompInstance(e),r=document.querySelector("#"+t+"_excelDlg");sf.base.isNullOrUndefined(n.element)||sf.base.isNullOrUndefined(n.element.blazor__instance)||sf.base.isNullOrUndefined(r)||setTimeout((function(){r.querySelector("#"+n.element.id+"_SearchBox").focus()}),10)},refreshOnDataChange:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t.element)||sf.base.isNullOrUndefined(t.element.blazor__instance)||t.element.blazor__instance.virtualContentModule.refreshOnDataChange()},updateAutofillPosition:function(e,t,n){var r=window.sfBlazor.getCompInstance(e);return sf.base.isNullOrUndefined(r.element)||sf.base.isNullOrUndefined(r.element.blazor__instance)?null:r.element.blazor__instance.selectionModule.updateAutofillPosition(t,n)},createBorder:function(e,t,n){var r=window.sfBlazor.getCompInstance(e);return sf.base.isNullOrUndefined(r.element)||sf.base.isNullOrUndefined(r.element.blazor__instance)?null:r.element.blazor__instance.selectionModule.createBorder(t,n)},removePersistItem:function(e,t){var n=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(n.element)&&!sf.base.isNullOrUndefined(n.element.blazor__instance)){var r=n.element.blazor__instance;r.getHeaderTable().style.width="",r.getContentTable().style.width="",0!=r.options.aggregatesCount&&(r.getFooterContent().querySelector(".e-table").style.width=""),r.options.frozenColumns>0&&(r.element.querySelector(".e-movableheader").querySelector(".e-table").style.width="",r.element.querySelector(".e-movablecontent").querySelector(".e-table").style.width="")}localStorage.removeItem(t)},focusChild:function(e,t,n){var r=window.sfBlazor.getCompInstance(e),i=r.element.querySelector('[data-uid="'+n+'"]').firstElementChild.children,o=r.iterateTemplateElementsForward(i);return sf.base.isNullOrUndefined(o)?r.element.querySelector('[data-uid="'+n+'"]').focus():o.focus(),!sf.base.isNullOrUndefined(o)},exportSave:function(e,t){if(navigator.msSaveBlob){for(var n=window.atob(t),r=new Uint8Array(n.length),i=0;i<n.length;i++)r[i]=n.charCodeAt(i);var o=new Blob([r.buffer],{type:"application/octet-stream"});navigator.msSaveBlob(o,e)}else{var s=document.createElement("a");s.download=e,s.href="data:application/octet-stream;base64,"+t,document.body.appendChild(s),s.click(),document.body.removeChild(s)}},destroy:function(e,t){var n=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(n.element)||sf.base.isNullOrUndefined(n.element.blazor__instance)||n.element.blazor__instance.destroy(t)},validation:function(e,t,n){var r=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(r.element)||sf.base.isNullOrUndefined(r.element.blazor__instance)||r.element.blazor__instance.editModule.createTooltip(t,n)},focusCell:function(e,t,n,r){void 0===r&&(r=!1);var i=window.sfBlazor.getCompInstance(e);!n||sf.base.isNullOrUndefined(i.element)||sf.base.isNullOrUndefined(i.element.blazor__instance)||!i.element.blazor__instance.options.frozenColumns||i.element.blazor__instance.options.showAddNewRow||(i.element.querySelector(".e-frozencontent").style.height=i.element.querySelector(".e-movablecontent").getBoundingClientRect().height+"px");var o="#"+t.replace(/[.]/g,"___");if(o+=":not(.e-disabled)",r)for(var s=[].slice.call(i.element.querySelectorAll("form")),l=void 0,a=0;a<s.length;a++)(l=s[a].querySelector("td:not(.e-hide)")).style.height=sf.base.closest(l,".e-row").getBoundingClientRect().height+"px";var d=i.element.querySelector(o);if(""!==t&&null===d&&o.includes("___")&&(o=o.split("___").pop(),d=i.element.querySelector("#"+o)),""===t&&i.element.querySelector("input.e-boolcell"))i.element.querySelector("input.e-boolcell").focus();else if(""!==t&&d){var u=i.getContent().querySelector("tbody").classList.contains("e-addedrow"),h=i.options.enableVirtualization&&i.options.allowEditing&&i.options.isEdit&&!u;d.focus({preventScroll:h})}},CurrentPageFocus:function(e,t,n){var r=window.sfBlazor.getCompInstance(e).element.querySelector(".e-numericcontainer");"PreviousPage"==t||r.querySelectorAll(".e-link:last-child")[0].innerText!=n?r.querySelector(".e-link").focus():r.querySelectorAll(".e-link:last-child")[0].focus()},pagerFocus:function(e,t){var n=window.sfBlazor.getCompInstance(e).element.querySelector(".e-gridpager").querySelector(".e-pagercontainer"),r=n.querySelector(".e-numericcontainer"),i=n.querySelector(".e-firstpage.e-pager-default"),o=n.querySelector(".e-prevpage.e-pager-default");if("ArrowDown"===t)return i?(i.focus(),"FirstPage"):o?(i.focus(),"PreviousPage"):(r.querySelectorAll(".e-link")[1].focus(),"1");if("ArrowRight"==t)return null!=i&&i.classList.contains("e-focused")?(o.focus(),"PreviousPage"):null!=o&&o.classList.contains("e-focused")||null!=n.querySelector(".e-pp.e-focused")?null==n.querySelector(".e-pp")||n.querySelector(".e-pp").classList.contains("e-focused")?(r.querySelectorAll(".e-link")[0].focus(),r.querySelectorAll(".e-link")[0].innerText):(n.querySelector(".e-pp").focus(),"PreviousPagerCount"):r.querySelectorAll(".e-link.e-focused").length>0&&null!=n.querySelector(".e-link.e-focused")&&null!=n.querySelector(".e-link.e-focused").nextElementSibling?(r.querySelector(".e-link.e-focused").nextElementSibling.focus(),r.querySelector(".e-link.e-focused").nextElementSibling.innerText):r.querySelectorAll(".e-link.e-focused").length>0&&null!=n.querySelector(".e-np")&&null==n.querySelector(".e-np.e-focused")?(n.querySelector(".e-np").focus(),"NextPagerCount"):r.querySelectorAll(".e-link.e-focused").length>0||n.querySelectorAll(".e-np.e-focused").length>0?null!=n.querySelector(".e-nextpage")?(n.querySelector(".e-nextpage").focus(),"NextPage"):(r.querySelector(".e-link.e-focused").focus(),r.querySelector(".e-link.e-focused").innerText):(n.querySelector(".e-nextpage.e-focused"),n.querySelector(".e-lastpage").focus(),"LastPage");if("ArrowLeft"==t){if(null!=o&&o.classList.contains("e-focused"))return i.focus(),"FirstPage";if(o&&n.querySelector(".e-pp.e-focused"))return o.focus(),"PreviousPage";if(r.querySelectorAll(".e-link")[0].classList.contains("e-focused"))return null!=n.querySelector(".e-pp")?(n.querySelector(".e-pp").focus(),"PreviousPagerCount"):o?(o.focus(),"PreviousPage"):(r.querySelectorAll(".e-link")[0].focus(),"1");if(r.querySelectorAll(".e-link.e-focused").length>0)return r.querySelector(".e-link.e-focused").previousElementSibling.focus(),r.querySelector(".e-link.e-focused").previousElementSibling.innerText;if(n.querySelectorAll(".e-nextpage.e-focused").length>0&&null!=n.querySelector(".e-np"))return n.querySelector(".e-np").focus(),"NextPagerCount";if(n.querySelectorAll(".e-nextpage.e-focused").length>0||n.querySelectorAll(".e-np.e-focused").length>0){var s=r.querySelectorAll(".e-link").length;return r.querySelectorAll(".e-link")[s-1].focus(),r.querySelectorAll(".e-link:last-child")[0].innerText}return null!=n.querySelector(".e-lastpage.e-focused")?(n.querySelector(".e-nextpage").focus(),"NextPage"):i.classList.contains(".e-disabled")?"0":(i.focus(),"FirstPage")}return"0"},setFrozenHeight:function(e){e.querySelector(".e-frozencontent").style.height=e.querySelector(".e-movablecontent").offsetHeight-o()+"px"},printGrid:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t.element)||sf.base.isNullOrUndefined(t.element.blazor__instance)||t.element.blazor__instance.print()},updateMediaColumns:function(e,t){var n=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(n.element)||sf.base.isNullOrUndefined(n.element.blazor__instance)||n.element.blazor__instance.columnChooserModule.updateMediaColumns(t)},copyToClipBoard:function(e,t){var n=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(n.element)||sf.base.isNullOrUndefined(n.element.blazor__instance)||n.element.blazor__instance.clipboardModule.copy(t)},preventCopyToClipBoard:function(e,t,n,r){var i=window.sfBlazor.getCompInstance(e),o=i.clipboardModule;sf.base.isNullOrUndefined(i.element)||sf.base.isNullOrUndefined(i.element.blazor__instance)||("Copy"===r?o.clipBoardData(t,n):"Paste"===r&&o.pasteAction(n,o.getSelectedRowCellIndexes()[0].rowIndex,o.getSelectedRowCellIndexes()[0].cellIndexes[0],t))},preventPasteAction:function(e,t,n,r,i,o){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s.element)||sf.base.isNullOrUndefined(s.element.blazor__instance)||s.element.blazor__instance.clipboardModule.pasteData(t,n,r,i,o)},setMediaColumns:function(e,t){var n=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(n.element)||sf.base.isNullOrUndefined(n.element.blazor__instance)||n.element.blazor__instance.columnChooserModule.setMediaColumns(t)},gridFocus:function(e){var t=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(t.element)){var n=t.element.querySelectorAll(".e-groupheadercell").length;if(0!=t.element.querySelectorAll(".e-toolbar-item").length){var r=t.element.querySelectorAll(".e-toolbar-item:not(.e-overlay)"),i=null;if(!sf.base.isNullOrUndefined(r)&&0!=r.length){var o=r.length-1;if(!sf.base.isNullOrUndefined(r[o].querySelector(".e-tbar-btn"))){for(var s=o;s>=0;s--)if(!sf.base.isNullOrUndefined(r[s].querySelector(".e-tbar-btn"))&&0==r[s].querySelector(".e-tbar-btn").tabIndex){i=r[s].querySelector(".e-tbar-btn");break}return void i.focus()}if(!sf.base.isNullOrUndefined(r[o].querySelector(".e-searchinput.e-input")))return void r[o].querySelector(".e-searchinput.e-input").focus()}}else{if(n>0&&(document.activeElement.classList.contains("e-headercell")||document.activeElement.classList.contains("e-recordplusexpand")||document.activeElement.classList.contains("e-recordpluscollapse"))){var l=t.element.querySelectorAll(".e-ungroupbutton");return void l[l.length-1].focus()}if(0==n&&t.options.allowGrouping&&document.activeElement.classList.contains("e-headercell"))return void t.element.querySelector(".e-groupdroparea").focus()}a(document.activeElement,"e-grid")&&t.element.focus()}},isMacDevice:function(){return-1!==navigator.userAgent.indexOf("Mac OS")},updateClonedMaskTranslates:function(e){var t=window.sfBlazor.getCompInstance(e).element.blazor__instance.getContent(),n=t.querySelector(".e-masked-table"),r=t.scrollHeight-n.getBoundingClientRect().height,i=t.scrollTop<=r?t.scrollTop:r;n.style.transform="translate(0px,"+i+"px)"},refreshScrollLeftPosition:function(e){var t=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(t.element)&&!sf.base.isNullOrUndefined(t.element.blazor__instance)){var n=t.element.blazor__instance.getContent(),r=n.scrollLeft;n.scrollLeft=r+20}},refreshGridPageSize:function(e){window.sfBlazor.getCompInstance(e).scrollModule.refresh()},focusColumnChooserCheckBox:function(e,t,n){var r=window.sfBlazor.getCompInstance(e),i=document.activeElement,o="Tab"==t||"ShiftTab"==t,s=function(e){r.element.querySelectorAll(e).forEach((function(e){e.classList.remove("e-colfocus")}))};n?(s(".e-ccheck.e-colfocus"),i.classList.contains("e-chk-hidden")&&o&&!sf.base.isNullOrUndefined(a(i,"e-ccheck"))&&a(document.activeElement,"e-ccheck").classList.add("e-colfocus")):(s(".e-cclist.e-colfocus"),i.classList.contains("e-chk-hidden")&&o&&!sf.base.isNullOrUndefined(a(i,"e-cclist"))&&a(document.activeElement,"e-cclist").classList.add("e-colfocus"))},scrollIntoView:function(e,t,n,r,i,o){void 0===i&&(i=!1),void 0===o&&(o=!1);var s=window.sfBlazor.getCompInstance(e).element.blazor__instance,l=s.getContent(),a=l.scrollTop;s.virtualContentModule.focusColumnIndex=t,i?l.scrollTop=l.scrollHeight:-1!=n&&(l.scrollTop=-1!=r?n*r:n*s.getRowHeight(),s.virtualContentModule.selectedRowIndex=n,s.virtualContentModule.isScrollIntoview=!0,!o&&(0==n&&0==a||a==l.scrollTop&&"left"!==s.virtualContentModule.scrollInfo.direction&&"right"!==s.virtualContentModule.scrollInfo.direction)?(s.dotNetRef.invokeMethodAsync("SelectRow",n,s.virtualContentModule.isScrollIntoview,s.virtualContentModule.focusColumnIndex),s.virtualContentModule.selectedRowIndex=-1):n>=s.options.totalItemCount&&(s.virtualContentModule.selectedRowIndex=s.options.totalItemCount-1));var d=s.virtualContentModule.vHelper.cOffsets,u=s.nColumnOffsets;setTimeout((function(){if(s.options.frozenColumns||-1==t||(s.options.enableColumnVirtualization?l.scrollLeft=d[t-1]:l.scrollLeft=u[t-1]),s.options.enableColumnVirtualization&&t>0&&s.options.frozenColumns){var e=t-(s.virtualContentModule.movableColumnIndex+1);l.scrollLeft=d[e]}}),20)}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfgrid');})})();