using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComRemitBlazor.Models
{
    [Table("Payee")]
    public class Payee
    {
        [Column("sn")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Sn { get; set; }
        
        [Key]
        [Column("CollectNo")]
        public string CollectNo { get; set; } = "";
        
        [Key]
        [Column("CollecAcc")]  
        public string CollecAcc { get; set; } = "";
        
        [NotMapped]
        public int Id
        {
            get { return CollectNo.GetHashCode() + CollecAcc.GetHashCode(); }
            set { /* 複合主鍵，不需要設定 */ }
        }
        
        [Column("CollectName")]
        public string? CollectName { get; set; }
        
        [Column("CollectId")]
        public string? CollectId { get; set; }
        
        [Column("Tel")]
        public string? Tel { get; set; }
        
        [Column("Zipcode")]
        public string? Zip { get; set; }
        
        [Column("Addr")]
        public string? Addr { get; set; }
        
        [Column("BelongUnit")]
        public string? BelongUnit { get; set; }
        
        [Column("BelongAcc")]
        public string? BelongAcc { get; set; }
        
        [Column("Shared")]
        public string? Shared { get; set; } = "否";
        
        [Column("Createdate")]
        public DateTime? Createdate { get; set; }

        [NotMapped]
        public string? FinancialName { get; set; }
    }
} 