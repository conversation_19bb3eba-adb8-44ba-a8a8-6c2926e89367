using Microsoft.EntityFrameworkCore;
using ComRemitBlazor.Models;
using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;

namespace ComRemitBlazor.Services
{
    public class DatabaseInitializationService
    {
        private readonly ComRemitDbContext _context;

        public DatabaseInitializationService(ComRemitDbContext context)
        {
            _context = context;
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                await _context.Database.CanConnectAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> EnsureFinancialTableExistsAsync()
        {
            try
            {
                var tableExists = await _context.Database.SqlQueryRaw<int>(
                    "SELECT COUNT(*) AS Value FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Finacial'")
                    .FirstOrDefaultAsync();

                return tableExists > 0;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<List<Financial>> GetFinancialDataAsync()
        {
            try
            {
                var data = await _context.Financial.ToListAsync();
                return data;
            }
            catch (Exception)
            {
                return new List<Financial>();
            }
        }

        public async Task<bool> DeleteFinancialAsync(int id)
        {
            try
            {
                var rowsAffected = await _context.Database.ExecuteSqlRawAsync(
                    "DELETE FROM Finacial WHERE sno = {0}", new object[] { id });
                
                return rowsAffected > 0;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<Financial?> GetFinancialByIdAsync(int id)
        {
            try
            {
                var data = await _context.Financial.FirstOrDefaultAsync(f => f.Id == id);
                return data;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public async Task<bool> CreateFinancialAsync(Financial financial)
        {
            try
            {
                var snoInfo = await CheckSnoColumnInfoAsync();
                bool isIdentity = snoInfo.ContainsKey("IsIdentity") && Convert.ToBoolean(snoInfo["IsIdentity"]);
                int rowsAffected;
                
                if (isIdentity)
                {
                    rowsAffected = await _context.Database.ExecuteSqlRawAsync(
                        @"INSERT INTO Finacial (AccountNo, FinancialName, FinancialAddr, FkindSno) 
                          VALUES ({0}, {1}, {2}, {3})", 
                        new object[] { financial.Code!, financial.Name!, financial.Address!, financial.CategoryId! });
                }
                else
                {
                    int nextSno = await GetNextSnoValueAsync();
                    rowsAffected = await _context.Database.ExecuteSqlRawAsync(
                        @"INSERT INTO Finacial (sno, AccountNo, FinancialName, FinancialAddr, FkindSno) 
                          VALUES ({0}, {1}, {2}, {3}, {4})", 
                        new object[] { nextSno, financial.Code!, financial.Name!, financial.Address!, financial.CategoryId! });
                }
                
                return rowsAffected > 0;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> UpdateFinancialAsync(Financial financial)
        {
            try
            {
                var rowsAffected = await _context.Database.ExecuteSqlRawAsync(
                    @"UPDATE Finacial 
                      SET AccountNo = {1}, FinancialName = {2}, FinancialAddr = {3}, FkindSno = {4}
                      WHERE sno = {0}", 
                    new object[] { financial.Id, financial.Code!, financial.Name!, financial.Address!, financial.CategoryId! });
                
                return rowsAffected > 0;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<Dictionary<string, object>> CheckSnoColumnInfoAsync()
        {
            try
            {
                var connection = _context.Database.GetDbConnection();
                await connection.OpenAsync();
                
                using var command = connection.CreateCommand();
                command.CommandText = @"
                    SELECT 
                        COLUMN_NAME,
                        DATA_TYPE,
                        IS_NULLABLE,
                        COLUMN_DEFAULT,
                        COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') AS IsIdentity
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'Finacial' AND COLUMN_NAME = 'sno'";
                
                var result = new Dictionary<string, object>();
                using var reader = await command.ExecuteReaderAsync();
                
                if (await reader.ReadAsync())
                {
                    result["COLUMN_NAME"] = reader["COLUMN_NAME"] ?? "";
                    result["DATA_TYPE"] = reader["DATA_TYPE"] ?? "";
                    result["IS_NULLABLE"] = reader["IS_NULLABLE"] ?? "";
                    result["COLUMN_DEFAULT"] = reader["COLUMN_DEFAULT"] ?? "NULL";
                    result["IsIdentity"] = reader["IsIdentity"] ?? false;
                }
                
                return result;
            }
            catch (Exception)
            {
                return new Dictionary<string, object>();
            }
        }

        public async Task<int> GetNextSnoValueAsync()
        {
            try
            {
                var connection = _context.Database.GetDbConnection();
                await connection.OpenAsync();
                
                using var command = connection.CreateCommand();
                command.CommandText = "SELECT ISNULL(MAX(sno), 0) + 1 FROM Finacial";
                
                var result = await command.ExecuteScalarAsync();
                int nextSno = Convert.ToInt32(result);
                
                return nextSno;
            }
            catch (Exception)
            {
                return 1; // 預設值
            }
        }
    }
} 