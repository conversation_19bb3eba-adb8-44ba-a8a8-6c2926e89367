/*!*  filename: sf-listview.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[36],{"./bundles/sf-listview.js":function(e,t,i){"use strict";i.r(t);i("./modules/sf-listview.js")},"./modules/sf-listview.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.ListView=function(){"use strict";var e={None:[],SlideLeft:["SlideRightOut","SlideLeftOut","SlideLeftIn","SlideRightIn"],SlideDown:["SlideTopOut","SlideBottomOut","SlideBottomIn","SlideTopIn"],Zoom:["FadeOut","FadeZoomOut","FadeZoomIn","FadeIn"],Fade:["FadeOut","FadeOut","FadeIn","FadeIn"]},t={None:[],SlideLeft:["SlideLeftOut","SlideRightOut","SlideRightIn","SlideLeftIn"],SlideDown:["SlideBottomOut","SlideTopOut","SlideTopIn","SlideBottomIn"],Zoom:["FadeZoomOut","FadeOut","FadeIn","FadeZoomIn"],Fade:["FadeOut","FadeOut","FadeIn","FadeIn"]},i="e-focused",s="e-list-item",n="e-has-child",r="e-list-header",l="e-disabled",a="e-check",o="none",c=function(){function c(e,t,i,s,n,r){this.dataSourceLevel=["defaultData_Key"],this.currentDataSourceKey="defaultData_Key",this.isWindow=!1,this.liDifference=0,this.liHeight=0,this.virtualListDifference=0,this.isCheckAll=!1,this.isFocus=!1,this.element=t,this.dotNetRef=i,this.dataId=e,this.showCheckBox=s.ShowCheckBox,this.showHeader=s.ShowHeader,this.enable=s.Enabled,this.currentUlElement=t.querySelector("ul"),this.enableVirtualization=s.EnableVirtualization,this.isWindow=!s.Height,this.enableSelectEvent=n,this.height=s.Height,this.headerTitleInfo=[s.HeaderTitle],this.selectedItems={defaultData_Key:s.SelectedElementIdInfo},this.enableRtl=s.EnableRtl,this.animation=s.Animation,this.isTemplate=s.Template,this.focusId=[],window.sfBlazor.setCompInstance(this),this.datasource=r}return c.prototype.initialize=function(){if(this.enableVirtualization){if(this.isWindow)this.dotNetRef.invokeMethodAsync("GetComponenetHeight",window.innerHeight);else if(-1!==this.height.indexOf("%")){var e=this.element.parentElement.getBoundingClientRect().height;this.dotNetRef.invokeMethodAsync("GetComponenetHeight",e/100*parseFloat(this.height))}this.updateLiElementHeight()}this.headerElement=this.element.querySelector(".e-headertext"),this.animationObject=new sf.base.Animation(this.animateOptions),this.wireEvents()},c.prototype.wireEvents=function(){sf.base.EventHandler.add(this.element,"focus",this.focusHandler,this),sf.base.EventHandler.add(this.element,"mousedown",this.mouseDownHandler,this),sf.base.EventHandler.add(this.element,"keydown",this.keyActionHandler,this),sf.base.EventHandler.add(this.element,"click",this.clickHandler,this),sf.base.EventHandler.add(this.element,"focusout",this.removeFocus,this),this.touchModule=new sf.base.Touch(this.element,{swipe:this.swipeActionHandler.bind(this)}),this.enableVirtualization?(sf.base.EventHandler.add(this.element,"scroll",this.scrollHandler,this),this.isWindow&&window.addEventListener("scroll",this.scrollHandler.bind(this))):(sf.base.EventHandler.add(this.element,"mouseover",this.mouseHoverHandler,this),sf.base.EventHandler.add(this.element,"mouseout",this.mouseOutHandler,this))},c.prototype.unWireEvents=function(){sf.base.EventHandler.remove(this.element,"focus",this.focusHandler),sf.base.EventHandler.remove(this.element,"mousedown",this.mouseDownHandler),sf.base.EventHandler.remove(this.element,"keydown",this.keyActionHandler),sf.base.EventHandler.remove(this.element,"click",this.clickHandler),sf.base.EventHandler.remove(this.element,"focusout",this.removeFocus),this.enableVirtualization?(sf.base.EventHandler.remove(this.element,"scroll",this.scrollHandler),this.isWindow&&window.removeEventListener("scroll",this.scrollHandler.bind(this))):(sf.base.EventHandler.remove(this.element,"mouseover",this.mouseHoverHandler),sf.base.EventHandler.remove(this.element,"mouseout",this.mouseOutHandler)),this.touchModule.destroy()},c.prototype.swipeActionHandler=function(e){"Right"===e.swipeDirection&&e.velocity>.5&&"touchend"===e.originalEvent.type&&(this.showCheckBox&&this.dataSourceLevel[this.dataSourceLevel.length-1]&&this.uncheckAllItems(),this.back())},c.prototype.showHideItem=function(e,t){var i=this.getLi(e);i&&(i.style.display=t)},c.prototype.enableState=function(e,t){var i=this.getLi(e);i&&(t?i.classList.remove("e-disabled"):i.classList.add("e-disabled"))},c.prototype.getLi=function(e){var t;return this.element&&(t=this.element.querySelector('[data-uid="'+e.id+'"]')),t},c.prototype.scrollHandler=function(e){var t,s=this.isWindow?e.target.documentElement.scrollTop:e.target.scrollTop;if(this.liHeight||this.updateLiElementHeight(),(t=Math.round(s/this.liHeight))-this.liDifference>=this.virtualListDifference||t-this.liDifference<=-1){var n=this.currentUlElement.querySelector("."+i);n&&(this.focusedElementId=n.getAttribute("data-uid"));var r=this.element.querySelector(".e-list-virtualcontainer");r&&(r.style.top=(t-1)*this.liHeight<0?"0px":(t-2)*this.liHeight+"px",this.liDifference=t,this.dotNetRef.invokeMethodAsync("VirtualScrolling",t-2))}},c.prototype.preventSelection=function(e){var t=this.mouseEvent.target,i=sf.base.closest(t,"."+s);e?this.dataSourceLevel.length>1&&this.dataSourceLevel.pop():this.performSelection(i)},c.prototype.performSelection=function(e){null!==e&&!e.classList.contains(l)&&this.enable&&(this.removeFocus(),this.focusId=[],this.addFocusId(e.getAttribute("data-uid")),this.showCheckBox?this.mouseEvent.target.classList.contains("e-frame")?this.checkUncheckItem(e):e.classList.contains(n)?(this.removeHover(),this.removeFocus(),this.setSelectLI(e,this.mouseEvent)):this.checkUncheckItem(e):(this.currentUlElement.querySelector("."+i)&&this.currentUlElement.querySelector("."+i).classList.remove(i),e.classList.contains(n)?this.setSelectLI(e,this.mouseEvent):e.classList.contains(i)||(e.classList.add(i),this.selectedItems[this.currentDataSourceKey][0]=e.getAttribute("data-uid"))))},c.prototype.updateLiElementHeight=function(){var e=this.element.querySelector(".e-list-virtualcontainer");if(e){this.currentUlElement||(this.currentUlElement=this.element.querySelector("ul"));var t=e.children[0];if(t){if(this.liHeight=t.getBoundingClientRect().height,this.liHeight&&this.enableVirtualization){var i=e.getBoundingClientRect().height;e.parentElement.style.height=i+"px"}}else{var n=sf.base.createElement("li",{className:s});e.appendChild(n),this.liHeight=e.children[0].getBoundingClientRect().height,sf.base.detach(n)}this.liHeight&&this.dotNetRef.invokeMethodAsync("UpdateLiElementHeight",this.liHeight)}},c.prototype.updateElementDifference=function(e){this.virtualListDifference=e,this.enableVirtualization&&this.updateLiElementHeight()},c.prototype.selectItem=function(e){var t=this.getLi(e);this.showCheckBox?this.setChecked(t,t.querySelector(".e-checkbox-wrapper")):(sf.base.isNullOrUndefined(t)?this.removeFocus():this.setSelectLI(t,null),this.selectedItems[this.currentDataSourceKey][0]=t.getAttribute("data-uid"))},c.prototype.clickHandler=function(e){if(this.currentUlElement&&!this.element.classList.contains("e-disabled")){var t=e.target,i=t.classList;if(this.mouseEvent=e,i.contains("e-icon-back")||i.contains("e-headertext"))this.back();else{var r=sf.base.closest(t.parentNode,"."+s)?sf.base.closest(t.parentNode,"."+s):sf.base.closest(t,"."+s);if(this.enableSelectEvent||this.performSelection(r),null!==r&&!r.classList.contains(l)&&this.enable){if(r.classList.contains(n)){var a=r.getAttribute("data-uid");-1===this.dataSourceLevel.indexOf(a)&&this.dataSourceLevel.push(a)}this.enableSelectEvent&&(this.isFocus=!1,this.getSelectEventData(r,e))}}}},c.prototype.checkUncheckItem=function(e){e.classList.add(i),e.querySelector("."+a)?this.uncheckItem(e):this.setChecked(e,e.querySelector(".e-checkbox-wrapper")),this.dotNetRef.invokeMethodAsync("UpdateData",!sf.base.isNullOrUndefined(e.querySelector("."+a)),e.getAttribute("data-uid"))},c.prototype.back=function(){if(this.dataSourceLevel.length>1){for(var e=this.element.querySelectorAll("ul"),t=this.element.querySelector("."+r),s=0;s<e.length;s++)this.dataSourceLevel.length>2?e[s].getAttribute("pid")===this.dataSourceLevel[this.dataSourceLevel.length-2]?(this.switchView(this.currentUlElement,e[s],!0),this.currentUlElement=e[s]):e[s].style.display=o:null===e[s].getAttribute("pid")?(this.switchView(this.currentUlElement,e[s],!0),this.currentUlElement=e[s]):e[s].style.display=o;if(this.dataSourceLevel.pop(),!this.showCheckBox)this.currentUlElement.querySelectorAll("."+i)[0].setAttribute("aria-selected","false");this.isTemplate||(this.headerTitleInfo.length>1&&this.headerTitleInfo.pop(),this.headerElement&&(this.headerElement.innerText=this.headerTitleInfo[this.headerTitleInfo.length-1])),this.currentDataSourceKey=this.dataSourceLevel[this.dataSourceLevel.length-1],1===this.dataSourceLevel.length&&t&&(t.children[0].style.display=o,t.classList.remove("e-list-navigation"));var n={IsInteracted:!(!event&&!this.isRootFocus),Level:this.dataSourceLevel.length>0?this.dataSourceLevel.length-1:this.dataSourceLevel.length};this.dotNetRef.invokeMethodAsync("TriggerBackEvent",n)}},c.prototype.setHoverLI=function(e){this.element.classList.contains(l)||e.classList.contains(l)||e.classList.add("e-hover")},c.prototype.mouseHoverHandler=function(e){var t=sf.base.closest(e.target.parentNode,"."+s);t&&this.setHoverLI(t)},c.prototype.mouseOutHandler=function(e){this.removeHover()},c.prototype.removeHover=function(){var e=this.element.querySelector(".e-hover");e&&e.classList.remove("e-hover")},c.prototype.removeFocus=function(){this.currentUlElement||(this.currentUlElement=this.element.querySelector("ul"));for(var e=0,t=this.currentUlElement.querySelectorAll("."+i);e<t.length;e++){t[e].classList.remove(i)}this.isRestrictFocus=void 0},c.prototype.isValidLI=function(e){return e&&e.classList.contains(s)&&!e.classList.contains("e-list-group-item")&&!e.classList.contains(l)},c.prototype.setSelectLI=function(e,t){if(this.enable&&this.isValidLI(e)&&!e.classList.contains(i)){var s=this.currentUlElement.querySelector("."+i);s&&this.removeFocusId(s.getAttribute("data-uid")),this.removeFocus(),this.addAriaAttribute(!0,e),this.removeHover(),e.classList.contains(n)&&this.renderSubList(e)}},c.prototype.addAriaAttribute=function(e,t){e?(this.isCheckAll||t.classList.add(i),this.addFocusId(t.getAttribute("data-uid"))):this.showCheckBox||e||!t.classList.contains(i)||(t.classList.remove(i),this.focusId.indexOf(t.getAttribute("data-uid"))>=0&&this.focusId.splice(this.focusId.indexOf(t.getAttribute("data-uid")),1)),(this.showCheckBox||t.classList.contains(n))&&t.setAttribute("aria-selected",e.toString())},c.prototype.renderSubList=function(e){var t=e,s=e.getAttribute("data-uid"),l=this.element.querySelector("."+r);if(e.classList.remove(i),e.classList.add(i),this.showHeader&&l&&(l.children[0].style.display=null,l.classList.add("e-list-navigation")),t.classList.contains(n)&&s){if(this.element.querySelector("[pid='"+s+"']"))this.renderChildList(s,[s]);else{var a={ElementId:s,Key:this.currentDataSourceKey};this.dotNetRef.invokeMethodAsync("ListChildDataSource",a)}this.isTemplate||(this.headerTitleInfo.push(t.innerText.trim()),this.headerElement&&(this.headerElement.innerText=this.headerTitleInfo[this.headerTitleInfo.length-1])),-1==this.dataSourceLevel.indexOf(s)&&this.dataSourceLevel.push(s),this.currentDataSourceKey=s}},c.prototype.renderChildList=function(e,t){var i=this.element.querySelectorAll("ul");i[i.length-1].getAttribute("pid")||i[i.length-1].setAttribute("pid",e);for(var s=0;s<i.length;s++)if(i[s].getAttribute("pid")===e){this.switchView(this.currentUlElement,i[s],!1),this.currentUlElement=i[s];var n=i[s].querySelectorAll(".e-list-item");if(n[0].getAttribute("aria-level")!==s.toString())for(var r=0;r<n.length;r++)n[r].setAttribute("aria-level",s.toString());t&&(this.selectedItems[e]=t)}},c.prototype.mouseDownHandler=function(){this.isRestrictFocus=null==this.isRestrictFocus||this.isRestrictFocus},c.prototype.focusHandler=function(e){this.isRestrictFocus?this.isRestrictFocus=!1:sf.base.isNullOrUndefined(this.focusId)||0==this.focusId.length?(this.isRootFocus=!0,this.arrowKeyHandler(e),this.isRootFocus=!1):(this.isFocus=!0,this.updateFocusElement(e))},c.prototype.keyActionHandler=function(e){if("INPUT"!=e.target.tagName&&"TEXTAREA"!=e.target.tagName)switch(e.keyCode){case 36:this.homeKeyHandler(e);break;case 35:this.homeKeyHandler(e,!0);break;case 40:this.arrowKeyHandler(e);break;case 38:this.arrowKeyHandler(e,!0);break;case 39:this.arrowKeyHandler(e);break;case 37:this.arrowKeyHandler(e,!0);break;case 13:this.enterKeyHandler(e);break;case 8:var t=this.element.querySelector("."+r+" .e-icon-back");t&&t.style.display!=o&&(this.showCheckBox&&this.currentDataSourceKey&&this.uncheckAllItems(),this.back());break;case 32:this.spaceKeyHandler(e)}},c.prototype.addFocusId=function(e){this.focusId.indexOf(e)<0&&this.focusId.push(e)},c.prototype.removeFocusId=function(e){this.focusId.indexOf(e)>=0&&this.focusId.splice(this.focusId.indexOf(e),1)},c.prototype.updateFocusElement=function(e){var t=this.currentUlElement.querySelector("."+i);t&&(t.classList.remove(i),this.focusId.indexOf(t.getAttribute("data-uid"))>=0&&this.focusId.splice(this.focusId.indexOf(t.getAttribute("data-uid")),1));for(var s=this.currentUlElement.children,n=0;n<s.length;n++)this.focusId.indexOf(s[n].getAttribute("data-uid"))>=0&&(s[n].classList.add(i),this.addFocusId(s[n].getAttribute("data-uid")),this.addAriaAttribute(!0,s[n]),this.element.setAttribute("aria-activedescendant",s[n].id.toString()),this.enableSelectEvent&&(this.getSelectEventData(s[n],e),this.isFocus=!1))},c.prototype.homeKeyHandler=function(e,t){e.preventDefault();var s=this.currentUlElement.querySelector("."+i);s&&(s.classList.remove(i),this.removeFocusId(s.getAttribute("data-uid")));var n=t?this.currentUlElement.children.length-1:0;n=this.currentUlElement.children[n].classList.contains("e-list-group-item")&&this.currentUlElement.children.length>1?n+1:n;var r=this.currentUlElement.children[n];this.addAriaAttribute(!0,r),r.classList.add(i),this.addFocusId(r.getAttribute("data-uid")),this.currentUlElement.children[n]?this.element.setAttribute("aria-activedescendant",this.currentUlElement.children[n].id.toString()):this.element.removeAttribute("aria-activedescendant"),this.enableSelectEvent&&this.getSelectEventData(r,e)},c.prototype.onArrowKeyDown=function(e,t){var r,l;return!sf.base.isNullOrUndefined(this.currentUlElement.querySelector("."+n))||this.showCheckBox?((l=this.currentUlElement.querySelector("."+i)||this.currentUlElement.querySelector("."+i))&&this.removeFocusId(l.getAttribute("data-uid")),r=this.getSiblingLI(this.currentUlElement.querySelectorAll("."+s),l,t),sf.base.isNullOrUndefined(r)||(l&&(l.classList.remove(i),this.showCheckBox||l.classList.remove(i),this.removeFocusId(l.getAttribute("data-uid"))),r.classList.contains(n)||this.showCheckBox?r.classList.add(i):this.setSelectLI(r,e))):(l=this.currentUlElement.querySelector("."+i),r=this.getSiblingLI(this.currentUlElement.querySelectorAll("."+s),l,t),this.setSelectLI(r,e)),r?this.element.setAttribute("aria-activedescendant",r.id.toString()):this.element.removeAttribute("aria-activedescendant"),r},c.prototype.getSiblingLI=function(e,t,i){var s,n,r=Array.prototype.slice.call(e);for(n=t?r.indexOf(t):!0===i?r.length:-1,s=r[n+(!0===i?-1:1)];s&&s.classList.contains(l);)s=!0===i?s.previousElementSibling:s.nextElementSibling;return s},c.prototype.arrowKeyHandler=function(e,t){if(this.isRootFocus||e.preventDefault(),this.currentUlElement){var i=this.onArrowKeyDown(e,t);i&&this.addFocusId(i.getAttribute("data-uid"));var s=this.element.getBoundingClientRect().top,n=this.element.getBoundingClientRect().height,r=void 0;if(i){var l=i.getBoundingClientRect().top,a=i.getBoundingClientRect().height;if(t)(r=this.isWindow?l:l-s)<0&&(this.isWindow?window.scroll(0,pageYOffset+r):this.element.scrollTop=this.element.scrollTop+r);else{var o=this.isWindow?window.innerHeight:n;(r=this.isWindow?l+a:l-s+a)>o&&(this.isWindow?window.scroll(0,pageYOffset+(r-o)):this.element.scrollTop=this.element.scrollTop+(r-o))}}}},c.prototype.enterKeyHandler=function(e){if(this.currentUlElement){var t=this.currentUlElement.querySelector("."+i);this.currentUlElement.querySelector("."+n)&&t?(t.classList.remove(i),this.showCheckBox&&(this.removeFocus(),this.removeHover()),this.setSelectLI(t,e)):sf.base.isNullOrUndefined(t)||this.spaceKeyHandler(e)}},c.prototype.checkAllItems=function(){this.isCheckAll=!0,this.updateCheckBoxState(!0),this.isCheckAll=!1},c.prototype.uncheckAllItems=function(){this.updateCheckBoxState(!1)},c.prototype.updateCheckBoxState=function(e){if(this.showCheckBox)for(var t=this.currentUlElement.querySelectorAll("li"),i=this.enableVirtualization?this.currentUlElement.querySelector(".e-list-virtualcontainer").childElementCount:this.currentUlElement.childElementCount,s=0;s<i;s++){var n=t[s].querySelector(".e-frame");n&&(e&&!n.classList.contains(a)?this.checkItem(t[s]):!e&&n.classList.contains(a)&&this.uncheckItem(t[s]))}},c.prototype.checkItem=function(e){this.toggleCheckBox(e,!0)},c.prototype.getCheckData=function(e,t,i){var s=e.id;if(i!==e.id||sf.base.isNullOrUndefined(e.id)){i=i.toLowerCase();for(var n=0,r=Object.entries(e);n<r.length;n++){var l=r[n],a=""+l[0],o=""+l[1];(a=a.toLowerCase())===i&&(s=o)}}var c=this.currentUlElement.querySelector("[data-uid='"+s+"']");t?this.checkItem(c):this.uncheckItem(c),this.removeFocus()},c.prototype.spaceKeyHandler=function(e){if(this.isTemplate||e.preventDefault(),this.enable&&this.showCheckBox&&this.currentUlElement){var t=this.currentUlElement.querySelector("."+i);!sf.base.isNullOrUndefined(t)&&sf.base.isNullOrUndefined(t.querySelector("."+a))?this.setChecked(t,t.querySelector(".e-checkbox-wrapper")):this.uncheckItem(t),this.enableSelectEvent&&this.getSelectEventData(t,e)}},c.prototype.setChecked=function(e,t){this.removeFocus(),e.classList.add(i),this.addAriaAttribute(!0,e),t&&(t.querySelector(".e-frame").classList.add(a),t.setAttribute("aria-checked","true")),this.selectedItems[this.currentDataSourceKey]&&-1===this.selectedItems[this.currentDataSourceKey].indexOf(e.getAttribute("data-uid"))&&this.selectedItems[this.currentDataSourceKey].push(e.getAttribute("data-uid"))},c.prototype.toggleCheckBox=function(e,t){if(this.showCheckBox){var i=e;if(!sf.base.isNullOrUndefined(i)){var s=i.querySelector(".e-frame");this.addAriaAttribute(t,i),sf.base.isNullOrUndefined(s)||(t?s.classList.add(a):s.classList.remove(a),s.parentElement.setAttribute("aria-checked",t?"true":"false"))}}},c.prototype.uncheckItem=function(e){e&&(this.selectedItems[this.currentDataSourceKey]&&-1!==this.selectedItems[this.currentDataSourceKey].indexOf(e.getAttribute("data-uid"))&&this.selectedItems[this.currentDataSourceKey].splice(this.selectedItems[this.currentDataSourceKey].indexOf(e.getAttribute("data-uid")),1),this.toggleCheckBox(e,!1))},c.prototype.addCheckClass=function(){this.currentUlElement||(this.currentUlElement=this.element.querySelector("ul"));for(var e=this.enableVirtualization?this.currentUlElement.querySelector(".e-list-virtualcontainer").children:this.currentUlElement.children,t=this.selectedItems[this.currentDataSourceKey],s=0;s<e.length;s++)this.showCheckBox?(this.focusedElementId&&(this.focusedElementId===e[s].getAttribute("data-uid")?e[s].classList.add(i):e[s].classList.remove(i)),t.length>0&&(-1!==t.indexOf(e[s].getAttribute("data-uid"))?this.toggleCheckBox(e[s],!0):this.toggleCheckBox(e[s],!1))):t[0]===e[s].getAttribute("data-uid")?e[s].classList.add(i):e[s].classList.remove(i);for(s=0;s<e.length;s++)this.showCheckBox?(this.focusedElementId&&(this.focusedElementId===e[s].getAttribute("data-uid")?e[s].classList.add(i):e[s].classList.remove(i)),t.length>0&&(-1!==t.indexOf(e[s].getAttribute("data-uid"))?this.toggleCheckBox(e[s],!0):this.toggleCheckBox(e[s],!1))):t&&t[0]===e[s].getAttribute("data-uid")?e[s].classList.add(i):e[s].classList.remove(i)},c.prototype.getSelectedItems=function(){var e=this.currentUlElement.querySelectorAll(".e-check");this.selectedItems[this.currentDataSourceKey]=[];for(var t=0;t<e.length;t++){var i=sf.base.closest(e[t],"."+s);this.selectedItems[this.currentDataSourceKey]&&-1===this.selectedItems[this.currentDataSourceKey].indexOf(i.getAttribute("data-uid"))&&this.selectedItems[this.currentDataSourceKey].push(i.getAttribute("data-uid"))}return{ElementId:this.selectedItems[this.currentDataSourceKey],Key:this.currentDataSourceKey}},c.prototype.getSelectEventData=function(e,t){if(e){var i="click"===t.type?!e.querySelector("."+a):!!e.querySelector("."+a),s={ElementId:e.getAttribute("data-uid"),IsChecked:i,Key:e.classList.contains(n)&&!t.target.classList.contains("e-frame")?this.dataSourceLevel[this.dataSourceLevel.length-2]:this.currentDataSourceKey,IsInteracted:!(!t&&!this.isRootFocus),Level:this.dataSourceLevel.length>0?this.dataSourceLevel.length-1:this.dataSourceLevel.length};this.isFocus||this.dotNetRef.invokeMethodAsync("TriggerClickEvent",s)}},c.prototype.switchView=function(i,s,n){var r=this;if(i&&s){var l=i.style.position,a="hidden"!==this.element.style.overflow?this.element.style.overflow:"",c=void 0,h=this.animation.duration;if(i.style.position="absolute",i.classList.add("e-view"),"Disable"===sf.base.animationMode){c=this.enableRtl?t.None:e.None,n=this.enableRtl,h=0}else if(this.animation.effect)"None"===this.animation.effect&&"Enable"===sf.base.animationMode&&(this.animation.effect="SlideLeft"),c=this.enableRtl?t[this.animation.effect]:e[this.animation.effect];else{c=e.SlideLeft,n=this.enableRtl,h=0}this.element.style.overflow="hidden",this.animationObject.animate(i,{name:n?c[0]:c[1],duration:h,timingFunction:this.animation.easing,end:function(e){i.style.display=o,r.element.style.overflow=a,i.style.position=l,i.classList.remove("e-view")}}),s.style.display="",this.animationObject.animate(s,{name:n?c[2]:c[3],duration:h,timingFunction:this.animation.easing,end:function(){r.dotNetRef.invokeMethodAsync("TriggerActionComplete",r.datasource)}}),this.currentUlElement=s}},c.prototype.setAnimation=function(e){this.animation=e},c.prototype.setSelectedItems=function(e){var t=this.element.querySelector("."+r);sf.base.isNullOrUndefined(e)||(this.selectedItems={defaultData_Key:e}),e.length>0?this.currentUlElement=this.element.querySelector('[data-uid="'+e+'"]').closest("ul"):this.currentUlElement=this.element.querySelector("ul");var i=this.element.querySelector('[data-uid="'+e+'"]').classList.contains("e-has-child");i?(this.dataSourceLevel=["defaultData_Key"],this.currentUlElement.style.removeProperty("display"),this.addCheckClass(),this.currentDataSourceKey="defaultData_Key"):this.selectedItems[this.currentDataSourceKey]=[this.currentDataSourceKey],this.showCheckBox&&this.removeFocus(),this.headerElement&&i&&(this.headerTitleInfo=this.headerTitleInfo.splice(0,1),this.headerElement.innerText=this.headerTitleInfo[this.headerTitleInfo.length-1]),1===this.dataSourceLevel.length&&t&&i&&(t.children[0].style.display=o,t.classList.remove("e-list-navigation"))},c.prototype.updateHeaderTitle=function(e){this.headerTitleInfo[0]=e,this.headerElement&&(this.headerElement.innerText=e)},c.prototype.destroy=function(){this.element.style.display=o,this.unWireEvents()},c}();return{initialize:function(e,t,i,s,n,r,l){if(t&&e){new c(e,t,i,s,n,l);var a=window.sfBlazor.getCompInstance(e);a.initialize(),a.updateElementDifference(r)}},renderChildList:function(e,t,i){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.renderChildList(t,i)},addActiveClass:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.addCheckClass()},showHideItem:function(e,t,i){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.showHideItem(t,i)},enableState:function(e,t,i){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.enableState(t,i)},back:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.back()},checkAllItems:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.checkAllItems()},uncheckAllItems:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.uncheckAllItems()},getCheckData:function(e,t,i,s){var n=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(n)&&null!=t)for(var r=0;r<t.length;r++)n.getCheckData(t[r],i,s)},selectItem:function(e,t){var i=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(i)&&null!=t)for(var s=0;s<t.length;s++)i.selectItem(t[s])},preventSelection:function(e,t){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.preventSelection(t)},updateLiElementHeight:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.updateLiElementHeight()},getCheckedItems:function(e){var t=window.sfBlazor.getCompInstance(e);return sf.base.isNullOrUndefined(t)?{}:t.getSelectedItems()},setAnimation:function(e,t){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.setAnimation(t)},setCheckedItems:function(e,t){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.setSelectedItems(t)},updateHeaderTitle:function(e,t){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.updateHeaderTitle(t)},destroy:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.destroy()},updateElementDifference:function(e,t){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.updateElementDifference(t)}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sflistview');})})();