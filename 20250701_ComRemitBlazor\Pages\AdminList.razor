@page "/adminlist"
@rendermode @(new Microsoft.AspNetCore.Components.Web.InteractiveServerRenderMode())
@using Microsoft.EntityFrameworkCore
@using ComRemitBlazor.Models
@using ComRemitBlazor.Services
@using QuestPDF.Fluent
@using QuestPDF.Helpers
@using QuestPDF.Infrastructure
@inject ComRemitDbContext Context
@inject IJSRuntime JSRuntime
@inject BarcodeService BarcodeService
@inject RemitedListService RemitedListService

<PageTitle>管理者頁面</PageTitle>

<h3>管理者頁面</h3>

<div class="row">
    <div class="col-md-6">
        <div class="form-group mb-3">
            <label for="searchId">彙整編號:</label>
            <input type="text" class="form-control" id="searchId" @bind="searchId" @onkeypress="@OnKeyPress" placeholder="請輸入彙整編號" />
        </div>
        <button class="btn btn-primary me-2" @onclick="SearchData" @onclick:preventDefault="true">搜尋</button>
    </div>
</div>

@if (showResults && !string.IsNullOrEmpty(searchResultMessage))
{
    <div class="mt-3">
        <div class="alert alert-info">
            <strong>彙整編號:</strong> @searchId<br/>
            @((MarkupString)searchResultMessage)
        </div>
        
        <div class="mt-3">
            <label for="cashDate">匯款日期:</label>
            <input type="date" class="form-control" @bind="selectedCashDate" />
            <button class="btn btn-success mt-2" @onclick="UpdateCashDate">設定匯款日期</button>
        </div>

        <div class="mt-3">
            <button class="btn btn-info" @onclick="ExportFile">@exportButtonText</button>
            <button class="btn btn-warning" @onclick="GenerateReport">產生報表</button>
            <button class="btn btn-secondary" @onclick="ExportFinancialFile">產生庫款系統匯出檔</button>
        </div>
    </div>
}

@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger mt-3">
        @errorMessage
    </div>
}

@if (!string.IsNullOrEmpty(successMessage))
{
    <div class="alert alert-success mt-3">
        @successMessage
    </div>
}

<script>
    function downloadFile(filename, content) {
        const element = document.createElement('a');
        element.setAttribute('href', 'data:text/plain;base64,' + content);
        element.setAttribute('download', filename);
        element.style.display = 'none';
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
    }

    function downloadPdfFile(filename, content) {
        const element = document.createElement('a');
        element.setAttribute('href', 'data:application/pdf;base64,' + content);
        element.setAttribute('download', filename);
        element.style.display = 'none';
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
    }
</script>

@code {
    private string searchId = "";
    private string searchResultMessage = "";
    private string errorMessage = "";
    private string successMessage = "";

    private bool showResults = false;
    private bool ifGenPostFile = true;
    private string exportButtonText = "匯出郵局匯出檔";
    private DateTime? selectedCashDate;

    // <--- 新增的輔助方法，集中處理ID轉換 ---
    private int? GetSearchIdAsInt()
    {
        if (int.TryParse(searchId, out var id))
        {
            return id;
        }
        errorMessage = "彙整編號格式不正確，必須為數字。";
        successMessage = ""; // 清除成功訊息
        showResults = false; // 隱藏結果區域
        return null;
    }

    private async Task OnKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SearchData();
        }
    }

    // <--- 修改後的 SearchData ---
    private async Task SearchData()
    {
        errorMessage = "";
        successMessage = "";
        showResults = false;

        var searchIdInt = GetSearchIdAsInt();
        if (searchIdInt == null)
        {
            StateHasChanged();
            return;
        }

        try
        {
          var remitedList = await Context.RemitedList
            .Where(r => r.ConSno.HasValue && r.ConSno.Value == searchIdInt.Value)
            .ToListAsync();

            if (!remitedList.Any())
            {
                errorMessage = "找不到對應的資料";
                showResults = false;
                StateHasChanged();
                return;
            }

            ifGenPostFile = remitedList.All(r => r.CollectNo == "7000021");
            exportButtonText = ifGenPostFile ? "匯出郵局匯出檔" : "匯出台銀匯出檔";

            var total = remitedList.Sum(r => (decimal)(r.RemitPrice ?? 0));
            var nameList = remitedList.Take(3).Select(r => r.CollecName ?? "").ToList();
            var names = string.Join(",", nameList);
            if (remitedList.Count > 3)
            {
                names += "等";
            }

            searchResultMessage = $"匯入{names}{remitedList.Count}人，共{total:N0}元。";
            showResults = true;
        }
        catch (Exception ex)
        {
            errorMessage = $"搜尋時發生錯誤: {ex.ToString()}";
            showResults = false;
        }
        StateHasChanged();
    }

    // <--- 修改後的 UpdateCashDate ---
    private async Task UpdateCashDate()
    {
        errorMessage = "";
        successMessage = "";

        if (!selectedCashDate.HasValue)
        {
            errorMessage = "請選擇匯款日期";
            return;
        }

        var searchIdInt = GetSearchIdAsInt();
        if (searchIdInt == null) return;
        
        try
        {
            var remitedList = await Context.RemitedList
                .Where(r => r.ConSno == searchIdInt.Value) // 使用 .Value
                .ToListAsync();

            foreach (var item in remitedList)
            {
                item.CashDate = selectedCashDate.Value;
            }

            await Context.SaveChangesAsync();
            successMessage = "匯款日期設定成功";
        }
        catch (Exception ex)
        {
            errorMessage = $"設定匯款日期時發生錯誤: {ex.Message}";
        }
    }

    // <--- 修改後的 ExportFile ---
    private async Task ExportFile()
    {
        errorMessage = "";
        successMessage = "";
        
        var searchIdInt = GetSearchIdAsInt();
        if (searchIdInt == null) return;
        
        try
        {
            var remitedList = await Context.RemitedList
                .Where(r => r.ConSno== searchIdInt.Value) // 使用 .Value
                .OrderBy(r => r.Sno)
                .ToListAsync();

            if (!remitedList.Any())
            {
                errorMessage = "沒有找到資料";
                return;
            }

            if (!remitedList.First().CashDate.HasValue)
            {
                errorMessage = "必須先指定【匯款日期】";
                return;
            }

            string content;
            string filename;

            if (ifGenPostFile)
            {
                content = GeneratePostalExportContent(remitedList);
                filename = $"CR{searchId.PadLeft(6, '0')}_POST.txt";
            }
            else
            {
                content = GenerateTWBankExportContent(remitedList);
                filename = $"CR{searchId.PadLeft(6, '0')}_TWBANK.txt";
            }

            var bytes = System.Text.Encoding.Default.GetBytes(content);
            var base64 = Convert.ToBase64String(bytes);
            await JSRuntime.InvokeVoidAsync("downloadFile", filename, base64);
            
            successMessage = $"匯出檔案成功: {filename}";
        }
        catch (Exception ex)
        {
            errorMessage = $"匯出檔案時發生錯誤: {ex.Message}";
        }
    }

    // <--- 修改後的 GenerateReport ---
    private async Task GenerateReport()
    {
        errorMessage = "";
        successMessage = "";
        
        var searchIdInt = GetSearchIdAsInt();
        if (searchIdInt == null) return;
        
        try
        {
            var remitedList = await Context.RemitedList
                .Where(r => r.ConSno == searchIdInt.Value) // 使用 .Value
                .OrderBy(r => r.Sno)
                .ToListAsync();

            if (!remitedList.Any())
            {
                errorMessage = "沒有找到資料";
                return;
            }

            var invalidAccounts = remitedList.Where(r => (r.CollecAcc?.Length ?? 0) > 14).ToList();
            if (invalidAccounts.Any())
            {
                var invalidNames = string.Join(", ", invalidAccounts.Select(r => r.CollecName).Take(5));
                if (invalidAccounts.Count > 5) invalidNames += "等";
                errorMessage = $"您所彙整的帳號格式不符(須為14碼以內，請確認其長度、或有無空白)，有問題人員為:({invalidNames})";
                return;
            }

            byte[] pdfBytes;
            string filename;

            if (ifGenPostFile)
            {
                var (pdfBytesResult, filenameResult) = await RemitedListService.PrintPdfAsync(searchIdInt.Value);
                pdfBytes = pdfBytesResult;
                filename = filenameResult;
            }
            else
            {
                pdfBytes = GenerateTWBankReportAdvanced(remitedList);
                filename = $"CR{searchId.PadLeft(6, '0')}_TWBANK.pdf";
            }

            var base64 = Convert.ToBase64String(pdfBytes);
            await JSRuntime.InvokeVoidAsync("downloadPdfFile", filename, base64);
            
            successMessage = $"報表產生成功: {filename}";
        }
        catch (Exception ex)
        {
            errorMessage = $"產生報表時發生錯誤: {ex.Message}";
        }
    }

    // <--- 修改後的 ExportFinancialFile ---
    private async Task ExportFinancialFile()
    {
        errorMessage = "";
        successMessage = "";
        
        var searchIdInt = GetSearchIdAsInt();
        if (searchIdInt == null) return;

        try
        {
            var remitedList = await Context.RemitedList
                .Where(r => r.ConSno == searchIdInt.Value) // 使用 .Value
                .ToListAsync();

            if (!remitedList.Any())
            {
                errorMessage = "沒有找到資料";
                return;
            }

            var mingGuoYear = DateTime.Now.Year - 1911;
            var content = GenerateFinancialExportContent(remitedList, mingGuoYear);
            var filename = $"CR{mingGuoYear}{searchId.PadLeft(5, '0')}.txt";

            var bytes = System.Text.Encoding.Default.GetBytes(content);
            var base64 = Convert.ToBase64String(bytes);
            await JSRuntime.InvokeVoidAsync("downloadFile", filename, base64);
            
            successMessage = $"庫款系統匯出檔成功: {filename}";
        }
        catch (Exception ex)
        {
            errorMessage = $"產生庫款系統匯出檔時發生錯誤: {ex.Message}";
        }
    }

    // ... 其餘的輔助方法 (Generate... 等) 維持不變 ...
    // ... I am omitting the rest of the unchanged helper functions for brevity ...
    // ... The code below this line is the same as in your original file ...
    
    private string GeneratePostalExportContent(List<ComRemitBlazor.Models.RemitedList> data)
    {
        var lines = new List<string>();
        var cashDate = data.First().CashDate!.Value;
        var cashDateStr = $"{cashDate.Year - 1911:D3}{cashDate.Month:D2}{cashDate.Day:D2}";

        foreach (var item in data)
        {
            var collectId = item.CollectId?.ToUpper().Trim() ?? "";
            if (collectId.Length == 8)
            {
                collectId = collectId.PadRight(10, ' ');
            }

            var account = item.CollecAcc?.Replace("-", "") ?? "";
            var amount = (int)(item.RemitPrice ?? 0);
            var amountStr = amount.ToString("D8");

            var line = $"**********      ********{cashDateStr}{account}{collectId}{amountStr}00               ";
            
            if (line.Length != 80)
            {
                if (line.Length > 80)
                    line = line.Substring(0, 80);
                else
                    line = line.PadRight(80, ' ');
            }
            
            lines.Add(line);
        }

        return string.Join("\r\n", lines);
    }

    private string GenerateTWBankExportContent(List<ComRemitBlazor.Models.RemitedList> data)
    {
        if (!data.Any()) return "";

        int applymoney;
        string flowNum = "00000"; 
        string cashDate = "";
        string filler1 = "                       ";
        string remitName = "";
        string filler2 = "                                                                ";
        string conOrga = "相關單位";
        int totalAmount = 0;
        int totalCost = 0;
        string memo = "";

        string firstRow = "1022";
        string batchNum = GenerateBatchNumber();

        var contentRows = new List<string>();
        
        foreach (var item in data)
        {
            if (totalAmount == 0)
            {
                cashDate = batchNum.Substring(0, 6);
                remitName = CHT_WordPadLeftRight($"宜府{conOrga}", "L", 68, ' ');
                firstRow = firstRow + batchNum + flowNum + cashDate + filler1 + remitName + filler2;
            }
            
            remitName = CHT_WordPadLeftRight(item.CollecName ?? "", "L", 68, ' ');
            
            // decimal.TryParse(item.RemitPrice, out var currentPrice);
            var currentPrice = (decimal)(item.RemitPrice ?? 0);

            if (item.IfFee == "是" || item.IfFee == "1")
                applymoney = (int)currentPrice - 30;
            else
                applymoney = (int)currentPrice;
                
            memo = item.RemitMemo ?? "";
            
            try
            {
                var byteStr = System.Text.Encoding.GetEncoding("big5").GetBytes(memo);
                if (byteStr.Length >= 50)
                    memo = SubStr(memo, 0, 50);
                else
                    memo = CHT_WordPadLeftRight(memo, "L", 50, ' ');
            }
            catch
            {
                if (memo.Length > 25)
                    memo = memo.Substring(0, 25);
                memo = memo.PadRight(50, ' ');
            }

            var contentRow = "2022" + batchNum + 
                           (totalAmount + 1).ToString().PadLeft(5, ' ') + 
                           cashDate + 
                           (item.CollectNo ?? "").Trim() + 
                           "11" + 
                           (item.CollecAcc ?? "").Replace("-", "").PadLeft(14, '0') + 
                           remitName + 
                           (applymoney.ToString() + "00").PadLeft(14, ' ') + 
                           memo;
                           
            contentRows.Add(contentRow);
            totalCost += applymoney;
            totalAmount++;
        }
        
        string lastRow = "3022";
        string filler3 = "                                                                                                                                         ";
        lastRow += batchNum + "99999" + cashDate + 
                  totalAmount.ToString().PadLeft(4, ' ') + 
                  (totalCost.ToString() + "00").PadLeft(14, ' ') + 
                  filler3;
        
        var result = firstRow + "\r\n" + string.Join("\r\n", contentRows) + "\r\n" + lastRow;
        return result;
    }









    private byte[] GeneratePostalReport(List<ComRemitBlazor.Models.RemitedList> data)
    {
        QuestPDF.Settings.License = LicenseType.Community;

        return Document.Create(container =>
        {
            container.Page(page =>
            {
                page.Size(PageSizes.A4);
                page.Margin(2, Unit.Centimetre);
                page.DefaultTextStyle(x => x.FontFamily("Microsoft YaHei").FontSize(11));

                page.Header().Element(container =>
                {
                    container.Column(column =>
                    {
                        column.Item().AlignCenter().Text("委託郵局代存員工薪資總表(408)")
                            .FontSize(18).Bold();
                        column.Item().PaddingVertical(10);
                    });
                });

                page.Content().Element(container =>
                {
                    container.Column(column =>
                    {
                        column.Item().Table(table =>
                        {
                            table.ColumnsDefinition(columns =>
                            {
                                columns.ConstantColumn(170);
                                columns.ConstantColumn(170);
                                columns.RelativeColumn();
                            });

                            table.Cell().Element(CellStyle).Text($"受託局名: 宜蘭中山路郵局\n\n受託局號: 0111000\n\n檔名: CR{searchId.PadLeft(6, '0')}_Post");
                            table.Cell().Element(CellStyle).Text("劃撥儲金帳號:********\n\n\n押碼值:");
                            
                            table.Cell().Element(CellStyle).Column(col =>
                            {
                                col.Item().Table(innerTable =>
                                {
                                    innerTable.ColumnsDefinition(cols =>
                                    {
                                        cols.ConstantColumn(30);
                                        cols.RelativeColumn();
                                        cols.ConstantColumn(30);
                                    });

                                    innerTable.Cell().Element(CellStyle).Text("勾註");
                                    innerTable.Cell().Element(CellStyle).Text("資料別及作業方式");
                                    innerTable.Cell().Element(CellStyle).Text("批次");
                                    
                                    innerTable.Cell().Element(CellStyle).Text("");
                                    innerTable.Cell().Element(CellStyle).Text("媒體薪資類(Y)");
                                    innerTable.Cell().Element(CellStyle).Text("");
                                    
                                    innerTable.Cell().Element(CellStyle).Text("");
                                    innerTable.Cell().Element(CellStyle).Text("媒體非薪資類(N)");
                                    innerTable.Cell().Element(CellStyle).Text("");
                                });
                            });
                        });

                        column.Item().PaddingVertical(5);

                        var totalAmount = data.Count;
                        //var totalCost = data.Sum(x => decimal.TryParse(x.RemitPrice, out var price) ? price : 0);
                        var totalCost = data.Sum(x => x.RemitPrice ?? 0);
                        var memo = data.FirstOrDefault()?.RemitMemo ?? "";

                        column.Item().Table(table =>
                        {
                            table.ColumnsDefinition(columns =>
                            {
                                columns.RelativeColumn();
                                columns.RelativeColumn();
                                columns.RelativeColumn();
                                columns.RelativeColumn();
                                columns.RelativeColumn();
                                columns.RelativeColumn();
                            });

                            table.Cell().Element(HeaderCellStyle).Text("指定轉存日期");
                            table.Cell().Element(HeaderCellStyle).Text("委存總件數");
                            table.Cell().Element(HeaderCellStyle).Text("委存總金額");
                            table.Cell().Element(HeaderCellStyle).Text("款項細目代碼");
                            table.Cell().Element(HeaderCellStyle).Text("款項來源代號\n(本欄由郵局填寫)");
                            table.Cell().Element(HeaderCellStyle).Text("備註");

                            table.Cell().Element(CellStyle).Text("");
                            table.Cell().Element(CellStyle).AlignCenter().Text($"{totalAmount:N0}");
                            table.Cell().Element(CellStyle).AlignCenter().Text($"{totalCost:N0}");
                            table.Cell().Element(CellStyle).Text("");
                            table.Cell().Element(CellStyle).Text("");
                            table.Cell().Element(CellStyle).Text(memo);
                        });

                        column.Item().PaddingVertical(10);

                        column.Item().Table(table =>
                        {
                            table.ColumnsDefinition(columns =>
                            {
                                columns.ConstantColumn(30);
                                columns.RelativeColumn(2);
                                columns.RelativeColumn(2);
                                columns.RelativeColumn(1.5f);
                                columns.RelativeColumn(1);
                            });

                            table.Cell().Element(HeaderCellStyle).Text("序號");
                            table.Cell().Element(HeaderCellStyle).Text("姓名");
                            table.Cell().Element(HeaderCellStyle).Text("帳號");
                            table.Cell().Element(HeaderCellStyle).Text("身分證字號");
                            table.Cell().Element(HeaderCellStyle).Text("金額");

                            for (int i = 0; i < data.Count; i++)
                            {
                                var item = data[i];
                                //decimal.TryParse(item.RemitPrice, out var price);
                                var price = (decimal)(item.RemitPrice ?? 0);
                                table.Cell().Element(CellStyle).AlignCenter().Text((i + 1).ToString());
                                table.Cell().Element(CellStyle).Text(item.CollecName ?? "");
                                table.Cell().Element(CellStyle).Text(item.CollecAcc ?? "");
                                table.Cell().Element(CellStyle).Text(item.CollectId ?? "");
                                table.Cell().Element(CellStyle).AlignRight().Text($"{price:N0}");
                            }
                        });


                    });
                });

                page.Foreground().PaddingBottom(20).PaddingRight(20).AlignBottom().AlignRight().Column(footerCol =>
                {
                    var qrText = $"CR{searchId.PadLeft(6, '0')}_Post";
                    var barcodeBytes = BarcodeService.GenerateCode128Barcode(qrText);
                    if (barcodeBytes.Length > 0)
                    {
                        footerCol.Item().Width(200).Image(barcodeBytes).FitWidth();
                    }
                    else
                    {
                        var barcodeAscii = BarcodeService.GenerateBarcodeAscii(qrText);
                        footerCol.Item().Width(200).Text(barcodeAscii).FontFamily("Courier New").FontSize(6);
                        footerCol.Item().Width(200).PaddingTop(2).Text(qrText).FontSize(8).AlignCenter();
                    }
                });

                page.Footer().AlignCenter().Text(text =>
                {
                    text.Span($"CR{searchId.PadLeft(6, '0')}_Post - ");
                    text.CurrentPageNumber();
                    text.Span(" / ");
                    text.TotalPages();
                });
            });
        }).GeneratePdf();
    }

    private byte[] GenerateTWBankReportAdvanced(List<ComRemitBlazor.Models.RemitedList> data)
    {
        QuestPDF.Settings.License = LicenseType.Community;

        var totalAmount = data.Count;
        //var totalCost = data.Sum(x => decimal.TryParse(x.RemitPrice, out var price) ? price : 0);
        var totalCost = data.Sum(x => (decimal)(x.RemitPrice ?? 0));
        var totalFee = data.Count(x => x.IfFee == "是" || x.IfFee == "1") * 30;
        var conOrga = "相關單位"; 
        var filename = $"CR{searchId.PadLeft(6, '0')}_TWBANK";
        var batchNum = GenerateBatchNumber();
        var conMemo = data.FirstOrDefault()?.RemitMemo ?? "113全國學生音樂比賽獎金";

        const int recordsPerPage = 18;
        var totalPages = Math.Max(1, (int)Math.Ceiling((double)data.Count / recordsPerPage));

        return Document.Create(container =>
        {
            container.Page(page =>
            {
                page.Size(PageSizes.A4);
                page.Margin(50, Unit.Point);
                page.MarginBottom(80, Unit.Point);
                page.DefaultTextStyle(x => x.FontFamily("Microsoft YaHei").FontSize(10));

                page.Content().Element(container =>
                {
                    container.Column(column =>
                    {
                        for (int copyIndex = 0; copyIndex < 3; copyIndex++)
                        {
                            for (int pageIndex = 0; pageIndex < totalPages; pageIndex++)
                            {
                                if (copyIndex > 0 || pageIndex > 0)
                                {
                                    column.Item().PageBreak();
                                }

                                var currentPageNumber = pageIndex + 1;
                                var startIndex = pageIndex * recordsPerPage;
                                var endIndex = Math.Min(startIndex + recordsPerPage, data.Count);
                                var pageData = data.Skip(startIndex).Take(endIndex - startIndex).ToList();

                                column.Item().Column(col =>
                                {
                                    col.Item().Row(row =>
                                    {
                                        row.RelativeItem(2).Text("整批匯款資料清單");
                                        row.RelativeItem(1).Text("");
                                        row.RelativeItem(2).Text($"頁次：{currentPageNumber}/{totalPages}");
                                    });

                                    col.Item().Row(row =>
                                    {
                                        row.RelativeItem(2).Text($"匯款人:宜府{conOrga}");
                                        row.RelativeItem(1).Text("");
                                        row.RelativeItem(2).Text($"製表日：{DateTime.Now.Year - 1911}年{DateTime.Now.Month}月{DateTime.Now.Day}日");
                                    });

                                    col.Item().Row(row =>
                                    {
                                        row.RelativeItem(2).Text("匯款行:0040222[台灣銀行宜蘭分行]");
                                        row.RelativeItem(1).Text("");
                                        row.RelativeItem(2).Text($"用途：{conMemo}");
                                    });

                                    col.Item().Row(row =>
                                    {
                                        row.RelativeItem(2).Text("匯款日: 年 月 日");
                                        row.RelativeItem(1).Text("");
                                        row.RelativeItem(2).Text($"批號：{batchNum}");
                                    });

                                    col.Item().Row(row =>
                                    {
                                        row.RelativeItem(2).Text("");
                                        row.RelativeItem(1).Text("");
                                        row.RelativeItem(2).Text($"檔名：{filename}");
                                    });

                                    col.Item().PaddingVertical(10);

                                    col.Item().Row(row =>
                                    {
                                        row.ConstantItem(35).Text("序號").AlignCenter();
                                        row.ConstantItem(55).Text("解款行").AlignCenter();
                                        row.ConstantItem(100).Text("帳號").AlignCenter();
                                        row.ConstantItem(55).Text("匯款金額").AlignCenter();
                                        row.ConstantItem(35).Text("手續費").AlignCenter();
                                        row.ConstantItem(60).Text("合計\n(帳款金額)").AlignCenter();
                                        row.RelativeItem(3).Text("收款人姓名\n備註").AlignCenter();
                                    });

                                    col.Item().Text("-------------------------------------------------------------------------------------");

                                    for (int i = 0; i < pageData.Count; i++)
                                    {
                                        var item = pageData[i];
                                        //decimal.TryParse(item.RemitPrice, out var currentPrice);
                                        var currentPrice = (decimal)(item.RemitPrice ?? 0);
                                        var fee = (item.IfFee == "是" || item.IfFee == "1") ? 30 : 0;
                                        var netAmount = currentPrice - fee;
                                        var memo = item.RemitMemo ?? "";
                                        if (memo.Length > 35)
                                            memo = memo.Substring(0, 35) + "..";

                                        col.Item().Row(row =>
                                        {
                                            row.ConstantItem(35).Text((startIndex + i + 1).ToString()).AlignCenter();
                                            row.ConstantItem(55).Text(item.CollectNo ?? "").AlignCenter();
                                            row.ConstantItem(100).Text((item.CollecAcc ?? "").Replace("-", ""));
                                            row.ConstantItem(55).Text($"{netAmount:N0}").AlignRight();
                                            row.ConstantItem(35).Text(fee.ToString()).AlignRight();
                                            row.ConstantItem(60).Text($"{currentPrice:N0}").AlignRight();
                                            row.RelativeItem(3).Text($"({item.CollecName}){memo}");
                                        });
                                    }

                                    if (pageIndex == totalPages - 1)
                                    {
                                        col.Item().PaddingVertical(5);

                                        col.Item().Row(row =>
                                        {
                                            row.RelativeItem(3).Text($"資料總筆數:{totalAmount}       資料總金額:").AlignRight();
                                            row.ConstantItem(55).Text($"{totalCost - totalFee:N0}").AlignRight();
                                            row.ConstantItem(35).Text($"{totalFee}").AlignRight();
                                            row.ConstantItem(60).Text($"{totalCost:N0}").AlignRight();
                                            row.RelativeItem(1).Text("");
                                        });

                                        col.Item().PaddingVertical(5);

                                        col.Item().Row(row =>
                                        {
                                            row.RelativeItem(1).Text("經辦:");
                                            row.RelativeItem(1).Text("單位主管:");
                                            row.RelativeItem(1).Text("製表人:");
                                            row.RelativeItem(1).Text("電話:");
                                        });

                                        col.Item().PaddingVertical(3);

                                        col.Item().Row(row =>
                                        {
                                            row.RelativeItem(2).Text("身分證字號:");
                                            row.RelativeItem(2).Text("電子檔傳送日期:");
                                        });
                                    }


                                });
                            }
                        }
                    });
                });

                page.Foreground().PaddingBottom(20).PaddingRight(20).AlignBottom().AlignRight().Column(footerCol =>
                {
                    var qrText = filename;
                    var barcodeBytes = BarcodeService.GenerateCode128Barcode(qrText);
                    if (barcodeBytes.Length > 0)
                    {
                        footerCol.Item().Width(200).Image(barcodeBytes).FitWidth();
                    }
                    else
                    {
                        var barcodeAscii = BarcodeService.GenerateBarcodeAscii(qrText);
                        footerCol.Item().Width(200).Text(barcodeAscii).FontFamily("Courier New").FontSize(6);
                        footerCol.Item().Width(200).PaddingTop(2).Text(qrText).FontSize(8).AlignCenter();
                    }
                });
            });
        }).GeneratePdf();
    }

    private byte[] GenerateTWBankReport(List<ComRemitBlazor.Models.RemitedList> data)
    {
        QuestPDF.Settings.License = LicenseType.Community;

        var totalAmount = data.Count;
        //var totalCost = data.Sum(x => decimal.TryParse(x.RemitPrice, out var price) ? price : 0);
        var totalCost = data.Sum(x => (decimal)(x.RemitPrice ?? 0));
        var totalFee = data.Count(x => x.IfFee == "是" || x.IfFee == "1") * 30;
        var conOrga = "相關單位";

        return Document.Create(container =>
        {
            container.Page(page =>
            {
                page.Size(PageSizes.A4);
                page.Margin(2, Unit.Centimetre);
                page.DefaultTextStyle(x => x.FontFamily("Microsoft YaHei").FontSize(10));

                page.Content().Element(container =>
                {
                    container.Column(column =>
                    {
                        column.Item().Table(table =>
                        {
                            table.ColumnsDefinition(columns =>
                            {
                                columns.RelativeColumn(2);
                                columns.RelativeColumn(1);
                                columns.RelativeColumn(2);
                            });

                            table.Cell().Element(HeaderCellStyle).Text("整批匯款資料清單");
                            table.Cell().Element(CellStyle).Text("");
                            table.Cell().Element(CellStyle).Text("頁次：1/1");

                            table.Cell().Element(CellStyle).Text($"匯款人:宜府{conOrga}");
                            table.Cell().Element(CellStyle).Text("");
                            table.Cell().Element(CellStyle).Text($"製表日：{DateTime.Now.Year - 1911}年{DateTime.Now.Month}月{DateTime.Now.Day}日");

                            table.Cell().Element(CellStyle).Text("匯款行:0040222[台灣銀行宜蘭分行]");
                            table.Cell().Element(CellStyle).Text("");
                            table.Cell().Element(CellStyle).Text($"用途：{data.FirstOrDefault()?.RemitMemo ?? ""}");

                            table.Cell().Element(CellStyle).Text("匯款日: 年 月 日");
                            table.Cell().Element(CellStyle).Text("");
                            table.Cell().Element(CellStyle).Text($"批號：{GenerateBatchNumber()}");

                            table.Cell().Element(CellStyle).Text("");
                            table.Cell().Element(CellStyle).Text("");
                            table.Cell().Element(CellStyle).Text($"檔名：CR{searchId.PadLeft(6, '0')}_TWBANK");
                        });

                        column.Item().PaddingVertical(10);

                        column.Item().Table(table =>
                        {
                            table.ColumnsDefinition(columns =>
                            {
                                columns.ConstantColumn(40);
                                columns.ConstantColumn(50);
                                columns.RelativeColumn(1.5f);
                                columns.RelativeColumn(0.8f);
                                columns.ConstantColumn(35);
                                columns.RelativeColumn(0.8f);
                                columns.RelativeColumn(2.5f);
                            });

                            table.Cell().Element(HeaderCellStyle).Text("序號");
                            table.Cell().Element(HeaderCellStyle).Text("解款行");
                            table.Cell().Element(HeaderCellStyle).Text("帳號");
                            table.Cell().Element(HeaderCellStyle).Text("匯款金額");
                            table.Cell().Element(HeaderCellStyle).Text("手續費");
                            table.Cell().Element(HeaderCellStyle).Text("合計\n(帳款金額)");
                            table.Cell().Element(HeaderCellStyle).Text("收款人姓名\n備註");

                            for (int i = 0; i < 7; i++)
                            {
                                table.Cell().Element(CellStyle).Text("─────────");
                            }

                            for (int i = 0; i < data.Count; i++)
                            {
                                var item = data[i];
                                //decimal.TryParse(item.RemitPrice, out var remitPrice);
                                var remitPrice = (decimal)(item.RemitPrice ?? 0);
                                var fee = (item.IfFee == "是" || item.IfFee == "1") ? 30 : 0;
                                var netAmount = remitPrice - fee;
                                var memo = item.RemitMemo ?? "";
                                if (memo.Length > 20)
                                    memo = memo.Substring(0, 20) + "..";

                                table.Cell().Element(CellStyle).AlignCenter().Text((i + 1).ToString());
                                table.Cell().Element(CellStyle).AlignCenter().Text(item.CollectNo ?? "");
                                table.Cell().Element(CellStyle).Text((item.CollecAcc ?? "").Replace("-", ""));
                                table.Cell().Element(CellStyle).AlignRight().Text($"{netAmount:N0}");
                                table.Cell().Element(CellStyle).AlignRight().Text(fee.ToString());
                                table.Cell().Element(CellStyle).AlignRight().Text($"{remitPrice:N0}");
                                table.Cell().Element(CellStyle).Text($"({item.CollecName}){memo}");
                            }
                        });

                        column.Item().PaddingVertical(10);

                        column.Item().Table(table =>
                        {
                            table.ColumnsDefinition(columns =>
                            {
                                columns.RelativeColumn(3);
                                columns.RelativeColumn(1);
                                columns.RelativeColumn(1);
                                columns.RelativeColumn(1);
                                columns.RelativeColumn(1);
                            });

                            table.Cell().Element(CellStyle).AlignRight().Text($"資料總筆數:{totalAmount}       資料總金額:");
                            table.Cell().Element(CellStyle).AlignRight().Text($"{totalCost - totalFee:N0}");
                            table.Cell().Element(CellStyle).AlignRight().Text($"{totalFee}");
                            table.Cell().Element(CellStyle).AlignRight().Text($"{totalCost:N0}");
                            table.Cell().Element(CellStyle).Text("");
                        });

                        column.Item().PaddingVertical(10);

                        column.Item().Table(table =>
                        {
                            table.ColumnsDefinition(columns =>
                            {
                                columns.RelativeColumn(1);
                                columns.RelativeColumn(1);
                            });

                            table.Cell().Element(CellStyle).Text("經辦:");
                            table.Cell().Element(CellStyle).Text("單位主管:");
                            table.Cell().Element(CellStyle).Text("製表人:");
                            table.Cell().Element(CellStyle).Text("電話:");
                            table.Cell().Element(CellStyle).Text("身分證字號:");
                            table.Cell().Element(CellStyle).Text("電子檔傳送日期:");
                        });


                    });
                });

                page.Foreground().PaddingBottom(20).PaddingRight(20).AlignBottom().AlignRight().Column(footerCol =>
                {
                    var qrText = $"CR{searchId.PadLeft(6, '0')}_TWBANK";
                    var barcodeBytes = BarcodeService.GenerateCode128Barcode(qrText);
                    if (barcodeBytes.Length > 0)
                    {
                        footerCol.Item().Width(200).Image(barcodeBytes).FitWidth();
                    }
                    else
                    {
                        var barcodeAscii = BarcodeService.GenerateBarcodeAscii(qrText);
                        footerCol.Item().Width(200).Text(barcodeAscii).FontFamily("Courier New").FontSize(6);
                        footerCol.Item().Width(200).PaddingTop(2).Text(qrText).FontSize(8).AlignCenter();
                    }
                });

                page.Footer().AlignCenter().Text($"CR{searchId.PadLeft(6, '0')}_TWBANK");
            });
        }).GeneratePdf();
    }

    private string GenerateFinancialExportContent(List<ComRemitBlazor.Models.RemitedList> data, int mingGuoYear)
    {
        var lines = new List<string>();

        foreach (var item in data)
        {
            var ifFeeString = (item.IfFee == "是" || item.IfFee == "1") ? "是" : "否";
            var line = $"CR{mingGuoYear}{item.ConSno},{item.CollectNo},{(item.CollecAcc ?? "").Replace("-", "")},{item.CollecName},{item.RemitPrice},{ifFeeString},{item.RemitMemo?.Trim()}";
            lines.Add(line);
        }

        return string.Join("\r\n", lines);
    }

    private string GenerateBatchNumber()
    {
        var today = DateTime.Now;
        var dateStr = $"{today.Year - 1911:D2}{today.Month:D2}{today.Day:D2}";
        return $"{dateStr}6101";
    }

    private static IContainer CellStyle(IContainer container)
    {
        return container
            .Border(0.5f)
            .BorderColor(Colors.Black)
            .PaddingVertical(5)
            .PaddingHorizontal(5);
    }

    private static IContainer HeaderCellStyle(IContainer container)
    {
        return container
            .Border(0.5f)
            .BorderColor(Colors.Black)
            .Background(Colors.Grey.Lighten3)
            .PaddingVertical(5)
            .PaddingHorizontal(5)
            .AlignCenter()
            .AlignMiddle();
    }
    
    private string CHT_WordPadLeftRight(string org, string RL, int sLen, char padStr)
    {
        var sResult = "";
        int orgLen = 0;
        int tLen = 0;
        for (int i = 0; i < org.Length; i++)
        {
            string s = org.Substring(i, 1);
            int vLen = 0;
            if (Convert.ToInt32(s[0]) > 128 || Convert.ToInt32(s[0]) < 0)
            {
                vLen = 2;
            }
            else
            {
                vLen = 1;
            }
            orgLen += vLen;
            if (orgLen > sLen)
            {
                orgLen -= vLen;
                break;
            }
            sResult += s;
        }
        tLen = sLen - (orgLen - org.Length);
        if (RL == "R")
        {
            return sResult.PadLeft(tLen, padStr);
        }
        else
        {
            return sResult.PadRight(tLen, padStr);
        }
    }

    private string SubStr(string a_SrcStr, int a_StartIndex, int a_Cnt)
    {
        try
        {
            var l_Encoding = System.Text.Encoding.GetEncoding("big5");
            byte[] l_byte = l_Encoding.GetBytes(a_SrcStr);
            if (a_Cnt <= 0)
                return "";
            if (a_StartIndex + 1 > l_byte.Length)
                return "";
            else
            {
                if (a_StartIndex + a_Cnt > l_byte.Length)
                    a_Cnt = l_byte.Length - a_StartIndex;
            }
            return l_Encoding.GetString(l_byte, a_StartIndex, a_Cnt);
        }
        catch
        {
            if (a_StartIndex < a_SrcStr.Length)
            {
                int length = Math.Min(a_Cnt, a_SrcStr.Length - a_StartIndex);
                return a_SrcStr.Substring(a_StartIndex, length);
            }
            return "";
        }
    }
}