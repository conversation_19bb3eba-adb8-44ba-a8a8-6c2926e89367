/*!*  filename: sf-daterangepicker.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[20],{"./bundles/sf-daterangepicker.js":function(e,t,n){"use strict";n.r(t);n("./modules/sf-daterangepicker.js")},"./modules/sf-daterangepicker.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.DateRangePicker=function(){"use strict";var e="e-left-calendar",t="e-right-calendar",n="e-calendar",o="e-header",s="e-focused-date",i=function(){function i(e,t,n,o,s){window.sfBlazor=window.sfBlazor,this.dataId=e,this.containerElement=t,this.element=n,this.options=s,window.sfBlazor.setCompInstance(this),this.dotNetRef=o}return i.prototype.initialize=function(){this.isMobile=window.matchMedia("(max-width:550px)").matches,this.defaultKeyConfigs={altUpArrow:"alt+uparrow",altDownArrow:"alt+downarrow",altRightArrow:"alt+rightarrow",altLeftArrow:"alt+leftarrow",escape:"escape",enter:"enter",controlUp:"ctrl+38",controlDown:"ctrl+40",moveDown:"downarrow",moveUp:"uparrow",moveLeft:"leftarrow",moveRight:"rightarrow",select:"enter",home:"home",end:"end",pageUp:"pageup",pageDown:"pagedown",shiftPageUp:"shift+pageup",shiftPageDown:"shift+pagedown",controlHome:"ctrl+home",controlEnd:"ctrl+end",shiftTab:"shift+tab"},new sf.base.KeyboardEvents(this.element,{eventName:"keydown",keyAction:this.keyInputHandler.bind(this),keyConfigs:this.defaultKeyConfigs})},i.prototype.keyInputHandler=function(n){var o;if(this.popupObj&&this.popupObj.element.classList.contains("e-popup")){var i=sf.base.closest(n.target,"."+t),r=(i=sf.base.isNullOrUndefined(i)?this.tableElement.querySelector("."+e):i).classList.contains(e),l=this.tableElement.querySelector("tr td."+s),a=this.tableElement.querySelectorAll("tr td.e-selected"),p=a[a.length-1],c=l&&sf.base.closest(l,"tbody")||p&&sf.base.closest(p,"tbody");!r&&this.leftCalendar.querySelectorAll("tr td."+s).length>0&&sf.base.removeClass(this.leftCalendar.querySelectorAll("tr td."+s),s),c&&c.focus(),o={Action:n.action,Key:n.key,Events:n,SelectDate:p?p.id:null,FocusedDate:l?l.id:null,classList:p?p.classList.toString():l?l.classList.toString():"e-cell",Id:l?l.id:p?p.id:null,TargetClassList:this.calendarElement.classList.toString(),IsLeftCalendar:r,FocusedDateClassList:l?l.classList.toString():null}}else o={Action:n.action,Key:n.key,Events:n};this.isDisposed||this.dotNetRef.invokeMethodAsync("InputKeyActionHandle",o),"select"!==n.action&&this.popupObj&&document.body.contains(this.popupObj.element)&&n.preventDefault()},i.prototype.getAppendToElement=function(e){var t=document.body;return"model"===e&&this.mobilePopupContainer?t=this.mobilePopupContainer:"body"===e||sf.base.isNullOrUndefined(e)||0===e.length||(t=sf.base.select(e,document)||t),t},i.prototype.renderPopup=function(e,t,n,o){e.querySelectorAll(".e-header.e-month").forEach((function(e){e.querySelectorAll("button, .e-day.e-title").forEach((function(e){e.setAttribute("tabindex","-1")}))})),this.options=o,this.popupHolder=t;var s=document.body.querySelector(".e-daterangepicker.e-popup-open");s&&sf.base.remove(s),this.createCalendar(e,o,n.appendTo),sf.base.Browser.isDevice&&(this.mobilePopupContainer=this.popupHolder.querySelector(".e-daterangepick-mob-popup-wrap"),document.body.appendChild(this.mobilePopupContainer)),this.getAppendToElement(n.appendTo).appendChild(this.popupContainer),this.presetHeight(),1e3===this.options.zIndex?this.popupObj.show(null,this.element):this.popupObj.show(null,null),this.setOverlayIndex(this.mobilePopupContainer,this.popupObj.element,this.modal,sf.base.Browser.isDevice),sf.base.Browser.isDevice&&(document.body.className+=" e-range-overflow",this.popupHolder.style.display="block",this.popupHolder.style.visibility="visible"),sf.base.EventHandler.add(document,"mousedown touchstart",this.documentHandler,this)},i.prototype.setOverlayIndex=function(e,t,n,o){if(o&&!sf.base.isNullOrUndefined(t)&&!sf.base.isNullOrUndefined(n)&&!sf.base.isNullOrUndefined(e)){var s=parseInt(t.style.zIndex,10)?parseInt(t.style.zIndex,10):1e3;n.style.zIndex=(s-1).toString(),e.style.zIndex=s.toString()}},i.prototype.closePopup=function(e,t){this.options=t,this.closeEventCallback(e)},i.prototype.refreshPopup=function(){this.isPopupOpen()&&this.popupObj.refreshPosition(),1e3===this.options.zIndex?this.popupObj.show(null,this.element):this.popupObj.show(null,null)},i.prototype.CalendarScrollHandler=function(e){var t="Right"===e.scrollDirection||"Left"===e.scrollDirection;this.isTouchstart&&t&&(this.dotNetRef.invokeMethodAsync("ScrollToNextSection","Left"==e.scrollDirection),this.isTouchstart=!1)},i.prototype.TouchStartHandler=function(e){this.isTouchstart=!0},i.prototype.createCalendar=function(s,i,r){var l=this;this.popupContainer=s,i.isCustomWindow&&(this.calendarElement=this.popupContainer.firstElementChild,this.tableElement=this.calendarElement,this.calendarElement.querySelector("table tbody").className="");var a=this.popupContainer.getBoundingClientRect().width;sf.base.Browser.isDevice&&(this.modal=this.popupHolder.querySelector(".e-range-modal"),this.modal.style.display="block",document.body.appendChild(this.modal)),i.isCustomWindow&&(sf.base.Browser.isDevice?(this.deviceCalPrevIcon=this.calendarElement.querySelector("."+n+" .e-prev"),this.deviceCalNextIcon=this.calendarElement.querySelector("."+n+" .e-next"),this.calendarElement.querySelector("."+n+" .e-icon-container")&&sf.base.remove(this.calendarElement.querySelector("."+n+" .e-icon-container")),this.calendarElement.querySelector("table").setAttribute("tabindex","-1"),this.calendarElement.querySelector("."+n+" ."+o).appendChild(this.deviceCalNextIcon),this.calendarElement.querySelector("."+n+" ."+o).appendChild(this.deviceCalPrevIcon),sf.base.prepend([this.deviceCalPrevIcon],this.calendarElement.querySelector("."+n+" ."+o)),this.calendarElement.querySelector("."+n+" .e-footer-container")&&sf.base.remove(this.calendarElement.querySelector("."+n+" .e-footer-container"))):(this.leftCalPrevIcon=this.calendarElement.querySelector("."+e+" .e-prev"),this.leftCalNextIcon=this.calendarElement.querySelector("."+e+" .e-next"),this.leftTitle=this.calendarElement.querySelector("."+e+" .e-title"),this.calendarElement.querySelector("."+e+" .e-icon-container")&&sf.base.remove(this.calendarElement.querySelector("."+e+" .e-icon-container")),this.calendarElement.querySelector("."+e+" ."+o).appendChild(this.leftCalNextIcon),this.calendarElement.querySelector("."+e+" ."+o).appendChild(this.leftCalPrevIcon),sf.base.prepend([this.leftCalPrevIcon],this.calendarElement.querySelector("."+e+" ."+o)),this.rightCalPrevIcon=this.calendarElement.querySelector("."+t+" .e-prev"),this.rightCalNextIcon=this.calendarElement.querySelector("."+t+" .e-next"),this.rightTitle=this.calendarElement.querySelector("."+t+" .e-title"),this.calendarElement.querySelector("."+t+" .e-icon-container")&&sf.base.remove(this.calendarElement.querySelector("."+t+" .e-icon-container")),this.calendarElement.querySelector("table").setAttribute("tabindex","-1"),this.calendarElement.querySelector("."+t+" ."+o).appendChild(this.rightCalNextIcon),this.calendarElement.querySelector("."+t+" ."+o).appendChild(this.rightCalPrevIcon),sf.base.prepend([this.rightCalPrevIcon],this.calendarElement.querySelector("."+t+" ."+o)),this.calendarElement.querySelector("."+e+" .e-footer-container")&&sf.base.remove(this.calendarElement.querySelector("."+e+" .e-footer-container")),this.calendarElement.querySelector("."+t+" .e-footer-container")&&sf.base.remove(this.calendarElement.querySelector("."+t+" .e-footer-container")))),this.popupObj=new sf.popups.Popup(this.popupContainer,{relateTo:this.isMobile?document.body:this.containerElement,position:this.isMobile?sf.base.isUndefined(i.presets&&i.presets[0]&&i.presets[0].start&&i.presets[0].end&&i.presets[0].label)?{X:"center",Y:"center"}:{X:"left",Y:"bottom"}:i.enableRtl?{X:"left",Y:"bottom"}:{X:"right",Y:"bottom"},offsetX:this.isMobile||i.enableRtl?0:-a,offsetY:4,collision:this.isMobile?sf.base.isUndefined(i.presets&&i.presets[0]&&i.presets[0].start&&i.presets[0].end&&i.presets[0].label)?{X:"fit",Y:"fit"}:{X:"fit"}:{X:"fit",Y:"flip"},targetType:this.isMobile?"container":"relative",enableRtl:i.enableRtl,zIndex:i.zIndex,open:function(){sf.base.Browser.isDevice&&l.calendarElement&&(l.touchModule=new sf.base.Touch(l.calendarElement.children[1],{scroll:l.CalendarScrollHandler.bind(l)}),sf.base.EventHandler.add(l.calendarElement.children[1],"touchstart",l.TouchStartHandler,l)),l.calendarElement&&(l.leftCalendar=l.calendarElement.querySelector("."+e),l.rightCalendar=l.calendarElement.querySelector("."+t));var n=l.popupObj&&l.popupObj.element.querySelector(".e-date-range-container");if(!l.isMobile&&n){new sf.base.KeyboardEvents(l.leftCalendar,{eventName:"keydown",keyAction:l.keyInputHandler.bind(l),keyConfigs:l.defaultKeyConfigs}),new sf.base.KeyboardEvents(l.rightCalendar,{eventName:"keydown",keyAction:l.keyInputHandler.bind(l),keyConfigs:l.defaultKeyConfigs});var o=l.popupContainer.querySelector(".e-cancel.e-btn"),s=l.popupContainer.querySelector(".e-apply.e-btn");new sf.base.KeyboardEvents(o,{eventName:"keydown",keyAction:l.keyInputHandler.bind(l),keyConfigs:{tab:"tab",altRightArrow:"alt+rightarrow",altLeftArrow:"alt+leftarrow"}}),new sf.base.KeyboardEvents(s,{eventName:"keydown",keyAction:l.keyInputHandler.bind(l),keyConfigs:{altRightArrow:"alt+rightarrow",altLeftArrow:"alt+leftarrow"}}),l.leftCalendar.querySelector("table").focus()}},close:function(){l.popupHolder.appendChild(l.popupContainer),l.popupObj&&l.popupObj.destroy(),l.isDisposed||l.dotNetRef.invokeMethodAsync("ClosePopup").catch((function(){})),l.popupObj=null},targetExitViewport:function(){sf.base.Browser.isDevice||l.isDisposed||l.dotNetRef.invokeMethodAsync("HidePopup",null)}})},i.prototype.closeEventCallback=function(e){!e.cancel&&this.popupObj&&this.popupObj.hide(),sf.base.Browser.isDevice&&this.modal&&(this.modal.style.display="none",this.modal.outerHTML="",this.modal=null),sf.base.Browser.isDevice&&(sf.base.removeClass([document.body],"e-range-overflow"),sf.base.isNullOrUndefined(this.mobilePopupContainer)||(this.mobilePopupContainer.remove(),this.mobilePopupContainer=null)),sf.base.EventHandler.remove(document,"mousedown touchstart",this.documentHandler)},i.prototype.documentHandler=function(e){if(!sf.base.isNullOrUndefined(this.popupObj)){var t=e.target;this.containerElement.contains(t)&&(sf.base.isNullOrUndefined(this.popupObj)||sf.base.closest(t,".e-daterangepicker.e-popup"))||"touchstart"!==e.type&&"mousedown"===e.type&&e.preventDefault(),sf.base.closest(t,".e-daterangepicker.e-popup")||sf.base.closest(t,".e-input-group")===this.containerElement||sf.base.closest(t,".e-daterangepicker.e-popup")&&!t.classList.contains("e-day")||this.isPopupOpen()&&document.body.contains(this.popupObj.element)&&this.applyFunction(e)}},i.prototype.applyFunction=function(e){"touchstart"!==e.type&&e.preventDefault(),document.activeElement!==this.element&&(this.element.focus(),sf.base.addClass([this.containerElement],["e-input-focus"])),this.dotNetRef.invokeMethodAsync("HidePopup",null),sf.base.closest(e.target,".e-input-group")||document.activeElement===this.element&&(sf.base.removeClass([this.containerElement],["e-input-focus"]),this.element.blur())},i.prototype.presetHeight=function(){var e=this.popupObj&&this.popupObj.element.querySelector(".e-presets"),t=this.popupObj&&this.popupObj.element.querySelector(".e-date-range-container");sf.base.isNullOrUndefined(e)||sf.base.isNullOrUndefined(t)||(e.style.height=t.getBoundingClientRect().height+"px")},i.prototype.getPopupHeight=function(){var e=parseInt("240px",10),t=this.popupContainer.getBoundingClientRect().height;return t>e?e:t},i.prototype.setScrollPosition=function(){if(!sf.base.isNullOrUndefined(this.popupContainer.querySelector(".e-active"))&&this.options.presets){var e=this.popupContainer.querySelector(".e-active");this.findScrollTop(e)}},i.prototype.findScrollTop=function(e){var t=this.getPopupHeight(),n=e.nextElementSibling,o=n?n.offsetTop:e.offsetTop,s=e.getBoundingClientRect().height;o+e.offsetTop>t?this.popupContainer.scrollTop=n?o-(t/2+s/2):o:this.popupContainer.scrollTop=0},i.prototype.isPopupOpen=function(){return!(sf.base.isNullOrUndefined(this.popupObj)||!this.popupObj.element.classList.contains("e-popup"))},i.prototype.updateAriaActiveDescendant=function(e){this.element.setAttribute("aria-activedescendant",e)},i.prototype.removeFocusDate=function(e,t){var n=e.querySelectorAll("tr td.e-focused-date");n.length>0&&sf.base.removeClass(n,s);for(var o=e.querySelectorAll("tr td"),i=0;i<o.length;i++)o[i].getAttribute("id").split("_")[0]==t&&(o[i].classList.contains("e-disabled")?sf.base.addClass(n,s):(sf.base.removeClass(o,s),sf.base.addClass([o[i]],s),sf.base.closest(o[i],"table").focus()))},i}();return{initialize:function(e,t,n,o,s){if(n){var r=new i(e,t,n,o,s);if(r.initialize(),!sf.base.isNullOrUndefined(sf.base.closest(n,"fieldset"))&&sf.base.closest(n,"fieldset").disabled){var l=s.enabled=!1;r.dotNetRef.invokeMethodAsync("UpdateFieldSetStatus",l)}}},renderPopup:function(e,t,n,o,s){var i=window.sfBlazor.getCompInstance(e);!sf.base.isNullOrUndefined(i)&&t&&n&&i.renderPopup(t,n,o,s)},updateScrollPosition:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.setScrollPosition()},updateAriaActiveDescendant:function(e,t){var n=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(n)||n.updateAriaActiveDescendant(t)},removeFocusDate:function(e,t,n){var o=window.sfBlazor.getCompInstance(e);!sf.base.isNullOrUndefined(o)&&t&&o.removeFocusDate(t,n)},closePopup:function(e,t,n){var o=window.sfBlazor.getCompInstance(e);!sf.base.isNullOrUndefined(o)&&t&&o.closePopup(t,n)},refreshPopup:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.refreshPopup()},focusIn:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||(document.activeElement!==t.element?setTimeout((function(){t.element.focus()}),100):t.element.focus())},focusOut:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.element.blur()},destroy:function(e,t,n,o,s){var i=window.sfBlazor.getCompInstance(e);!sf.base.isNullOrUndefined(i)&&t&&t instanceof HTMLElement&&n&&(i.isDisposed=!0,i.closePopup(o,s))}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfdaterangepicker');})})();