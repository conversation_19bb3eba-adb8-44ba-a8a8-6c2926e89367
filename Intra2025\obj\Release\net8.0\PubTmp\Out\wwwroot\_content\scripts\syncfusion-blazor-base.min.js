/*!*  filename: syncfusion-blazor-base.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
!function(e){function t(t){for(var n,i,o=t[0],M=t[1],a=0,s=[];a<o.length;a++)i=o[a],Object.prototype.hasOwnProperty.call(r,i)&&r[i]&&s.push(r[i][0]),r[i]=0;for(n in M)Object.prototype.hasOwnProperty.call(M,n)&&(e[n]=M[n]);for(u&&u(t);s.length;)s.shift()()}var n={},r={0:0};function i(e){var t=function(e){return o.p+""+({1:"data",2:"drawings",3:"navigationsbase",4:"popup",5:"popupsbase",6:"sf-accordion",7:"sf-accumulation-chart",8:"sf-barcode",9:"sf-breadcrumb",10:"sf-bullet-chart",11:"sf-calendar",12:"sf-carousel",13:"sf-chart",14:"sf-chart3D",15:"sf-circulargauge",16:"sf-colorpicker",17:"sf-contextmenu",18:"sf-dashboard-layout",19:"sf-datepicker",20:"sf-daterangepicker",21:"sf-diagramcomponent",22:"sf-dialog",23:"sf-drop-down-button",24:"sf-dropdownlist",25:"sf-dropdowntree",26:"sf-filemanager",27:"sf-floating-action-button",28:"sf-gantt",29:"sf-grid",30:"sf-heatmap",31:"sf-image-editor",32:"sf-inplaceeditor",33:"sf-kanban",34:"sf-lineargauge",35:"sf-listbox",36:"sf-listview",37:"sf-maps",38:"sf-maskedtextbox",39:"sf-mention",40:"sf-menu",41:"sf-multiselect",42:"sf-numerictextbox",43:"sf-otp-input",44:"sf-pager",45:"sf-pdfviewer",46:"sf-pivotview",47:"sf-progressbar",48:"sf-range-navigator",49:"sf-rating",50:"sf-richtexteditor",51:"sf-schedule",52:"sf-sidebar",53:"sf-signature",54:"sf-slider",55:"sf-smith-chart",56:"sf-sparkline",57:"sf-speeddial",58:"sf-spinner",59:"sf-splitter",60:"sf-stepper",61:"sf-stock-chart",62:"sf-svg-export",63:"sf-tab",64:"sf-textarea",65:"sf-textbox",66:"sf-timepicker",67:"sf-toast",68:"sf-toolbar",69:"sf-tooltip",70:"sf-treegrid",71:"sf-treemap",72:"sf-treeview",73:"sf-uploader",74:"sortable",75:"spinner",76:"splitbuttonsbase",77:"svgbase"}[e]||e)+".min.js"}(e);try{return t+"?v=26.1.undefined"}catch(e){return t}}function o(t){if(n[t])return n[t].exports;var r=n[t]={i:t,l:!1,exports:{}};return e[t].call(r.exports,r,r.exports,o),r.l=!0,r.exports}o.e=function(e){var t=[],n=r[e];if(0!==n)if(n)t.push(n[2]);else{var M=new Promise((function(t,i){n=r[e]=[t,i]}));t.push(n[2]=M);var a,s=document.createElement("script");s.charset="utf-8",s.timeout=120,o.nc&&s.setAttribute("nonce",o.nc),s.src=i(e);var u=new Error;a=function(t){s.onerror=s.onload=null,clearTimeout(c);var n=r[e];if(0!==n){if(n){var i=t&&("load"===t.type?"missing":t.type),o=t&&t.target&&t.target.src;u.message="Loading chunk "+e+" failed.\n("+i+": "+o+")",u.name="ChunkLoadError",u.type=i,u.request=o,n[1](u)}r[e]=void 0}};var c=setTimeout((function(){a({type:"timeout",target:s})}),12e4);s.onerror=s.onload=a,document.head.appendChild(s)}return Promise.all(t)},o.m=e,o.c=n,o.d=function(e,t,n){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(o.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)o.d(n,r,function(t){return e[t]}.bind(null,r));return n},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="_content/Syncfusion.Blazor/scripts/",o.oe=function(e){throw console.error(e),e};var M=window.webpackJsonp=window.webpackJsonp||[],a=M.push.bind(M);M.push=t,M=M.slice();for(var s=0;s<M.length;s++)t(M[s]);var u=a;o(o.s="./index.js")}({"./index.js":function(e,t,n){"use strict";n.r(t);n("./modules/base.js"),n("./syncfusion-blazor.js"),n("./modules/sf-import-script.js");function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */i=function(){return t};var e,t={},n=Object.prototype,o=n.hasOwnProperty,M=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function N(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{N({},"")}catch(e){N=function(e,t,n){return e[t]=n}}function g(e,t,n,r){var i=t&&t.prototype instanceof y?t:y,o=Object.create(i.prototype),a=new S(r||[]);return M(o,"_invoke",{value:L(e,n,a)}),o}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=g;var j="suspendedStart",D="executing",p="completed",z={};function y(){}function I(){}function d(){}var h={};N(h,s,(function(){return this}));var f=Object.getPrototypeOf,m=f&&f(f(v([])));m&&m!==n&&o.call(m,s)&&(h=m);var T=d.prototype=y.prototype=Object.create(h);function A(e){["next","throw","return"].forEach((function(t){N(e,t,(function(e){return this._invoke(t,e)}))}))}function O(e,t){function n(i,M,a,s){var u=l(e[i],e,M);if("throw"!==u.type){var c=u.arg,N=c.value;return N&&"object"==r(N)&&o.call(N,"__await")?t.resolve(N.__await).then((function(e){n("next",e,a,s)}),(function(e){n("throw",e,a,s)})):t.resolve(N).then((function(e){c.value=e,a(c)}),(function(e){return n("throw",e,a,s)}))}s(u.arg)}var i;M(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,i){n(e,r,t,i)}))}return i=i?i.then(o,o):o()}})}function L(t,n,r){var i=j;return function(o,M){if(i===D)throw Error("Generator is already running");if(i===p){if("throw"===o)throw M;return{value:e,done:!0}}for(r.method=o,r.arg=M;;){var a=r.delegate;if(a){var s=x(a,r);if(s){if(s===z)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===j)throw i=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=D;var u=l(t,n,r);if("normal"===u.type){if(i=r.done?p:"suspendedYield",u.arg===z)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=p,r.method="throw",r.arg=u.arg)}}}function x(t,n){var r=n.method,i=t.iterator[r];if(i===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,x(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),z;var o=l(i,t.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,z;var M=o.arg;return M?M.done?(n[t.resultName]=M.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,z):M:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,z)}function b(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(b,this),this.reset(!0)}function v(t){if(t||""===t){var n=t[s];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,M=function n(){for(;++i<t.length;)if(o.call(t,i))return n.value=t[i],n.done=!1,n;return n.value=e,n.done=!0,n};return M.next=M}}throw new TypeError(r(t)+" is not iterable")}return I.prototype=d,M(T,"constructor",{value:d,configurable:!0}),M(d,"constructor",{value:I,configurable:!0}),I.displayName=N(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===I||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,N(e,c,"GeneratorFunction")),e.prototype=Object.create(T),e},t.awrap=function(e){return{__await:e}},A(O.prototype),N(O.prototype,u,(function(){return this})),t.AsyncIterator=O,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var M=new O(g(e,n,r,i),o);return t.isGeneratorFunction(n)?M:M.next().then((function(e){return e.done?e.value:M.next()}))},A(T),N(T,c,"Generator"),N(T,s,(function(){return this})),N(T,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=v,S.prototype={constructor:S,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(r,i){return a.type="throw",a.arg=t,n.next=r,i&&(n.method="next",n.arg=e),!!i}for(var i=this.tryEntries.length-1;i>=0;--i){var M=this.tryEntries[i],a=M.completion;if("root"===M.tryLoc)return r("end");if(M.tryLoc<=this.prev){var s=o.call(M,"catchLoc"),u=o.call(M,"finallyLoc");if(s&&u){if(this.prev<M.catchLoc)return r(M.catchLoc,!0);if(this.prev<M.finallyLoc)return r(M.finallyLoc)}else if(s){if(this.prev<M.catchLoc)return r(M.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<M.finallyLoc)return r(M.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var M=i?i.completion:{};return M.type=e,M.arg=t,i?(this.method="next",this.next=i.finallyLoc,z):this.complete(M)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),z},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),z}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;E(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:v(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),z}},t}function o(e,t,n,r,i,o,M){try{var a=e[o](M),s=a.value}catch(e){return void n(e)}a.done?t(s):Promise.resolve(s).then(r,i)}function M(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var M=e.apply(t,n);function a(e){o(M,r,i,a,s,"next",e)}function s(e){o(M,r,i,a,s,"throw",e)}a(void 0)}))}}!function(e){function t(e){return r.apply(this,arguments)}function r(){return(r=M(i().mark((function e(t){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:e.t0=t,e.next="data"===e.t0?3:"drawings"===e.t0?6:"navigationsbase"===e.t0?9:"popup"===e.t0?12:"popupsbase"===e.t0?15:"sf-accordion"===e.t0?18:"sf-accumulation-chart"===e.t0?21:"sf-barcode"===e.t0?24:"sf-breadcrumb"===e.t0?27:"sf-bullet-chart"===e.t0?30:"sf-calendar"===e.t0?33:"sf-carousel"===e.t0?36:"sf-chart"===e.t0?39:"sf-chart3D"===e.t0?42:"sf-circulargauge"===e.t0?45:"sf-colorpicker"===e.t0?48:"sf-contextmenu"===e.t0?51:"sf-dashboard-layout"===e.t0?54:"sf-datepicker"===e.t0?57:"sf-daterangepicker"===e.t0?60:"sf-diagramcomponent"===e.t0?63:"sf-dialog"===e.t0?66:"sf-drop-down-button"===e.t0?69:"sf-dropdownlist"===e.t0?72:"sf-dropdowntree"===e.t0?75:"sf-filemanager"===e.t0?78:"sf-floating-action-button"===e.t0?81:"sf-gantt"===e.t0?84:"sf-grid"===e.t0?87:"sf-heatmap"===e.t0?90:"sf-image-editor"===e.t0?93:"sf-inplaceeditor"===e.t0?96:"sf-kanban"===e.t0?99:"sf-lineargauge"===e.t0?102:"sf-listbox"===e.t0?105:"sf-listview"===e.t0?108:"sf-maps"===e.t0?111:"sf-maskedtextbox"===e.t0?114:"sf-mention"===e.t0?117:"sf-menu"===e.t0?120:"sf-multiselect"===e.t0?123:"sf-numerictextbox"===e.t0?126:"sf-otp-input"===e.t0?129:"sf-pager"===e.t0?132:"sf-pdfviewer"===e.t0?135:"sf-pivotview"===e.t0?138:"sf-progressbar"===e.t0?141:"sf-range-navigator"===e.t0?144:"sf-rating"===e.t0?147:"sf-richtexteditor"===e.t0?150:"sf-schedule"===e.t0?153:"sf-sidebar"===e.t0?156:"sf-signature"===e.t0?159:"sf-slider"===e.t0?162:"sf-smith-chart"===e.t0?165:"sf-sparkline"===e.t0?168:"sf-speeddial"===e.t0?171:"sf-spinner"===e.t0?174:"sf-splitter"===e.t0?177:"sf-stepper"===e.t0?180:"sf-stock-chart"===e.t0?183:"sf-svg-export"===e.t0?186:"sf-tab"===e.t0?189:"sf-textarea"===e.t0?192:"sf-textbox"===e.t0?195:"sf-timepicker"===e.t0?198:"sf-toast"===e.t0?201:"sf-toolbar"===e.t0?204:"sf-tooltip"===e.t0?207:"sf-treegrid"===e.t0?210:"sf-treemap"===e.t0?213:"sf-treeview"===e.t0?216:"sf-uploader"===e.t0?219:"sortable"===e.t0?222:"spinner"===e.t0?225:"splitbuttonsbase"===e.t0?228:"svgbase"===e.t0?231:234;break;case 3:return e.next=5,n.e(1).then(n.bind(null,"./bundles/data.js")).then((function(){}));case 5:return e.abrupt("break",234);case 6:return e.next=8,n.e(2).then(n.bind(null,"./bundles/drawings.js")).then((function(){}));case 8:return e.abrupt("break",234);case 9:return e.next=11,n.e(3).then(n.bind(null,"./bundles/navigationsbase.js")).then((function(){}));case 11:return e.abrupt("break",234);case 12:return e.next=14,n.e(4).then(n.bind(null,"./bundles/popup.js")).then((function(){}));case 14:return e.abrupt("break",234);case 15:return e.next=17,n.e(5).then(n.bind(null,"./bundles/popupsbase.js")).then((function(){}));case 17:return e.abrupt("break",234);case 18:return e.next=20,n.e(6).then(n.bind(null,"./bundles/sf-accordion.js")).then((function(){}));case 20:return e.abrupt("break",234);case 21:return e.next=23,n.e(7).then(n.bind(null,"./bundles/sf-accumulation-chart.js")).then((function(){}));case 23:return e.abrupt("break",234);case 24:return e.next=26,n.e(8).then(n.bind(null,"./bundles/sf-barcode.js")).then((function(){}));case 26:return e.abrupt("break",234);case 27:return e.next=29,n.e(9).then(n.bind(null,"./bundles/sf-breadcrumb.js")).then((function(){}));case 29:return e.abrupt("break",234);case 30:return e.next=32,n.e(10).then(n.bind(null,"./bundles/sf-bullet-chart.js")).then((function(){}));case 32:return e.abrupt("break",234);case 33:return e.next=35,n.e(11).then(n.bind(null,"./bundles/sf-calendar.js")).then((function(){}));case 35:return e.abrupt("break",234);case 36:return e.next=38,n.e(12).then(n.bind(null,"./bundles/sf-carousel.js")).then((function(){}));case 38:return e.abrupt("break",234);case 39:return e.next=41,n.e(13).then(n.bind(null,"./bundles/sf-chart.js")).then((function(){}));case 41:return e.abrupt("break",234);case 42:return e.next=44,n.e(14).then(n.bind(null,"./bundles/sf-chart3D.js")).then((function(){}));case 44:return e.abrupt("break",234);case 45:return e.next=47,n.e(15).then(n.bind(null,"./bundles/sf-circulargauge.js")).then((function(){}));case 47:return e.abrupt("break",234);case 48:return e.next=50,n.e(16).then(n.bind(null,"./bundles/sf-colorpicker.js")).then((function(){}));case 50:return e.abrupt("break",234);case 51:return e.next=53,n.e(17).then(n.bind(null,"./bundles/sf-contextmenu.js")).then((function(){}));case 53:return e.abrupt("break",234);case 54:return e.next=56,n.e(18).then(n.bind(null,"./bundles/sf-dashboard-layout.js")).then((function(){}));case 56:return e.abrupt("break",234);case 57:return e.next=59,n.e(19).then(n.bind(null,"./bundles/sf-datepicker.js")).then((function(){}));case 59:return e.abrupt("break",234);case 60:return e.next=62,n.e(20).then(n.bind(null,"./bundles/sf-daterangepicker.js")).then((function(){}));case 62:return e.abrupt("break",234);case 63:return e.next=65,n.e(21).then(n.bind(null,"./bundles/sf-diagramcomponent.js")).then((function(){}));case 65:return e.abrupt("break",234);case 66:return e.next=68,n.e(22).then(n.bind(null,"./bundles/sf-dialog.js")).then((function(){}));case 68:return e.abrupt("break",234);case 69:return e.next=71,n.e(23).then(n.bind(null,"./bundles/sf-drop-down-button.js")).then((function(){}));case 71:return e.abrupt("break",234);case 72:return e.next=74,n.e(24).then(n.bind(null,"./bundles/sf-dropdownlist.js")).then((function(){}));case 74:return e.abrupt("break",234);case 75:return e.next=77,n.e(25).then(n.bind(null,"./bundles/sf-dropdowntree.js")).then((function(){}));case 77:return e.abrupt("break",234);case 78:return e.next=80,n.e(26).then(n.bind(null,"./bundles/sf-filemanager.js")).then((function(){}));case 80:return e.abrupt("break",234);case 81:return e.next=83,n.e(27).then(n.bind(null,"./bundles/sf-floating-action-button.js")).then((function(){}));case 83:return e.abrupt("break",234);case 84:return e.next=86,n.e(28).then(n.bind(null,"./bundles/sf-gantt.js")).then((function(){}));case 86:return e.abrupt("break",234);case 87:return e.next=89,n.e(29).then(n.bind(null,"./bundles/sf-grid.js")).then((function(){}));case 89:return e.abrupt("break",234);case 90:return e.next=92,n.e(30).then(n.bind(null,"./bundles/sf-heatmap.js")).then((function(){}));case 92:return e.abrupt("break",234);case 93:return e.next=95,n.e(31).then(n.bind(null,"./bundles/sf-image-editor.js")).then((function(){}));case 95:return e.abrupt("break",234);case 96:return e.next=98,n.e(32).then(n.bind(null,"./bundles/sf-inplaceeditor.js")).then((function(){}));case 98:return e.abrupt("break",234);case 99:return e.next=101,n.e(33).then(n.bind(null,"./bundles/sf-kanban.js")).then((function(){}));case 101:return e.abrupt("break",234);case 102:return e.next=104,n.e(34).then(n.bind(null,"./bundles/sf-lineargauge.js")).then((function(){}));case 104:return e.abrupt("break",234);case 105:return e.next=107,n.e(35).then(n.bind(null,"./bundles/sf-listbox.js")).then((function(){}));case 107:return e.abrupt("break",234);case 108:return e.next=110,n.e(36).then(n.bind(null,"./bundles/sf-listview.js")).then((function(){}));case 110:return e.abrupt("break",234);case 111:return e.next=113,n.e(37).then(n.bind(null,"./bundles/sf-maps.js")).then((function(){}));case 113:return e.abrupt("break",234);case 114:return e.next=116,n.e(38).then(n.bind(null,"./bundles/sf-maskedtextbox.js")).then((function(){}));case 116:return e.abrupt("break",234);case 117:return e.next=119,n.e(39).then(n.bind(null,"./bundles/sf-mention.js")).then((function(){}));case 119:return e.abrupt("break",234);case 120:return e.next=122,n.e(40).then(n.bind(null,"./bundles/sf-menu.js")).then((function(){}));case 122:return e.abrupt("break",234);case 123:return e.next=125,n.e(41).then(n.bind(null,"./bundles/sf-multiselect.js")).then((function(){}));case 125:return e.abrupt("break",234);case 126:return e.next=128,n.e(42).then(n.bind(null,"./bundles/sf-numerictextbox.js")).then((function(){}));case 128:return e.abrupt("break",234);case 129:return e.next=131,n.e(43).then(n.bind(null,"./bundles/sf-otp-input.js")).then((function(){}));case 131:return e.abrupt("break",234);case 132:return e.next=134,n.e(44).then(n.bind(null,"./bundles/sf-pager.js")).then((function(){}));case 134:return e.abrupt("break",234);case 135:return e.next=137,n.e(45).then(n.bind(null,"./bundles/sf-pdfviewer.js")).then((function(){}));case 137:return e.abrupt("break",234);case 138:return e.next=140,n.e(46).then(n.bind(null,"./bundles/sf-pivotview.js")).then((function(){}));case 140:return e.abrupt("break",234);case 141:return e.next=143,n.e(47).then(n.bind(null,"./bundles/sf-progressbar.js")).then((function(){}));case 143:return e.abrupt("break",234);case 144:return e.next=146,n.e(48).then(n.bind(null,"./bundles/sf-range-navigator.js")).then((function(){}));case 146:return e.abrupt("break",234);case 147:return e.next=149,n.e(49).then(n.bind(null,"./bundles/sf-rating.js")).then((function(){}));case 149:return e.abrupt("break",234);case 150:return e.next=152,n.e(50).then(n.bind(null,"./bundles/sf-richtexteditor.js")).then((function(){}));case 152:return e.abrupt("break",234);case 153:return e.next=155,n.e(51).then(n.bind(null,"./bundles/sf-schedule.js")).then((function(){}));case 155:return e.abrupt("break",234);case 156:return e.next=158,n.e(52).then(n.bind(null,"./bundles/sf-sidebar.js")).then((function(){}));case 158:return e.abrupt("break",234);case 159:return e.next=161,n.e(53).then(n.bind(null,"./bundles/sf-signature.js")).then((function(){}));case 161:return e.abrupt("break",234);case 162:return e.next=164,n.e(54).then(n.bind(null,"./bundles/sf-slider.js")).then((function(){}));case 164:return e.abrupt("break",234);case 165:return e.next=167,n.e(55).then(n.bind(null,"./bundles/sf-smith-chart.js")).then((function(){}));case 167:return e.abrupt("break",234);case 168:return e.next=170,n.e(56).then(n.bind(null,"./bundles/sf-sparkline.js")).then((function(){}));case 170:return e.abrupt("break",234);case 171:return e.next=173,n.e(57).then(n.bind(null,"./bundles/sf-speeddial.js")).then((function(){}));case 173:return e.abrupt("break",234);case 174:return e.next=176,n.e(58).then(n.bind(null,"./bundles/sf-spinner.js")).then((function(){}));case 176:return e.abrupt("break",234);case 177:return e.next=179,n.e(59).then(n.bind(null,"./bundles/sf-splitter.js")).then((function(){}));case 179:return e.abrupt("break",234);case 180:return e.next=182,n.e(60).then(n.bind(null,"./bundles/sf-stepper.js")).then((function(){}));case 182:return e.abrupt("break",234);case 183:return e.next=185,n.e(61).then(n.bind(null,"./bundles/sf-stock-chart.js")).then((function(){}));case 185:return e.abrupt("break",234);case 186:return e.next=188,n.e(62).then(n.bind(null,"./bundles/sf-svg-export.js")).then((function(){}));case 188:return e.abrupt("break",234);case 189:return e.next=191,n.e(63).then(n.bind(null,"./bundles/sf-tab.js")).then((function(){}));case 191:return e.abrupt("break",234);case 192:return e.next=194,n.e(64).then(n.bind(null,"./bundles/sf-textarea.js")).then((function(){}));case 194:return e.abrupt("break",234);case 195:return e.next=197,n.e(65).then(n.bind(null,"./bundles/sf-textbox.js")).then((function(){}));case 197:return e.abrupt("break",234);case 198:return e.next=200,n.e(66).then(n.bind(null,"./bundles/sf-timepicker.js")).then((function(){}));case 200:return e.abrupt("break",234);case 201:return e.next=203,n.e(67).then(n.bind(null,"./bundles/sf-toast.js")).then((function(){}));case 203:return e.abrupt("break",234);case 204:return e.next=206,n.e(68).then(n.bind(null,"./bundles/sf-toolbar.js")).then((function(){}));case 206:return e.abrupt("break",234);case 207:return e.next=209,n.e(69).then(n.bind(null,"./bundles/sf-tooltip.js")).then((function(){}));case 209:return e.abrupt("break",234);case 210:return e.next=212,n.e(70).then(n.bind(null,"./bundles/sf-treegrid.js")).then((function(){}));case 212:return e.abrupt("break",234);case 213:return e.next=215,n.e(71).then(n.bind(null,"./bundles/sf-treemap.js")).then((function(){}));case 215:return e.abrupt("break",234);case 216:return e.next=218,n.e(72).then(n.bind(null,"./bundles/sf-treeview.js")).then((function(){}));case 218:return e.abrupt("break",234);case 219:return e.next=221,n.e(73).then(n.bind(null,"./bundles/sf-uploader.js")).then((function(){}));case 221:return e.abrupt("break",234);case 222:return e.next=224,n.e(74).then(n.bind(null,"./bundles/sortable.js")).then((function(){}));case 224:return e.abrupt("break",234);case 225:return e.next=227,n.e(75).then(n.bind(null,"./bundles/spinner.js")).then((function(){}));case 227:return e.abrupt("break",234);case 228:return e.next=230,n.e(76).then(n.bind(null,"./bundles/splitbuttonsbase.js")).then((function(){}));case 230:return e.abrupt("break",234);case 231:return e.next=233,n.e(77).then(n.bind(null,"./bundles/svgbase.js")).then((function(){}));case 233:return e.abrupt("break",234);case 234:case"end":return e.stop()}}),e)})))).apply(this,arguments)}window.sfBlazor.loadDependencies=function(){var e=M(i().mark((function e(n){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!o[n]){e.next=3;break}return e.next=3,Promise.all(o[n].map(function(){var e=M(i().mark((function e(n){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t(n);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())).then((function(){}));case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();var o={sftextbox:["sf-textbox"],sftextarea:["sf-textarea"],sfnumerictextbox:["sf-textbox","sf-numerictextbox"],sfmaskedtextbox:["sf-textbox","sf-maskedtextbox"],sfuploader:["spinner","sf-uploader"],sfcalendar:["sf-calendar"],sfdatepicker:["sf-calendar","sf-textbox","popupsbase","popup","sf-datepicker"],sfdatetimepicker:["sf-calendar","sf-textbox","popupsbase","popup","sf-datepicker"],sfdaterangepicker:["sf-calendar","sf-textbox","popupsbase","popup","sf-daterangepicker"],sfdiagramcomponent:["sf-accordion","sf-diagramcomponent"],sftimepicker:["sf-textbox","popupsbase","popup","sf-timepicker"],sfautocomplete:["sf-textbox","sf-spinner","popupsbase","popup","sf-dropdownlist"],sfcombobox:["sf-textbox","sf-spinner","popupsbase","popup","sf-dropdownlist"],sfdropdownlist:["sf-textbox","sf-spinner","popupsbase","popup","sf-dropdownlist"],sfmention:["sf-spinner","popupsbase","popup","sf-mention"],sfmultiselect:["sf-textbox","sf-spinner","popupsbase","popup","sf-multiselect"],sffab:["sf-floating-action-button"],sfspeeddial:["sf-floating-action-button","sf-speeddial"],sfdropdownbutton:["popup","popupsbase","splitbuttonsbase","sf-drop-down-button"],sfsplitbutton:["popup","popupsbase","splitbuttonsbase","sf-drop-down-button"],sfprogressbutton:["sf-spinner"],sflistbox:["sortable","popup","popupsbase","sf-textbox","sf-spinner","sf-listbox"],sfcolorpicker:["splitbuttonsbase","sf-drop-down-button","sf-textbox","sf-numerictextbox","popup","popupsbase","sf-tooltip","sf-slider","sf-colorpicker"],sfrating:["sf-rating"],sfotpinput:["sf-otp-input"],sfsignature:["sf-signature"],sfimageeditor:["spinner","navigationsbase","sf-toolbar","sf-uploader","sf-colorpicker","splitbuttonsbase","sf-drop-down-button","sf-tooltip","sf-spinner","popupsbase","popup","sf-dialog","sf-slider","sf-image-editor"],sfdataform:["sf-textbox","sf-spinner","popupsbase","popup","sf-dropdownlist","sf-multiselect","sf-datepicker","sf-timepicker","sf-numerictextbox"],sfcontextmenu:["navigationsbase","popup","popupsbase","sf-contextmenu"],sfmenu:["navigationsbase","sf-contextmenu","popup","popupsbase","sf-menu"],sfbreadcrumb:["popup","popupsbase","sf-breadcrumb"],sfquerybuilder:["sf-dropdownlist","splitbuttonsbase","sf-drop-down-button","sf-tooltip","sf-spinner","popupsbase","sf-multiselect","sf-numerictextbox","sf-calendar","sf-textbox","popup","sf-datepicker","sf-querybuilder"],sfgrid:["sf-grid","sf-textbox","popupsbase","popup","sf-dropdownlist","navigationsbase","sf-contextmenu","sf-toolbar","sf-spinner","sf-calendar","sf-datepicker","sf-numerictextbox","sf-dropdownlist","sf-dialog","sf-pager"],sfaccordion:["sf-accordion"],sfcarousel:["sf-carousel"],sftab:["navigationsbase","popupsbase","popup","sf-toolbar","sf-tab"],sftoolbar:["navigationsbase","popupsbase","popup","sf-toolbar"],sfschedule:["navigationsbase","popupsbase","popup","sf-calendar","sf-datepicker","sf-dialog","sf-dropdownlist","sf-multiselect","sf-numerictextbox","sf-sidebar","sf-spinner","sf-timepicker","sf-treeview","sf-textbox","sf-toolbar","sf-tooltip","sf-schedule"],sfbarcodegenerator:["sf-barcode"],sfmaps:["sf-svg-export","sf-maps"],sfcirculargauge:["sf-svg-export","sf-circulargauge"],sflineargauge:["sf-svg-export","sf-lineargauge"],sfchart:["svgbase","sf-svg-export","sf-chart"],sfchart3d:["svgbase","sf-svg-export","sf-chart3D"],sfaccumulationchart:["svgbase","sf-svg-export","sf-accumulation-chart"],sfstockchart:["svgbase","sf-svg-export","navigationsbase","popup","popupsbase","sf-daterangepicker","sf-drop-down-button","sf-textbox","sf-toolbar","splitbuttonsbase","sf-chart","sf-range-navigator","sf-stock-chart"],sfbulletchart:["sf-svg-export","sf-bullet-chart"],sfsparkline:["sf-sparkline"],sftreemap:["sf-svg-export","sf-treemap"],sfprogressbar:["sf-progressbar"],sfsmithchart:["svgbase","sf-svg-export","sf-smith-chart"],sfrangenavigator:["svgbase","sf-svg-export","sf-range-navigator"],sfheatmap:["sf-svg-export","svgbase","sf-heatmap"],sffilemanager:["spinner","popup","popupsbase","splitbuttonsbase","navigationsbase","sf-dialog","sf-treeview","sf-toolbar","sf-spinner","sf-uploader","sf-drop-down-button","sf-contextmenu","sf-splitter","sf-textbox","sf-calendar","sf-datepicker","sf-numerictextbox","sf-dropdownlist","sf-grid","sf-filemanager"],sfslider:["popup","popupsbase","sf-tooltip","sf-slider"],sftooltip:["popup","popupsbase","sf-tooltip"],sflistview:["sf-listview"],sfdashboardlayout:["sf-dashboard-layout"],sfsidebar:["sf-sidebar"],sfstepper:["sf-stepper"],sftreeview:["sf-textbox","sf-treeview"],sfdropdowntree:["sf-spinner","popupsbase","popup","sf-textbox","sf-treeview","sf-dropdowntree"],sfpivotview:["navigationsbase","popupsbase","popup","splitbuttonsbase","svgbase","sf-spinner","sf-datepicker","sf-dialog","sf-dropdownlist","sf-numerictextbox","sf-treeview","sf-textbox","sf-toolbar","sf-tooltip","sf-colorpicker","sf-menu","sf-contextmenu","sf-tab","sf-drop-down-button","sf-svg-export","sf-chart","sf-accumulation-chart","sf-grid","sf-pivotview"],sftreegrid:["sf-textbox","popupsbase","popup","sf-dropdownlist","navigationsbase","sf-contextmenu","sf-toolbar","sf-spinner","sf-datepicker","sf-numerictextbox","sf-dropdownlist","sf-dialog","sf-grid","sf-treegrid"],sfspinner:["sf-spinner"],sfsplitter:["sf-splitter"],sftoast:["popupsbase","popup","sf-toast"],sfdialog:["popupsbase","popup","sf-dialog"],sfrichtexteditor:["sf-dialog","spinner","navigationsbase","sf-toolbar","sf-uploader","sf-textbox","sf-numerictextbox","sf-colorpicker","sf-drop-down-button","sf-richtexteditor"],sfinplaceeditor:["popupsbase","popup","sf-spinner","sf-tooltip","sf-textbox","sf-inplaceeditor"],sfkanban:["popupsbase","popup","sf-spinner","sf-tooltip","sf-dialog","sf-textbox","sf-listview","sf-dropdownlist","sf-numerictextbox","sf-kanban"],sfgantt:["sf-richtexteditor","sf-datepicker","popupsbase","popup","navigationsbase","sf-dropdownlist","sf-multiselect","sf-contextmenu","sf-toolbar","sf-tab","sf-textbox","sf-numerictextbox","sf-maskedtextbox","sf-dialog","sf-tooltip","sf-spinner","sf-splitter","sf-grid","sf-treegrid","sf-gantt"],sfpdfviewer:["syncfusion-blazor-extended","popupsbase","popup","drawings","sf-pdfviewer","navigationsbase","splitbuttonsbase","sf-toast","sf-listview","sf-drop-down-button","sf-tooltip","sf-dialog","sf-contextmenu","sf-menu","sf-toolbar","sf-colorpicker","sf-numerictextbox","sf-textbox","sf-slider","sf-dropdownlist","sf-listbox","sf-treeview"],sfpager:["sf-pager","sf-dropdownlist"]}}()},"./modules/base.js":function(e,t){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}window.sf=window.sf||{},window.sf.base=function(e){"use strict";var t=0,r=!1;function i(e,t){var n=t;return n.unshift(void 0),new(Function.prototype.bind.apply(e,n))}function o(e){var t,n=new Uint16Array(5);(window.msCrypto||window.crypto).getRandomValues(n);var r="ej2"+function(e){for(var t="",n=0;n<5;n++)t+=(n?",":"")+e[parseInt(n.toString(),10)];return t}(n),i=function(n){n.source===window&&"string"==typeof n.data&&n.data.length<=32&&n.data===r&&(e(),t())};return window.addEventListener("message",i,!1),window.postMessage(r,"*"),t=function(){window.removeEventListener("message",i),e=i=r=void 0}}function M(e,t){for(var n=t,r=e.replace(/\[/g,".").replace(/\]/g,"").split("."),i=0;i<r.length&&!j(n);i++)n=n[r[parseInt(i.toString(),10)]];return n}function a(e,t,n){var r,i,o=e.replace(/\[/g,".").replace(/\]/g,"").split("."),M=n||{},a=M,s=o.length;for(r=0;r<s;r++)i=o[parseInt(r.toString(),10)],r+1===s?a[""+i]=void 0===t?{}:t:l(a[""+i])&&(a[""+i]={}),a=a[""+i];return M}function s(e,t){delete e[""+t]}var u="undefined"!=typeof window?window:{};function c(e){return!l(e)&&e.constructor==={}.constructor}function N(e,t){if(!l(t))for(var n=e,r=t,i=0,o=Object.keys(t);i<o.length;i++){var M=o[i];l(n.deepMerge)||-1===n.deepMerge.indexOf(M)||!c(r[""+M])&&!Array.isArray(r[""+M])?n[""+M]=r[""+M]:g(n[""+M],n[""+M],r[""+M],!0)}}function g(e,t,r,i){var o=e&&"object"===n(e)?e:{},M=arguments.length;i&&(M-=1);for(var a=function(e){if(!s[e])return"continue";var t=s[e];Object.keys(t).forEach((function(e){var n,r=o[""+e],M=t[""+e],a=Array.isArray(M)&&Array.isArray(r)&&M.length!==r.length,s=!y()||!(r instanceof Event||a);i&&s&&(c(M)||Array.isArray(M))?c(M)?(n=r||{},Array.isArray(n)&&n.hasOwnProperty("isComplexArray")?g(n,{},M,i):o[""+e]=g(n,{},M,i)):(n=y()?r&&Object.keys(M).length:r||[],o[""+e]=g([],n,M,n&&n.length||M&&M.length)):o[""+e]=M}))},s=arguments,u=1;u<M;u++)a(u);return o}function l(e){return null==e}function j(e){return void 0===e}function D(e,t){var n;return function(){var r=this,i=arguments,o=function(){return n=null,e.apply(r,i)};clearTimeout(n),n=setTimeout(o,t)}}function p(e,t){return e===t||!(e===document||!e)&&p(e.parentNode,t)}function z(e){try{throw new Error(e)}catch(e){throw e.message+"\n"+e.stack}}function y(){return r}function I(){if("undefined"!=typeof window){var e=new Uint16Array(5);return(window.msCrypto||window.crypto).getRandomValues(e)}}var d,h,f={latn:{_digits:"**********",_type:"numeric"}},m={decimal:".",group:",",percentSign:"%",plusSign:"+",minusSign:"-",infinity:"∞",nan:"NaN",exponential:"E"},T=[0,1,2,3,4,5,6,7,8,9],A=function(){function e(){}return e.getMainObject=function(e,t){return M(y()?t:"main."+t,e)},e.getNumberingSystem=function(e){return M("supplemental.numberingSystems",e)||this.numberingSystems},e.reverseObject=function(e,t){for(var n={},r=0,i=t||Object.keys(e);r<i.length;r++){var o=i[r];n.hasOwnProperty(e[o])||(n[e[o]]=o)}return n},e.getSymbolRegex=function(e){var t=e.map((function(e){return e.replace(/([.*+?^=!:${}()|[\]/\\])/g,"\\$1")})).join("|");return new RegExp(t,"g")},e.getSymbolMatch=function(e){for(var t={},n=0,r=Object.keys(m);n<r.length;n++){var i=r[n];t[e[i]]=m[i]}return t},e.constructRegex=function(e){for(var t=e.length,n="",r=0;r<t;r++)n+=r!==t-1?e[parseInt(r.toString(),10)]+"|":e[parseInt(r.toString(),10)];return n},e.convertValueParts=function(e,t,n){return e.replace(t,(function(e){return n[e]}))},e.getDefaultNumberingSystem=function(e){var t={};return t.obj=M("numbers",e),t.nSystem=M("defaultNumberingSystem",t.obj),t},e.getCurrentNumericOptions=function(e,t,n,r){var i={},o=this.getDefaultNumberingSystem(e);if(!j(o.nSystem)||r){var a=r?M("obj.mapperDigits",o):M(o.nSystem+"._digits",t);if(!j(a)){i.numericPair=this.reverseObject(a,T);var s=RegExp;i.numberParseRegex=new s(this.constructRegex(a),"g"),i.numericRegex="["+a[0]+"-"+a[9]+"]",n&&(i.numericRegex=a[0]+"-"+a[9],i.symbolNumberSystem=M(r?"numberSymbols":"symbols-numberSystem-"+o.nSystem,o.obj),i.symbolMatch=this.getSymbolMatch(i.symbolNumberSystem),i.numberSystem=o.nSystem)}}return i},e.getNumberMapper=function(e,t,n){var r={mapper:{}},i=this.getDefaultNumberingSystem(e);if(!j(i.nSystem)){r.numberSystem=i.nSystem,r.numberSymbols=M("symbols-numberSystem-"+i.nSystem,i.obj),r.timeSeparator=M("timeSeparator",r.numberSymbols);var o=M(i.nSystem+"._digits",t);if(!j(o))for(var a=0,s=T;a<s.length;a++){var u=s[a];r.mapper[u]=o[u]}}return r},e.nPair="numericPair",e.nRegex="numericRegex",e.numberingSystems=f,e}(),O={DJF:"Fdj",ERN:"Nfk",ETB:"Br",NAD:"$",ZAR:"R",XAF:"FCFA",GHS:"GH₵",XDR:"XDR",AED:"د.إ.",BHD:"د.ب.",DZD:"د.ج.",EGP:"ج.م.",ILS:"₪",IQD:"د.ع.",JOD:"د.ا.",KMF:"CF",KWD:"د.ك.",LBP:"ل.ل.",LYD:"د.ل.",MAD:"د.م.",MRU:"أ.م.",OMR:"ر.ع.",QAR:"ر.ق.",SAR:"ر.س.",SDG:"ج.س.",SOS:"S",SSP:"£",SYP:"ل.س.",TND:"د.ت.",YER:"ر.ي.",CLP:"$",INR:"₹",TZS:"TSh",EUR:"€",AZN:"₼",RUB:"₽",BYN:"Br",ZMW:"K",BGN:"лв.",NGN:"₦",XOF:"CFA",BDT:"৳",CNY:"¥",BAM:"КМ",UGX:"USh",USD:"$",CZK:"Kč",GBP:"£",DKK:"kr.",KES:"Ksh",CHF:"CHF",MVR:"ރ.",BTN:"Nu.",XCD:"EC$",AUD:"$",BBD:"$",BIF:"FBu",BMD:"$",BSD:"$",BWP:"P",BZD:"$",CAD:"$",NZD:"$",FJD:"$",FKP:"£",GIP:"£",GMD:"D",GYD:"$",HKD:"$",IDR:"Rp",JMD:"$",KYD:"$",LRD:"$",MGA:"Ar",MOP:"MOP$",MUR:"Rs",MWK:"MK",MYR:"RM",PGK:"K",PHP:"₱",PKR:"Rs",RWF:"RF",SBD:"$",SCR:"SR",SEK:"kr",SGD:"$",SHP:"£",SLL:"Le",ANG:"NAf.",SZL:"E",TOP:"T$",TTD:"$",VUV:"VT",WST:"WS$",ARS:"$",BOB:"Bs",BRL:"R$",COP:"$",CRC:"₡",CUP:"$",DOP:"$",GTQ:"Q",HNL:"L",MXN:"$",NIO:"C$",PAB:"B/.",PEN:"S/",PYG:"₲",UYU:"$",VES:"Bs.S",IRR:"ريال",GNF:"FG",CDF:"FC",HTG:"G",XPF:"FCFP",HRK:"kn",HUF:"Ft",AMD:"֏",ISK:"kr",JPY:"¥",GEL:"₾",CVE:"​",KZT:"₸",KHR:"៛",KPW:"₩",KRW:"₩",KGS:"сом",AOA:"Kz",LAK:"₭",MZN:"MTn",MKD:"ден",MNT:"₮",BND:"$",MMK:"K",NOK:"kr",NPR:"रु",AWG:"Afl.",SRD:"$",PLN:"zł",AFN:"؋",STN:"Db",MDL:"L",RON:"lei",UAH:"₴",LKR:"රු.",ALL:"Lekë",RSD:"дин.",TJS:"смн",THB:"฿",TMT:"m.",TRY:"₺",UZS:"сўм",VND:"₫",TWD:"NT$"};d=e.HijriParser||(e.HijriParser={}),h=[28607,28636,28665,28695,28724,28754,28783,28813,28843,28872,28901,28931,28960,28990,29019,29049,29078,29108,29137,29167,29196,29226,29255,29285,29315,29345,29375,29404,29434,29463,29492,29522,29551,29580,29610,29640,29669,29699,29729,29759,29788,29818,29847,29876,29906,29935,29964,29994,30023,30053,30082,30112,30141,30171,30200,30230,30259,30289,30318,30348,30378,30408,30437,30467,30496,30526,30555,30585,30614,30644,30673,30703,30732,30762,30791,30821,30850,30880,30909,30939,30968,30998,31027,31057,31086,31116,31145,31175,31204,31234,31263,31293,31322,31352,31381,31411,31441,31471,31500,31530,31559,31589,31618,31648,31676,31706,31736,31766,31795,31825,31854,31884,31913,31943,31972,32002,32031,32061,32090,32120,32150,32180,32209,32239,32268,32298,32327,32357,32386,32416,32445,32475,32504,32534,32563,32593,32622,32652,32681,32711,32740,32770,32799,32829,32858,32888,32917,32947,32976,33006,33035,33065,33094,33124,33153,33183,33213,33243,33272,33302,33331,33361,33390,33420,33450,33479,33509,33539,33568,33598,33627,33657,33686,33716,33745,33775,33804,33834,33863,33893,33922,33952,33981,34011,34040,34069,34099,34128,34158,34187,34217,34247,34277,34306,34336,34365,34395,34424,34454,34483,34512,34542,34571,34601,34631,34660,34690,34719,34749,34778,34808,34837,34867,34896,34926,34955,34985,35015,35044,35074,35103,35133,35162,35192,35222,35251,35280,35310,35340,35370,35399,35429,35458,35488,35517,35547,35576,35605,35635,35665,35694,35723,35753,35782,35811,35841,35871,35901,35930,35960,35989,36019,36048,36078,36107,36136,36166,36195,36225,36254,36284,36314,36343,36373,36403,36433,36462,36492,36521,36551,36580,36610,36639,36669,36698,36728,36757,36786,36816,36845,36875,36904,36934,36963,36993,37022,37052,37081,37111,37141,37170,37200,37229,37259,37288,37318,37347,37377,37406,37436,37465,37495,37524,37554,37584,37613,37643,37672,37701,37731,37760,37790,37819,37849,37878,37908,37938,37967,37997,38027,38056,38085,38115,38144,38174,38203,38233,38262,38292,38322,38351,38381,38410,38440,38469,38499,38528,38558,38587,38617,38646,38676,38705,38735,38764,38794,38823,38853,38882,38912,38941,38971,39001,39030,39059,39089,39118,39148,39178,39208,39237,39267,39297,39326,39355,39385,39414,39444,39473,39503,39532,39562,39592,39621,39650,39680,39709,39739,39768,39798,39827,39857,39886,39916,39946,39975,40005,40035,40064,40094,40123,40153,40182,40212,40241,40271,40300,40330,40359,40389,40418,40448,40477,40507,40536,40566,40595,40625,40655,40685,40714,40744,40773,40803,40832,40862,40892,40921,40951,40980,41009,41039,41068,41098,41127,41157,41186,41216,41245,41275,41304,41334,41364,41393,41422,41452,41481,41511,41540,41570,41599,41629,41658,41688,41718,41748,41777,41807,41836,41865,41894,41924,41953,41983,42012,42042,42072,42102,42131,42161,42190,42220,42249,42279,42308,42337,42367,42397,42426,42456,42485,42515,42545,42574,42604,42633,42662,42692,42721,42751,42780,42810,42839,42869,42899,42929,42958,42988,43017,43046,43076,43105,43135,43164,43194,43223,43253,43283,43312,43342,43371,43401,43430,43460,43489,43519,43548,43578,43607,43637,43666,43696,43726,43755,43785,43814,43844,43873,43903,43932,43962,43991,44021,44050,44080,44109,44139,44169,44198,44228,44258,44287,44317,44346,44375,44405,44434,44464,44493,44523,44553,44582,44612,44641,44671,44700,44730,44759,44788,44818,44847,44877,44906,44936,44966,44996,45025,45055,45084,45114,45143,45172,45202,45231,45261,45290,45320,45350,45380,45409,45439,45468,45498,45527,45556,45586,45615,45644,45674,45704,45733,45763,45793,45823,45852,45882,45911,45940,45970,45999,46028,46058,46088,46117,46147,46177,46206,46236,46265,46295,46324,46354,46383,46413,46442,46472,46501,46531,46560,46590,46620,46649,46679,46708,46738,46767,46797,46826,46856,46885,46915,46944,46974,47003,47033,47063,47092,47122,47151,47181,47210,47240,47269,47298,47328,47357,47387,47417,47446,47476,47506,47535,47565,47594,47624,47653,47682,47712,47741,47771,47800,47830,47860,47890,47919,47949,47978,48008,48037,48066,48096,48125,48155,48184,48214,48244,48273,48303,48333,48362,48392,48421,48450,48480,48509,48538,48568,48598,48627,48657,48687,48717,48746,48776,48805,48834,48864,48893,48922,48952,48982,49011,49041,49071,49100,49130,49160,49189,49218,49248,49277,49306,49336,49365,49395,49425,49455,49484,49514,49543,49573,49602,49632,49661,49690,49720,49749,49779,49809,49838,49868,49898,49927,49957,49986,50016,50045,50075,50104,50133,50163,50192,50222,50252,50281,50311,50340,50370,50400,50429,50459,50488,50518,50547,50576,50606,50635,50665,50694,50724,50754,50784,50813,50843,50872,50902,50931,50960,50990,51019,51049,51078,51108,51138,51167,51197,51227,51256,51286,51315,51345,51374,51403,51433,51462,51492,51522,51552,51582,51611,51641,51670,51699,51729,51758,51787,51816,51846,51876,51906,51936,51965,51995,52025,52054,52083,52113,52142,52171,52200,52230,52260,52290,52319,52349,52379,52408,52438,52467,52497,52526,52555,52585,52614,52644,52673,52703,52733,52762,52792,52822,52851,52881,52910,52939,52969,52998,53028,53057,53087,53116,53146,53176,53205,53235,53264,53294,53324,53353,53383,53412,53441,53471,53500,53530,53559,53589,53619,53648,53678,53708,53737,53767,53796,53825,53855,53884,53913,53943,53973,54003,54032,54062,54092,54121,54151,54180,54209,54239,54268,54297,54327,54357,54387,54416,54446,54476,54505,54535,54564,54593,54623,54652,54681,54711,54741,54770,54800,54830,54859,54889,54919,54948,54977,55007,55036,55066,55095,55125,55154,55184,55213,55243,55273,55302,55332,55361,55391,55420,55450,55479,55508,55538,55567,55597,55627,55657,55686,55716,55745,55775,55804,55834,55863,55892,55922,55951,55981,56011,56040,56070,56100,56129,56159,56188,56218,56247,56276,56306,56335,56365,56394,56424,56454,56483,56513,56543,56572,56601,56631,56660,56690,56719,56749,56778,56808,56837,56867,56897,56926,56956,56985,57015,57044,57074,57103,57133,57162,57192,57221,57251,57280,57310,57340,57369,57399,57429,57458,57487,57517,57546,57576,57605,57634,57664,57694,57723,57753,57783,57813,57842,57871,57901,57930,57959,57989,58018,58048,58077,58107,58137,58167,58196,58226,58255,58285,58314,58343,58373,58402,58432,58461,58491,58521,58551,58580,58610,58639,58669,58698,58727,58757,58786,58816,58845,58875,58905,58934,58964,58994,59023,59053,59082,59111,59141,59170,59200,59229,59259,59288,59318,59348,59377,59407,59436,59466,59495,59525,59554,59584,59613,59643,59672,59702,59731,59761,59791,59820,59850,59879,59909,59939,59968,59997,60027,60056,60086,60115,60145,60174,60204,60234,60264,60293,60323,60352,60381,60411,60440,60469,60499,60528,60558,60588,60618,60648,60677,60707,60736,60765,60795,60824,60853,60883,60912,60942,60972,61002,61031,61061,61090,61120,61149,61179,61208,61237,61267,61296,61326,61356,61385,61415,61445,61474,61504,61533,61563,61592,61621,61651,61680,61710,61739,61769,61799,61828,61858,61888,61917,61947,61976,62006,62035,62064,62094,62123,62153,62182,62212,62242,62271,62301,62331,62360,62390,62419,62448,62478,62507,62537,62566,62596,62625,62655,62685,62715,62744,62774,62803,62832,62862,62891,62921,62950,62980,63009,63039,63069,63099,63128,63157,63187,63216,63246,63275,63305,63334,63363,63393,63423,63453,63482,63512,63541,63571,63600,63630,63659,63689,63718,63747,63777,63807,63836,63866,63895,63925,63955,63984,64014,64043,64073,64102,64131,64161,64190,64220,64249,64279,64309,64339,64368,64398,64427,64457,64486,64515,64545,64574,64603,64633,64663,64692,64722,64752,64782,64811,64841,64870,64899,64929,64958,64987,65017,65047,65076,65106,65136,65166,65195,65225,65254,65283,65313,65342,65371,65401,65431,65460,65490,65520,65549,65579,65608,65638,65667,65697,65726,65755,65785,65815,65844,65874,65903,65933,65963,65992,66022,66051,66081,66110,66140,66169,66199,66228,66258,66287,66317,66346,66376,66405,66435,66465,66494,66524,66553,66583,66612,66641,66671,66700,66730,66760,66789,66819,66849,66878,66908,66937,66967,66996,67025,67055,67084,67114,67143,67173,67203,67233,67262,67292,67321,67351,67380,67409,67439,67468,67497,67527,67557,67587,67617,67646,67676,67705,67735,67764,67793,67823,67852,67882,67911,67941,67971,68e3,68030,68060,68089,68119,68148,68177,68207,68236,68266,68295,68325,68354,68384,68414,68443,68473,68502,68532,68561,68591,68620,68650,68679,68708,68738,68768,68797,68827,68857,68886,68916,68946,68975,69004,69034,69063,69092,69122,69152,69181,69211,69240,69270,69300,69330,69359,69388,69418,69447,69476,69506,69535,69565,69595,69624,69654,69684,69713,69743,69772,69802,69831,69861,69890,69919,69949,69978,70008,70038,70067,70097,70126,70156,70186,70215,70245,70274,70303,70333,70362,70392,70421,70451,70481,70510,70540,70570,70599,70629,70658,70687,70717,70746,70776,70805,70835,70864,70894,70924,70954,70983,71013,71042,71071,71101,71130,71159,71189,71218,71248,71278,71308,71337,71367,71397,71426,71455,71485,71514,71543,71573,71602,71632,71662,71691,71721,71751,71781,71810,71839,71869,71898,71927,71957,71986,72016,72046,72075,72105,72135,72164,72194,72223,72253,72282,72311,72341,72370,72400,72429,72459,72489,72518,72548,72577,72607,72637,72666,72695,72725,72754,72784,72813,72843,72872,72902,72931,72961,72991,73020,73050,73080,73109,73139,73168,73197,73227,73256,73286,73315,73345,73375,73404,73434,73464,73493,73523,73552,73581,73611,73640,73669,73699,73729,73758,73788,73818,73848,73877,73907,73936,73965,73995,74024,74053,74083,74113,74142,74172,74202,74231,74261,74291,74320,74349,74379,74408,74437,74467,74497,74526,74556,74586,74615,74645,74675,74704,74733,74763,74792,74822,74851,74881,74910,74940,74969,74999,75029,75058,75088,75117,75147,75176,75206,75235,75264,75294,75323,75353,75383,75412,75442,75472,75501,75531,75560,75590,75619,75648,75678,75707,75737,75766,75796,75826,75856,75885,75915,75944,75974,76003,76032,76062,76091,76121,76150,76180,76210,76239,76269,76299,76328,76358,76387,76416,76446,76475,76505,76534,76564,76593,76623,76653,76682,76712,76741,76771,76801,76830,76859,76889,76918,76948,76977,77007,77036,77066,77096,77125,77155,77185,77214,77243,77273,77302,77332,77361,77390,77420,77450,77479,77509,77539,77569,77598,77627,77657,77686,77715,77745,77774,77804,77833,77863,77893,77923,77952,77982,78011,78041,78070,78099,78129,78158,78188,78217,78247,78277,78307,78336,78366,78395,78425,78454,78483,78513,78542,78572,78601,78631,78661,78690,78720,78750,78779,78808,78838,78867,78897,78926,78956,78985,79015,79044,79074,79104,79133,79163,79192,79222,79251,79281,79310,79340,79369,79399,79428,79458,79487,79517,79546,79576,79606,79635,79665,79695,79724,79753,79783,79812,79841,79871,79900,79930,79960,79990],d.getHijriDate=function(e){var t=e.getDate(),n=e.getMonth(),r=e.getFullYear(),i=n+1,o=r;i<3&&(o-=1,i+=12);var M=Math.floor(o/100),a=M-Math.floor(M/4)-2,s=Math.floor(365.25*(o+4716))+Math.floor(30.6001*(i+1))+t-a-1524,u=s+(a=(M=Math.floor((s-1867216.25)/36524.25))-Math.floor(M/4)+1)+1524,c=Math.floor((u-122.1)/365.25),N=Math.floor(365.25*c),g=Math.floor((u-N)/30.6001);t=u-N-Math.floor(30.6001*g),(n=Math.floor((u-N)/20.6001))>13&&(c+=1,n-=12),n-=1,r=c-4716;var l=s-24e5,j=s-1948084,D=Math.floor(j/10631);j-=10631*D;var p=Math.floor((j-.1335)/(10631/30)),z=30*D+p;j-=Math.floor(p*(10631/30)+.1335);var y=Math.floor((j+28.5001)/29.5);13===y&&(y=12);for(var I=j-Math.floor(29.5001*y-29),d=0;d<h.length&&!(h[parseInt(d.toString(),10)]>l);d++);var f=d+16260,m=Math.floor((f-1)/12),T=m+1,A=f-12*m,O=l-h[d-1]+1;return(O+"").length>2&&(O=I,A=y,T=z),{year:T,month:A,date:O}},d.toGregorian=function(e,t,n){var r=n+h[12*(e-1)+1+(t-1)-16260-1]-1+24e5,i=Math.floor(r+.5),o=Math.floor((i-1867216.25)/36524.25),M=1524+(o=i+1+o-Math.floor(o/4)),a=Math.floor((M-122.1)/365.25),s=Math.floor(365.25*a),u=Math.floor((M-s)/30.6001),c=M-s-Math.floor(30.6001*u),N=u-(u>13.5?13:1),g=a-(N>2.5?4716:4715);return g<=0&&N--,new Date(g+"/"+N+"/"+c)};var L=/\/MMMMM|MMMM|MMM|a|LLLL|LLL|EEEEE|EEEE|E|K|cccc|ccc|WW|W|G+|z+/gi,x=["sun","mon","tue","wed","thu","fri","sat"],b={m:"getMinutes",h:"getHours",H:"getHours",s:"getSeconds",d:"getDate",f:"getMilliseconds"},E={M:"month",d:"day",E:"weekday",c:"weekday",y:"year",m:"minute",h:"hour",H:"hour",s:"second",L:"month",a:"designator",z:"timeZone",Z:"timeZone",G:"era",f:"milliseconds"},S=function(){function t(){}return t.dateFormat=function(t,n,r){var i=this,o=e.IntlBase.getDependables(r,t,n.calendar),a=M("parserObject.numbers",o),s=o.dateObject,u={isIslamic:e.IntlBase.islamicRegex.test(n.calendar)};y()&&n.isServerRendered&&(n=e.IntlBase.compareBlazorDateFormats(n,t));var c=n.format||e.IntlBase.getResultantPattern(n.skeleton,o.dateObject,n.type,!1,y()?t:"");if(u.dateSeperator=y()?M("dateSeperator",s):e.IntlBase.getDateSeparator(o.dateObject),j(c))z("Format options or type given must be invalid");else{c=e.IntlBase.ConvertDateToWeekFormat(c),y()&&(c=c.replace(/tt/,"a")),u.pattern=c,u.numMapper=y()?g({},a):A.getNumberMapper(o.parserObject,A.getNumberingSystem(r));for(var N=0,l=c.match(L)||[];N<l.length;N++){var D=l[N],p=D.length,I=D[0];switch("K"===I&&(I="h"),I){case"E":case"c":y()?u.weekday=M("days."+e.IntlBase.monthIndex[p],s):u.weekday=o.dateObject[e.IntlBase.days]["stand-alone"][e.IntlBase.monthIndex[p]];break;case"M":case"L":y()?u.month=M("months."+e.IntlBase.monthIndex[p],s):u.month=o.dateObject[e.IntlBase.month]["stand-alone"][e.IntlBase.monthIndex[p]];break;case"a":u.designator=y()?M("dayPeriods",s):M("dayPeriods.format.wide",s);break;case"G":var d=p<=3?"eraAbbr":4===p?"eraNames":"eraNarrow";u.era=y()?M("eras",s):M("eras."+d,o.dateObject);break;case"z":u.timeZone=M("dates.timeZoneNames",o.parserObject)}}}return function(e){return isNaN(e.getDate())?null:i.intDateFormatter(e,u)}},t.intDateFormatter=function(t,n){for(var r="",i=n.pattern.match(e.IntlBase.dateParseRegex),o=this.getCurrentDateValue(t,n.isIslamic),M=0,a=i;M<a.length;M++){var s=a[M],u=s.length,c=s[0];"K"===c&&(c="h");var N=void 0,g="",j=void 0,D=void 0,p="";switch(c){case"M":case"L":N=o.month,u>2?r+=n.month[N]:j=!0;break;case"E":case"c":r+=n.weekday[x[t.getDay()]];break;case"H":case"h":case"m":case"s":case"d":case"f":if(j=!0,"d"===c)N=o.date;else if("f"===c){j=!1,D=!0;var z=(g=(g=t[b[c]]().toString()).substring(0,u)).length;if(u!==z){if(u>3)continue;for(var y=0;y<u-z;y++)g="0"+g.toString()}p+=g}else N=t[b[c]]();"h"===c&&(N=N%12||12);break;case"y":D=!0,p+=o.year,2===u&&(p=p.substr(p.length-2));break;case"a":var I=t.getHours()<12?"am":"pm";r+=n.designator[I];break;case"G":var d=t.getFullYear()<0?0:1,h=n.era[d];l(h)&&(h=n.era[d?0:1]),r+=h||"";break;case"'":r+="''"===s?"'":s.replace(/'/g,"");break;case"z":var f=t.getTimezoneOffset(),m=u<4?"+H;-H":n.timeZone.hourFormat;m=m.replace(/:/g,n.numMapper.timeSeparator),0===f?r+=n.timeZone.gmtZeroFormat:(D=!0,p=this.getTimeZoneValue(f,m)),p=n.timeZone.gmtFormat.replace(/\{0\}/,p);break;case":":r+=n.numMapper.numberSymbols.timeSeparator;break;case"/":r+=n.dateSeperator;break;case"W":j=!0,N=e.IntlBase.getWeekOfYear(t);break;default:r+=s}j&&(D=!0,p=this.checkTwodigitNumber(N,u)),D&&(r+=A.convertValueParts(p,e.IntlBase.latnParseRegex,n.numMapper.mapper))}return r},t.getCurrentDateValue=function(t,n){return n?e.HijriParser.getHijriDate(t):{year:t.getFullYear(),month:t.getMonth()+1,date:t.getDate()}},t.checkTwodigitNumber=function(e,t){var n=e+"";return 2===t&&2!==n.length?"0"+n:n},t.getTimeZoneValue=function(e,t){var n=this,r=t.split(";")[e>0?1:0],i=Math.abs(e);return r.replace(/HH?|mm/g,(function(e){var t=e.length,r=-1!==e.indexOf("H");return n.checkTwodigitNumber(Math.floor(r?i/60:i%60),t)}))},t}(),v={ms:"minimumSignificantDigits",ls:"maximumSignificantDigits",mf:"minimumFractionDigits",lf:"maximumFractionDigits"},w=["infinity","nan","group","decimal","exponential"],k=function(){function t(){}return t.numberFormatter=function(t,n,r){var i,o=this,a=g({},n),s={},u={},c=e.IntlBase.getDependables(r,t,"",!0),N=c.numericObject;u.numberMapper=y()?g({},N):A.getNumberMapper(c.parserObject,A.getNumberingSystem(r),!0),u.currencySymbol=y()?M("currencySymbol",N):e.IntlBase.getCurrencySymbol(c.numericObject,a.currency||e.defaultCurrencyCode,n.altSymbol),u.percentSymbol=y()?M("numberSymbols.percentSign",N):u.numberMapper.numberSymbols.percentSign,u.minusSymbol=y()?M("numberSymbols.minusSign",N):u.numberMapper.numberSymbols.minusSign;var l=u.numberMapper.numberSymbols;if(n.format&&!e.IntlBase.formatRegex.test(n.format))s=e.IntlBase.customFormat(n.format,u,c.numericObject);else{if(g(a,e.IntlBase.getProperNumericSkeleton(n.format||"N")),a.isCurrency="currency"===a.type,a.isPercent="percent"===a.type,y()||(i=e.IntlBase.getSymbolPattern(a.type,u.numberMapper.numberSystem,c.numericObject,a.isAccount)),a.groupOne=this.checkValueRange(a.maximumSignificantDigits,a.minimumSignificantDigits,!0),this.checkValueRange(a.maximumFractionDigits,a.minimumFractionDigits,!1,!0),j(a.fractionDigits)||(a.minimumFractionDigits=a.maximumFractionDigits=a.fractionDigits),j(a.useGrouping)&&(a.useGrouping=!0),a.isCurrency&&!y()&&(i=i.replace(/\u00A4/g,e.IntlBase.defaultCurrency)),y())s.nData=g({},{},M(a.type+"nData",N)),s.pData=g({},{},M(a.type+"pData",N)),"currency"===a.type&&n.currency&&e.IntlBase.replaceBlazorCurrency([s.pData,s.nData],u.currencySymbol,n.currency);else{var D=i.split(";");s.nData=e.IntlBase.getFormatData(D[1]||"-"+D[0],!0,u.currencySymbol),s.pData=e.IntlBase.getFormatData(D[0],!1,u.currencySymbol),a.useGrouping&&(a.groupSeparator=l[w[2]],a.groupData=this.getGroupingDetails(D[0]))}if(j(a.minimumFractionDigits)&&(a.minimumFractionDigits=s.nData.minimumFraction),j(a.maximumFractionDigits)){var p=s.nData.maximumFraction;a.maximumFractionDigits=j(p)&&a.isPercent?0:p}var z=a.minimumFractionDigits,I=a.maximumFractionDigits;j(z)||j(I)||z>I&&(a.maximumFractionDigits=z)}return g(s.nData,a),g(s.pData,a),function(e){return isNaN(e)?l[w[1]]:isFinite(e)?o.intNumberFormatter(e,s,u,n):l[w[0]]}},t.getGroupingDetails=function(t){var n={},r=t.match(e.IntlBase.negativeDataRegex);if(r&&r[4]){var i=r[4],o=i.lastIndexOf(",");if(-1!==o){var M=i.split(".")[0];n.primary=M.length-o-1;var a=i.lastIndexOf(",",o-1);-1!==a&&(n.secondary=o-1-a)}}return n},t.checkValueRange=function(e,t,n,r){var i=r?"f":"s",o=0,M=v["l"+i],a=v["m"+i];if(j(e)||(this.checkRange(e,M,r),o++),j(t)||(this.checkRange(t,a,r),o++),2===o){if(!(e<t))return!0;z(a+"specified must be less than the"+M)}else n&&1===o&&z("Both"+a+"and"+a+"must be present");return!1},t.checkRange=function(e,t,n){var r=n?[0,20]:[1,21];(e<r[0]||e>r[1])&&z(t+"value must be within the range"+r[0]+"to"+r[1])},t.intNumberFormatter=function(t,n,r,i){var o;if(!j(n.nData.type)){t<0?(t*=-1,o=n.nData):o=0===t&&n.zeroData||n.pData;var M="";if(o.isPercent&&(t*=100),o.groupOne)M=this.processSignificantDigits(t,o.minimumSignificantDigits,o.maximumSignificantDigits);else if(M=this.processFraction(t,o.minimumFractionDigits,o.maximumFractionDigits,i),o.minimumIntegerDigits&&(M=this.processMinimumIntegers(M,o.minimumIntegerDigits)),r.isCustomFormat&&o.minimumFractionDigits<o.maximumFractionDigits&&/\d+\.\d+/.test(M)){for(var a=M.split("."),s=a[1],u=s.length-1;u>=0&&("0"===s[""+u]&&u>=o.minimumFractionDigits);u--)s=s.slice(0,u);M=a[0]+"."+s}return"scientific"===o.type&&(M=(M=t.toExponential(o.maximumFractionDigits)).replace("e",r.numberMapper.numberSymbols[w[4]])),M=M.replace(".",r.numberMapper.numberSymbols[w[3]]),M="#,###,,;(#,###,,)"===o.format?this.customPivotFormat(parseInt(M,10)):M,o.useGrouping&&(M=this.groupNumbers(M,o.groupData.primary,o.groupSeparator||",",r.numberMapper.numberSymbols[w[3]]||".",o.groupData.secondary)),M=A.convertValueParts(M,e.IntlBase.latnParseRegex,r.numberMapper.mapper),"N/A"===o.nlead?o.nlead:"0"===M&&i&&"0"===i.format?M+o.nend:o.nlead+M+o.nend}},t.processSignificantDigits=function(e,t,n){var r=e+"";return r.length<t?e.toPrecision(t):+(r=e.toPrecision(n))+""},t.groupNumbers=function(e,t,n,r,i){for(var o=!l(i)&&0!==i,M=e.split(r),a=M[0],s=a.length,u="";s>t;)u=a.slice(s-t,s)+(u.length?n+u:""),s-=t,o&&(t=i,o=!1);return M[0]=a.slice(0,s)+(u.length?n:"")+u,M.join(r)},t.processFraction=function(e,t,n,r){var i=(e+"").split(".")[1],o=i?i.length:0;if(t&&o<t){var M="";if(0!==o){M+=e;for(var a=0;a<t-o;a++)M+="0";return M}return M=e.toFixed(t),e.toFixed(t)}if(!l(n)&&(o>n||0===n))return e.toFixed(n);var s=e+"";return"0"===s[0]&&r&&"###.00"===r.format&&(s=s.slice(1)),s},t.processMinimumIntegers=function(e,t){var n=e.split("."),r=n[0],i=r.length;if(i<t){for(var o=0;o<t-i;o++)r="0"+r;n[0]=r}return n.join(".")},t.customPivotFormat=function(e){if(e>=5e5){var t=(e/=1e6).toString().split("."),n=(t[0],t[1]);return n&&+n.substring(0,1)>=5?Math.ceil(e).toString():Math.floor(e).toString()}return""},t}(),Q=/^[0-9]*$/,U={minute:"setMinutes",hour:"setHours",second:"setSeconds",day:"setDate",month:"setMonth",milliseconds:"setMilliseconds"},Y=function(){function t(){}return t.dateParser=function(t,n,r){var i=this,o=e.IntlBase.getDependables(r,t,n.calendar),a=A.getCurrentNumericOptions(o.parserObject,A.getNumberingSystem(r),!1,y()),s={};y()&&n.isServerRendered&&(n=e.IntlBase.compareBlazorDateFormats(n,t));var u,c=n.format||e.IntlBase.getResultantPattern(n.skeleton,o.dateObject,n.type,!1,y()?t:""),N="";if(j(c))z("Format options or type given must be invalid");else{c=e.IntlBase.ConvertDateToWeekFormat(c),s={isIslamic:e.IntlBase.islamicRegex.test(n.calendar),pattern:c,evalposition:{},culture:t};for(var g=c.match(e.IntlBase.dateParseRegex)||[],D=g.length,p=0,I=0,d=!1,h=a.numericRegex,f=y()?o.parserObject.numbers:A.getNumberMapper(o.parserObject,A.getNumberingSystem(r)),m=0;m<D;m++){var T=g[parseInt(m.toString(),10)],O=T.length,L="K"===T[0]?"h":T[0],x=void 0,b=void 0,S=E[L],v=2===O?"":"?";switch(d&&(p=I,d=!1),L){case"E":case"c":var w=void 0;w=y()?M("days."+e.IntlBase.monthIndex[O],o.dateObject):o.dateObject[e.IntlBase.days]["stand-alone"][e.IntlBase.monthIndex[O]];var k=A.reverseObject(w);N+="("+Object.keys(k).join("|")+")";break;case"M":case"L":case"d":case"m":case"s":case"h":case"H":case"f":if(b=!0,("M"===L||"L"===L)&&O>2){var Q=void 0;Q=y()?M("months."+e.IntlBase.monthIndex[O],o.dateObject):o.dateObject.months["stand-alone"][e.IntlBase.monthIndex[O]],s[S]=A.reverseObject(Q),N+="("+Object.keys(s[S]).join("|")+")"}else if("f"===L){if(O>3)continue;x=!0,N+="("+h+h+"?"+h+"?)"}else x=!0,N+="("+h+h+v+")";"h"===L&&(s.hour12=!0);break;case"W":N+="("+h+(1===O?"?":"")+h+")";break;case"y":b=x=!0,N+=2===O?"("+h+h+")":"("+h+"{"+O+",})";break;case"a":b=!0;var U=y()?M("dayPeriods",o.dateObject):M("dayPeriods.format.wide",o.dateObject);s[S]=A.reverseObject(U),N+="("+Object.keys(s[S]).join("|")+")";break;case"G":b=!0;var Y=O<=3?"eraAbbr":4===O?"eraNames":"eraNarrow";s[S]=A.reverseObject(y()?M("eras",o.dateObject):M("eras."+Y,o.dateObject)),N+="("+Object.keys(s[S]).join("|")+"?)";break;case"z":b=0!==(new Date).getTimezoneOffset(),s[S]=M("dates.timeZoneNames",o.parserObject);var C=s[S],P=(u=O<4)?"+H;-H":C.hourFormat;P=P.replace(/:/g,f.timeSeparator),N+="("+this.parseTimeZoneRegx(P,C,h)+")?",d=!0,I=u?6:12;break;case"'":N+="("+T.replace(/'/g,"")+")?";break;default:N+="([\\D])"}if(b&&(s.evalposition[""+S]={isNumber:x,pos:m+1+p,hourOnly:u}),m===D-1&&!l(N)){var F=RegExp;s.parserRegex=new F("^"+N+"$","i")}}}return function(t){var n=i.internalDateParse(t,s,a);if(l(n)||!Object.keys(n).length)return null;if(s.isIslamic){var r={},o=n.year,M=n.day,u=n.month,c=o?o+"":"",N=2===c.length;o&&u&&M&&!N||(r=e.HijriParser.getHijriDate(new Date)),N&&(o=parseInt((r.year+"").slice(0,2)+c,10));var g=e.HijriParser.toGregorian(o||r.year,u||r.month,M||r.date);n.year=g.getFullYear(),n.month=g.getMonth()+1,n.day=g.getDate()}return i.getDateObject(n)}},t.getDateObject=function(e,t){var n=t||new Date;n.setMilliseconds(0);var r=e.year,i=e.designator,o=e.timeZone;if(!j(r)){if((r+"").length<=2)r+=100*Math.floor(n.getFullYear()/100);n.setFullYear(r)}for(var M=0,a=["hour","minute","second","milliseconds","month","day"];M<a.length;M++){var s=a[M],u=e[s];if(j(u)&&"day"===s&&n.setDate(1),!j(u))if("month"===s){if((u-=1)<0||u>11)return new Date("invalid");var c=n.getDate();n.setDate(1),n[U[s]](u);var N=new Date(n.getFullYear(),u+1,0).getDate();n.setDate(c<N?c:N)}else{if("day"===s){var g=new Date(n.getFullYear(),n.getMonth()+1,0).getDate();if(u<1||u>g)return null}n[U[s]](u)}}if(!j(i)){var l=n.getHours();"pm"===i?n.setHours(l+(12===l?0:12)):12===l&&n.setHours(0)}if(!j(o)){var D=o-n.getTimezoneOffset();0!==D&&n.setMinutes(n.getMinutes()+D)}return n},t.internalDateParse=function(e,t,n){var r=e.match(t.parserRegex),i={hour:0,minute:0,second:0};if(l(r))return null;for(var o=0,M=Object.keys(t.evalposition);o<M.length;o++){var a=M[o],s=t.evalposition[""+a],u=r[s.pos];if(s.isNumber)i[a]=this.internalNumberParser(u,n);else if("timeZone"!==a||j(u)){u="month"!==a||t.isIslamic||"en"!==t.culture&&"en-GB"!==t.culture&&"en-US"!==t.culture?u:u[0].toUpperCase()+u.substring(1).toLowerCase(),u="month"!==a&&"designator"===a&&t.culture&&-1!==t.culture.indexOf("en-")&&-1===["en-US","en-MH","en-MP"].indexOf(t.culture)?u.toLowerCase():u,i[a]=t[a][u]}else{var c=s.pos,N=void 0,g=r[c+1],D=!j(g);s.hourOnly?N=60*this.getZoneValue(D,g,r[c+4],n):(N=60*this.getZoneValue(D,g,r[c+7],n),N+=this.getZoneValue(D,r[c+4],r[c+10],n)),l(N)||(i[""+a]=N)}}return t.hour12&&(i.hour12=!0),i},t.internalNumberParser=function(e,t){return e=A.convertValueParts(e,t.numberParseRegex,t.numericPair),Q.test(e)?+e:null},t.parseTimeZoneRegx=function(e,t,n){var r,i,o=t.gmtFormat,M="("+n+")("+n+")";return r=e.replace("+","\\+"),i=(r=-1!==e.indexOf("HH")?r.replace(/HH|mm/g,"("+M+")"):r.replace(/H|m/g,"("+M+"?)")).split(";").map((function(e){return o.replace("{0}",e)})),r=i.join("|")+"|"+t.gmtZeroFormat},t.getZoneValue=function(e,t,n,r){var i=e?t:n;if(!i)return 0;var o=this.internalNumberParser(i,r);return e?-o:o},t}(),C=new RegExp("^([^0-9]*)(([0-9,]*[0-9]+)(.[0-9]+)?)([Ee][+-]?[0-9]+)?([^0-9]*)$"),P=/,/g,F=["minusSign","infinity"],R=function(){function t(){}return t.numberParser=function(t,n,r){var i,o=this,a=e.IntlBase.getDependables(r,t,"",!0),s={custom:!0};e.IntlBase.formatRegex.test(n.format)||!n.format?(g(s,e.IntlBase.getProperNumericSkeleton(n.format||"N")),s.custom=!1,s.fractionDigits||n.maximumFractionDigits&&(s.maximumFractionDigits=n.maximumFractionDigits)):g(s,e.IntlBase.customFormat(n.format,null,null));var u,c=M("numbers",a.parserObject);if(i=A.getCurrentNumericOptions(a.parserObject,A.getNumberingSystem(r),!0,y()),s.symbolRegex=A.getSymbolRegex(Object.keys(i.symbolMatch)),s.infinity=i.symbolNumberSystem[F[1]],y())s.nData=g({},{},M(s.type+"nData",c)),s.pData=g({},{},M(s.type+"pData",c)),"currency"===s.type&&n.currency&&e.IntlBase.replaceBlazorCurrency([s.pData,s.nData],M("currencySymbol",c),n.currency);else if(u=e.IntlBase.getSymbolPattern(s.type,i.numberSystem,a.numericObject,s.isAccount)){var N=(u=u.replace(/\u00A4/g,e.IntlBase.defaultCurrency)).split(";");s.nData=e.IntlBase.getFormatData(N[1]||"-"+N[0],!0,""),s.pData=e.IntlBase.getFormatData(N[0],!0,"")}return function(e){return o.getParsedNumber(e,s,i)}},t.getParsedNumber=function(e,t,n){var r,i,o,M,a,s;if(-1!==e.indexOf(t.infinity))return 1/0;e=A.convertValueParts(e,t.symbolRegex,n.symbolMatch),0===(e=-1!==(e=A.convertValueParts(e,n.numberParseRegex,n.numericPair)).indexOf("-")?e.replace("-.","-0."):e).indexOf(".")&&(e="0"+e);var u=e.match(C);if(l(u))return NaN;M=u[1],o=u[2];var c=u[5];return a=u[6],i=(r=t.custom?M===t.nData.nlead&&a===t.nData.nend:-1!==M.indexOf(t.nData.nlead)&&-1!==a.indexOf(t.nData.nend))?t.nData.isPercent:t.pData.isPercent,o=o.replace(P,""),c&&(o+=c),s=+o,("percent"===t.type||i)&&(s/=100),(t.custom||t.fractionDigits)&&(s=parseFloat(s.toFixed(t.custom?r?t.nData.maximumFractionDigits:t.pData.maximumFractionDigits:t.fractionDigits))),t.maximumFractionDigits&&(s=this.convertMaxFracDigits(o,t,s,r)),r&&(s*=-1),s},t.convertMaxFracDigits=function(e,t,n,r){var i=e.split(".");return i[1]&&i[1].length>t.maximumFractionDigits&&(n=+n.toFixed(t.custom?r?t.nData.maximumFractionDigits:t.pData.maximumFractionDigits:t.maximumFractionDigits)),n},t}(),V=function(){function e(e){this.ranArray=[],this.boundedEvents={},l(e)||(this.context=e)}return e.prototype.on=function(e,t,n,r){if(!l(t)){var i=n||this.context;this.notExist(e)?this.boundedEvents[""+e]=[{handler:t,context:i,id:r}]:l(r)?this.isHandlerPresent(this.boundedEvents[""+e],t)||this.boundedEvents[""+e].push({handler:t,context:i}):-1===this.ranArray.indexOf(r)&&(this.ranArray.push(r),this.boundedEvents[""+e].push({handler:t,context:i,id:r}))}},e.prototype.off=function(e,t,n){if(!this.notExist(e)){var r=M(e,this.boundedEvents);if(t){for(var i=0;i<r.length;i++)if(n){if(r[parseInt(i.toString(),10)].id===n){r.splice(i,1);var o=this.ranArray.indexOf(n);-1!==o&&this.ranArray.splice(o,1);break}}else if(t===r[parseInt(i.toString(),10)].handler){r.splice(i,1);break}}else delete this.boundedEvents[""+e]}},e.prototype.notify=function(e,t,n,r){if(this.notExist(e))n&&n.call(this,t);else{t&&(t.name=e);var i=M(e,this.boundedEvents).slice(0);if(window.Blazor)return this.blazorCallback(i,t,n,r,0);for(var o=0,a=i;o<a.length;o++){var s=a[o];s.handler.call(s.context,t)}n&&n.call(this,t)}},e.prototype.blazorCallback=function(e,t,n,r,i){var o=this,M=i===e.length-1;if(i<e.length){var a=e[parseInt(i.toString(),10)],s=a.handler.call(a.context,t);if(s&&"function"==typeof s.then){if(!n)return s;s.then((function(s){if(s="string"==typeof s&&o.isJson(s)?JSON.parse(s,o.dateReviver):s,g(t,t,s,!0),!n||!M)return o.blazorCallback(e,t,n,r,i+1);n.call(a.context,t)})).catch((function(e){r&&r.call(a.context,"string"==typeof e&&o.isJson(e)?JSON.parse(e,o.dateReviver):e)}))}else{if(!n||!M)return this.blazorCallback(e,t,n,r,i+1);n.call(a.context,t)}}},e.prototype.dateReviver=function(e,t){return y&&"string"==typeof t&&null!==t.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)?new Date(t):t},e.prototype.isJson=function(e){try{JSON.parse(e)}catch(e){return!1}return!0},e.prototype.destroy=function(){this.boundedEvents=this.context=void 0},e.prototype.offIntlEvents=function(){var e=this.boundedEvents.notifyExternalChange;if(e){for(var t=0;t<e.length;t++){var n=e[""+t].context;n&&n.detectFunction&&n.randomId&&!n.isRendered&&(this.off("notifyExternalChange",n.detectFunction,n.randomId),t--)}this.boundedEvents.notifyExternalChange.length||delete this.boundedEvents.notifyExternalChange}},e.prototype.notExist=function(e){return!1===this.boundedEvents.hasOwnProperty(e)||this.boundedEvents[e].length<=0},e.prototype.isHandlerPresent=function(e,t){for(var n=0,r=e;n<r.length;n++){if(r[n].handler===t)return!0}return!1},e}(),B=new V;e.rightToLeft=!1;var W={};e.defaultCulture="en-US",e.defaultCurrencyCode="USD";var G=function(){function t(e){e&&(this.culture=e)}return t.prototype.getDateFormat=function(e){return S.dateFormat(this.getCulture(),e||{type:"date",skeleton:"short"},W)},t.prototype.getNumberFormat=function(t){return t&&!t.currency&&(t.currency=e.defaultCurrencyCode),y()&&t&&!t.format&&(t.minimumFractionDigits=0),k.numberFormatter(this.getCulture(),t||{},W)},t.prototype.getDateParser=function(e){return Y.dateParser(this.getCulture(),e||{skeleton:"short",type:"date"},W)},t.prototype.getNumberParser=function(e){return y()&&e&&!e.format&&(e.minimumFractionDigits=0),R.numberParser(this.getCulture(),e||{format:"N"},W)},t.prototype.formatNumber=function(e,t){return this.getNumberFormat(t)(e)},t.prototype.formatDate=function(e,t){return this.getDateFormat(t)(e)},t.prototype.parseDate=function(e,t){return this.getDateParser(t)(e)},t.prototype.parseNumber=function(e,t){return this.getNumberParser(t)(e)},t.prototype.getDatePattern=function(t,n){return e.IntlBase.getActualDateTimeFormat(this.getCulture(),t,W,n)},t.prototype.getNumberPattern=function(t,n){return e.IntlBase.getActualNumberFormat(this.getCulture(),t,W,n)},t.prototype.getFirstDayOfWeek=function(){return e.IntlBase.getWeekData(this.getCulture(),W)},t.prototype.getCulture=function(){return this.culture||e.defaultCulture},t}();var H=RegExp,Z={"en-US":{d:"M/d/y",D:"EEEE, MMMM d, y",f:"EEEE, MMMM d, y h:mm a",F:"EEEE, MMMM d, y h:mm:s a",g:"M/d/y h:mm a",G:"M/d/yyyy h:mm:ss tt",m:"MMMM d",M:"MMMM d",r:"ddd, dd MMM yyyy HH':'mm':'ss 'GMT'",R:"ddd, dd MMM yyyy HH':'mm':'ss 'GMT'",s:"yyyy'-'MM'-'dd'T'HH':'mm':'ss",t:"h:mm tt",T:"h:m:s tt",u:"yyyy'-'MM'-'dd HH':'mm':'ss'Z'",U:"dddd, MMMM d, yyyy h:mm:ss tt",y:"MMMM yyyy",Y:"MMMM yyyy"}};!function(t){t.negativeDataRegex=/^(('[^']+'|''|[^*#@0,.E])*)(\*.)?((([#,]*[0,]*0+)(\.0*[0-9]*#*)?)|([#,]*@+#*))(E\+?0+)?(('[^']+'|''|[^*#@0,.E])*)$/,t.customRegex=/^(('[^']+'|''|[^*#@0,.])*)(\*.)?((([0#,]*[0,]*[0#]*[0#\ ]*)(\.[0#]*)?)|([#,]*@+#*))(E\+?0+)?(('[^']+'|''|[^*#@0,.E])*)$/,t.latnParseRegex=/0|1|2|3|4|5|6|7|8|9/g;var n=/[0-9]/g;t.defaultCurrency="$";var r=["infinity","nan","group","decimal"],i=/G|M|L|H|c|'| a|yy|y|EEEE|E/g,o={G:"",M:"m",L:"m",H:"h",c:"d","'":'"'," a":" AM/PM",yy:"yy",y:"yyyy",EEEE:"dddd",E:"ddd"};t.dateConverterMapper=/dddd|ddd/gi;t.islamicRegex=/^islamic/;var a={sun:0,mon:1,tue:2,wed:3,thu:4,fri:5,sat:6};t.formatRegex=new H("(^[ncpae]{1})([0-1]?[0-9]|20)?$","i"),t.currencyFormatRegex=new H("(^[ca]{1})([0-1]?[0-9]|20)?$","i");var s={$:"isCurrency","%":"isPercent","-":"isNegative",0:"nlead",1:"nend"};function u(e,n,r,i,o){var a,s=r||"date";if(o)a=p({skeleton:e},o).format||p({skeleton:"d"},"en-US").format;else{if(-1!==t.basicPatterns.indexOf(e)){if(a=M(s+"Formats."+e,n),"dateTime"===s){var u=M("dateFormats."+e,n),c=M("timeFormats."+e,n);a=a.replace("{1}",u).replace("{0}",c)}}else a=M("dateTimeFormats.availableFormats."+e,n);j(a)&&"yMd"===e&&(a="M/d/y")}return a}function c(e,n,r,i){var o={},a=r||"gregorian";if(o.parserObject=A.getMainObject(e,n)||(y()?t.blazorDefaultObject:t.defaultObject),i)o.numericObject=M("numbers",o.parserObject);else{var s=y()?"dates":"dates.calendars."+a;o.dateObject=M(s,o.parserObject)}return o}function N(e,t,n,r){return M(e+"Formats-numberSystem-"+t+(r?".accounting":".standard"),n)||(r?M(e+"Formats-numberSystem-"+t+".standard",n):"")}function D(e){var n=e.match(t.dateConverterMapper);if(n&&y()){var r=3===n[0].length?"EEE":"EEEE";return e.replace(t.dateConverterMapper,r)}return e}function p(e,t){var n=e.format||e.skeleton,r=M((t||"en-US")+"."+n,Z);return r||(r=M("en-US."+n,Z)),r&&(r=D(r),e.format=r.replace(/tt/,"a")),e}function z(e){var n=e.match(t.formatRegex),r={},i=n[1].toUpperCase();return r.isAccount="A"===i,r.type=t.patternMatcher[i],e.length>1&&(r.fractionDigits=parseInt(n[2],10)),r}function I(e,r,i,o){var M=o?{}:{nlead:"",nend:""},a=e.match(t.customRegex);if(a){o||(M.nlead=d(a[1],i),M.nend=d(a[10],i),M.groupPattern=a[4]);var s=a[7];if(s&&r){var u=s.match(n);l(u)?M.minimumFraction=0:M.minimumFraction=u.length,M.maximumFraction=s.length-1}}return M}function d(e,n){return e?(e=e.replace(t.defaultCurrency,n),""===n?e.trim():e):""}function h(e,t,n){return M("currencies."+t+(n?"."+n:".symbol"),e)||M("currencies."+t+".symbol-alt-narrow",e)||"$"}function f(e,n,i){var o={type:"decimal",minimumFractionDigits:0,maximumFractionDigits:0},M=e.match(t.customRegex);if(l(M)||""===M[5]&&"N/A"!==e)return o.type=void 0,o;o.nlead=M[1],o.nend=M[10];var a=M[6],s=!!a.match(/\ $/g),u=-1!==a.replace(/\ $/g,"").indexOf(" ");o.useGrouping=-1!==a.indexOf(",")||u,a=a.replace(/,/g,"");var c=M[7];if(-1!==a.indexOf("0")&&(o.minimumIntegerDigits=a.length-a.indexOf("0")),l(c)||(o.minimumFractionDigits=c.lastIndexOf("0"),o.maximumFractionDigits=c.lastIndexOf("#"),-1===o.minimumFractionDigits&&(o.minimumFractionDigits=0),(-1===o.maximumFractionDigits||o.maximumFractionDigits<o.minimumFractionDigits)&&(o.maximumFractionDigits=o.minimumFractionDigits)),l(n)?g(o,m([o.nlead,o.nend],"%","%")):(n.isCustomFormat=!0,g(o,m([o.nlead,o.nend],"$",n.currencySymbol)),o.isCurrency||g(o,m([o.nlead,o.nend],"%",n.percentSymbol))),!l(i)){var j=N(o.type,n.numberMapper.numberSystem,i,!1);o.useGrouping&&(o.groupSeparator=u?" ":n.numberMapper.numberSymbols[r[2]],o.groupData=k.getGroupingDetails(j.split(";")[0])),o.nlead=o.nlead.replace(/'/g,""),o.nend=s?" "+o.nend.replace(/'/g,""):o.nend.replace(/'/g,"")}return o}function m(e,t,n){for(var r={nlead:e[0],nend:e[1]},i=0;i<2;i++){var o=e[parseInt(i.toString(),10)],M=o.indexOf(t);if(-1!==M&&(M<o.indexOf("'")||M>o.lastIndexOf("'"))){r[s[i]]=o.substr(0,M)+n+o.substr(M+1),r[s[t]]=!0,r.type=r.isCurrency?"currency":"percent";break}}return r}function T(e,t,n){e+=".";for(var r=0;r<t;r++)e+="0";if(t<n)for(var i=n-t,o=0;o<i;o++)e+="#";return e}function L(e,t){for(var n=e.split("."),r="",i=0;i<t;i++)r+="0";return n[1]?r+"."+n[1]:r}function x(e){var t=e.split("."),n=t[0],r=3-n.length%3;e="";for(var i=(n=(r&&1===r?"#":2===r?"##":"")+n).length-1;i>0;i-=3)e=","+n[i-2]+n[i-1]+n[parseInt(i.toString(),10)]+e;return e=e.slice(1),t[1]?e+"."+t[1]:e}t.dateParseRegex=/([a-z])\1*|'([^']|'')+'|''|./gi,t.basicPatterns=["short","medium","long","full"],t.defaultObject={dates:{calendars:{gregorian:{months:{"stand-alone":{abbreviated:{1:"Jan",2:"Feb",3:"Mar",4:"Apr",5:"May",6:"Jun",7:"Jul",8:"Aug",9:"Sep",10:"Oct",11:"Nov",12:"Dec"},narrow:{1:"J",2:"F",3:"M",4:"A",5:"M",6:"J",7:"J",8:"A",9:"S",10:"O",11:"N",12:"D"},wide:{1:"January",2:"February",3:"March",4:"April",5:"May",6:"June",7:"July",8:"August",9:"September",10:"October",11:"November",12:"December"}}},days:{"stand-alone":{abbreviated:{sun:"Sun",mon:"Mon",tue:"Tue",wed:"Wed",thu:"Thu",fri:"Fri",sat:"Sat"},narrow:{sun:"S",mon:"M",tue:"T",wed:"W",thu:"T",fri:"F",sat:"S"},short:{sun:"Su",mon:"Mo",tue:"Tu",wed:"We",thu:"Th",fri:"Fr",sat:"Sa"},wide:{sun:"Sunday",mon:"Monday",tue:"Tuesday",wed:"Wednesday",thu:"Thursday",fri:"Friday",sat:"Saturday"}}},dayPeriods:{format:{wide:{am:"AM",pm:"PM"}}},eras:{eraNames:{0:"Before Christ","0-alt-variant":"Before Common Era",1:"Anno Domini","1-alt-variant":"Common Era"},eraAbbr:{0:"BC","0-alt-variant":"BCE",1:"AD","1-alt-variant":"CE"},eraNarrow:{0:"B","0-alt-variant":"BCE",1:"A","1-alt-variant":"CE"}},dateFormats:{full:"EEEE, MMMM d, y",long:"MMMM d, y",medium:"MMM d, y",short:"M/d/yy"},timeFormats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},dateTimeFormats:{full:"{1} 'at' {0}",long:"{1} 'at' {0}",medium:"{1}, {0}",short:"{1}, {0}",availableFormats:{d:"d",E:"ccc",Ed:"d E",Ehm:"E h:mm a",EHm:"E HH:mm",Ehms:"E h:mm:ss a",EHms:"E HH:mm:ss",Gy:"y G",GyMMM:"MMM y G",GyMMMd:"MMM d, y G",GyMMMEd:"E, MMM d, y G",h:"h a",H:"HH",hm:"h:mm a",Hm:"HH:mm",hms:"h:mm:ss a",Hms:"HH:mm:ss",hmsv:"h:mm:ss a v",Hmsv:"HH:mm:ss v",hmv:"h:mm a v",Hmv:"HH:mm v",M:"L",Md:"M/d",MEd:"E, M/d",MMM:"LLL",MMMd:"MMM d",MMMEd:"E, MMM d",MMMMd:"MMMM d",ms:"mm:ss",y:"y",yM:"M/y",yMd:"M/d/y",yMEd:"E, M/d/y",yMMM:"MMM y",yMMMd:"MMM d, y",yMMMEd:"E, MMM d, y",yMMMM:"MMMM y"}}},islamic:{months:{"stand-alone":{abbreviated:{1:"Muh.",2:"Saf.",3:"Rab. I",4:"Rab. II",5:"Jum. I",6:"Jum. II",7:"Raj.",8:"Sha.",9:"Ram.",10:"Shaw.",11:"Dhuʻl-Q.",12:"Dhuʻl-H."},narrow:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12"},wide:{1:"Muharram",2:"Safar",3:"Rabiʻ I",4:"Rabiʻ II",5:"Jumada I",6:"Jumada II",7:"Rajab",8:"Shaʻban",9:"Ramadan",10:"Shawwal",11:"Dhuʻl-Qiʻdah",12:"Dhuʻl-Hijjah"}}},days:{"stand-alone":{abbreviated:{sun:"Sun",mon:"Mon",tue:"Tue",wed:"Wed",thu:"Thu",fri:"Fri",sat:"Sat"},narrow:{sun:"S",mon:"M",tue:"T",wed:"W",thu:"T",fri:"F",sat:"S"},short:{sun:"Su",mon:"Mo",tue:"Tu",wed:"We",thu:"Th",fri:"Fr",sat:"Sa"},wide:{sun:"Sunday",mon:"Monday",tue:"Tuesday",wed:"Wednesday",thu:"Thursday",fri:"Friday",sat:"Saturday"}}},dayPeriods:{format:{wide:{am:"AM",pm:"PM"}}},eras:{eraNames:{0:"AH"},eraAbbr:{0:"AH"},eraNarrow:{0:"AH"}},dateFormats:{full:"EEEE, MMMM d, y G",long:"MMMM d, y G",medium:"MMM d, y G",short:"M/d/y GGGGG"},timeFormats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},dateTimeFormats:{full:"{1} 'at' {0}",long:"{1} 'at' {0}",medium:"{1}, {0}",short:"{1}, {0}",availableFormats:{d:"d",E:"ccc",Ed:"d E",Ehm:"E h:mm a",EHm:"E HH:mm",Ehms:"E h:mm:ss a",EHms:"E HH:mm:ss",Gy:"y G",GyMMM:"MMM y G",GyMMMd:"MMM d, y G",GyMMMEd:"E, MMM d, y G",h:"h a",H:"HH",hm:"h:mm a",Hm:"HH:mm",hms:"h:mm:ss a",Hms:"HH:mm:ss",M:"L",Md:"M/d",MEd:"E, M/d",MMM:"LLL",MMMd:"MMM d",MMMEd:"E, MMM d",MMMMd:"MMMM d",ms:"mm:ss",y:"y G",yyyy:"y G",yyyyM:"M/y GGGGG",yyyyMd:"M/d/y GGGGG",yyyyMEd:"E, M/d/y GGGGG",yyyyMMM:"MMM y G",yyyyMMMd:"MMM d, y G",yyyyMMMEd:"E, MMM d, y G",yyyyMMMM:"MMMM y G",yyyyQQQ:"QQQ y G",yyyyQQQQ:"QQQQ y G"}}}},timeZoneNames:{hourFormat:"+HH:mm;-HH:mm",gmtFormat:"GMT{0}",gmtZeroFormat:"GMT"}},numbers:{currencies:{USD:{displayName:"US Dollar",symbol:"$","symbol-alt-narrow":"$"},EUR:{displayName:"Euro",symbol:"€","symbol-alt-narrow":"€"},GBP:{displayName:"British Pound","symbol-alt-narrow":"£"}},defaultNumberingSystem:"latn",minimumGroupingDigits:"1","symbols-numberSystem-latn":{decimal:".",group:",",list:";",percentSign:"%",plusSign:"+",minusSign:"-",exponential:"E",superscriptingExponent:"×",perMille:"‰",infinity:"∞",nan:"NaN",timeSeparator:":"},"decimalFormats-numberSystem-latn":{standard:"#,##0.###"},"percentFormats-numberSystem-latn":{standard:"#,##0%"},"currencyFormats-numberSystem-latn":{standard:"¤#,##0.00",accounting:"¤#,##0.00;(¤#,##0.00)"},"scientificFormats-numberSystem-latn":{standard:"#E0"}}},t.blazorDefaultObject={numbers:{mapper:{0:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9"},mapperDigits:"**********",numberSymbols:{decimal:".",group:",",plusSign:"+",minusSign:"-",percentSign:"%",nan:"NaN",timeSeparator:":",infinity:"∞"},timeSeparator:":",currencySymbol:"$",currencypData:{nlead:"$",nend:"",groupSeparator:",",groupData:{primary:3},maximumFraction:2,minimumFraction:2},percentpData:{nlead:"",nend:"%",groupSeparator:",",groupData:{primary:3},maximumFraction:2,minimumFraction:2},percentnData:{nlead:"-",nend:"%",groupSeparator:",",groupData:{primary:3},maximumFraction:2,minimumFraction:2},currencynData:{nlead:"($",nend:")",groupSeparator:",",groupData:{primary:3},maximumFraction:2,minimumFraction:2},decimalnData:{nlead:"-",nend:"",groupData:{primary:3},maximumFraction:2,minimumFraction:2},decimalpData:{nlead:"",nend:"",groupData:{primary:3},maximumFraction:2,minimumFraction:2}},dates:{dayPeriods:{am:"AM",pm:"PM"},dateSeperator:"/",days:{abbreviated:{sun:"Sun",mon:"Mon",tue:"Tue",wed:"Wed",thu:"Thu",fri:"Fri",sat:"Sat"},short:{sun:"Su",mon:"Mo",tue:"Tu",wed:"We",thu:"Th",fri:"Fr",sat:"Sa"},wide:{sun:"Sunday",mon:"Monday",tue:"Tuesday",wed:"Wednesday",thu:"Thursday",fri:"Friday",sat:"Saturday"}},months:{abbreviated:{1:"Jan",2:"Feb",3:"Mar",4:"Apr",5:"May",6:"Jun",7:"Jul",8:"Aug",9:"Sep",10:"Oct",11:"Nov",12:"Dec"},wide:{1:"January",2:"February",3:"March",4:"April",5:"May",6:"June",7:"July",8:"August",9:"September",10:"October",11:"November",12:"December"}},eras:{1:"AD"}}},t.monthIndex={3:"abbreviated",4:"wide",5:"narrow",1:"abbreviated"},t.month="months",t.days="days",t.patternMatcher={C:"currency",P:"percent",N:"decimal",A:"currency",E:"scientific"},t.getResultantPattern=u,t.getDependables=c,t.getSymbolPattern=N,t.ConvertDateToWeekFormat=D,t.compareBlazorDateFormats=p,t.getProperNumericSkeleton=z,t.getFormatData=I,t.changeCurrencySymbol=d,t.getCurrencySymbol=h,t.customFormat=function(e,t,n){for(var r={},i=e.split(";"),o=["pData","nData","zeroData"],M=0;M<i.length;M++)r[o[M]]=f(i[M],t,n);return l(r.nData)&&(r.nData=g({},r.pData),r.nData.nlead=l(t)?"-"+r.nData.nlead:t.minusSymbol+r.nData.nlead),r},t.customNumberFormat=f,t.isCurrencyPercent=m,t.getDateSeparator=function(e){var t=(M("dateFormats.short",e)||"").match(/[d‏M‏]([^d‏M])[d‏M‏]/i);return t?t[1]:"/"},t.getActualDateTimeFormat=function(e,t,n,r){var a=c(n,e,t.calendar);y()&&(t=p(t,e));var s=t.format||u(t.skeleton,a.dateObject,t.type);if(r){if(-1!==(s=s.replace(i,(function(e){return o[e]}))).indexOf("z")){var N=s.match(/z/g).length,g=void 0,l={timeZone:{}};l.numMapper=A.getNumberMapper(a.parserObject,A.getNumberingSystem(n)),l.timeZone=M("dates.timeZoneNames",a.parserObject);var j=(new Date).getTimezoneOffset(),D=N<4?"+H;-H":l.timeZone.hourFormat;D=D.replace(/:/g,l.numMapper.timeSeparator),0===j?g=l.timeZone.gmtZeroFormat:(g=S.getTimeZoneValue(j,D),g=l.timeZone.gmtFormat.replace(/\{0\}/,g)),s=s.replace(/[z]+/,'"'+g+'"')}s=s.replace(/ $/,"")}return s},t.getActualNumberFormat=function(n,r,i,o){var a,s,u=c(i,n,"",!0),l={custom:!0},j=u.numericObject,D={},p=(r.format||"").match(t.currencyFormatRegex),d=t.formatRegex.test(r.format)?z(r.format||"N"):{},f={};if(p){f.numberMapper=y()?g({},u.numericObject):A.getNumberMapper(u.parserObject,A.getNumberingSystem(i),!0);var m=y()?M("currencySymbol",u.numericObject):h(u.numericObject,r.currency||e.defaultCurrencyCode,r.altSymbol),O=N("currency",f.numberMapper.numberSystem,u.numericObject,/a/i.test(r.format)),b=(O=O.replace(/\u00A4/g,m)).split(";");D.hasNegativePattern=!!y()||b.length>1,D.nData=y()?M(d.type+"nData",j):I(b[1]||"-"+b[0],!0,m),D.pData=y()?M(d.type+"pData",j):I(b[0],!1,m),p[2]||r.minimumFractionDigits||r.maximumFractionDigits||(a=I(O.split(";")[0],!0,"",!0).minimumFraction)}if(t.formatRegex.test(r.format)||!r.format){if(g(l,z(r.format||"N")),l.custom=!1,s="###0",l.fractionDigits||r.minimumFractionDigits||r.maximumFractionDigits||a){l.fractionDigits&&(r.minimumFractionDigits=r.maximumFractionDigits=l.fractionDigits),s=T(s,a||l.fractionDigits||r.minimumFractionDigits||0,r.maximumFractionDigits||0)}if(r.minimumIntegerDigits&&(s=L(s,r.minimumIntegerDigits)),r.useGrouping&&(s=x(s)),"currency"===l.type||l.type&&y()){y()&&"currency"!==l.type&&(D.pData=M(l.type+"pData",j),D.nData=M(l.type+"nData",j));var E=s;s=D.pData.nlead+E+D.pData.nend,(D.hasNegativePattern||y())&&(s+=";"+D.nData.nlead+E+D.nData.nend)}"percent"!==l.type||y()||(s+=" %")}else s=r.format.replace(/'/g,'"');return Object.keys(f).length>0&&(s=o?s:function(e,t){if(-1!==e.indexOf(",")){var n=e.split(",");e=n[0]+M("numberMapper.numberSymbols.group",t)+n[1].replace(".",M("numberMapper.numberSymbols.decimal",t))}else e=e.replace(".",M("numberMapper.numberSymbols.decimal",t));return e}(s,f)),s},t.fractionDigitsPattern=T,t.minimumIntegerPattern=L,t.groupingPattern=x,t.getWeekData=function(e,t){var n="sun",r=M("supplemental.weekData.firstDay",t),i=e;return/en-/.test(i)&&(i=i.slice(3)),i=i.slice(0,2).toUpperCase()+i.substr(2),r&&(n=r[""+i]||r[i.slice(0,2)]||"sun"),a[""+n]},t.replaceBlazorCurrency=function(e,t,n){var r=M(n||"",O);if(t!==r)for(var i=0,o=e;i<o.length;i++){var a=o[i];a.nend=a.nend.replace(t,r),a.nlead=a.nlead.replace(t,r)}},t.getWeekOfYear=function(e){var t,n=new Date(e.getFullYear(),0,1),r=n.getDay();r=r>=0?r:r+7;var i=Math.floor((e.getTime()-n.getTime()-6e4*(e.getTimezoneOffset()-n.getTimezoneOffset()))/864e5)+1;if(r<4){if((t=Math.floor((i+r-1)/7)+1)>52){var o=new Date(e.getFullYear()+1,0,1).getDay();t=(o=o>=0?o:o+7)<4?1:53}}else t=Math.floor((i+r-1)/7);return t}}(e.IntlBase||(e.IntlBase={}));var J=/^(.*?):[ \t]*([^\r\n]*)$/gm,X=function(){function e(e,t,r,i){this.mode=!0,this.emitError=!0,this.options={},"string"==typeof e?(this.url=e,this.type=t?t.toUpperCase():"GET",this.mode=!!l(r)||r):"object"===n(e)&&(this.options=e,N(this,this.options)),this.type=this.type?this.type.toUpperCase():"GET",this.contentType=void 0!==this.contentType?this.contentType:i}return e.prototype.send=function(e){var t=this;this.data=l(e)?this.data:e;var n={cancel:!1,httpRequest:null};return new Promise((function(e,r){t.httpRequest=new XMLHttpRequest,t.httpRequest.onreadystatechange=function(){t.stateChange(e,r)},l(t.onLoad)||(t.httpRequest.onload=t.onLoad),l(t.onProgress)||(t.httpRequest.onprogress=t.onProgress),l(t.onAbort)||(t.httpRequest.onabort=t.onAbort),l(t.onError)||(t.httpRequest.onerror=t.onError),l(t.onUploadProgress)||(t.httpRequest.upload.onprogress=t.onUploadProgress),t.httpRequest.open(t.type,t.url,t.mode),l(t.data)||null===t.contentType||t.httpRequest.setRequestHeader("Content-Type",t.contentType||"application/json; charset=utf-8"),t.beforeSend&&(n.httpRequest=t.httpRequest,t.beforeSend(n)),n.cancel||t.httpRequest.send(l(t.data)?null:t.data)}))},e.prototype.successHandler=function(e){return this.onSuccess&&this.onSuccess(e,this),e},e.prototype.failureHandler=function(e){return this.onFailure&&this.onFailure(this.httpRequest),e},e.prototype.stateChange=function(e,t){var n=this.httpRequest.responseText;if(this.dataType&&"json"===this.dataType.toLowerCase())if(""===n)n=void 0;else try{n=JSON.parse(n)}catch(e){}4===this.httpRequest.readyState&&(this.httpRequest.status>=200&&this.httpRequest.status<=299||304===this.httpRequest.status?e(this.successHandler(n)):this.emitError?t(new Error(this.failureHandler(this.httpRequest.statusText))):e())},e.prototype.getResponseHeader=function(e){var t,n;t={};for(var r=J.exec(this.httpRequest.getAllResponseHeaders());r;)t[r[1].toLowerCase()]=r[2],r=J.exec(this.httpRequest.getAllResponseHeaders());return l(n=t[e.toLowerCase()])?null:n},e}(),_=function(){function e(e,t,n){this.type="GET",this.emitError=!0,"string"==typeof e?(this.url=e,this.type=l(t)?this.type:t.toUpperCase(),this.contentType=n):c(e)&&Object.keys(e).length>0&&N(this,e),this.contentType=l(this.contentType)?"application/json; charset=utf-8":this.contentType}return e.prototype.send=function(e){var t=this,n={"application/json":"json","multipart/form-data":"formData","application/octet-stream":"blob","application/x-www-form-urlencoded":"formData"};try{l(this.fetchRequest)&&"GET"===this.type?this.fetchRequest=new Request(this.url,{method:this.type}):l(this.fetchRequest)&&(this.data=l(e)?this.data:e,this.fetchRequest=new Request(this.url,{method:this.type,headers:{"Content-Type":this.contentType},body:this.data}));var r={cancel:!1,fetchRequest:this.fetchRequest};return this.triggerEvent(this.beforeSend,r),r.cancel?null:(this.fetchResponse=fetch(this.fetchRequest),this.fetchResponse.then((function(e){if(t.triggerEvent(t.onLoad,e),!e.ok)throw e;for(var r="text",i=0,o=Object.keys(n);i<o.length;i++){var M=o[i];e.headers.get("Content-Type")&&-1!==e.headers.get("Content-Type").indexOf(M)&&(r=n[M])}return e[r]()})).then((function(e){return t.triggerEvent(t.onSuccess,e,t),e})).catch((function(e){var n={};return t.emitError&&(t.triggerEvent(t.onFailure,e),n=Promise.reject(e)),n})))}catch(e){return e}},e.prototype.triggerEvent=function(e,t,n){l(e)||"function"!=typeof e||e(t,n)},e}(),K=/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini|mobile/i,q=/msie|trident/i,$=/Trident\/7\./,ee=/(ipad|iphone|ipod touch)/i,te=/(ipad|iphone|ipod touch);.*os 7_\d|(ipad|iphone|ipod touch);.*os 8_\d/i,ne=/android/i,re=/trident|windows phone|edge/i,ie=/(version)[ /]([\w.]+)/i,oe={OPERA:/(opera|opr)(?:.*version|)[ /]([\w.]+)/i,EDGE:/(edge)(?:.*version|)[ /]([\w.]+)/i,CHROME:/(chrome|crios)[ /]([\w.]+)/i,PANTHOMEJS:/(phantomjs)[ /]([\w.]+)/i,SAFARI:/(safari)[ /]([\w.]+)/i,WEBKIT:/(webkit)[ /]([\w.]+)/i,MSIE:/(msie|trident) ([\w.]+)/i,MOZILLA:/(mozilla)(?:.*? rv:([\w.]+)|)/i};"undefined"!=typeof window&&(window.browserDetails=window.browserDetails||{});var Me=function(){function e(){}return e.extractBrowserDetail=function(){for(var t={culture:{}},n=[],r=0,i=Object.keys(oe);r<i.length;r++){var o=i[r];if(n=e.userAgent.match(oe[""+o])){if(t.name="opr"===n[1].toLowerCase()?"opera":n[1].toLowerCase(),t.name="crios"===n[1].toLowerCase()?"chrome":t.name,t.version=n[2],t.culture.name=t.culture.language=navigator.language,e.userAgent.match($)){t.name="msie";break}var M=e.userAgent.match(ie);"safari"===t.name&&M&&(t.version=M[2]);break}}return t},e.getEvent=function(t){var n={start:{isPointer:"pointerdown",isTouch:"touchstart",isDevice:"mousedown"},move:{isPointer:"pointermove",isTouch:"touchmove",isDevice:"mousemove"},end:{isPointer:"pointerup",isTouch:"touchend",isDevice:"mouseup"},cancel:{isPointer:"pointercancel",isTouch:"touchcancel",isDevice:"mouseleave"}};return e.isPointer?n[""+t].isPointer:e.isTouch?n[""+t].isTouch+(e.isDevice?"":" "+n[""+t].isDevice):n[""+t].isDevice},e.getTouchStartEvent=function(){return e.getEvent("start")},e.getTouchEndEvent=function(){return e.getEvent("end")},e.getTouchMoveEvent=function(){return e.getEvent("move")},e.getTouchCancelEvent=function(){return e.getEvent("cancel")},e.isSafari=function(){return e.isDevice&&e.isIos&&e.isTouch&&"undefined"!=typeof window&&-1===window.navigator.userAgent.toLowerCase().indexOf("iphone")&&window.navigator.userAgent.toLowerCase().indexOf("safari")>-1},e.getValue=function(t,n){var r="undefined"!=typeof window?window.browserDetails:{};return"undefined"!=typeof navigator&&"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1&&!0===e.isTouch&&!oe.CHROME.test(navigator.userAgent)&&(r.isIos=!0,r.isDevice=!0,r.isTouch=!0,r.isPointer=!0),void 0===r[""+t]?r[""+t]=n.test(e.userAgent):r[""+t]},Object.defineProperty(e,"userAgent",{get:function(){return e.uA},set:function(t){e.uA=t,window.browserDetails={}},enumerable:!0,configurable:!0}),Object.defineProperty(e,"info",{get:function(){return j(window.browserDetails.info)?window.browserDetails.info=e.extractBrowserDetail():window.browserDetails.info},enumerable:!0,configurable:!0}),Object.defineProperty(e,"isIE",{get:function(){return e.getValue("isIE",q)},enumerable:!0,configurable:!0}),Object.defineProperty(e,"isTouch",{get:function(){return j(window.browserDetails.isTouch)?window.browserDetails.isTouch="ontouchstart"in window.navigator||window&&window.navigator&&window.navigator.maxTouchPoints>0||"ontouchstart"in window:window.browserDetails.isTouch},enumerable:!0,configurable:!0}),Object.defineProperty(e,"isPointer",{get:function(){return j(window.browserDetails.isPointer)?window.browserDetails.isPointer="pointerEnabled"in window.navigator:window.browserDetails.isPointer},enumerable:!0,configurable:!0}),Object.defineProperty(e,"isMSPointer",{get:function(){return j(window.browserDetails.isMSPointer)?window.browserDetails.isMSPointer="msPointerEnabled"in window.navigator:window.browserDetails.isMSPointer},enumerable:!0,configurable:!0}),Object.defineProperty(e,"isDevice",{get:function(){return e.getValue("isDevice",K)},enumerable:!0,configurable:!0}),Object.defineProperty(e,"isIos",{get:function(){return e.getValue("isIos",ee)},enumerable:!0,configurable:!0}),Object.defineProperty(e,"isIos7",{get:function(){return e.getValue("isIos7",te)},enumerable:!0,configurable:!0}),Object.defineProperty(e,"isAndroid",{get:function(){return e.getValue("isAndroid",ne)},enumerable:!0,configurable:!0}),Object.defineProperty(e,"isWebView",{get:function(){return j(window.browserDetails.isWebView)?(window.browserDetails.isWebView=!(j(window.cordova)&&j(window.PhoneGap)&&j(window.phonegap)&&"object"!==window.forge),window.browserDetails.isWebView):window.browserDetails.isWebView},enumerable:!0,configurable:!0}),Object.defineProperty(e,"isWindows",{get:function(){return e.getValue("isWindows",re)},enumerable:!0,configurable:!0}),Object.defineProperty(e,"touchStartEvent",{get:function(){return j(window.browserDetails.touchStartEvent)?window.browserDetails.touchStartEvent=e.getTouchStartEvent():window.browserDetails.touchStartEvent},enumerable:!0,configurable:!0}),Object.defineProperty(e,"touchMoveEvent",{get:function(){return j(window.browserDetails.touchMoveEvent)?window.browserDetails.touchMoveEvent=e.getTouchMoveEvent():window.browserDetails.touchMoveEvent},enumerable:!0,configurable:!0}),Object.defineProperty(e,"touchEndEvent",{get:function(){return j(window.browserDetails.touchEndEvent)?window.browserDetails.touchEndEvent=e.getTouchEndEvent():window.browserDetails.touchEndEvent},enumerable:!0,configurable:!0}),Object.defineProperty(e,"touchCancelEvent",{get:function(){return j(window.browserDetails.touchCancelEvent)?window.browserDetails.touchCancelEvent=e.getTouchCancelEvent():window.browserDetails.touchCancelEvent},enumerable:!0,configurable:!0}),e.uA="undefined"!=typeof navigator?navigator.userAgent:"",e}(),ae=function(){function e(){}return e.addOrGetEventData=function(e){return"__eventList"in e?e.__eventList.events:(e.__eventList={},e.__eventList.events=[])},e.add=function(t,n,r,i,o){var M,a=e.addOrGetEventData(t);M=o?D(r,o):r,i&&(M=M.bind(i));for(var s=n.split(" "),u=0;u<s.length;u++)a.push({name:s[parseInt(u.toString(),10)],listener:r,debounce:M}),Me.isIE?t.addEventListener(s[parseInt(u.toString(),10)],M):t.addEventListener(s[parseInt(u.toString(),10)],M,{passive:!1});return M},e.remove=function(t,n,r){for(var i=e.addOrGetEventData(t),o=n.split(" "),M=function(e){var n,M=-1;i&&0!==i.length&&i.some((function(t,i){return t.name===o[parseInt(e.toString(),10)]&&t.listener===r&&(M=i,n=t.debounce,!0)})),-1!==M&&i.splice(M,1),n&&t.removeEventListener(o[parseInt(e.toString(),10)],n)},a=0;a<o.length;a++)M(a)},e.clearEvents=function(t){var n,r;r=g([],r,n=e.addOrGetEventData(t));for(var i=0;i<r.length;i++){var o=r[parseInt(i.toString(),10)];t.removeEventListener(o.name,o.debounce),n.shift()}},e.trigger=function(t,n,r){for(var i=0,o=e.addOrGetEventData(t);i<o.length;i++){var M=o[i];M.name===n&&M.debounce.call(this,r)}},e}(),se=/^svg|^path|^g/;function ue(e,t){var n=se.test(e)?document.createElementNS("http://www.w3.org/2000/svg",e):document.createElement(e);return void 0===t||(n.innerHTML=t.innerHTML?t.innerHTML:"",void 0!==t.className&&(n.className=t.className),void 0!==t.id&&(n.id=t.id),void 0!==t.styles&&n.setAttribute("style",t.styles),void 0!==t.attrs&&pe(n,t.attrs)),n}function ce(e,t){for(var n=ge(t),r=RegExp,i=0,o=e;i<o.length;i++)for(var s=o[i],u=0,N=n;u<N.length;u++){var g=N[u];if(c(s)){var j=M("attributes.className",s);l(j)?a("attributes.className",g,s):new r("\\b"+g+"\\b","i").test(j)||a("attributes.className",j+" "+g,s)}else s.classList.contains(g)||s.classList.add(g)}return e}function Ne(e,t){for(var n=ge(t),r=0,i=e;r<i.length;r++){var o=i[r],s=c(o);if(s?M("attributes.className",o):""!==o.className)for(var u=0,N=n;u<N.length;u++){var g=N[u];if(s){var l=M("attributes.className",o).split(" "),j=l.indexOf(g);-1!==j&&l.splice(j,1),a("attributes.className",l.join(" "),o)}else o.classList.remove(g)}}return e}function ge(e){var t=[];return"string"==typeof e?t.push(e):t=e,t}function le(e){var t=e;return""===t.style.visibility&&t.offsetWidth>0}function je(e){e.querySelectorAll("script").forEach((function(e){var t=document.createElement("script");t.text=e.innerHTML,document.head.appendChild(t),De(t)}))}function De(e){var t=e.parentNode;if(t)return t.removeChild(e)}function pe(e,t){for(var n=e,r=0,i=Object.keys(t);r<i.length;r++){var o=i[r];if(c(n)){var M=o;"tabindex"===o&&(M="tabIndex"),n.attributes[""+M]=t[""+o]}else n.setAttribute(o,t[""+o])}return n}function ze(e,t){return void 0===t&&(t=document),e=Ie(e),t.querySelector(e)}function ye(e,t){return void 0===t&&(t=document),e=Ie(e),t.querySelectorAll(e)}function Ie(e){var t=/(!|"|\$|%|&|'|\(|\)|\*|\/|:|;|<|=|\?|@|\]|\^|`|{|}|\||\+|~)/g;if(e.match(/#[0-9]/g)||e.match(t)){for(var n=e.split(","),r=0;r<n.length;r++){for(var i=n[parseInt(r.toString(),10)].split(" "),o=0;o<i.length;o++)if(i[parseInt(o.toString(),10)].indexOf("#")>-1&&!i[parseInt(o.toString(),10)].match(/\[.*\]/)){var M=i[parseInt(o.toString(),10)].split("#");if(M[1].match(/^\d/)||M[1].match(t)){var a=i[parseInt(o.toString(),10)].split(".");a[0]=a[0].replace(/#/,"[id='")+"']",i[parseInt(o.toString(),10)]=a.join(".")}}n[parseInt(r.toString(),10)]=i.join(" ")}return n.join(",")}return e}function de(e,t){var n=e;if("function"==typeof n.closest)return n.closest(t);for(;n&&1===n.nodeType;){if(fe(n,t))return n;n=n.parentNode}return null}function he(e,t){void 0!==t&&Object.keys(t).forEach((function(n){e.style[n]=t[n]}))}function fe(e,t){var n=e.matches||e.msMatchesSelector||e.webkitMatchesSelector;return n?n.call(e,t):-1!==[].indexOf.call(document.querySelectorAll(t),e)}var me=new RegExp("]"),Te=function(){function t(e,t){this.isRendered=!1,this.isComplexArraySetter=!1,this.isServerRendered=!1,this.allowServerDataBinding=!0,this.isProtectedOnChange=!0,this.properties={},this.changedProperties={},this.oldProperties={},this.bulkChanges={},this.refreshing=!1,this.ignoreCollectionWatch=!1,this.finalUpdate=function(){},this.childChangedProperties={},this.modelObserver=new V(this),j(t)||(this.element="string"==typeof t?document.querySelector(t):t,l(this.element)||(this.isProtectedOnChange=!1,this.addInstance())),j(e)||this.setProperties(e,!0),this.isDestroyed=!1}return t.prototype.setProperties=function(e,t){var n=this.isProtectedOnChange;this.isProtectedOnChange=!!t,N(this,e),!0!==t?(N(this.changedProperties,e),this.dataBind()):y()&&this.isRendered&&this.serverDataBind(e),this.finalUpdate(),this.changedProperties={},this.oldProperties={},this.isProtectedOnChange=n},t.callChildDataBind=function(e,t){for(var n=0,r=Object.keys(e);n<r.length;n++){var i=r[n];if(t[""+i]instanceof Array)for(var o=0,M=t[""+i];o<M.length;o++){var a=M[o];void 0!==a.dataBind&&a.dataBind()}else t[""+i].dataBind()}},t.prototype.clearChanges=function(){this.finalUpdate(),this.changedProperties={},this.oldProperties={},this.childChangedProperties={}},t.prototype.dataBind=function(){if(t.callChildDataBind(this.childChangedProperties,this),Object.getOwnPropertyNames(this.changedProperties).length){var e=this.isProtectedOnChange,n=this.changedProperties,r=this.oldProperties;this.clearChanges(),this.isProtectedOnChange=!0,this.onPropertyChanged(n,r),this.isProtectedOnChange=e}},t.prototype.serverDataBind=function(e){if(y()){e=e||{},g(this.bulkChanges,{},e,!0);this.allowServerDataBinding&&window.sfBlazor.updateModel&&(window.sfBlazor.updateModel(this),this.bulkChanges={})}},t.prototype.saveChanges=function(e,t,n){if(y()){var r={};r[""+e]=t,this.serverDataBind(r)}this.isProtectedOnChange||(this.oldProperties[""+e]=n,this.changedProperties[""+e]=t,this.finalUpdate(),this.finalUpdate=o(this.dataBind.bind(this)))},t.prototype.addEventListener=function(e,t){this.modelObserver.on(e,t)},t.prototype.removeEventListener=function(e,t){this.modelObserver.off(e,t)},t.prototype.trigger=function(e,t,n,r){var i=this;if(!0!==this.isDestroyed){var o=this.isProtectedOnChange;this.isProtectedOnChange=!1;var a=this.modelObserver.notify(e,t,n,r);if(me.test(e)){var s=M(e,this);if(s){if(window.Blazor){var u=s.call(this,t);u&&"function"==typeof u.then?n?u.then((function(e){n&&(e="string"==typeof e&&i.modelObserver.isJson(e)?JSON.parse(e):e,n.call(i,e))})).catch((function(e){r&&(e="string"==typeof e&&i.modelObserver.isJson(e)?JSON.parse(e):e,r.call(i,e))})):a=u:n&&n.call(this,t)}else s.call(this,t),n&&n.call(this,t)}else n&&n.call(this,t)}return this.isProtectedOnChange=o,a}},t.prototype.addInstance=function(){var e="e-"+this.getModuleName().toLowerCase();ce([this.element],["e-lib",e]),l(this.element.ej2_instances)?a("ej2_instances",[this],this.element):this.element.ej2_instances.push(this)},t.prototype.destroy=function(){var t=this;this.element.ej2_instances=this.element.ej2_instances?this.element.ej2_instances.filter((function(n){return e.proxyToRaw?e.proxyToRaw(n)!==e.proxyToRaw(t):n!==t})):[],Ne([this.element],["e-"+this.getModuleName()]),0===this.element.ej2_instances.length&&Ne([this.element],["e-lib"]),this.clearChanges(),this.modelObserver.destroy(),this.isDestroyed=!0},t}();function Ae(e,t,n,r){return e.properties.hasOwnProperty(t)&&e.properties[t]instanceof r||(e.properties[""+t]=i(r,[e,t,n])),e.properties[""+t]}function Oe(e,t,n,r,o,M){for(var a=[],s=n?n.length:0,u=0;u<s;u++){var c=r;if(M&&(c=r(n[parseInt(u.toString(),10)],e)),o){var N=i(c,[e,t,{},!0]);N.setProperties(n[parseInt(u.toString(),10)],!0),a.push(N)}else a.push(i(c,[e,t,n[parseInt(u.toString(),10)],!1]))}return a}function Le(e,t){return function(){return this.properties.hasOwnProperty(t)||(this.properties[""+t]=e),this.properties[""+t]}}function xe(e,t){return function(n){if(this.properties[""+t]!==n){var r=this.properties.hasOwnProperty(t)?this.properties[t]:e;this.saveChanges(t,n,r),this.properties[""+t]=n}}}function be(e,t,n){return function(){return Ae(this,t,e,n)}}function Ee(e,t,n){return function(r){Ae(this,t,e,n).setProperties(r)}}function Se(e,t,n){return function(){var r=n({});return this.properties.hasOwnProperty(t)?this.properties[""+t]:Ae(this,t,e,r)}}function ve(e,t,n){return function(r){var i=n(r,this);Ae(this,t,e,i).setProperties(r)}}function we(e,t,n){return function(){var r=this;if(!this.properties.hasOwnProperty(t)){var i=Oe(this,t,e,n,!1);this.properties[""+t]=i}var o=void 0!==this.controlParent&&this.controlParent.ignoreCollectionWatch||this.ignoreCollectionWatch;return this.properties[t].hasOwnProperty("push")||o||["push","pop"].forEach((function(e){var i={value:Ye(e,t,n,r.properties[""+t]).bind(r),configurable:!0};Object.defineProperty(r.properties[""+t],e,i)})),this.properties[t].hasOwnProperty("isComplexArray")||Object.defineProperty(this.properties[""+t],"isComplexArray",{value:!0}),this.properties[""+t]}}function ke(e,t,n){return function(r){this.isComplexArraySetter=!0;var i=Oe(this,t,e,n,!1),o=Oe(this,t,r,n,!0);this.isComplexArraySetter=!1,this.saveChanges(t,o,i),this.properties[""+t]=o}}function Qe(e,t,n){return function(r){var i=this.properties.hasOwnProperty(t)?this.properties[t]:e,o=Oe(this,t,r,n,!0,!0);this.saveChanges(t,o,i),this.properties[""+t]=o}}function Ue(e,t,n){return function(){var r=n({});if(!this.properties.hasOwnProperty(t)){var i=Oe(this,t,e,r,!1);this.properties[""+t]=i}return this.properties[""+t]}}function Ye(e,t,n,r){return function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var o=this.propName?this.getParentKey()+"."+t+"-":t+"-";switch(e){case"push":for(var M=0;M<n.length;M++){var a=n[parseInt(M.toString(),10)];Array.prototype[""+e].apply(r,[a]);var s=Ce(o+(r.length-1),a,!this.controlParent,e);this.serverDataBind(s,n[parseInt(M.toString(),10)],!1,e)}break;case"pop":Array.prototype[""+e].apply(r);var u=Ce(o+r.length,null,!this.controlParent,e);this.serverDataBind(u,{ejsAction:"pop"},!1,e)}return r}}function Ce(e,t,r,i){var o=e;if(r&&((o={})[""+e]=t,t&&"object"===n(t))){o[""+e].ejsAction=i}return o}function Pe(e){return function(t,n){var r={set:xe(e,n),get:Le(e,n),enumerable:!0,configurable:!0};Object.defineProperty(t,n,r),Be(t,n,"prop",e)}}function Fe(e,t){return function(n,r){var i={set:Ee(e,r,t),get:be(e,r,t),enumerable:!0,configurable:!0};Object.defineProperty(n,r,i),Be(n,r,"complexProp",e,t)}}function Re(){return function(e,t){var n={set:function(e){var n=this.properties[""+t];if(n!==e){var r=function e(t,n){if(!1===t.hasOwnProperty("parentObj"))return{context:t,prefix:n};var r=M("propName",t);return r&&(n=r+"-"+n),e(M("parentObj",t),n)}(this,t);!1===j(n)&&r.context.removeEventListener(r.prefix,n),r.context.addEventListener(r.prefix,e),this.properties[""+t]=e}},get:Le(void 0,t),enumerable:!0,configurable:!0};Object.defineProperty(e,t,n),Be(e,t,"event")}}function Ve(e){}function Be(e,t,n,r,i){j(e.propList)&&(e.propList={props:[],complexProps:[],colProps:[],events:[],propNames:[],complexPropNames:[],colPropNames:[],eventNames:[]}),e.propList[n+"s"].push({propertyName:t,defaultValue:r,type:i}),e.propList[n+"Names"].push(t)}var We,Ge,He=(We=function(e,t){return(We=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}We(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),Ze=function(e,t,r,i){var o,M=arguments.length,a=M<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,r):i;if("object"===("undefined"==typeof Reflect?"undefined":n(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,r,i);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(M<3?o(a):M>3?o(t,r,a):o(t,r))||a);return M>3&&a&&Object.defineProperty(t,r,a),a},Je=function(t){function n(e){var n=t.call(this,e,void 0)||this;return n.easing={ease:"cubic-bezier(0.250, 0.100, 0.250, 1.000)",linear:"cubic-bezier(0.250, 0.250, 0.750, 0.750)",easeIn:"cubic-bezier(0.420, 0.000, 1.000, 1.000)",easeOut:"cubic-bezier(0.000, 0.000, 0.580, 1.000)",easeInOut:"cubic-bezier(0.420, 0.000, 0.580, 1.000)",elasticInOut:"cubic-bezier(0.5,-0.58,0.38,1.81)",elasticIn:"cubic-bezier(0.17,0.67,0.59,1.81)",elasticOut:"cubic-bezier(0.7,-0.75,0.99,1.01)"},n}var r;return He(n,t),r=n,n.prototype.animate=function(e,t){t=t||{};var n=this.getModel(t);if("string"==typeof e)for(var i=0,o=Array.prototype.slice.call(ye(e,document));i<o.length;i++){var M=o[i];n.element=M,r.delayAnimation(n)}else n.element=e,r.delayAnimation(n)},n.stop=function(e,t){e.style.animation="",e.removeAttribute("e-animate");var n=e.getAttribute("e-animation-id");if(n){var r=parseInt(n,10);cancelAnimationFrame(r),e.removeAttribute("e-animation-id")}t&&t.end&&t.end.call(this,t)},n.delayAnimation=function(t){"Disable"===e.animationMode||e.animationMode===e.GlobalAnimationMode.Disable?(t.begin&&t.begin.call(this,t),t.end&&t.end.call(this,t)):t.delay?setTimeout((function(){r.applyAnimation(t)}),t.delay):r.applyAnimation(t)},n.applyAnimation=function(e){var t=this;e.timeStamp=0;var n=0,r=0,i=0,o=e.duration;e.element.setAttribute("e-animate","true");!function M(a){try{if(a){i=0===i?a:i,e.timeStamp=a+e.timeStamp-i,i=a,!n&&e.begin&&e.begin.call(t,e),n+=1;var s=e.timeStamp/n;e.timeStamp<o&&e.timeStamp+s<o&&e.element.getAttribute("e-animate")?(e.element.style.animation=e.name+" "+e.duration+"ms "+e.timingFunction,e.progress&&e.progress.call(t,e),requestAnimationFrame(M)):(cancelAnimationFrame(r),e.element.removeAttribute("e-animation-id"),e.element.removeAttribute("e-animate"),e.element.style.animation="",e.end&&e.end.call(t,e))}else r=requestAnimationFrame(M),e.element.setAttribute("e-animation-id",r.toString())}catch(n){cancelAnimationFrame(r),e.element.removeAttribute("e-animation-id"),e.fail&&e.fail.call(t,n)}}()},n.prototype.getModel=function(e){return{name:e.name||this.name,delay:e.delay||this.delay,duration:void 0!==e.duration?e.duration:this.duration,begin:e.begin||this.begin,end:e.end||this.end,fail:e.fail||this.fail,progress:e.progress||this.progress,timingFunction:this.easing[e.timingFunction]?this.easing[e.timingFunction]:e.timingFunction||this.easing[this.timingFunction]}},n.prototype.onPropertyChanged=function(e,t){},n.prototype.getModuleName=function(){return"animation"},n.prototype.destroy=function(){},Ze([Pe("FadeIn")],n.prototype,"name",void 0),Ze([Pe(400)],n.prototype,"duration",void 0),Ze([Pe("ease")],n.prototype,"timingFunction",void 0),Ze([Pe(0)],n.prototype,"delay",void 0),Ze([Re()],n.prototype,"progress",void 0),Ze([Re()],n.prototype,"begin",void 0),Ze([Re()],n.prototype,"end",void 0),Ze([Re()],n.prototype,"fail",void 0),n=r=Ze([Ve],n)}(Te);function Xe(e){var t=e.target,n=this.rippleOptions.selector,r=n?de(t,n):t;if(!(!r||this.rippleOptions&&de(t,this.rippleOptions.ignore))){var i=r.getBoundingClientRect(),o=e.pageX-document.body.scrollLeft,M=e.pageY-(!document.body.scrollTop&&document.documentElement?document.documentElement.scrollTop:document.body.scrollTop),a=Math.max(Math.abs(o-i.left),Math.abs(o-i.right)),s=Math.max(Math.abs(M-i.top),Math.abs(M-i.bottom)),u=Math.sqrt(a*a+s*s),c=2*u+"px",N=o-i.left-u,g=M-i.top-u;this.rippleOptions&&this.rippleOptions.isCenterRipple&&(N=0,g=0,c="100%"),r.classList.add("e-ripple");var l=ue("div",{className:"e-ripple-element",styles:"width: "+c+";height: "+c+";left: "+N+"px;top: "+g+"px;transition-duration: "+this.rippleOptions.duration.toString()+"ms;"});r.appendChild(l),window.getComputedStyle(l).getPropertyValue("opacity"),l.style.transform="scale(1)",r!==this.parent&&ae.add(r,"mouseleave",Ke,{parent:this.parent,rippleOptions:this.rippleOptions})}}function _e(e){qe(e,this)}function Ke(e){qe(e,this)}function qe(e,t){var n=t.rippleOptions.duration,r=e.target,i=t.rippleOptions.selector,o=i?de(r,i):r;if(o&&(!o||-1!==o.className.indexOf("e-ripple"))){var M=ye(".e-ripple-element",o),a=M[M.length-1];a&&(a.style.opacity="0.5"),t.parent!==o&&ae.remove(o,"mouseleave",Ke),setTimeout((function(){a&&a.parentNode&&a.parentNode.removeChild(a),o.getElementsByClassName("e-ripple-element").length||o.classList.remove("e-ripple"),t.done&&t.done(e)}),n)}}e.isRippleEnabled=!1,(Ge=e.GlobalAnimationMode||(e.GlobalAnimationMode={})).Default="Default",Ge.Enable="Enable",Ge.Disable="Disable";var $e=function(){function e(e){this.loadedModules=[],this.parent=e}return e.prototype.inject=function(e,t){var n=e.length;if(0!==n){this.loadedModules.length&&this.clearUnusedModule(e);for(var r=0;r<n;r++)for(var o=e[parseInt(r.toString(),10)],M=0,s=t;M<s.length;M++){var u=s[M],c=o.member;if(u&&u.prototype.getModuleName()===o.member&&!this.isModuleLoaded(c)){var N=i(u,o.args),g=this.getMemberName(c);o.isProperty?a(g,u,this.parent):a(g,N,this.parent);var l=o;l.member=g,this.loadedModules.push(l)}}}else this.clean()},e.prototype.clean=function(){for(var e=0,t=this.loadedModules;e<t.length;e++){var n=t[e];n.isProperty||M(n.member,this.parent).destroy()}this.loadedModules=[]},e.prototype.getNonInjectedModules=function(e){var t=this;return e.filter((function(e){return!t.isModuleLoaded(e.member)}))},e.prototype.clearUnusedModule=function(e){for(var t=this,n=e.map((function(e){return t.getMemberName(e.member)})),r=0,i=this.loadedModules.filter((function(e){return-1===n.indexOf(e.member)}));r<i.length;r++){var o=i[r];o.isProperty||M(o.member,this.parent).destroy(),this.loadedModules.splice(this.loadedModules.indexOf(o),1),s(this.parent,o.member)}},e.prototype.getMemberName=function(e){return e[0].toLowerCase()+e.substring(1)+"Module"},e.prototype.isModuleLoaded=function(e){for(var t=0,n=this.loadedModules;t<n.length;t++){if(n[t].member===this.getMemberName(e))return!0}return!1},e}(),et=function(){function e(e,t,n,r){this.isComplexArraySetter=!1,this.properties={},this.changedProperties={},this.childChangedProperties={},this.oldProperties={},this.finalUpdate=function(){},this.callChildDataBind=M("callChildDataBind",Te),this.parentObj=e,this.controlParent=this.parentObj.controlParent||this.parentObj,this.propName=t,this.isParentArray=r,this.setProperties(n,!0)}return e.prototype.updateChange=function(e,t){!0===e?this.parentObj.childChangedProperties[""+t]=e:delete this.parentObj.childChangedProperties[""+t],this.parentObj.updateChange&&this.parentObj.updateChange(e,this.parentObj.propName)},e.prototype.updateTimeOut=function(){if(this.parentObj.updateTimeOut)this.parentObj.finalUpdate(),this.parentObj.updateTimeOut();else{var e=setTimeout(this.parentObj.dataBind.bind(this.parentObj));this.finalUpdate=function(){clearTimeout(e)}}},e.prototype.clearChanges=function(){this.finalUpdate(),this.updateChange(!1,this.propName),this.oldProperties={},this.changedProperties={}},e.prototype.setProperties=function(e,t){!0===t?(N(this,e),this.updateChange(!1,this.propName),this.clearChanges()):N(this,e)},e.prototype.dataBind=function(){if(this.callChildDataBind(this.childChangedProperties,this),this.isParentArray){var e=this.parentObj[this.propName].indexOf(this);Object.keys(this.changedProperties).length&&(a(this.propName+"."+e,this.changedProperties,this.parentObj.changedProperties),a(this.propName+"."+e,this.oldProperties,this.parentObj.oldProperties))}else this.parentObj.changedProperties[this.propName]=this.changedProperties,this.parentObj.oldProperties[this.propName]=this.oldProperties;this.clearChanges()},e.prototype.saveChanges=function(e,t,n,r){this.controlParent.isProtectedOnChange||(r||this.serverDataBind(e,t,!0),this.oldProperties[""+e]=n,this.changedProperties[""+e]=t,this.updateChange(!0,this.propName),this.finalUpdate(),this.updateTimeOut())},e.prototype.serverDataBind=function(e,t,n,r){if(y()&&!this.parentObj.isComplexArraySetter){var i,o={},M=n?this.getParentKey(!0)+"."+e:e;if(-1!==M.indexOf(".")){var a=M.split(".");i=o;for(var s=0;s<a.length;s++){var u=s===a.length-1;i[a[parseInt(s.toString(),10)]]=u?t:{},i=u?i:i[a[parseInt(s.toString(),10)]]}}else o[""+M]={},i=o[""+M],o[""+M][""+e]=t;if(this.isParentArray){i.ejsAction=r||"none"}this.controlParent.serverDataBind(o)}},e.prototype.getParentKey=function(e){var t="",n=this.propName;if(this.isParentArray){t=this.parentObj[this.propName].indexOf(this);var r=this.parentObj[this.propName].length;r=e?r:r>0?r-1:0,n+=t=-1!==t?"-"+t:"-"+r}return this.controlParent!==this.parentObj&&(n=this.parentObj.getParentKey()+"."+this.propName+t),n},e}();var tt,nt,rt=(tt=function(e,t){return(tt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}tt(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),it=function(e,t,r,i){var o,M=arguments.length,a=M<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,r):i;if("object"===("undefined"==typeof Reflect?"undefined":n(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,r,i);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(M<3?o(a):M>3?o(t,r,a):o(t,r))||a);return M>3&&a&&Object.defineProperty(t,r,a),a},ot=0,Mt=0;e.versionBasedStatePersistence=!1;var at=function(t){function r(n,r){var i=t.call(this,n,r)||this;return i.randomId=I(),i.isStringTemplate=!1,i.needsID=!1,i.isReactHybrid=!1,l(i.enableRtl)&&i.setProperties({enableRtl:e.rightToLeft},!0),l(i.locale)&&i.setProperties({locale:e.defaultCulture},!0),i.moduleLoader=new $e(i),i.localObserver=new V(i),B.on("notifyExternalChange",i.detectFunction,i,i.randomId),j(r)||i.appendTo(),i}return rt(r,t),r.prototype.requiredModules=function(){return[]},r.prototype.destroy=function(){this.isDestroyed||(this.enablePersistence&&(this.setPersistData(),this.detachUnloadEvent()),this.localObserver.destroy(),this.refreshing||(Ne([this.element],["e-control"]),this.trigger("destroyed",{cancel:!1}),t.prototype.destroy.call(this),this.moduleLoader.clean(),B.off("notifyExternalChange",this.detectFunction,this.randomId)))},r.prototype.refresh=function(){this.refreshing=!0,this.moduleLoader.clean(),this.destroy(),this.clearChanges(),this.localObserver=new V(this),this.preRender(),this.injectModules(),this.render(),this.refreshing=!1},r.prototype.accessMount=function(){this.mount&&!this.isReactHybrid&&this.mount()},r.prototype.getRootElement=function(){return this.isReactHybrid?this.actualElement:this.element},r.prototype.getLocalData=function(){var t=this.getModuleName()+this.element.id;return e.versionBasedStatePersistence?window.localStorage.getItem(t+this.ej2StatePersistenceVersion):window.localStorage.getItem(t)},r.prototype.attachUnloadEvent=function(){this.handleUnload=this.handleUnload.bind(this),window.addEventListener("unload",this.handleUnload)},r.prototype.handleUnload=function(){this.setPersistData()},r.prototype.detachUnloadEvent=function(){window.removeEventListener("unload",this.handleUnload)},r.prototype.appendTo=function(e){if(l(e)||"string"!=typeof e?l(e)||(this.element=e):this.element=ze(e,document),!l(this.element)){var n="e-"+this.getModuleName().toLowerCase();ce([this.element],["e-control",n]),this.isProtectedOnChange=!1,this.needsID&&!this.element.id&&(this.element.id=this.getUniqueID(this.getModuleName())),this.enablePersistence&&(this.mergePersistData(),this.attachUnloadEvent());var r=M("ej2_instances",this.element);r&&-1!==r.indexOf(this)||t.prototype.addInstance.call(this),this.preRender(),this.injectModules();var i={schedule:"all",diagram:"all",PdfViewer:"all",grid:["logger"],richtexteditor:["link","table","image","audio","video","formatPainter","emojiPicker","pasteCleanup","htmlEditor","toolbar"],treegrid:["filter"],gantt:["tooltip"],chart:["Export","Zoom"]},o=this.getModuleName();if(this.requiredModules&&(!i[""+o]||"all"!==i[""+o]))for(var a=this.requiredModules(),s=0,u=this.moduleLoader.getNonInjectedModules(a);s<u.length;s++){var c=u[s],N=c.name?c.name:c.member;if(!i[""+o]||-1===i[""+o].indexOf(c.member)){var g=o.charAt(0).toUpperCase()+o.slice(1);console.warn('[WARNING] :: Module "'+N+'" is not available in '+g+" component! You either misspelled the module name or forgot to load it.")}}this.render(),this.mount?this.accessMount():this.trigger("created")}},r.prototype.renderComplete=function(e){if(y()){window.sfBlazor.renderComplete(this.element,e)}this.isRendered=!0},r.prototype.dataBind=function(){this.injectModules(),t.prototype.dataBind.call(this)},r.prototype.on=function(e,t,n){if("string"==typeof e)this.localObserver.on(e,t,n);else for(var r=0,i=e;r<i.length;r++){var o=i[r];this.localObserver.on(o.event,o.handler,o.context)}},r.prototype.off=function(e,t){if("string"==typeof e)this.localObserver.off(e,t);else for(var n=0,r=e;n<r.length;n++){var i=r[n];this.localObserver.off(i.event,i.handler)}},r.prototype.notify=function(e,t){!0!==this.isDestroyed&&this.localObserver.notify(e,t)},r.prototype.getInjectedModules=function(){return this.injectedModules},r.Inject=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.prototype.injectedModules||(this.prototype.injectedModules=[]);for(var n=0;n<e.length;n++)-1===this.prototype.injectedModules.indexOf(e[parseInt(n.toString(),10)])&&this.prototype.injectedModules.push(e[parseInt(n.toString(),10)])},r.prototype.createElement=function(e,t,n){return ue(e,t)},r.prototype.triggerStateChange=function(e,t){this.isReactHybrid&&(this.setState(),this.currentContext={calls:e,args:t})},r.prototype.injectModules=function(){this.injectedModules&&this.injectedModules.length&&this.moduleLoader.inject(this.requiredModules(),this.injectedModules)},r.prototype.detectFunction=function(e){var t=Object.keys(e);t.length&&(this[t[0]]=e[t[0]])},r.prototype.mergePersistData=function(){var t;l(t=e.versionBasedStatePersistence?window.localStorage.getItem(this.getModuleName()+this.element.id+this.ej2StatePersistenceVersion):window.localStorage.getItem(this.getModuleName()+this.element.id))||""===t||this.setProperties(JSON.parse(t),!0)},r.prototype.setPersistData=function(){this.isDestroyed||(e.versionBasedStatePersistence?window.localStorage.setItem(this.getModuleName()+this.element.id+this.ej2StatePersistenceVersion,this.getPersistData()):window.localStorage.setItem(this.getModuleName()+this.element.id,this.getPersistData()))},r.prototype.renderReactTemplates=function(e){l(e)||e()},r.prototype.clearTemplate=function(e,t){},r.prototype.getUniqueID=function(e){return this.isHistoryChanged()&&(ot=0),nt=this.pageID(location.href),Mt=history.length,e+"_"+nt+"_"+ot++},r.prototype.pageID=function(e){var t=0;if(0===e.length)return t;for(var n=0;n<e.length;n++){t=(t<<5)-t+e.charCodeAt(n),t&=t}return Math.abs(t)},r.prototype.isHistoryChanged=function(){return nt!==this.pageID(location.href)||Mt!==history.length},r.prototype.addOnPersist=function(e){for(var t=this,n={},r=0,i=e;r<i.length;r++){var o,s=i[r];j(o=M(s,this))||a(s,this.getActualProperties(o),n)}return JSON.stringify(n,(function(e,n){return t.getActualProperties(n)}))},r.prototype.getActualProperties=function(e){return e instanceof et?M("properties",e):e},r.prototype.ignoreOnPersist=function(e){return JSON.stringify(this.iterateJsonProperties(this.properties,e))},r.prototype.iterateJsonProperties=function(e,t){for(var r={},i=function(i){if(-1===t.indexOf(i)){var M=e[i];if("object"!==n(M)||M instanceof Array)r[""+i]=M;else{var a=t.filter((function(e){return new RegExp(i+".").test(e)})).map((function(e){return e.replace(i+".","")}));r[""+i]=o.iterateJsonProperties(o.getActualProperties(M),a)}}},o=this,M=0,a=Object.keys(e);M<a.length;M++){i(a[M])}return r},it([Pe(!1)],r.prototype,"enablePersistence",void 0),it([Pe()],r.prototype,"enableRtl",void 0),it([Pe()],r.prototype,"locale",void 0),r=it([Ve],r)}(Te);"undefined"!=typeof window&&window.addEventListener("popstate",(function(){ot=0}));var st,ut,ct,Nt,gt=(st=function(e,t){return(st=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}st(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),lt=function(e,t,r,i){var o,M=arguments.length,a=M<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,r):i;if("object"===("undefined"==typeof Reflect?"undefined":n(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,r,i);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(M<3?o(a):M>3?o(t,r,a):o(t,r))||a);return M>3&&a&&Object.defineProperty(t,r,a),a},jt={left:0,top:0,bottom:0,right:0},Dt={isDragged:!1},pt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return gt(t,e),lt([Pe(0)],t.prototype,"left",void 0),lt([Pe(0)],t.prototype,"top",void 0),t}(et),zt=function(e){function t(t,n){var i=e.call(this,n,t)||this;return i.dragLimit=r.getDefaultPosition(),i.borderWidth=r.getDefaultPosition(),i.padding=r.getDefaultPosition(),i.diffX=0,i.prevLeft=0,i.prevTop=0,i.dragProcessStarted=!1,i.eleTop=0,i.tapHoldTimer=0,i.externalInitialize=!1,i.diffY=0,i.parentScrollX=0,i.parentScrollY=0,i.droppables={},i.bind(),i}var r;return gt(t,e),r=t,t.prototype.bind=function(){this.toggleEvents(),Me.isIE&&ce([this.element],"e-block-touch"),this.droppables[this.scope]={}},t.getDefaultPosition=function(){return g({},jt)},t.prototype.toggleEvents=function(e){var t;j(this.handle)||(t=ze(this.handle,this.element));var n=this.enableTapHold&&Me.isDevice&&Me.isTouch?this.mobileInitialize:this.initialize;e?ae.remove(t||this.element,Me.isSafari()?"touchstart":Me.touchStartEvent,n):ae.add(t||this.element,Me.isSafari()?"touchstart":Me.touchStartEvent,n,this)},t.prototype.mobileInitialize=function(e){var t=this,n=e.currentTarget;this.tapHoldTimer=setTimeout((function(){t.externalInitialize=!0,t.removeTapholdTimer(),t.initialize(e,n)}),this.tapHoldThreshold),ae.add(document,Me.isSafari()?"touchmove":Me.touchMoveEvent,this.removeTapholdTimer,this),ae.add(document,Me.isSafari()?"touchend":Me.touchEndEvent,this.removeTapholdTimer,this)},t.prototype.removeTapholdTimer=function(){clearTimeout(this.tapHoldTimer),ae.remove(document,Me.isSafari()?"touchmove":Me.touchMoveEvent,this.removeTapholdTimer),ae.remove(document,Me.isSafari()?"touchend":Me.touchEndEvent,this.removeTapholdTimer)},t.prototype.getScrollableParent=function(e,t){return l(e)?null:e[{vertical:"scrollHeight",horizontal:"scrollWidth"}[""+t]]>e[{vertical:"clientHeight",horizontal:"clientWidth"}[""+t]]&&("vertical"===t?e.scrollTop>0:e.scrollLeft>0)?("vertical"===t?(this.parentScrollY=this.parentScrollY+(0===this.parentScrollY?e.scrollTop:e.scrollTop-this.parentScrollY),this.tempScrollHeight=e.scrollHeight):(this.parentScrollX=this.parentScrollX+(0===this.parentScrollX?e.scrollLeft:e.scrollLeft-this.parentScrollX),this.tempScrollWidth=e.scrollWidth),l(e)?e:this.getScrollableParent(e.parentNode,t)):this.getScrollableParent(e.parentNode,t)},t.prototype.getScrollableValues=function(){this.parentScrollX=0,this.parentScrollY=0;this.element.classList.contains("e-dialog")&&this.element.classList.contains("e-dlg-modal"),this.getScrollableParent(this.element.parentNode,"vertical"),this.getScrollableParent(this.element.parentNode,"horizontal")},t.prototype.initialize=function(e,t){if(this.currentStateTarget=e.target,!this.isDragStarted()){if(this.isDragStarted(!0),this.externalInitialize=!1,this.target=e.currentTarget||t,this.dragProcessStarted=!1,this.abort){var n=this.abort;"string"==typeof n&&(n=[n]);for(var r=0;r<n.length;r++)if(!l(de(e.target,n[parseInt(r.toString(),10)])))return void(this.isDragStarted()&&this.isDragStarted(!0))}this.preventDefault&&!j(e.changedTouches)&&"touchstart"!==e.type&&e.preventDefault(),this.element.setAttribute("aria-grabbed","true");var i=this.getCoordinates(e);if(this.initialPosition={x:i.pageX,y:i.pageY},!this.clone){var o=this.element.getBoundingClientRect();this.getScrollableValues(),e.clientX===e.pageX&&(this.parentScrollX=0),e.clientY===e.pageY&&(this.parentScrollY=0),this.relativeXPosition=i.pageX-(o.left+this.parentScrollX),this.relativeYPosition=i.pageY-(o.top+this.parentScrollY)}this.externalInitialize?this.intDragStart(e):(ae.add(document,Me.isSafari()?"touchmove":Me.touchMoveEvent,this.intDragStart,this),ae.add(document,Me.isSafari()?"touchend":Me.touchEndEvent,this.intDestroy,this)),this.toggleEvents(!0),"touchstart"!==e.type&&this.isPreventSelect&&document.body.classList.add("e-prevent-select"),this.externalInitialize=!1,ae.trigger(document.documentElement,Me.isSafari()?"touchstart":Me.touchStartEvent,e)}},t.prototype.intDragStart=function(e){this.removeTapholdTimer();var t=!j(e.changedTouches);if(!t||1===e.changedTouches.length){var n,r=this.getCoordinates(e),i=getComputedStyle(this.element);this.margin={left:parseInt(i.marginLeft,10),top:parseInt(i.marginTop,10),right:parseInt(i.marginRight,10),bottom:parseInt(i.marginBottom,10)};var o=this.element;if(this.clone&&this.dragTarget){var M=de(e.target,this.dragTarget);l(M)||(o=M)}this.isReplaceDragEle&&(o=this.currentStateCheck(e.target,o)),this.offset=this.calculateParentPosition(o),this.position=this.getMousePosition(e,this.isDragScroll);var a=this.initialPosition.x-r.pageX,s=this.initialPosition.y-r.pageY;if(Math.sqrt(a*a+s*s)>=this.distance||this.externalInitialize){var u=this.getHelperElement(e);if(!u||l(u))return;t&&e.preventDefault();var c=this.helperElement=u;if(this.parentClientRect=this.calculateParentPosition(c.offsetParent),this.dragStart){var N={event:e,element:o,target:this.getProperTargetElement(e),bindEvents:y()?this.bindDragEvents.bind(this):null,dragElement:c};this.trigger("dragStart",N)}this.dragArea?this.setDragArea():(this.dragLimit={left:0,right:0,bottom:0,top:0},this.borderWidth={top:0,left:0}),n={left:this.position.left-this.parentClientRect.left,top:this.position.top-this.parentClientRect.top},this.clone&&!this.enableTailMode&&(this.diffX=this.position.left-this.offset.left,this.diffY=this.position.top-this.offset.top),this.getScrollableValues();var g=getComputedStyle(o),D=parseFloat(g.marginTop);this.clone&&0!==D&&(n.top+=D),this.eleTop=isNaN(parseFloat(g.top))?0:parseFloat(g.top)-this.offset.top,this.enableScrollHandler&&!this.clone&&(n.top-=this.parentScrollY,n.left-=this.parentScrollX);var p=this.getProcessedPositionValue({top:n.top-this.diffY+"px",left:n.left-this.diffX+"px"});this.dragArea&&"string"!=typeof this.dragArea&&this.dragArea.classList.contains("e-kanban-content")&&"relative"===this.dragArea.style.position&&(n.top+=this.dragArea.scrollTop),this.dragElePosition={top:n.top,left:n.left},he(c,this.getDragPosition({position:"absolute",left:p.left,top:p.top})),ae.remove(document,Me.isSafari()?"touchmove":Me.touchMoveEvent,this.intDragStart),ae.remove(document,Me.isSafari()?"touchend":Me.touchEndEvent,this.intDestroy),y()||this.bindDragEvents(c)}}},t.prototype.bindDragEvents=function(e){le(e)?(ae.add(document,Me.isSafari()?"touchmove":Me.touchMoveEvent,this.intDrag,this),ae.add(document,Me.isSafari()?"touchend":Me.touchEndEvent,this.intDragStop,this),this.setGlobalDroppables(!1,this.element,e)):(this.toggleEvents(),document.body.classList.remove("e-prevent-select"))},t.prototype.elementInViewport=function(e){for(this.top=e.offsetTop,this.left=e.offsetLeft,this.width=e.offsetWidth,this.height=e.offsetHeight;e.offsetParent;)e=e.offsetParent,this.top+=e.offsetTop,this.left+=e.offsetLeft;return this.top>=window.pageYOffset&&this.left>=window.pageXOffset&&this.top+this.height<=window.pageYOffset+window.innerHeight&&this.left+this.width<=window.pageXOffset+window.innerWidth},t.prototype.getProcessedPositionValue=function(e){return this.queryPositionInfo?this.queryPositionInfo(e):e},t.prototype.calculateParentPosition=function(e){if(l(e))return{left:0,top:0};var t=e.getBoundingClientRect(),n=getComputedStyle(e);return{left:t.left+window.pageXOffset-parseInt(n.marginLeft,10),top:t.top+window.pageYOffset-parseInt(n.marginTop,10)}},t.prototype.intDrag=function(e){if(j(e.changedTouches)||1===e.changedTouches.length){var t,n;this.clone&&e.changedTouches&&Me.isDevice&&Me.isTouch&&e.preventDefault(),this.position=this.getMousePosition(e,this.isDragScroll);var r=this.getDocumentWidthHeight("Height");r<this.position.top&&(this.position.top=r);var i=this.getDocumentWidthHeight("Width");if(i<this.position.left&&(this.position.left=i),this.drag){var o=this.getProperTargetElement(e);this.trigger("drag",{event:e,element:this.element,target:o})}var M=this.checkTargetElement(e);if(M.target&&M.instance){var a=!0;this.hoverObject&&(this.hoverObject.instance!==M.instance?this.triggerOutFunction(e,M):a=!1),a&&(M.instance.dragData[this.scope]=this.droppables[this.scope],M.instance.intOver(e,M.target),this.hoverObject=M)}else this.hoverObject&&this.triggerOutFunction(e,M);var s=this.droppables[this.scope].helper;this.parentClientRect=this.calculateParentPosition(this.helperElement.offsetParent);var u=this.parentClientRect.left,c=this.parentClientRect.top,N=this.getCoordinates(e),g=N.pageX,D=N.pageY,p=this.position.left-this.diffX,z=this.position.top-this.diffY,y=getComputedStyle(s);if(this.dragArea){if(this.enableAutoScroll&&this.setDragArea(),this.pageX!==g||this.skipDistanceCheck){var I=s.offsetWidth+(parseFloat(y.marginLeft)+parseFloat(y.marginRight));t=this.dragLimit.left>p&&p>0?this.dragLimit.left:this.dragLimit.right+window.pageXOffset<p+I&&p>0?p-(p-this.dragLimit.right)+window.pageXOffset-I:p<0?this.dragLimit.left:p}if(this.pageY!==D||this.skipDistanceCheck){var d=s.offsetHeight+(parseFloat(y.marginTop)+parseFloat(y.marginBottom));n=this.dragLimit.top>z&&z>0?this.dragLimit.top:this.dragLimit.bottom+window.pageYOffset<z+d&&z>0?z-(z-this.dragLimit.bottom)+window.pageYOffset-d:z<0?this.dragLimit.top:z}}else t=p,n=z;var h,f,m=c+this.borderWidth.top,T=u+this.borderWidth.left;if(this.dragProcessStarted&&(l(n)&&(n=this.prevTop),l(t)&&(t=this.prevLeft)),this.helperElement.classList.contains("e-treeview"))this.dragArea?(this.dragLimit.top=this.clone?this.dragLimit.top:0,h=n-m<0?this.dragLimit.top:n-this.borderWidth.top,f=t-T<0?this.dragLimit.left:t-this.borderWidth.left):(h=n-this.borderWidth.top,f=t-this.borderWidth.left);else if(this.dragArea){var A=this.helperElement.classList.contains("e-dialog");this.dragLimit.top=this.clone?this.dragLimit.top:0,h=n-m<0?this.dragLimit.top:n-m,f=t-T<0?A?t-(T-this.borderWidth.left):this.dragElePosition.left:t-T}else h=n-m,f=t-T;var O=parseFloat(getComputedStyle(this.element).marginTop);if(O>0&&(this.clone&&(h+=O,z<0&&(O+z>=0?h=O+z:h-=O),this.dragArea&&(h=this.dragLimit.bottom<h?this.dragLimit.bottom:h)),n-m<0))if(z+O+(s.offsetHeight-m)>=0){var L=this.dragLimit.top+z-m;L+O+m<0?h-=O+m:h=L}else h-=O+m;if(this.dragArea&&this.helperElement.classList.contains("e-treeview"))h=h+(d=s.offsetHeight+(parseFloat(y.marginTop)+parseFloat(y.marginBottom)))>this.dragLimit.bottom?this.dragLimit.bottom-d:h;this.enableScrollHandler&&!this.clone&&(h-=this.parentScrollY,f-=this.parentScrollX),this.dragArea&&"string"!=typeof this.dragArea&&this.dragArea.classList.contains("e-kanban-content")&&"relative"===this.dragArea.style.position&&(h+=this.dragArea.scrollTop);var x=this.getProcessedPositionValue({top:h+"px",left:f+"px"});he(s,this.getDragPosition(x)),this.elementInViewport(s)||!this.enableAutoScroll||this.helperElement.classList.contains("e-treeview")||this.helperElement.scrollIntoView();var b=document.querySelectorAll(":hover");if(this.enableAutoScroll&&this.helperElement.classList.contains("e-treeview")){0===b.length&&(b=this.getPathElements(e));var E=this.getScrollParent(b,!1);this.elementInViewport(this.helperElement)?this.getScrollPosition(E,h):this.elementInViewport(this.helperElement)||(0===(b=[].slice.call(document.querySelectorAll(":hover"))).length&&(b=this.getPathElements(e)),E=this.getScrollParent(b,!0),this.getScrollPosition(E,h))}this.dragProcessStarted=!0,this.prevLeft=t,this.prevTop=n,this.position.left=t,this.position.top=n,this.pageX=g,this.pageY=D}},t.prototype.getScrollParent=function(e,t){for(var n,r=t?e.reverse():e,i=r.length-1;i>=0;i--)if(("auto"===(n=window.getComputedStyle(r[parseInt(i.toString(),10)])["overflow-y"])||"scroll"===n)&&r[parseInt(i.toString(),10)].scrollHeight>r[parseInt(i.toString(),10)].clientHeight)return r[parseInt(i.toString(),10)];if("visible"===(n=window.getComputedStyle(document.scrollingElement)["overflow-y"]))return document.scrollingElement.style.overflow="auto",document.scrollingElement},t.prototype.getScrollPosition=function(e,t){if(e&&e===document.scrollingElement)e.clientHeight+document.scrollingElement.scrollTop-this.helperElement.clientHeight<t&&e.getBoundingClientRect().height+this.parentClientRect.top>t?e.scrollTop+=this.helperElement.clientHeight:e.scrollTop>t-this.helperElement.clientHeight&&(e.scrollTop-=this.helperElement.clientHeight);else if(e&&e!==document.scrollingElement){var n=document.scrollingElement.scrollTop,r=this.helperElement.clientHeight;e.clientHeight+e.getBoundingClientRect().top-r+n<t?e.scrollTop+=this.helperElement.clientHeight:e.getBoundingClientRect().top>t-r-n&&(e.scrollTop-=this.helperElement.clientHeight)}},t.prototype.getPathElements=function(e){var t=e.clientX>0?e.clientX:0,n=e.clientY>0?e.clientY:0;return document.elementsFromPoint(t,n)},t.prototype.triggerOutFunction=function(e,t){this.hoverObject.instance.intOut(e,t.target),this.hoverObject.instance.dragData[this.scope]=null,this.hoverObject=null},t.prototype.getDragPosition=function(e){var t=g({},e);return this.axis&&("x"===this.axis?delete t.top:"y"===this.axis&&delete t.left),t},t.prototype.getDocumentWidthHeight=function(e){var t=document.body,n=document.documentElement;return Math.max(t["scroll"+e],n["scroll"+e],t["offset"+e],n["offset"+e],n["client"+e])},t.prototype.intDragStop=function(e){if(this.dragProcessStarted=!1,j(e.changedTouches)||1===e.changedTouches.length){if(-1!==["touchend","pointerup","mouseup"].indexOf(e.type)){if(this.dragStop){var t=this.getProperTargetElement(e);this.trigger("dragStop",{event:e,element:this.element,target:t,helper:this.helperElement})}this.intDestroy(e)}else this.element.setAttribute("aria-grabbed","false");var n=this.checkTargetElement(e);n.target&&n.instance&&(n.instance.dragStopCalled=!0,n.instance.dragData[this.scope]=this.droppables[this.scope],n.instance.intDrop(e,n.target)),this.setGlobalDroppables(!0),document.body.classList.remove("e-prevent-select")}},t.prototype.intDestroy=function(e){this.dragProcessStarted=!1,this.toggleEvents(),document.body.classList.remove("e-prevent-select"),this.element.setAttribute("aria-grabbed","false"),ae.remove(document,Me.isSafari()?"touchmove":Me.touchMoveEvent,this.intDragStart),ae.remove(document,Me.isSafari()?"touchend":Me.touchEndEvent,this.intDragStop),ae.remove(document,Me.isSafari()?"touchend":Me.touchEndEvent,this.intDestroy),ae.remove(document,Me.isSafari()?"touchmove":Me.touchMoveEvent,this.intDrag),this.isDragStarted()&&this.isDragStarted(!0)},t.prototype.onPropertyChanged=function(e,t){},t.prototype.getModuleName=function(){return"draggable"},t.prototype.isDragStarted=function(e){return e&&(Dt.isDragged=!Dt.isDragged),Dt.isDragged},t.prototype.setDragArea=function(){var e,t,r,i=0,o=0;if(r="string"===n(this.dragArea)?ze(this.dragArea):this.dragArea){var M=r.getBoundingClientRect();e=r.scrollWidth?r.scrollWidth:M.right-M.left,t=r.scrollHeight?this.dragArea&&!l(this.helperElement)&&this.helperElement.classList.contains("e-treeview")?r.clientHeight:r.scrollHeight:M.bottom-M.top;for(var a=["Top","Left","Bottom","Right"],s=getComputedStyle(r),u=0;u<a.length;u++){var c=a[parseInt(u.toString(),10)],N=s["border"+c+"Width"],g=s["padding"+c],j=c.toLowerCase();this.borderWidth[""+j]=isNaN(parseFloat(N))?0:parseFloat(N),this.padding[""+j]=isNaN(parseFloat(g))?0:parseFloat(g)}i=this.dragArea&&!l(this.helperElement)&&this.helperElement.classList.contains("e-treeview")?M.top+document.scrollingElement.scrollTop:M.top,o=M.left,this.dragLimit.left=o+this.borderWidth.left+this.padding.left,this.dragLimit.top=r.offsetTop+this.borderWidth.top+this.padding.top,this.dragLimit.right=o+e-(this.borderWidth.right+this.padding.right),this.dragLimit.bottom=i+t-(this.borderWidth.bottom+this.padding.bottom)}},t.prototype.getProperTargetElement=function(e){var t,n=this.getCoordinates(e),r=this.helperElement.style.pointerEvents||"",i=-1!==e.type.indexOf("pointer")&&"safari"===Me.info.name&&parseInt(Me.info.version,10)>12;return p(e.target,this.helperElement)||-1!==e.type.indexOf("touch")||i?(this.helperElement.style.pointerEvents="none",t=document.elementFromPoint(n.clientX,n.clientY),this.helperElement.style.pointerEvents=r):t=e.target,t},t.prototype.currentStateCheck=function(e,t){return l(this.currentStateTarget)||this.currentStateTarget===e?l(t)?e:t:this.currentStateTarget},t.prototype.getMousePosition=function(e,t){var n,r,i=void 0!==e.srcElement?e.srcElement:e.target,o=this.getCoordinates(e),M=l(i.offsetParent);if(t?(n=this.clone?o.pageX:o.pageX+(M?0:i.offsetParent.scrollLeft)-this.relativeXPosition,r=this.clone?o.pageY:o.pageY+(M?0:i.offsetParent.scrollTop)-this.relativeYPosition):(n=this.clone?o.pageX:o.pageX+window.pageXOffset-this.relativeXPosition,r=this.clone?o.pageY:o.pageY+window.pageYOffset-this.relativeYPosition),document.scrollingElement&&!t&&!this.clone){var a=document.scrollingElement,s=a.scrollHeight>0&&a.scrollHeight>a.clientHeight&&a.scrollTop>0;n=a.scrollWidth>0&&a.scrollWidth>a.clientWidth&&a.scrollLeft>0?n-a.scrollLeft:n,r=s?r-a.scrollTop:r}return{left:n-(this.margin.left+this.cursorAt.left),top:r-(this.margin.top+this.cursorAt.top)}},t.prototype.getCoordinates=function(e){return e.type.indexOf("touch")>-1?e.changedTouches[0]:e},t.prototype.getHelperElement=function(e){var t;return this.clone?this.helper?t=this.helper({sender:e,element:this.target}):(t=ue("div",{className:"e-drag-helper e-block-touch",innerHTML:"Draggable"}),document.body.appendChild(t)):t=this.element,t},t.prototype.setGlobalDroppables=function(e,t,n){this.droppables[this.scope]=e?null:{draggable:t,helper:n,draggedElement:this.element}},t.prototype.checkTargetElement=function(e){var t=this.getProperTargetElement(e),n=this.getDropInstance(t);if(!n&&t&&!l(t.parentNode)){var r=de(t.parentNode,".e-droppable")||t.parentElement;r&&(n=this.getDropInstance(r))}return{target:t,instance:n}},t.prototype.getDropInstance=function(e){var t,n=e&&e.ej2_instances;if(n)for(var r=0,i=n;r<i.length;r++){var o=i[r];if("droppable"===o.getModuleName()){t=o;break}}return t},t.prototype.destroy=function(){this.toggleEvents(!0),e.prototype.destroy.call(this)},lt([Fe({},pt)],t.prototype,"cursorAt",void 0),lt([Pe(!0)],t.prototype,"clone",void 0),lt([Pe()],t.prototype,"dragArea",void 0),lt([Pe()],t.prototype,"isDragScroll",void 0),lt([Pe()],t.prototype,"isReplaceDragEle",void 0),lt([Pe(!0)],t.prototype,"isPreventSelect",void 0),lt([Re()],t.prototype,"drag",void 0),lt([Re()],t.prototype,"dragStart",void 0),lt([Re()],t.prototype,"dragStop",void 0),lt([Pe(1)],t.prototype,"distance",void 0),lt([Pe()],t.prototype,"handle",void 0),lt([Pe()],t.prototype,"abort",void 0),lt([Pe()],t.prototype,"helper",void 0),lt([Pe("default")],t.prototype,"scope",void 0),lt([Pe("")],t.prototype,"dragTarget",void 0),lt([Pe()],t.prototype,"axis",void 0),lt([Pe()],t.prototype,"queryPositionInfo",void 0),lt([Pe(!1)],t.prototype,"enableTailMode",void 0),lt([Pe(!1)],t.prototype,"skipDistanceCheck",void 0),lt([Pe(!0)],t.prototype,"preventDefault",void 0),lt([Pe(!1)],t.prototype,"enableAutoScroll",void 0),lt([Pe(!1)],t.prototype,"enableTapHold",void 0),lt([Pe(750)],t.prototype,"tapHoldThreshold",void 0),lt([Pe(!1)],t.prototype,"enableScrollHandler",void 0),t=r=lt([Ve],t)}(Te),yt=(ut=function(e,t){return(ut=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}ut(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),It=function(e,t,r,i){var o,M=arguments.length,a=M<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,r):i;if("object"===("undefined"==typeof Reflect?"undefined":n(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,r,i);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(M<3?o(a):M>3?o(t,r,a):o(t,r))||a);return M>3&&a&&Object.defineProperty(t,r,a),a},dt=function(e){function t(t,n){var r=e.call(this,n,t)||this;return r.mouseOver=!1,r.dragData={},r.dragStopCalled=!1,r.bind(),r}return yt(t,e),t.prototype.bind=function(){this.wireEvents()},t.prototype.wireEvents=function(){ae.add(this.element,Me.isSafari()?"touchend":Me.touchEndEvent,this.intDrop,this)},t.prototype.onPropertyChanged=function(e,t){},t.prototype.getModuleName=function(){return"droppable"},t.prototype.intOver=function(e,t){if(!this.mouseOver){var n=this.dragData[this.scope];this.trigger("over",{event:e,target:t,dragData:n}),this.mouseOver=!0}},t.prototype.intOut=function(e,t){this.mouseOver&&(this.trigger("out",{evt:e,target:t}),this.mouseOver=!1)},t.prototype.intDrop=function(e,t){if(this.dragStopCalled){this.dragStopCalled=!1;var n,r=!0,i=this.dragData[this.scope],o=!!i&&(i.helper&&le(i.helper));o&&(n=this.isDropArea(e,i.helper,t),this.accept&&(r=fe(i.helper,this.accept))),o&&this.drop&&n.canDrop&&r&&this.trigger("drop",{event:e,target:n.target,droppedElement:i.helper,dragData:i}),this.mouseOver=!1}},t.prototype.isDropArea=function(e,t,n){var r={canDrop:!0,target:n||e.target},i="touchend"===e.type;if(i||r.target===t){t.style.display="none";var o=i?e.changedTouches[0]:e,M=document.elementFromPoint(o.clientX,o.clientY);r.canDrop=!1,r.canDrop=p(M,this.element),r.canDrop&&(r.target=M),t.style.display=""}return r},t.prototype.destroy=function(){ae.remove(this.element,Me.isSafari()?"touchend":Me.touchEndEvent,this.intDrop),e.prototype.destroy.call(this)},It([Pe()],t.prototype,"accept",void 0),It([Pe("default")],t.prototype,"scope",void 0),It([Re()],t.prototype,"drop",void 0),It([Re()],t.prototype,"over",void 0),It([Re()],t.prototype,"out",void 0),t=It([Ve],t)}(Te),ht=(ct=function(e,t){return(ct=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}ct(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),ft=function(e,t,r,i){var o,M=arguments.length,a=M<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,r):i;if("object"===("undefined"==typeof Reflect?"undefined":n(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,r,i);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(M<3?o(a):M>3?o(t,r,a):o(t,r))||a);return M>3&&a&&Object.defineProperty(t,r,a),a},mt={backspace:8,tab:9,enter:13,shift:16,control:17,alt:18,pause:19,capslock:20,space:32,escape:27,pageup:33,pagedown:34,end:35,home:36,leftarrow:37,uparrow:38,rightarrow:39,downarrow:40,insert:45,delete:46,f1:112,f2:113,f3:114,f4:115,f5:116,f6:117,f7:118,f8:119,f9:120,f10:121,f11:122,f12:123,semicolon:186,plus:187,comma:188,minus:189,dot:190,forwardslash:191,graveaccent:192,openbracket:219,backslash:220,closebracket:221,singlequote:222},Tt=function(e){function t(t,r){var i=e.call(this,r,t)||this;return i.keyPressHandler=function(e){for(var t=e.altKey,r=e.ctrlKey,o=e.shiftKey,M=e.which,a=0,s=Object.keys(i.keyConfigs);a<s.length;a++)for(var u=s[a],c=0,N=i.keyConfigs[""+u].split(",");c<N.length;c++){var g=N[c],l=n.getKeyConfigData(g.trim());t===l.altKey&&r===l.ctrlKey&&o===l.shiftKey&&M===l.keyCode&&(e.action=u,i.keyAction&&i.keyAction(e))}},i.bind(),i}var n;return ht(t,e),n=t,t.prototype.destroy=function(){this.unwireEvents(),e.prototype.destroy.call(this)},t.prototype.onPropertyChanged=function(e,t){},t.prototype.bind=function(){this.wireEvents()},t.prototype.getModuleName=function(){return"keyboard"},t.prototype.wireEvents=function(){this.element.addEventListener(this.eventName,this.keyPressHandler)},t.prototype.unwireEvents=function(){this.element.removeEventListener(this.eventName,this.keyPressHandler)},t.getKeyConfigData=function(e){if(e in this.configCache)return this.configCache[""+e];var t=e.toLowerCase().split("+"),r={altKey:-1!==t.indexOf("alt"),ctrlKey:-1!==t.indexOf("ctrl"),shiftKey:-1!==t.indexOf("shift"),keyCode:null};return t[t.length-1].length>1&&Number(t[t.length-1])?r.keyCode=Number(t[t.length-1]):r.keyCode=n.getKeyCode(t[t.length-1]),n.configCache[""+e]=r,r},t.getKeyCode=function(e){return mt[""+e]||e.toUpperCase().charCodeAt(0)},t.configCache={},ft([Pe({})],t.prototype,"keyConfigs",void 0),ft([Pe("keyup")],t.prototype,"eventName",void 0),ft([Re()],t.prototype,"keyAction",void 0),t=n=ft([Ve],t)}(Te),At=function(){function t(t,n,r){this.controlName=t,this.localeStrings=n,this.setLocale(r||e.defaultCulture)}return t.prototype.setLocale=function(e){var n=this.intGetControlConstant(t.locale,e);this.currentLocale=n||this.localeStrings},t.load=function(e){this.locale=g(this.locale,e,{},!0)},t.prototype.getConstant=function(e){return l(this.currentLocale[""+e])?this.localeStrings[""+e]||"":this.currentLocale[""+e]},t.prototype.intGetControlConstant=function(e,t){return e[""+t]?e[""+t][this.controlName]:null},t.locale={},t}(),Ot=(Nt=function(e,t){return(Nt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}Nt(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),Lt=function(e,t,r,i){var o,M=arguments.length,a=M<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,r):i;if("object"===("undefined"==typeof Reflect?"undefined":n(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,r,i);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(M<3?o(a):M>3?o(t,r,a):o(t,r))||a);return M>3&&a&&Object.defineProperty(t,r,a),a},xt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Ot(t,e),Lt([Pe(50)],t.prototype,"swipeThresholdDistance",void 0),t}(et),bt=/(Up|Down)/,Et=function(e){function t(t,n){var r=e.call(this,n,t)||this;return r.touchAction=!0,r.tapCount=0,r.startEvent=function(e){if(!0===r.touchAction){var t=r.updateChangeTouches(e);void 0!==e.changedTouches&&(r.touchAction=!1),r.isTouchMoved=!1,r.movedDirection="",r.startPoint=r.lastMovedPoint={clientX:t.clientX,clientY:t.clientY},r.startEventData=t,r.hScrollLocked=r.vScrollLocked=!1,r.tStampStart=Date.now(),r.timeOutTapHold=setTimeout((function(){r.tapHoldEvent(e)}),r.tapHoldThreshold),ae.add(r.element,Me.touchMoveEvent,r.moveEvent,r),ae.add(r.element,Me.touchEndEvent,r.endEvent,r),ae.add(r.element,Me.touchCancelEvent,r.cancelEvent,r)}},r.moveEvent=function(e){var t=r.updateChangeTouches(e);r.movedPoint=t,r.isTouchMoved=!(t.clientX===r.startPoint.clientX&&t.clientY===r.startPoint.clientY);var n={};r.isTouchMoved&&(clearTimeout(r.timeOutTapHold),r.calcScrollPoints(e),n=g(n,{},{startEvents:r.startEventData,originalEvent:e,startX:r.startPoint.clientX,startY:r.startPoint.clientY,distanceX:r.distanceX,distanceY:r.distanceY,scrollDirection:r.scrollDirection,velocity:r.getVelocity(t)}),r.trigger("scroll",n),r.lastMovedPoint={clientX:t.clientX,clientY:t.clientY})},r.cancelEvent=function(e){clearTimeout(r.timeOutTapHold),clearTimeout(r.timeOutTap),r.tapCount=0,r.swipeFn(e),ae.remove(r.element,Me.touchCancelEvent,r.cancelEvent)},r.endEvent=function(e){r.swipeFn(e),r.isTouchMoved||"function"==typeof r.tap&&(r.trigger("tap",{originalEvent:e,tapCount:++r.tapCount}),r.timeOutTap=setTimeout((function(){r.tapCount=0}),r.tapThreshold)),r.modeclear()},r.swipeFn=function(e){clearTimeout(r.timeOutTapHold),clearTimeout(r.timeOutTap);var t=r.updateChangeTouches(e),n=t.clientX-r.startPoint.clientX,i=t.clientY-r.startPoint.clientY;n=Math.floor(n<0?-1*n:n),i=Math.floor(i<0?-1*i:n),r.isTouchMoved=n>1||i>1,/Firefox/.test(Me.userAgent)&&0===t.clientX&&0===t.clientY&&"mouseup"===e.type&&(r.isTouchMoved=!1),r.endPoint=t,r.calcPoints(e);var o={originalEvent:e,startEvents:r.startEventData,startX:r.startPoint.clientX,startY:r.startPoint.clientY,distanceX:r.distanceX,distanceY:r.distanceY,swipeDirection:r.movedDirection,velocity:r.getVelocity(t)};if(r.isTouchMoved){var M=void 0,a=r.swipeSettings.swipeThresholdDistance;M=g(M,r.defaultArgs,o);var s=!1,u=r.element,c=r.isScrollable(u),N=bt.test(r.movedDirection);(a<r.distanceX&&!N||a<r.distanceY&&N)&&(s=!c||r.checkSwipe(u,N)),s&&r.trigger("swipe",M)}r.modeclear()},r.modeclear=function(){r.modeClear=setTimeout((function(){r.touchAction=!0}),"function"!=typeof r.tap?0:20),r.lastTapTime=(new Date).getTime(),ae.remove(r.element,Me.touchMoveEvent,r.moveEvent),ae.remove(r.element,Me.touchEndEvent,r.endEvent),ae.remove(r.element,Me.touchCancelEvent,r.cancelEvent)},r.bind(),r}return Ot(t,e),t.prototype.onPropertyChanged=function(e,t){},t.prototype.bind=function(){this.wireEvents(),Me.isIE&&this.element.classList.add("e-block-touch")},t.prototype.destroy=function(){this.unwireEvents(),e.prototype.destroy.call(this)},t.prototype.wireEvents=function(){ae.add(this.element,Me.touchStartEvent,this.startEvent,this)},t.prototype.unwireEvents=function(){ae.remove(this.element,Me.touchStartEvent,this.startEvent)},t.prototype.getModuleName=function(){return"touch"},t.prototype.isScrollable=function(e){var t=getComputedStyle(e),n=t.overflow+t.overflowX+t.overflowY;return!!/(auto|scroll)/.test(n)},t.prototype.tapHoldEvent=function(e){var t;this.tapCount=0,this.touchAction=!0,ae.remove(this.element,Me.touchMoveEvent,this.moveEvent),ae.remove(this.element,Me.touchEndEvent,this.endEvent),t={originalEvent:e},this.trigger("tapHold",t),ae.remove(this.element,Me.touchCancelEvent,this.cancelEvent)},t.prototype.calcPoints=function(e){var t=this.updateChangeTouches(e);this.defaultArgs={originalEvent:e},this.distanceX=Math.abs(Math.abs(t.clientX)-Math.abs(this.startPoint.clientX)),this.distanceY=Math.abs(Math.abs(t.clientY)-Math.abs(this.startPoint.clientY)),this.distanceX>this.distanceY?this.movedDirection=t.clientX>this.startPoint.clientX?"Right":"Left":this.movedDirection=t.clientY<this.startPoint.clientY?"Up":"Down"},t.prototype.calcScrollPoints=function(e){var t=this.updateChangeTouches(e);this.defaultArgs={originalEvent:e},this.distanceX=Math.abs(Math.abs(t.clientX)-Math.abs(this.lastMovedPoint.clientX)),this.distanceY=Math.abs(Math.abs(t.clientY)-Math.abs(this.lastMovedPoint.clientY)),(this.distanceX>this.distanceY||!0===this.hScrollLocked)&&!1===this.vScrollLocked?(this.scrollDirection=t.clientX>this.lastMovedPoint.clientX?"Right":"Left",this.hScrollLocked=!0):(this.scrollDirection=t.clientY<this.lastMovedPoint.clientY?"Up":"Down",this.vScrollLocked=!0)},t.prototype.getVelocity=function(e){var t=e.clientX,n=e.clientY,r=Date.now(),i=t-this.startPoint.clientX,o=n-this.startPoint.clientX,M=r-this.tStampStart;return Math.sqrt(i*i+o*o)/M},t.prototype.checkSwipe=function(e,t){var n=["scroll","offset"],r=t?["Height","Top"]:["Width","Left"];return e[n[0]+r[0]]<=e[n[1]+r[0]]||(0===e[n[0]+r[1]]||e[n[1]+r[0]]+e[n[0]+r[1]]>=e[n[0]+r[0]])},t.prototype.updateChangeTouches=function(e){return e.changedTouches&&0!==e.changedTouches.length?e.changedTouches[0]:e},Lt([Re()],t.prototype,"tap",void 0),Lt([Re()],t.prototype,"tapHold",void 0),Lt([Re()],t.prototype,"swipe",void 0),Lt([Re()],t.prototype,"scroll",void 0),Lt([Pe(350)],t.prototype,"tapThreshold",void 0),Lt([Pe(750)],t.prototype,"tapHoldThreshold",void 0),Lt([Fe({},xt)],t.prototype,"swipeSettings",void 0),t=Lt([Ve],t)}(Te),St=new RegExp("\\n|\\r|\\s\\s+","g"),vt=new RegExp(/'|"/g),wt=new RegExp("if ?\\("),kt=new RegExp("else if ?\\("),Qt=new RegExp("else"),Ut=new RegExp("for ?\\("),Yt=new RegExp("(/if|/for)"),Ct=new RegExp("\\((.*)\\)",""),Pt=new RegExp("^[0-9]+$","g"),Ft=new RegExp("[\\w\"'.\\s+]+","g"),Rt=new RegExp('"(.*?)"',"g"),Vt=new RegExp("[\\w\"'@#$.\\s-+]+","g"),Bt=new RegExp("\\${([^}]*)}","g"),Wt=/^\..*/gm,Gt=/\\/gi,Ht=/\\\\/gi,Zt=new RegExp("[\\w\"'@#$.\\s+]+","g"),Jt=/\window\./gm;function Xt(e,t,n){if("function"==typeof e)return e;var r=function(e,t,n,r){var i=0,o=[],M=e.match(/class="([^"]+|)\s{2}/g),a="";M&&M.forEach((function(t){a=t.replace(/\s\s+/g," "),e=e.replace(t,a)}));if(Bt.test(e)){for(var s=!1,u="",c=0;c<e.length;c++)"$"===e[c+""]&&"{"===e[c+1]?s=!0:"}"===e[c+""]&&(s=!1),u+='"'!==e[c+""]||s?e[c+""]:'\\"';e=u}else e=e.replace(/\\?"/g,'\\"');return e.replace(St,"").replace(Rt,"'$1'").replace(Bt,(function(e,M,a,s){var u=M.match(Ct);if(u){var c=u[1];if(kt.test(M))M='";} '+M.replace(u[1],c.replace(Ft,(function(e){return _t(e=e.trim(),!vt.test(e)&&-1===o.indexOf(e),t,o,r)})))+'{ \n str = str + "';else if(wt.test(M))M='"; '+M.replace(u[1],c.replace(Vt,(function(e){return $t(e,t,o,r)})))+'{ \n str = str + "';else if(Ut.test(M)){var N=u[1].split(" of ");M='"; '+M.replace(u[1],(function(e){return o.push(N[0]),o.push(N[0]+"Index"),"var i"+(i+=1)+"=0; i"+i+" < "+_t(N[1],!0,t,o,r)+".length; i"+i+"++"}))+"{ \n "+N[0]+"= "+_t(N[1],!0,t,o,r)+"[i"+i+"]; \n var "+N[0]+"Index=i"+i+'; \n str = str + "'}else{var g=M.split("("),l=n&&n.hasOwnProperty(g[0])?"this.":"global";l=/\./.test(g[0])?"":l;var j=u[1].split(",");0===u[1].length||/data/.test(j[0])||/window./.test(j[0])||(u[1]="global"===l?t+"."+u[1]:u[1]);if(Jt.test(M)&&/\]\./gm.test(M)||/@|\$|#/gm.test(M)){/@|\$|#|\]\./gm.test(M)&&(M='"+ '+("global"===l?"":l)+M.replace(u[1],c.replace(Zt,(function(e){return $t(e,t,o,r)})))+'+ "')}else M='" + '+("global"===l?"":l)+M.replace(c,_t(u[1].replace(/,( |)data.|,/gi,","+t+".").replace(/,( |)data.window/gi,",window"),"global"!==l,t,o,r))+'+"'}}else Qt.test(M)?M='"; '+M.replace(Qt,'} else { \n str = str + "'):M.match(Yt)?M=M.replace(Yt,'"; \n } \n str = str + "'):/@|#|\$/gm.test(M)?(M.match(Gt)&&(M=qt(M)),M='"+'+Kt(M,-1===o.indexOf(M),t,o)+'"]+"'):M=M.match(Gt)?'"+'+Kt(M=qt(M),-1===o.indexOf(M),t,o)+'"]+"':""!==M?'"+'+_t(M.replace(/,/gi,"+"+t+"."),-1===o.indexOf(M),t,o,r)+'+"':"${}";return M}))}(e,"data",t,n);return new Function("data",'var str="'+r+"\";var valueRegEx = (/value=\\'([A-Za-z0-9 _]*)((.)([\\w)(!-;?-■\\s]+)['])/g);\n        var hrefRegex = (/(?:href)([\\s='\"./]+)([\\w-./?=&\\\\#\"]+)((.)([\\w)(!-;/?-■\\s]+)['])/g);\n        if(str.match(valueRegEx)){\n            var check = str.match(valueRegEx);\n            var str1 = str;\n            for (var i=0; i < check.length; i++) {\n                var check1 = str.match(valueRegEx)[i].split('value=')[1];\n                var change = check1.match(/^'/) !== null ? check1.replace(/^'/, '\"') : check1;\n                change =change.match(/.$/)[0] === '\\'' ? change.replace(/.$/,'\"') : change;\n                str1 = str1.replace(check1, change);\n            }\n            str = str.replace(str, str1);\n        }\n        else if (str.match(/(?:href='')/) === null) {\n            if(str.match(hrefRegex)) {\n                var check = str.match(hrefRegex);\n                var str1 = str;\n                for (var i=0; i < check.length; i++) {\n                    var check1 = str.match(hrefRegex)[i].split('href=')[1];\n                    if (check1) {\n                        var change = check1.match(/^'/) !== null ? check1.replace(/^'/, '\"') : check1;\n                        change =change.match(/.$/)[0] === '\\'' ? change.replace(/.$/,'\"') : change;\n                        str1 = str1.replace(check1, change);\n                    }\n                }\n                str = str.replace(str, str1);\n            }\n        }\n         return str;").bind(t)}function _t(e,t,n,r,i){return!t||Pt.test(e)||-1!==r.indexOf(e.split(".")[0])||i||"true"===e||"false"===e?e:n+"."+e}function Kt(e,t,n,r){return t&&!Pt.test(e)&&-1===r.indexOf(e.split(".")[0])?n+'["'+e:e}function qt(e){return e=e.match(Ht)?e:e.replace(Gt,"\\\\")}function $t(e,t,n,r){e=e.trim();if(/\window\./gm.test(e))return e;var i=/'|"/gm;return/@|\$|#/gm.test(e)&&(e=Kt(e,-1===n.indexOf(e),t,n)+'"]'),Wt.test(e)?function(e,t,n,r){return!t||Pt.test(e)||-1!==r.indexOf(e.split(".")[0])||/^\..*/gm.test(e)?e:n+"."+e}(e,!i.test(e)&&-1===n.indexOf(e),t,n):_t(e,!i.test(e)&&-1===n.indexOf(e),t,n,r)}var en=/^[\n\r.]+<tr|^<tr/,tn=/^[\n\r.]+<svg|^<path|^<g/,nn={};function rn(){return"-"+Math.random().toString(36).substr(2,5)}var on={compile:(new(function(){function e(){}return e.prototype.compile=function(e,t,n){return void 0===t&&(t={}),Xt(e,t)},e}())).compile},Mn=["script","style","iframe[src]",'link[href*="javascript:"]','object[type="text/x-scriptlet"]','object[data^="data:text/html;base64"]','img[src^="data:text/html;base64"]','[src^="javascript:"]','[dynsrc^="javascript:"]','[lowsrc^="javascript:"]','[type^="application/x-shockwave-flash"]'],an=[{attribute:"href",selector:'[href*="javascript:"]'},{attribute:"background",selector:'[background^="javascript:"]'},{attribute:"style",selector:'[style*="javascript:"]'},{attribute:"style",selector:'[style*="expression("]'},{attribute:"href",selector:'a[href^="data:text/html;base64"]'}],sn=["onchange","onclick","onmouseover","onmouseout","onkeydown","onload","onerror","onblur","onfocus","onbeforeload","onbeforeunload","onkeyup","onsubmit","onafterprint","onbeforeonload","onbeforeprint","oncanplay","oncanplaythrough","oncontextmenu","ondblclick","ondrag","ondragend","ondragenter","ondragleave","ondragover","ondragstart","ondrop","ondurationchange","onemptied","onended","onformchange","onforminput","onhaschange","oninput","oninvalid","onkeypress","onloadeddata","onloadedmetadata","onloadstart","onmessage","onmousedown","onmousemove","onmouseup","onmousewheel","onoffline","onoine","ononline","onpagehide","onpageshow","onpause","onplay","onplaying","onpopstate","onprogress","onratechange","onreadystatechange","onredo","onresize","onscroll","onseeked","onseeking","onselect","onstalled","onstorage","onsuspend","ontimeupdate","onundo","onunload","onvolumechange","onwaiting","onmouseenter","onmouseleave","onstart","onpropertychange","oncopy","ontoggle","onpointerout","onpointermove","onpointerleave","onpointerenter","onpointerrawupdate","onpointerover","onbeforecopy","onbeforecut","onbeforeinput"],un=function(){function e(){}return e.beforeSanitize=function(){return{selectors:{tags:Mn,attributes:an}}},e.sanitize=function(e){if(l(e))return e;var t=this.beforeSanitize();return this.serializeValue(t,e)},e.serializeValue=function(e,t){this.removeAttrs=e.selectors.attributes,this.removeTags=e.selectors.tags,this.wrapElement=document.createElement("div"),this.wrapElement.innerHTML=t,this.removeXssTags(),this.removeJsEvents(),this.removeXssAttrs();var n=this.wrapElement.innerHTML;return this.removeElement(),this.wrapElement=null,n.replace(/&amp;/g,"&")},e.removeElement=function(){for(var e=this.wrapElement.children,t=0;t<e.length;t++)for(var n=e[parseInt(t.toString(),10)].attributes,r=0;r<n.length;r++)this.wrapElement.children[parseInt(t.toString(),10)].removeAttribute(n[parseInt(r.toString(),10)].localName)},e.removeXssTags=function(){var e=this.wrapElement.querySelectorAll(this.removeTags.join(","));e.length>0&&e.forEach((function(e){De(e)}))},e.removeJsEvents=function(){var e=this.wrapElement.querySelectorAll("["+sn.join("],[")+"]");e.length>0&&e.forEach((function(e){sn.forEach((function(t){e.hasAttribute(t)&&e.removeAttribute(t)}))}))},e.removeXssAttrs=function(){var e=this;this.removeAttrs.forEach((function(t,n){var r=e.wrapElement.querySelectorAll(t.selector);r.length>0&&r.forEach((function(e){e.removeAttribute(t.attribute)}))}))},e}();return e.blazorCultureFormats=Z,e.Ajax=X,e.Fetch=_,e.Animation=Je,e.rippleEffect=function(t,n,r){var i=function(e){return{selector:e&&e.selector?e.selector:null,ignore:e&&e.ignore?e.ignore:null,rippleFlag:e&&e.rippleFlag,isCenterRipple:e&&e.isCenterRipple,duration:e&&e.duration?e.duration:350}}(n);return!1===i.rippleFlag||void 0===i.rippleFlag&&!e.isRippleEnabled?function(){}:(t.setAttribute("data-ripple","true"),ae.add(t,"mousedown",Xe,{parent:t,rippleOptions:i}),ae.add(t,"mouseup",_e,{parent:t,rippleOptions:i,done:r}),ae.add(t,"mouseleave",Ke,{parent:t,rippleOptions:i}),Me.isPointer&&ae.add(t,"transitionend",Ke,{parent:t,rippleOptions:i}),function(){t.removeAttribute("data-ripple"),ae.remove(t,"mousedown",Xe),ae.remove(t,"mouseup",_e),ae.remove(t,"mouseleave",Ke),ae.remove(t,"transitionend",Ke)})},e.enableRipple=function(t){return e.isRippleEnabled=t,e.isRippleEnabled},e.setGlobalAnimation=function(t){e.animationMode=t},e.Base=Te,e.getComponent=function(e,t){var n,r,i="string"==typeof e?document.getElementById(e):e;for(r=0;r<i.ej2_instances.length;r++){if(n=i.ej2_instances[parseInt(r.toString(),10)],"string"==typeof t){if(t===n.getModuleName())return n}else if(n instanceof t)return n}},e.Browser=Me,e.Component=at,e.ChildProperty=et,e.Position=pt,e.Draggable=zt,e.Droppable=dt,e.EventHandler=ae,e.onIntlChange=B,e.cldrData=W,e.Internationalization=G,e.setCulture=function(t){e.defaultCulture=t,B.notify("notifyExternalChange",{locale:e.defaultCulture})},e.loadCldr=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var i=r[n];g(W,i,{},!0)}},e.enableRtl=function(t){void 0===t&&(t=!0),e.rightToLeft=t,B.notify("notifyExternalChange",{enableRtl:e.rightToLeft})},e.KeyboardEvents=Tt,e.L10n=At,e.ModuleLoader=$e,e.Property=Pe,e.Complex=Fe,e.ComplexFactory=function(e){return function(t,n){var r={set:ve({},n,e),get:Se({},n,e),enumerable:!0,configurable:!0};Object.defineProperty(t,n,r),Be(t,n,"complexProp",{},e)}},e.Collection=function(e,t){return function(n,r){var i={set:ke(e,r,t),get:we(e,r,t),enumerable:!0,configurable:!0};Object.defineProperty(n,r,i),Be(n,r,"colProp",e,t)}},e.CollectionFactory=function(e){return function(t,n){var r={set:Qe([],n,e),get:Ue([],n,e),enumerable:!0,configurable:!0};Object.defineProperty(t,n,r),Be(t,n,"colProp",{},e)}},e.Event=Re,e.NotifyPropertyChanges=Ve,e.SwipeSettings=xt,e.Touch=Et,e.blazorTemplates=nn,e.getRandomId=rn,e.compile=function(e,t,n){var r=on.compile(e,t,n);return function(e,t,n,i,o,M,a,s){var u=r(e,t,n,a,s);if(y()&&!o){var c=i+rn();if(nn[""+i]||(nn[""+i]=[]),l(M))e.BlazorTemplateId=c,nn[""+i].push(e);else for(var N=0,g=Object.keys(nn[""+i][parseInt(M.toString(),10)]);N<g.length;N++){var j=g[N];"BlazorTemplateId"!==j&&e[""+j]&&(nn[""+i][parseInt(M.toString(),10)][""+j]=e[""+j]),"BlazorTemplateId"===j&&(c=nn[""+i][parseInt(M.toString(),10)][""+j])}return"rowTemplate"===n?[ue("tr",{id:c,className:"e-blazor-template"})]:[ue("div",{id:c,className:"e-blazor-template"})]}return"string"==typeof u?tn.test(u)?ue("svg",{innerHTML:u}).childNodes:ue(en.test(u)?"table":"div",{innerHTML:u}).childNodes:u}},e.updateBlazorTemplate=function(e,t,n,r,i){if(y()){window.sfBlazor.updateTemplate(t,nn[""+e],e,n,i),!1!==r&&(nn[""+e]=[])}},e.resetBlazorTemplate=function(e,t,n){var r=document.getElementById(e);if(r)for(var i=r.getElementsByClassName("blazor-inner-template"),o=0;o<i.length;o++){var M=" ";M=l(n)?i[parseInt(o.toString(),10)].getAttribute("data-templateId"):i[parseInt(n.toString(),10)].getAttribute("data-templateId");var a=document.getElementById(M);if(a)for(var s=a.childNodes.length,u=0;u<s;u++)l(n)?i[parseInt(o.toString(),10)].appendChild(a.childNodes[0]):(i[parseInt(n.toString(),10)].appendChild(a.childNodes[0]),o=i.length)}},e.initializeCSPTemplate=function(e,t){var n;return e.prototype.CSPTemplate=!0,l(t)?n=e:(n=e.bind(t)).prototype=Object.create(e.prototype),n},e.createInstance=i,e.setImmediate=o,e.getValue=M,e.setValue=a,e.deleteObject=s,e.containerObject=u,e.isObject=c,e.merge=N,e.extend=g,e.isNullOrUndefined=l,e.isUndefined=j,e.getUniqueID=function(e){return e+"_"+t++},e.debounce=D,e.compareElementParent=p,e.throwError=z,e.print=function(e,t){var n=document.createElement("div"),r=[].slice.call(document.getElementsByTagName("head")[0].querySelectorAll("base, link, style")),i=[].slice.call(document.getElementsByTagName("body")[0].querySelectorAll("link, style"));if(i.length)for(var o=0,M=i.length;o<M;o++)r.push(i[parseInt(o.toString(),10)]);var a="";l(t)&&(t=window.open("","print","height=452,width=1024,tabbar=no")),n.appendChild(e.cloneNode(!0));var s=0;for(M=r.length;s<M;s++)a+=r[parseInt(s.toString(),10)].outerHTML;t.document.write("<!DOCTYPE html> <html><head>"+a+"</head><body>"+n.innerHTML+"<script> (function() { window.ready = true; })(); <\/script></body></html>"),t.document.close(),t.focus();var u=setInterval((function(){t.ready&&(t.print(),t.close(),clearInterval(u))}),500);return t},e.formatUnit=function(e){var t=e+"";return t.match(/auto|cm|mm|in|px|pt|pc|%|em|ex|ch|rem|vw|vh|vmin|vmax/)?t:t+"px"},e.enableBlazorMode=function(){r=!0},e.isBlazor=y,e.getElement=function(e){return e instanceof Node||!y()||l(e.xPath)?e:document.evaluate(e.xPath,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue},e.getInstance=function(e,t){var n="string"==typeof e?document.querySelector(e):e;if(n.ej2_instances)for(var r=0,i=n.ej2_instances;r<i.length;r++){var o=i[r];if(o instanceof t)return o}return null},e.uniqueID=I,e.createElement=ue,e.addClass=ce,e.removeClass=Ne,e.isVisible=le,e.prepend=function(e,t,n){for(var r=document.createDocumentFragment(),i=0,o=e;i<o.length;i++){var M=o[i];r.appendChild(M)}return t.insertBefore(r,t.firstElementChild),n&&je(t),e},e.append=function(e,t,n){var r=document.createDocumentFragment();if(e instanceof NodeList)for(;e.length>0;)r.appendChild(e[0]);else for(var i=0,o=e;i<o.length;i++){var M=o[i];r.appendChild(M)}return t.appendChild(r),n&&je(t),e},e.detach=De,e.remove=function(e){var t=e.parentNode;ae.clearEvents(e),t.removeChild(e)},e.attributes=pe,e.select=ze,e.selectAll=ye,e.closest=de,e.getAttributeOrDefault=function(e,t,n){var r,i=c(e);return l(r=i?M("attributes."+t,e):e.getAttribute(t))&&n&&(i?e.attributes[""+t]=n:e.setAttribute(t,n.toString()),r=n),r},e.setStyleAttribute=he,e.classList=function(e,t,n){ce([e],t),Ne([e],n)},e.matches=fe,e.Observer=V,e.SanitizeHtmlHelper=un,e.componentList=["grid","pivotview","treegrid","spreadsheet","rangeNavigator","DocumentEditor","listbox","inplaceeditor","PdfViewer","richtexteditor","DashboardLayout","chart","stockChart","circulargauge","diagram","heatmap","lineargauge","maps","slider","smithchart","barcode","sparkline","treemap","bulletChart","kanban","daterangepicker","schedule","gantt","signature","query-builder","drop-down-tree","carousel","filemanager","uploader","accordion","tab","treeview"],e}({})},"./modules/sf-import-script.js":function(e,t,n){"use strict";window.sessionStorage.getItem("IgnoreJsIsolation")&&(window.sessionStorage.removeItem("IgnoreJsIsolation"),DotNet.invokeMethodAsync("Syncfusion.Blazor","SetIsDevice",sfBlazor.isDevice()))},"./syncfusion-blazor.js":function(e,t){var n={instances:[],getElementByXpath:function(e){return document.evaluate(e,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue},getElement:function(e,t,n){var r=null!=e&&null!=window[e]?window[e][t]:null;return null!=r?r:window.sfBlazor.getElementByXpath(n)},getAttribute:function(e,t,n,r){var i=window.sfBlazor.getElement(e,t,n);if(null!=i)return i.getAttribute(r)},setAttribute:function(e,t,n,r,i){window.sfBlazor.getElement(e,t,n).setAttribute(r,i)},addClass:function(e,t,n,r){sf.base.addClass([window.sfBlazor.getElement(e,t,n)],r)},removeClass:function(e,t,n,r){sf.base.removeClass([window.sfBlazor.getElement(e,t,n)],r)},getClassList:function(e,t,n){return Array.prototype.slice.call(window.sfBlazor.getElement(e,t,n).classList)},enableRipple:function(e){sf.base.enableRipple(e)},isDevice:function(e){return e&&this.enableRtl(e),sf.base.Browser.isDevice},animate:function(e,t){new sf.base.Animation(t).animate(e)},setGlobalAnimationValue:function(e){sf.base.setGlobalAnimation(e)},callRipple:function(e,t){sf.base.rippleEffect(e,t)},createXPathFromElement:function(e){for(var t=document.getElementsByTagName("*"),n=[];e&&1===e.nodeType;e=e.parentNode)if(e.hasAttribute("id")){for(var r=0,i=0;i<t.length&&(t[i].hasAttribute("id")&&t[i].id===e.id&&r++,!(r>1));i++);if(1===r)return n.unshift('id("'+e.getAttribute("id")+'")'),n.join("/");n.unshift(e.localName.toLowerCase()+'[@id="'+e.getAttribute("id")+'"]')}else{for(var o=1,M=e.previousSibling;M;M=M.previousSibling)M.localName===e.localName&&o++;n.unshift(e.localName.toLowerCase()+"["+o+"]")}return n.length?"/"+n.join("/"):null},getDomObject:function(e,t,n){var r=e+sf.base.getUniqueID(e),i={id:t.id,class:t.className,xPath:window.sfBlazor.createXPathFromElement(t),domUUID:r},o=n&&n.elementID;return o&&(window[o]=sf.base.isNullOrUndefined(window[o])?{}:window[o],window[o][r]=t,i.elementID=o),i},focusButton:function(e){e.focus()},setCompInstance:function(e){window.sfBlazor.instances[e.dataId]=e},getCompInstance:function(e){return window.sfBlazor.instances[e]},disposeWindowsInstance:function(e){e&&delete window.sfBlazor.instances[e]},setProgress:function(e,t,n,r,i,o){return n=n.querySelector(".e-spinner"),window.requestAnimationFrame((function(){i&&(e.style[o?"height":"width"]=r+"%"),t.parentElement.setAttribute("aria-valuenow",r.toString()),100===r&&(t.classList.remove("e-cont-animate","e-animate-end"),n.style.width="auto",n.style.height="auto")}))},setAnimation:function(e,t,n,r,i,o){t=t.querySelector(".e-spinner"),new sf.base.Animation({}).animate(e,{duration:r,name:"Progress"+n,timingFunction:i,begin:function(){o&&(t.style.width=Math.max(t.offsetWidth,e.offsetWidth)+"px",t.style.height=Math.max(t.offsetHeight,e.offsetHeight)+"px",e.classList.add("e-cont-animate"))},end:function(){e.classList.add("e-animate-end")}})},cancelAnimation:function(e){window.cancelAnimationFrame(e)},getSpinnerTheme:function(e){var t=e?window.getComputedStyle(e,":after").getPropertyValue("content"):"Material";return t&&t.replace(/['"]+/g,"")},chipKeydownHandler:function(e){e&&e.addEventListener("keydown",(function(e){e.target&&e.target.classList.contains("e-chip")&&" "==e.key&&e.preventDefault()}))},MediaQuery:{initialize:function(e){return e.dataId?(sf.base.extend(e,this,e),window.sfBlazor.setCompInstance(e),e.activeBreakpoint="",e.isMediaChanged=!1,e.initializeMediaQueries(e),e.updateActiveBreakpoint(e,!0),e.activeBreakpoint):null},initializeMediaQueries:function(e){for(var t=0;t<e.mediaBreakpoints.length;t++){var n=window.matchMedia(e.mediaBreakpoints[t].mediaQuery);e.mediaBreakpoints[t].mq=n,sf.base.EventHandler.add(n,"change",e.mediaQueryChangeHandler,e)}},updateActiveBreakpoint:function(e,t){for(var n=!1,r=0;r<e.mediaBreakpoints.length;r++){if(e.mediaBreakpoints[r].mq.matches){e.isMediaChanged&&!n||t||(e.isMediaChanged=e.activeBreakpoint!=e.mediaBreakpoints[r].breakpoint),e.activeBreakpoint=e.mediaBreakpoints[r].breakpoint;break}e.activeBreakpoint==e.mediaBreakpoints[r].breakpoint&&(n=e.isMediaChanged=!0,e.activeBreakpoint="")}},mediaQueryChangeHandler:function(){this.updateActiveBreakpoint(this),this.isMediaChanged&&(this.dotNetRef.invokeMethodAsync("UpdateActiveBreakpoint",this.activeBreakpoint),this.isMediaChanged=!1)},destroyComponent:function(){for(var e=0;e<this.mediaBreakpoints.length;e++)sf.base.EventHandler.remove(this.mediaBreakpoints[e].mq,"change",this.mediaQueryChangeHandler)},destroy:function(e){e&&window.sfBlazor.getCompInstance(e).destroyComponent()}},validateBlazorLicense:function(e,t,n){if(n){var r=sf.base.createElement("div",{innerHTML:'<div style="position: fixed;\n                width: 100%;\n                height: 100%;\n                top: 0;\n                left: 0;\n                right: 0;\n                bottom: 0;\n                background-color: rgba(0, 0, 0, 0.5);\n                z-index: 99999;">\n                    <div style="background: #FFFFFF;\n                    height: 490px;\n                    width: 840px;\n                    font-family: Helvetica Neue, Helvetica, Arial;\n                    color: #000000;\n                    box-shadow: 0px 4.8px 14.4px rgb(0 0 0 / 18%), 0px 25.6px 57.6px rgb(0 0 0 / 22%);\n                    display: block;\n                    margin: 6% auto;\n                    border-radius: 20px;">\n                        <div style="\n                        position: absolute;\n                        width: 838px;\n                        height: 80px;\n                        background-color: #F9F9F9;\n                        border: 1px solid #EEEEEE;\n                        border-top-left-radius: 20px;\n                        border-top-right-radius: 20px;">\n                <img src="data:image/svg+xml;base64,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" style="\n                text-align: left;\n                width: 146px;\n                position: absolute;\n                top: 24px;\n                left: 40px;"></div>\n                <div style="position: relative;\n                top: 104px;\n                left: 32px;\n                font-size: 20px;\n                text-align: left;\n                font-weight: 700;\n                letter-spacing: 0.02em;\n                font-style: normal;\n                line-height: 125%;">Claim your FREE account and get a key in less than a minute</div>\n                        <ul style="font-size: 15px;\n                        margin-top: 15px;\n                        font-weight: 400;\n                        color: #333333;\n                        letter-spacing: 0.01em;\n                        position: relative;\n                        left: 40px;\n                        top: 103px;\n                        line-height: 180%;">\n                            <li><span>Access to a 30-day free trial of any of our products.</span></li>\n                            <li><span>Access to 24x5 support by developers via the <a\n                                        href="https://support.syncfusion.com/create"\n                                        style="text-decoration: none;\n                            color: #0D6EFD;\n                            font-weight: 500;">support tickets</a>, <a\n                                        href="https://www.syncfusion.com/forums"\n                                        style="text-decoration: none;\n                            color: #0D6EFD;\n                            font-weight: 500;">forum</a>, <a\n                                        href="https://www.syncfusion.com/feedback"\n                                        style="text-decoration: none;\n                            color: #0D6EFD;\n                            font-weight: 500;">feature & feedback page</a> and chat.</span></li>\n                            <li><span>200+ <a\n                                        href="https://www.syncfusion.com/succinctly-free-ebooks"\n                                        style="text-decoration: none;\n                            color: #0D6EFD;\n                            font-weight: 500;">ebooks</a> on the latest technologies, industry trends, and research topics.</span>\n                            </li>\n                            <li><span>Largest collection of over 7,000 flat and wireframe icons for free with Syncfusion <a\n                                        href="https://www.syncfusion.com/downloads/metrostudio"\n                                        style="text-decoration: none;\n                            color: #0D6EFD;\n                            font-weight: 500;">Metro Studio</a>.</span></li>\n                            <li><span>Free and unlimited access to Syncfusion technical <a\n                                        href="https://www.syncfusion.com/blogs/"\n                                        style="text-decoration: none;\n                            color: #0D6EFD;\n                            font-weight: 500;">blogs</a> and <a\n                                        href="https://www.syncfusion.com/resources/techportal/whitepapers"\n                                        style="text-decoration: none;\n                            color: #0D6EFD;\n                            font-weight: 500;">white papers.</a></span></li>\n                        </ul>\n                        <div style="font-size: 18px;\n                        font-weight: 700;\n                        position: relative;\n                        line-height: 125%;\n                        letter-spacing: 0.02em;\n                        top: 113px;\n                        left: 32px;">Syncfusion is trusted by 29,000+ businesses worldwide</div>\n                <img src=\'data:image/svg+xml;base64,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\'style="width: 742.5px;\n                    position: relative;\n                    top: 123px;\n                    left: 32px;"/>\n                    <a href="'.concat(t,'" style="float: left;\n                    border-radius: 56px;\n                    background: #0D6EFD;\n                    padding-top: 12px;\n                    width: 280px;\n                    height: 45px;\n                    text-align: center;\n                    position: relative;\n                    top: 141px;\n                    left: 270px;\n                    font-size: 17px;\n                    color: white;\n                    text-decoration: none;\n                    line-height: 125%;\n                    letter-spacing: 0.02em;">Claim your free account</a>\n                        <div style="\n                        font-size: 14px;\n                        position: relative;\n                        top: 197px;\n                        left: 15px;\n                        letter-spacing: 0.02em;\n                        font-weight: 500;\n                        line-height: 125%;">have a Syncfusion account? <a\n                href="https://www.syncfusion.com/account/login?ReturnUrl=/account/login"\n                style="text-decoration: none;\n                color: #0D6EFD;\n                font-weight: 500;">Sign In</a></div>\n                    </div>\n                </div>')});document.body.appendChild(r)}else{var i=sf.base.createElement("div",{innerHTML:"<img src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgY2xpcC1wYXRoPSJ1cmwoI2NsaXAwXzE5OV80KSI+CjxwYXRoIGQ9Ik0xMiAyMUMxNi45NzA2IDIxIDIxIDE2Ljk3MDYgMjEgMTJDMjEgNy4wMjk0NCAxNi45NzA2IDMgMTIgM0M3LjAyOTQ0IDMgMyA3LjAyOTQ0IDMgMTJDMyAxNi45NzA2IDcuMDI5NDQgMjEgMTIgMjFaIiBzdHJva2U9IiM3MzczNzMiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik0xMS4yNSAxMS4yNUgxMlYxNi41SDEyLjc1IiBmaWxsPSIjNjE2MDYzIi8+CjxwYXRoIGQ9Ik0xMS4yNSAxMS4yNUgxMlYxNi41SDEyLjc1IiBzdHJva2U9IiM3MzczNzMiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik0xMS44MTI1IDlDMTIuNDMzOCA5IDEyLjkzNzUgOC40OTYzMiAxMi45Mzc1IDcuODc1QzEyLjkzNzUgNy4yNTM2OCAxMi40MzM4IDYuNzUgMTEuODEyNSA2Ljc1QzExLjE5MTIgNi43NSAxMC42ODc1IDcuMjUzNjggMTAuNjg3NSA3Ljg3NUMxMC42ODc1IDguNDk2MzIgMTEuMTkxMiA5IDExLjgxMjUgOVoiIGZpbGw9IiM3MzczNzMiLz4KPC9nPgo8ZGVmcz4KPGNsaXBQYXRoIGlkPSJjbGlwMF8xOTlfNCI+CjxyZWN0IHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgZmlsbD0id2hpdGUiLz4KPC9jbGlwUGF0aD4KPC9kZWZzPgo8L3N2Zz4K' style=\"\n            position: absolute;\n            left: 16px;\n            height: 24px;\"/><span>".concat(e,' <a style="text-decoration: none;color: #0D6EFD;font-weight: 500;" href="').concat(t,'">Claim your free account</a>.</span>')});i.setAttribute("style","position: fixed;\n            top: 10px;\n            left: 10px;\n            right: 10px;\n            font-size: 14px;\n            background: #EEF2FF;\n            color: #222222;\n            z-index: *********;\n            text-align: left;\n            border: 1px solid #EEEEEE;\n            padding: 10px 11px 10px 50px;\n            border-radius: 8px;\n            font-family: Helvetica Neue, Helvetica, Arial;"),document.body.appendChild(i)}}};sf.base.enableBlazorMode(),window.sfBlazor=window.sfBlazor||{},Object.assign(window.sfBlazor,n)}});