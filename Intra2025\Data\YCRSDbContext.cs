﻿using Intra2025.Models;
using Intra2025.Models.YouthCareReportService;
using Microsoft.EntityFrameworkCore;

namespace Intra2025.Data
{
    public class YCRSDbContext : DbContext
    {
        public DbSet<YCRS_ChildRecord> YCRS_ChildRecord { get; set; }
        public DbSet<YCRS_CareRecord> YCRS_CareRecord { get; set; }
        public DbSet<YCRS_AccessLog> YCRS_AccessLog { get; set; }
        public DbSet<YCRS_Files> YCRS_Files { get; set; }

        public YCRSDbContext(DbContextOptions<YCRSDbContext> options)
            : base(options)
        {
        }
    }
}
