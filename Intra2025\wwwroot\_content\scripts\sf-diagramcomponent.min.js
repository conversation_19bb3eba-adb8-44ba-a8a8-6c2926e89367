/*!*  filename: sf-diagramcomponent.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[21],{"./bundles/sf-diagramcomponent.js":function(e,t,i){"use strict";i.r(t);i("./modules/sf-diagramcomponent.js")},"./modules/sf-diagramcomponent.js":function(e,t){function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Diagram=function(){"use strict";var e=function(){function e(e,t,i,n){this.x=Number.MAX_VALUE,this.y=Number.MAX_VALUE,this.width=0,this.height=0,void 0===e||void 0===t?(e=t=Number.MAX_VALUE,i=n=0):(void 0===i&&(i=0),void 0===n&&(n=0)),this.x=e,this.y=t,this.width=i,this.height=n}return e.empty=new e(Number.MAX_VALUE,Number.MIN_VALUE,0,0),e}(),t=function(e,t){this.width=e,this.height=t};function n(e,t){for(var i=t.split(";"),n=0;n<i.length;n++){var r=i[n].split(":");2===r.length&&(e.style[r[0].trim()]=r[1].trim())}}function r(e,t){for(var i=Object.keys(t),r=0;r<i.length;r++)"style"!==i[r]?e.setAttribute(i[r],t[i[r]]):n(e,t[i[r]])}function a(e,t,i){for(var n=e;n&&!(i?n.id===t:o(n,t));)n=n.parentNode;return n}function o(e,t){return(" "+("object"===i(e.className)?e.className.animVal:e.className)+" ").indexOf(" "+t+" ")>-1}function l(e){var t=e;if(t)for(;null!==t.parentNode;){var i=t.classList;if(-1!=t.id.indexOf("_diagramlayer")||-1!=t.id.indexOf("_htmlLayer_div")||-1!=t.id.indexOf("_diagramPorts_svg")||-1!=t.id.indexOf("_diagramAdornerLayer"))return!0;if(i.contains("e-symbol-draggable"))return!0;t=t.parentNode}return!1}var s,d,h=function(){function e(e,t){var i=this;this.over=function(e){for(var t,n=document.getElementById("clonedNode"),r=i.symbolPaletteInstance.length-1;r>=0;r--)if(e.dragData.draggable.id===i.symbolPaletteInstance[r].id){t=i.symbolPaletteInstance[r].componentInstance;break}if(t&&t.invokeMethodAsync("SymbolPaletteDragEnter",e.target.id.split("_")[0]),n){sf.base.remove(n);var a=document.getElementsByClassName("e-cloneproperties e-draganddrop e-dragclone");a[0].style.width="1px",a[0].style.height="1px"}},this.drop=function(e){for(var t,n=i.symbolPaletteInstance.length-1;n>=0;n--)if(e.dragData.draggable.id===i.symbolPaletteInstance[n].id){t=i.symbolPaletteInstance[n].componentInstance;break}var r=a(e.target,"e-diagram");if(r&&e.event.changedTouches&&e.event.changedTouches.length>0){var o=void 0;for(n=i.symbolPaletteInstance.length-1;n>=0;n--){o=i.symbolPaletteInstance[n].componentInstance;break}o&&window.sfBlazor.Diagram.invokeDiagramEvents(e.event,o,void 0,e),t&&t.invokeMethodAsync("SymbolPaletteDrop",r.id,!0)}else t&&t.invokeMethodAsync("SymbolPaletteDrop",r.id,!1);sf.base.remove(e.droppedElement)},this.out=function(e){for(var t,n=i.symbolPaletteInstance.length-1;n>=0;n--)if(e.target&&e.target.children[0].id===i.symbolPaletteInstance[n].id||e.target&&e.target.parentNode&&e.target.parentNode.parentNode&&e.target.parentNode.parentNode.parentElement&&e.target.parentNode.parentNode.parentElement.id===i.symbolPaletteInstance[n].id||i.draggable.target&&i.draggable.target.id==i.symbolPaletteInstance[n].id){t=i.symbolPaletteInstance[n].componentInstance;break}t&&(e.evt.changedTouches&&e.evt.changedTouches.length>0&&window.sfBlazor.Diagram.invokeDiagramEvents(e.evt,t,void 0,e),t.invokeMethodAsync("SymbolPaletteDragLeave"))},this.helper=function(e){var t=document.getElementsByClassName("e-control e-accordion")[0],i=sf.base.createElement("div",{className:"e-cloneproperties e-draganddrop e-dragclone",styles:'color:"transparent" height:"auto",  width:'+t.offsetWidth}),n=document.getElementById("previewID");return null===n&&("touchmove"!=e.sender.type||"touchmove"==e.sender.type&&l(e.sender.target))&&(n=e.sender.target),n?(i.setAttribute("id","helperElement"),document.body.appendChild(i),i):null},this.dragStart=function(e){e.bindEvents(e.dragElement)},this.drag=function(e){if(a(e.target,"e-diagram")){if(e.event.touches&&e.event.touches.length>0){var t=void 0;for(o=i.symbolPaletteInstance.length-1;o>=0;o--)if(e.element.id===i.symbolPaletteInstance[o].id){t=i.symbolPaletteInstance[o].componentInstance;break}t&&window.sfBlazor.Diagram.invokeDiagramEvents(e.event,t,void 0,e)}}else{for(var n=document.getElementById("previewID"),r=void 0,o=i.symbolPaletteInstance.length-1;o>=0;o--)if(e.element.id===i.symbolPaletteInstance[o].id){r=i.symbolPaletteInstance[o].allowDrag;break}if(n){n.style.visibility="";var l=n.cloneNode(!0);l.style.display="Block",l.style.visibility=!0,l.setAttribute("class","e-cloneproperties e-draganddrop e-dragclone"),l.setAttribute("id","clonedNode");var s=document.getElementById("helperElement");s&&s.children[0]&&s.removeChild(s.children[0]),r||(s.style.opacity="0"),s.appendChild(l)}}},this.dragStop=function(e){var t=document.getElementsByClassName("e-cloneproperties e-draganddrop e-dragclone");if(t.length>0&&(t[0].style.width="1px",t[0].style.height="1px"),t.length>1)for(var n=t.length-1;n>0;n--)"helperElement"==t[n].id&&t[n].remove();if(!a(e.target,"e-diagram")){var r=document.getElementById("helperElement");r&&r.remove();for(var o=void 0,l=i.symbolPaletteInstance.length-1;l>=0;l--)if(e.element.id===i.symbolPaletteInstance[l].id){o=i.symbolPaletteInstance[l].componentInstance;break}o&&o.invokeMethodAsync("ElementDropToOutSideDiagram")}},this.symbolPaletteInstance=t,e&&this.initializeDrag(e)}return e.prototype.initializeDrag=function(e){var t=e.children[1];this.draggable=new sf.base.Draggable(t,{dragTarget:".e-symbol-draggable",helper:this.helper,dragStart:this.dragStart,drag:this.drag,dragStop:this.dragStop,preventDefault:!1});for(var i=document.getElementsByClassName("e-control e-diagram e-lib e-droppable e-tooltip"),n=0;n<i.length;n++)this.droppable=new sf.base.Droppable(i[n],{accept:".e-dragclone",drop:this.drop,over:this.over,out:this.out});for(var r=document.getElementsByClassName("e-acrdn-header-content"),a=0;a<r.length;a++)r[a].style.textDecoration="inherit"},e.prototype.destroy=function(){this.draggable.destroy()},e}(),c=function(e,t,i,n){return new(i||(i=Promise))((function(r,a){function o(e){try{s(n.next(e))}catch(e){a(e)}}function l(e){try{s(n.throw(e))}catch(e){a(e)}}function s(e){e.done?r(e.value):new i((function(t){t(e.value)})).then(o,l)}s((n=n.apply(e,t||[])).next())}))},g=function(e,t){var i,n,r,a,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return a={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(a){return function(l){return function(a){if(i)throw new TypeError("Generator is already executing.");for(;o;)try{if(i=1,n&&(r=2&a[0]?n.return:a[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,a[1])).done)return r;switch(n=0,r&&(a=[2&a[0],r.value]),a[0]){case 0:case 1:r=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,n=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!r||a[1]>r[0]&&a[1]<r[3])){o.label=a[1];break}if(6===a[0]&&o.label<r[1]){o.label=r[1],r=a;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(a);break}r[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],n=0}finally{i=r=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,l])}}},u=!1,m=!1,f=!1,v=!1,p=!1,y=!1,w=!1,b=!1,x=!1,E=!1,S=0,B=!1,k=[],C=!1,I="",P={},A={},D=0,N=0,L={},T=!1,M=!1,W="Arial",_="black",O="Center",H=12,X="None",K="Wrap",Y="WrapWithOverflow",R="CollapseSpace";return{createHtmlElement:function(e,t){var i=sf.base.createElement(e);return t&&this.setAttribute(i,t),i},setAttributeSvg:function(e,t){for(var i=Object.keys(t),n=0;n<i.length;n++)"style"!==i[n]?e.setAttribute(i[n],t[i[n]]):this.applyStyleAgainstCsp(e,t[i[n]])},applyStyleAgainstCsp:function(e,t){for(var i=t.split(";"),n=0;n<i.length;n++){var r=i[n].split(":");2===r.length&&(e.style[r[0].trim()]=r[1].trim())}},setCanvasSize:function(e,t,i){e&&(e.setAttribute("width",t.toString()),e.setAttribute("height",i.toString()))},getHTMLLayer:function(e){var t=null;window[e+"_htmlLayer"]?t=window[e+"_htmlLayer"]:(t=this.getDiagramElement(e).getElementsByClassName("e-html-layer")[0],window[e+"_htmlLayer"]=t);return t},getDiagramElement:function(e,t){var i;return t&&(i=document.getElementById(t)),"msie"===sf.base.Browser.info.name||"edge"===sf.base.Browser.info.name?i?i.querySelector("#"+e):document.getElementById(e):i?i.querySelector("#"+CSS.escape(e)):document.getElementById(e)},createCanvas:function(e,t,i){var n=this.createHtmlElement("canvas",{id:e});return this.setCanvasSize(n,t,i),n},setAttribute:function(e,t){for(var i=Object.keys(t),n=0;n<i.length;n++)e.setAttribute(i[n],t[i[n]])},createMeasureElements:function(e,t,i,n,r,a,o,l,s,d,h){this.updateZoomPanTool(e),this.updateDrawingTool(t),this.updateInnerLayerSize(i,n,r,void 0,l,s,d,h),a&&o&&this.onAddWireEvents(a,o);if(window.measureElement)window.measureElement.usageCount+=1;else{var c=this.createHtmlElement("div",{id:"measureElement",style:"visibility:hidden ; height: 0px ; width: 0px; overflow: hidden;"}),g=this.createHtmlElement("span",{style:"display:inline-block ; line-height: normal"});c.appendChild(g);var u;u=this.createHtmlElement("img",{}),c.appendChild(u);var m=document.createElementNS("http://www.w3.org/2000/svg","svg");m.setAttribute("xlink","http://www.w3.org/1999/xlink"),c.appendChild(m);var f=document.createElementNS("http://www.w3.org/2000/svg","path");m.appendChild(f);var v=document.createElementNS("http://www.w3.org/2000/svg","text");v.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),m.appendChild(v),window.measureElement=c,window.measureElement.usageCount=1,document.body.appendChild(c);window.measureElementCount?window.measureElementCount++:window.measureElementCount=1}},updateZoomPanTool:function(e){w=e},updateDrawingTool:function(e){b=e},updateGridlines:function(e,t,i,n){var r=document.getElementById(e);if(r&&(r.setAttribute("width",t.width.toString()),r.setAttribute("height",t.height.toString()),r.children.length>0))for(var a=0;a<r.children.length;a++)i&&i.length?r.children[a].setAttribute("d",i[a]):n&&n.length&&(r.children[a].setAttribute("cx",n[a].x.toString()),r.children[a].setAttribute("cy",n[a].y.toString()))},updatePageBreaks:function(e,t,i,n,r,a,o,l,s,d,h,c){var g=document.getElementById(e),u=document.getElementById(t);if(g&&g.children.length>0)for(var m=0;m<g.children.length;m++)g.children[m].setAttribute("x",i),g.children[m].setAttribute("y",n),g.children[m].setAttribute("width",r),g.children[m].setAttribute("height",a);if(u&&u.children.length>0){var f=0;for(m=0;m<u.children.length;m++)if(-1!=u.children[m].id.indexOf("_backgroundLayerrect"))u.children[m].setAttribute("x",i),u.children[m].setAttribute("y",n),u.children[m].setAttribute("width",r),u.children[m].setAttribute("height",a),u.children[m].setAttribute("transform",o);else if(c){var v=(l[0+f]+s)*h,p=(l[1+f]+d)*h,y=(l[2+f]+s)*h,w=(l[3+f]+d)*h;u.children[m].setAttribute("x1",v),u.children[m].setAttribute("y1",p),u.children[m].setAttribute("x2",y),u.children[m].setAttribute("y2",w),f+=4}}},updateInnerLayerSize:function(e,t,i,n,r,a,o,l,s,d,h,c,g){var u;d&&(t=(u=this.measureScrollValues(d)).width,i=u.height);var m=void 0;if(null!=e&&null!=t&&null!=i&&e.length>0)for(var f=void 0,v=0;v<e.length-1;v++)if(f=document.getElementById(e[v]),v>=e.length-6){if(-1!=e[v].indexOf("_pattern"))this.updateGridlines(e[v],a,o,l);else if(-1!=e[v].indexOf("gridline")&&r&&f){var p=r.scale,w=-r.tx*p,b=-r.ty*p,x="translate("+r.tx*p+","+r.ty*p+")";f.setAttribute("transform",x),f.children[0].setAttribute("x",w.toString()),f.children[0].setAttribute("y",b.toString())}else if(r&&f){p=r.scale;var E="translate("+r.tx*p+","+r.ty*p+"),scale("+p+")";f.setAttribute("transform",E),m=e[v].split("_")[0];var S=document.getElementById(m+"_diagram_PolygonHelper");S&&S.setAttribute("transform",E);var B=document.getElementById(m+"_diagram_ConnectorHelper");B&&B.setAttribute("transform",E);var k=document.getElementById(m+"_htmlLayer_div"),C="position: absolute; top: 0px; left: 0px; pointer-events: all; transform: translate("+r.tx*p+"px,"+r.ty*p+"px) scale("+p+")";k.setAttribute("style",C);var I=document.getElementById(m+"_SelectorElement");if(s&&I&&I.children.length>0&&I.children.length==s.length)for(var P=0;P<I.children.length;P++)if("line"==I.children[P].tagName){var A=s[P],D=A.startPoint.x+A.x,N=A.startPoint.y+A.y,L=A.endPoint.x+A.x,T=A.endPoint.y+A.y;I.children[P].setAttribute("x1",D.toString()),I.children[P].setAttribute("y1",N.toString()),I.children[P].setAttribute("x2",L.toString()),I.children[P].setAttribute("y2",T.toString()),I.children[P].setAttribute("transform","rotate("+A.angle+" "+(A.x+A.width*A.pivotX)+" "+(A.y+A.height*A.pivotY)+")")}else if("path"==I.children[P].tagName){var M=s[P];I.children[P].setAttribute("transform","rotate("+M.angle+","+(M.x+M.width*M.pivotX)+","+(M.y+M.height*M.pivotY)+")translate("+M.x+","+M.y+")")}else if("rect"==I.children[P].tagName){var W=s[P];I.children[P].setAttribute("x",W.x),I.children[P].setAttribute("y",W.y),I.children[P].setAttribute("width",W.width),I.children[P].setAttribute("height",W.height),I.children[P].setAttribute("transform","rotate("+W.angle+","+(W.x+W.width*W.pivotX)+","+(W.y+W.height*W.pivotY)+")")}else"circle"==I.children[P].tagName&&(I.children[P].setAttribute("cx",s[P].cx),I.children[P].setAttribute("cy",s[P].cy),I.children[P].setAttribute("visibility",s[P].visible?"visible":"hidden"))}}else f&&(f.style.width=t,f.style.height=i);if(y&&(y=!1),!d&&null!=e&&e.length>0){var _=document.getElementById(e[e.length-1]);u=this.onChangeScrollValues(_,n)}if(null!=h&&null!=m){var O=m+"_backgroundImageLayer",H=m+"_backgroundLayer",X=(w=(r.tx+h.x)*r.scale,b=(r.ty+h.y)*r.scale,h.height*r.scale),K=h.width*r.scale,Y="rotate(0 "+(w+0*K)+" "+(b+0*X)+")";this.updatePageBreaks(O,H,w,b,K,X,Y,c,r.tx,r.ty,r.scale,g)}return u},onChangeScrollValues:function(e,t){return u&&(u=!1),null!=e&&null!=t&&null!=t.x&&null!=t.y?(m&&(m=!1,f=!0),e.scrollLeft=t.x,e.scrollTop=t.y,this.measureScrollValues(e.id)):null},measureScrollValues:function(t){var i=document.getElementById(t);return new e(i.scrollLeft,i.scrollTop,i.scrollWidth,i.scrollHeight)},measurePath:function(t){if(t){window.measureElement.style.visibility="visible";var i=window.measureElement.children[2],n=this.getChildNode(i)[0];n.setAttribute("d",t);var r=n.getBBox(),a=new e(r.x,r.y,r.width,r.height);return window.measureElement.style.visibility="hidden",a}return new e(0,0,0,0)},setVerticalRulerLabelAttribute:function(e,t,i){var n=document.getElementsByClassName(e);if(n.length>0)for(var r=0;r<n.length;r++){var a=n[r],o=a.getBBox(),l=i?-(o.width+2)+","+(t/2-o.height):-(o.width+2)+","+(t/2-o.height/2);a.setAttribute("x","0"),a.setAttribute("y",o.height.toString()),a.setAttribute("transform","rotate(270) translate("+l+")"),a.setAttribute("fill","black")}},openUrl:function(e){return c(this,void 0,void 0,(function(){return g(this,(function(t){return window.open(e,"_blank"),[2]}))}))},textEdit:function(e,t,i,n,a,o,l,s){var d=this,h=document.getElementById("diagram_editTextBoxDiv");e.id=s;var c=document.getElementById("diagram_editBox"),g=e.content;if(!h&&!c){h=this.createHtmlElement("div",{}),c=this.createHtmlElement("textarea",{});var u=document.getElementsByClassName("e-control e-diagram e-lib e-droppable e-tooltip"),m=-1;if(u.length>0)for(var f=0;f<u.length;f++){var v="#"+u[f].id+"_content";if(u[f].querySelector(v).id.split("_")[0]==i){m=f;break}}if(-1!=m){v="#"+u[m].id+"_content";u[m].querySelector(v).appendChild(h),h.appendChild(c),c.appendChild(document.createTextNode(g)),this.diagram_editTextBoxDiv=h,this.diagram_editBox=c}}var p=e.bounds.width<a.width?e.bounds.width:a.width;p=90>p?90:p;var y=this.measureHtmlText(e.style,t,g,void 0,void 0,p);0==y.width&&0==y.height&&(y.width=50,y.height=12);var w=l?o.scale:1;y.width=Math.max(y.width,50),r(h,{id:"diagram_editTextBoxDiv",style:"position: absolute;left:"+((n.x+o.tx)*o.scale-y.width/2*w-2.5)+"px;top:"+((n.y+o.ty)*o.scale-y.height/2*w-3)+"px;width:"+(y.width+1)*w+"px;height:"+y.height*w+"px; containerName:temp;"});var b=e.style;r(c,{id:"diagram_editBox",style:"width:"+(y.width+1)*w+"px;height:"+y.height*w+"px;line-height: normal;resize: none;outline: none;overflow: hidden;;font-family:"+b.fontFamily+";font-size:"+b.fontSize*w+"px;text-align:"+e.style.textAlign.toLocaleLowerCase()+";",class:"e-diagram-text-edit"}),c.style.fontWeight=b.bold?"bold":"",c.style.fontStyle=b.italic?"italic":"",c.style.lineHeight=(1.2*b.fontSize+"px;").toString();var x=document.getElementById(e.id+"_text");x&&x.setAttribute("visibility","hidden"),c.style.textDecoration=b.textDecoration?b.textDecoration:"",c.addEventListener("input",(function(i){d.inputChange(i,e,t,a,n,o,l)})),c.select(),window.annotation=e},inputChange:function(e,t,i,n,r,a,o){var l,s,d,h,c,g=this.diagram_editBox,u=this.diagram_editTextBoxDiv,m=g.value,f=m.split("\n");l=(l=n.width<t.bounds.width?n.width:t.bounds.width)>90?l:90,c=this.measureHtmlText(t.style,i,m,void 0,void 0,l),s=Number(g.style.fontSize.split("px")[0]),f.length>1&&""===f[f.length-1]&&(c.height=c.height+s);var v=o?a.scale:1;h=(90>(h=c.width)?90:h)*v,d=(12>c.height?12:c.height)*v,u.style.left=(r.x+a.tx)*a.scale-h/2-2.5+"px",u.style.top=(r.y+a.ty)*a.scale-d/2-3+"px",u.style.width=h+"px",u.style.height=d+"px",g.style.minHeight="12px",g.style.minWidth="90px",g.style.width=h+"px",g.style.height=d+"px"},measureHtmlText:function(e,t,i,n,r,a){var o={},l=this.createHtmlElement("span",{style:"display:inline-block; line-height: normal"});return e.bold&&(l.style.fontWeight="bold"),e.italic&&(l.style.fontStyle="italic"),void 0!==n&&(l.style.width=n.toString()+"px"),void 0!==r&&(l.style.height=r.toString()+"px"),void 0!==a&&(l.style.maxWidth=a.toString()+"px"),l.style.fontFamily=e.fontFamily,l.style.fontSize=e.fontSize+"px",l.style.color=e.color,l.textContent=i,l.style.whiteSpace=this.whiteSpaceToString(t,e.textWrapping),l.style.wordBreak=void 0!==a?"break-word":this.wordBreakToString(e.textWrapping),document.body.appendChild(l),o.width=l.offsetWidth,o.height=l.offsetHeight,document.body.removeChild(l),o},getChildNode:function(e){var t,i=[];if("msie"===sf.base.Browser.info.name||"edge"===sf.base.Browser.info.name)for(var n=0;n<e.childNodes.length;n++)1===(t=e.childNodes[n]).nodeType&&i.push(t);else i=e.children;return i},measureBounds:function(e,t,i,n,r){return c(this,void 0,void 0,(function(){var a,o,l,s,d,h,c,u,m,f,v,p,y,w,b,x,E,S,B,k,C,P,N,L,T,M,W,_,O;return g(this,(function(g){switch(g.label){case 0:if(null!=n)for(o=document.getElementsByClassName("e-acrdn-panel e-content-hide"),a=[],O=0;O<o.length;O++)a[O]=o[O].style.display,o[O].style.display="block";if(l={},s={},d={},h={},c={},u="measureElement",e)for(M=Object.keys(e).map((function(t){return[e[t],t]})),window[u].style.visibility="visible",S=window[u].children[2],m=this.getChildNode(S)[0],_=0;_<M.length;_++)"Path"==M[_][0]?(p=M[_][1],m.setAttribute("d",p),v=m.getBBox(),T={x:v.x,y:v.y,width:v.width,height:v.height},s[p]=T):-1!=M[_][0].indexOf("GetBoundingClientRect")&&((f=document.getElementById(M[_][1]))&&(v=f.getBoundingClientRect(),s[M[_][0]]={x:v.x,y:v.y,width:v.width,height:v.height}),"GetBoundingClientRect"==M[_][0]&&(I=M[_][1],s.GetScrollerBounds=this.measureScrollValues(M[_][1])));if(t)for(M=Object.keys(t).map((function(e){return[t[e],e]})),_=0;_<M.length;_++)p=M[_][1],y=t[p].content,w=t[p].style,b=t[p].bounds,x=t[p].nodeSize,b.width=null==b.width?void 0:b.width,b.height=null==b.height?void 0:b.height,x.width=null==x.width?void 0:x.width,x.height=null==x.height?void 0:x.height,d[p]=this.measureText(b,w,y,b.width||x.width);return i&&(E=Object.keys(i).map((function(e){return[i[e],e]}))).length>0?(0,M={},[4,this.loadImage(E,0,M)]):[3,2];case 1:g.sent(),h=M,g.label=2;case 2:if(n&&(M=Object.keys(n).map((function(e){return[n[e],e]})),window[u].style.visibility="visible",S=window[u].children[2],M.length>0)){for(B={},k={},_=0;_<M.length;_++)C=M[_][0],(P=document.getElementById(C))&&((N=P.cloneNode(!0)).id=N.id+"_copy",S.appendChild(N),L=N.getBoundingClientRect(),T=S.getBoundingClientRect(),k={x:L.left-T.left,y:L.top-T.top,width:L.width,height:L.height},B[M[_][1]]=k,S.removeChild(N));c=B}if(s.GetScrollerWidth=this.getScrollerWidth(),l.Path=s,l.Text=d,l.Image=h,l.Native=c,r){for(28500,M=JSON.stringify(l),(l={}).MeasureData="",W=[],_=0;_<M.length;_+=28500)0==_&&M.length<28500?l.MeasureData=M.slice(_,_+28500):(W.push(D),A[D.toString()]=M.slice(_,_+28500),D++);W.length>0&&(l.ChunkMeasureData=[W[0],W[W.length-1]].toString())}if(null!=a)for(O=0;O<o.length;O++)o[O].style.display=a[O].toString();return window[u].style.visibility="hidden",[2,l]}}))}))},getChunkPathPointsData:function(e){var t=L[e];return delete L[e],t},getChunkMeasureData:function(e){var t=A[e];return delete A[e],t},measureText:function(e,t,i,n,r){var a,o,l={},s={width:0,height:0},d=this.getTextOptions(i,e,t,n);return a=this.wrapSvgText(d,r,n),o=this.wrapSvgTextAlign(d,a),s.width=o.width,o.width>=n&&"Wrap"!==d.textOverflow&&(s.width=n),s.height=a.length*d.fontSize*1.2,o.width>d.width&&"Wrap"!==d.textOverflow&&"NoWrap"===d.textWrapping&&(a[0].text=this.overFlow(d.content,d)),l.Bounds=s,l.WrapBounds=o,l.ChildNodes=a,l},getTextOptions:function(e,t,i,n){var r={fill:i.fill,stroke:i.strokeColor,strokeWidth:i.strokeWidth,dashArray:i.strokeDashArray,opacity:i.opacity,gradient:i.gradient,width:n||t.width,height:t.height};return r.fontSize=i.fontSize||H,r.fontFamily=i.fontFamily||W,r.textOverflow=i.textOverflow||K,r.textDecoration=i.textDecoration||X,r.doWrap=i.doWrap,r.whiteSpace=this.whiteSpaceToString(i.whiteSpace||R,i.textWrapping||Y),r.content=e,r.textWrapping=i.textWrapping||Y,r.breakWord=this.wordBreakToString(i.textWrapping||Y),r.textAlign=this.textAlignToString(i.textAlign||O),r.color=i.color||_,r.italic=i.italic||M,r.bold=i.bold||T,r.dashArray="",r.strokeWidth=0,r.fill="",r},whiteSpaceToString:function(e,t){if("NoWrap"===t&&"PreserveAll"===e)return"pre";var i="";switch(e){case"CollapseAll":i="nowrap";break;case"CollapseSpace":i="pre-line";break;case"PreserveAll":i="pre-wrap"}return i},wordBreakToString:function(e){var t="";switch(e){case"Wrap":t="breakall";break;case"NoWrap":t="keepall";break;case"WrapWithOverflow":t="normal";break;case"LineThrough":t="line-through"}return t},textAlignToString:function(e){var t="";switch(e){case"Center":t="center";break;case"Left":t="left";break;case"Right":t="right"}return t},wrapSvgText:function(e,t,i){var n,r,a=[],o=0,l=t||e.content;if("nowrap"!==e.whiteSpace&&"pre"!==e.whiteSpace)if("breakall"===e.breakWord)for(n="",n+=l[0],o=0;o<l.length;o++)if((r=this.bBoxText(n,e))>=e.width&&n.length>0)a[a.length]={text:n,x:0,dy:0,width:r},n="";else{(n+=l[o+1]||"").indexOf("\n")>-1&&(a[a.length]={text:n,x:0,dy:0,width:this.bBoxText(n,e)},n="");var s=this.bBoxText(n,e);Math.ceil(s)+2>=e.width&&n.length>0&&(a[a.length]={text:n,x:0,dy:0,width:s},n=""),o===l.length-1&&n.length>0&&(a[a.length]={text:n,x:0,dy:0,width:s},n="")}else a=this.wordWrapping(e,t,i);else a[a.length]={text:l,x:0,dy:0,width:this.bBoxText(l,e)};return a},wordWrapping:function(e,t,i){var n=[],r="",a=0,o=0,l="nowrap"!==e.whiteSpace,s=t||e.content;if(null!=s){var d=s.split("\n"),h=void 0,c=void 0,g=void 0,u=void 0;for(a=0;a<d.length;a++)for(h="NoWrap"!==e.textWrapping?d[a].split(" "):"NoWrap"===e.textWrapping?[d[a]]:d,o=0;o<h.length;o++){c=(r+=((0!==o||1===h.length)&&l&&r.length>0?" ":"")+h[o])+" "+(h[o+1]||"");var m=this.bBoxText(c,e);Math.floor(m)>(i||e.width)-2&&r.length>0?(n[n.length]={text:r,x:0,dy:0,width:c===r?m:r===u?g:this.bBoxText(r,e)},r=""):o===h.length-1&&(n[n.length]={text:r,x:0,dy:0,width:m},r=""),u=c,g=m}}return n},wrapSvgTextAlign:function(e,t){var i,n,r={x:0,width:0},a=0;for(a=0;a<t.length;a++)n=i=t[a].width,i="left"===e.textAlign?0:"center"===e.textAlign?i>e.width&&("Ellipsis"===e.textOverflow||"Clip"===e.textOverflow)?0:-i/2:"right"===e.textAlign?-i:t.length>1?0:-i/2,t[a].dy=1.2*e.fontSize,t[a].x=i,r?(r.x=Math.min(r.x,i),r.width=Math.max(r.width,n)):r={x:i,width:n};return r},overFlow:function(e,t){var i,n=0,r=0,a=0,o="";i=e.length;var l=0;do{a>0&&(n=r),r=Math.floor(this.middleElement(n,i)),o+=e.substr(n,r),a=this.bBoxText(o,t)}while(a<=t.width);for(o=o.substr(0,n),l=n;l<i;l++)if(o+=e[l],(a=this.bBoxText(o,t))>=t.width){e=e.substr(0,o.length-1);break}return"Ellipsis"===t.textOverflow?(e=e.substr(0,e.length-3),e+="..."):e=e.substr(0,e.length),e},middleElement:function(e,t){return(e+t)/2},getScrollerWidth:function(){var e=this.createHtmlElement("div",{style:"visibility:hidden; width: 100px"});document.body.appendChild(e);var t=e.getBoundingClientRect().width;e.style.overflow="scroll";var i=this.createHtmlElement("div",{style:"width:100%"});e.appendChild(i);var n=i.getBoundingClientRect().width;return e.parentNode.removeChild(e),{x:0,y:0,width:t-n,height:0}},pathPoints:function(e,t){return c(this,void 0,void 0,(function(){var i,n,r,a,o,l;return g(this,(function(s){if(i={},e)for(a=Object.keys(e).map((function(t){return[e[t],t]})),l=0;l<a.length;l++)a.length>0&&(n=a[l][1],i[a[l][0]]=this.findSegmentPoints(n));if(t){for((r={}).PathPoints=i,28500,a=JSON.stringify(r),o=[],(r={}).PathPoints="",l=0;l<a.length;l+=28500)0==l&&a.length<28500?r.PathPoints=a.slice(l,l+28500):(o.push(N),L[N.toString()]=a.slice(l,l+28500),N++);return o.length>0&&(r.ChunkPathPoints=[o[0],o[o.length-1]].toString()),[2,r]}return[2,i]}))}))},findSegmentPoints:function(e){var t,i,n=[];window.measureElement.style.visibility="visible";var r=window.measureElement.children[2],a=this.getChildNode(r)[0];a.setAttributeNS(null,"d",e);var o=a.getTotalLength();for(i=0;i<=o;i+=10)t=a.getPointAtLength(i),n.push({x:t.x,y:t.y});return window.measureElement.style.visibility="hidden",n},loadImage:function(e,t,i){return c(this,void 0,void 0,(function(){var n,a,o;return g(this,(function(l){switch(l.label){case 0:return n=new Promise((function(i,n){var a={};window.measureElement.style.visibility="visible",window.measureElement.children[1].setAttribute("src",e[t][0]),window.measureElement.style.visibility="hidden";var o=document.createElement("img");o.setAttribute("src",e[t][0]),r(o,{id:"imagesf"+t+"imageNode",style:"display: none;"}),document.body.appendChild(o),o.onload=function(e){var t=e.currentTarget;a={width:t.width,height:t.height},i(a)}})),a=i,o=e[t][1],[4,n];case 1:return a[o]=l.sent(),t!=e.length-1?[3,2]:[2,i];case 2:return t++,[4,this.loadImage(e,t,i)];case 3:l.sent(),l.label=4;case 4:return[2]}}))}))},bBoxText:function(e,t){window.measureElement.style.visibility="visible";var i=window.measureElement.children[2],r=this.getChildNode(i)[1];r.textContent=e,n(r,"font-size:"+t.fontSize+"px; font-family:"+t.fontFamily+";font-weight:"+(t.bold?"bold":"normal"));var a=r.getBBox().width;return window.measureElement.style.visibility="hidden",a},endEdit:function(e){setTimeout((function(){var e=document.getElementById(i.id+"_text");e&&e.setAttribute("visibility","visible")}),100);var t=this.diagram_editBox,i=window.annotation,n=t.value,r=i.style,a=i.bounds,o=i.nodeSize;o.width=null==o.width?void 0:o.width,o.height=null==o.height?void 0:o.height;var l={},s={};return l[i.id]=this.measureText(a,r,n,a.width||o.width),l[i.id].Content=n,s.Text=l,this.diagram_editTextBoxDiv.remove(),s},onAddWireEvents:function(e,t){var i=this,n=document.getElementById(e);P[e]=t;var r=this;n.addEventListener("mousedown",(function(e){"Mac68K"!==window.navigator.platform&&"MacPPC"!==window.navigator.platform&&"MacIntel"!==window.navigator.platform||e.preventDefault(),i.invokeDiagramEvents(e,t)})),window.addEventListener("resize",(function(e){setTimeout((function(){r.invokeDiagramEvents(e,null,I)}),800)})),n.addEventListener("mousemove",(function(e){"Mac68K"!==window.navigator.platform&&"MacPPC"!==window.navigator.platform&&"MacIntel"!==window.navigator.platform||e.preventDefault(),i.invokeDiagramEvents(e,t)})),n.addEventListener("mouseup",(function(e){i.invokeDiagramEvents(e,t)})),n.addEventListener("mouseleave",(function(e){i.invokeDiagramEvents(e,t)})),n.addEventListener("touchstart",(function(e){"Mac68K"!==window.navigator.platform&&"MacPPC"!==window.navigator.platform&&"MacIntel"!==window.navigator.platform||e.preventDefault(),i.invokeDiagramEvents(e,t)})),n.addEventListener("touchmove",(function(e){"Mac68K"!==window.navigator.platform&&"MacPPC"!==window.navigator.platform&&"MacIntel"!==window.navigator.platform||e.preventDefault(),i.invokeDiagramEvents(e,t)})),n.addEventListener("touchend",(function(e){i.invokeDiagramEvents(e,t)})),n.addEventListener("scroll",(function(e){i.invokeDiagramEvents(e,t)})),n.addEventListener("mousewheel",(function(e){i.invokeDiagramEvents(e,t)})),n.addEventListener("wheel",(function(e){i.invokeDiagramEvents(e,t)})),n.addEventListener("keydown",(function(e){i.invokeDiagramEvents(e,t)})),n.addEventListener("keyup",(function(e){i.invokeDiagramEvents(e,t)}))},invokeDiagramEvents:function(e,t,i,n){var r,a="diagram_editTextBoxDiv",o=this[a],d={};if("resize"==e.type){var h=document.getElementById(i);if(!h){if(document.getElementsByClassName("e-diagram").length>0){var c=document.getElementsByClassName("e-diagram").length;i=document.getElementsByClassName("e-diagram")[c-1].id+"_content",h=document.getElementById(i),I=i}if(!h)return}t=P[i]}if("mousedown"!=e.type&&"touchstart"!=e.type||(p=!0,"touchstart"==e.type&&(k=this.getTouches(e),x=l(e.target)&&!w&&!e.target.id.indexOf("_icon"),!function(e){var t=e;if(t)for(;null!==t.parentNode;){var i=t.classList;if(i.contains("e-diagram-userhandle-circle")||i.contains("e-diagram-userhandle-path"))return!0;t=t.parentNode}return!1}(e.target)?(l(e.target)||document.getElementsByClassName("e-diagram-helper").length>0)&&(E=!0,s=setTimeout((function(){!sf.base.Browser.isDevice&&E&&(E=!1)}),300)):e.preventDefault())),"touchmove"==e.type&&s&&clearTimeout(s),("mouseup"==e.type||"touchend"==e.type||"mouseleave"==e.type)&&(p=!1,"touchend"==e.type)){var g=(new Date).getTime()-S;B=g<300&&g>0,S=(new Date).getTime(),s&&clearTimeout(s),e.cancelable&&(E||sf.base.Browser.isDevice&&(l(e.target)||document.getElementsByClassName("e-diagram-helper").length>0))&&e.preventDefault()}if("mouseleave"!=e.type&&"Escape"!=e.key&&("mousedown"!=e.type&&"touchstart"!=e.type||"diagram_editBox"==e.target.id)||!o||(window.isEscape="Escape"==e.key,r=this.endEdit(e,this),this[a]=o=document.getElementById("diagram_editTextBoxDiv"),e.currentTarget.focus()),d=this.getMouseEvents(e,i,n),"Escape"==e.key){var b=document.getElementById("helperElement");b&&(sf.base.remove(b),b.remove())}if("mousewheel"!=e.type&&"wheel"!=e.type||!e.ctrlKey){if(!o&&(("mousemove"==e.type||"touchmove"==e.type)&&!u||"mousemove"!=e.type&&"touchmove"!=e.type||!v))if(("mousemove"==e.type||"touchmove"==e.type&&!y)&&p&&!sf.base.Browser.isDevice&&w&&(u=!0,d.isPan=!0),n)t.invokeMethodAsync("InvokeDiagramEvents",d);else if("keydown"==e.type||"keyup"==e.type||"scroll"!=e.type||"scroll"==e.type&&!y)if("mousewheel"==e.type||"wheel"==e.type)if(t.invokeMethodAsync("InvokeDiagramEvents",d,r),e.shiftKey){var C=e.currentTarget.scrollWidth>e.currentTarget.clientWidth,A=0==e.currentTarget.scrollLeft,D=e.currentTarget.scrollLeft==e.currentTarget.scrollWidth-e.currentTarget.clientWidth;A&&e.wheelDelta>0||D&&e.wheelDelta<0||!C||e.preventDefault()}else{var N=e.currentTarget.scrollHeight>e.currentTarget.clientHeight,L=0==e.currentTarget.scrollTop,T=e.currentTarget.scrollTop==e.currentTarget.scrollHeight-e.currentTarget.clientHeight;L&&e.wheelDelta>0||T&&e.wheelDelta<0||!N||e.preventDefault()}else"touchmove"==e.type?(x||(d.type="scroll"),t.invokeMethodAsync("InvokeDiagramEvents",d,r)):"scroll"!=e.type||"scroll"==e.type&&!f?(!sf.base.Browser.isDevice||sf.base.Browser.isDevice&&(e.target.id.indexOf("_icon")||k.length>1&&e.touches.length>1||!w||"touchstart"!=e.type&&"touchmove"!=e.type&&"touchend"!=e.type&&"mousedown"!=e.type&&"mousemove"!=e.type&&"mouseup"!=e.type||!w))&&t.invokeMethodAsync("InvokeDiagramEvents",d,r):"scroll"==e.type&&f&&(f=!1)}else m?setTimeout((function(){m=!1,y=!1}),1e3):(m=!0,t.invokeMethodAsync("InvokeDiagramEvents",d,r));"mouseup"!=e.type&&"mousemove"!=e.type&&"touchend"!=e.type&&"touchmove"!=e.type||!e.currentTarget||!e.currentTarget.id||this[a]||this.isForeignObject(e.target)||e.currentTarget.focus(),"mouseup"!=e.type&&"touchend"!=e.type&&"mouseleave"!=e.type||(x=!1,k=[])},isForeignObject:function(e,t){var i=e;if(i)for(;null!==i.parentNode;){if("string"==typeof i.className&&(!t&&-1!==i.className.indexOf("foreign-object")||t&&-1!==i.className.indexOf("e-diagram-text-edit")))return i;i=i.parentNode}return null},getTouches:function(e){var t=[];if(e&&e.touches&&e.touches.length>0)for(var i=0;i<e.touches.length;i++)t.push({pageX:e.touches[i].pageX,pageY:e.touches[i].pageY,screenX:e.touches[i].screenX,screenY:e.touches[i].screenY,clientX:e.touches[i].clientX,clientY:e.touches[i].clientY});return t},getMouseEvents:function(e,t,i){var n={};if(-1!=e.type.indexOf("touch")?((n={altKey:e.altKey,shiftKey:e.shiftKey,ctrlKey:e.ctrlKey,detail:B?2:1,metaKey:e.metaKey,type:e.type}).touches=this.getTouches(e),e.changedTouches&&e.changedTouches[0]&&(n.clientX=n.offsetX=e.changedTouches[0].clientX,n.clientY=n.offsetY=e.changedTouches[0].clientY),i&&"out"==i.name&&(n.type="mouseleave"),"touchmove"==e.type&&e.cancelable&&(!sf.base.Browser.isDevice||sf.base.Browser.isDevice&&(k.length>1&&e.touches.length>1||(b||x||l(e.target))&&!w))&&(x=!0,e.preventDefault())):n={altKey:e.altKey,shiftKey:e.shiftKey,ctrlKey:e.ctrlKey,detail:e.detail,metaKey:e.metaKey,screenX:e.screenX,screenY:e.screenY,clientX:e.clientX,clientY:e.clientY,offsetX:e.offsetX,offsetY:e.offsetY,type:e.type,key:e.key,keyCode:e.keyCode,button:e.button},t&&"resize"==e.type){var r=this.measureScrollValues(t),o=document.getElementById(t);n.diagramCanvasScrollBounds=r,n.diagramGetBoundingClientRect=o.getBoundingClientRect()}else if(i){if(i.target.id&&""!=i.target.id){var s=a(i.target,"e-diagram");if(s&&s.id&&""!=s.id){var d=s.id;r=this.measureScrollValues(d);n.diagramCanvasScrollBounds=r,n.diagramGetBoundingClientRect=document.getElementById(d+"_content").getBoundingClientRect()}}}else if(e.currentTarget&&e.currentTarget.id){r=this.measureScrollValues(e.currentTarget.id);n.diagramCanvasScrollBounds=r,n.diagramGetBoundingClientRect=e.currentTarget.getBoundingClientRect()}else if(e.target){r=this.measureScrollValues(e.target.id);n.diagramCanvasScrollBounds=r,n.diagramGetBoundingClientRect=e.target.getBoundingClientRect()}return"mousewheel"!=e.type&&"wheel"!=e.type||(e.ctrlKey&&e.preventDefault(),e.currentTarget.focus(),n.wheelDelta=e.wheelDelta,y=!0),"touchmove"==e.type&&e.touches.length>1&&(y=!0),"mousedown"!=e.type&&"touchstart"!=e.type||(v=!0),"mouseup"!=e.type&&"touchend"!=e.type||(v=!1),t||this.isForeignObject(e.target,!0)||"mousemove"!=e.type&&"mouseleave"!=e.type&&"mouseup"!=e.type&&(this.isForeignObject(e.target)||"mousedown"!=e.type&&"keydown"!=e.type&&"keyup"!=e.type)&&(!this.isForeignObject(e.target)||"mousedown"!=e.type||"keydown"!=e.type||"keyup"!=e.type)||y||e.preventDefault(),n},initialiseModule:function(e,t,i){return c(this,void 0,void 0,(function(){var n,r,a,o=this;return g(this,(function(l){return"symbolPaletteInstance",window.symbolPaletteInstance?(n={id:e.children[1].id,componentInstance:t,allowDrag:i},window.symbolPaletteInstance.push(n)):(n={id:e.children[1].id,componentInstance:t,allowDrag:i},window.symbolPaletteInstance=[],window.symbolPaletteInstance.push(n)),r=document.getElementById(e.children[1].id+"textEnter"),(a=document.getElementById(e.children[1].id+"iconSearch"))&&a.addEventListener("click",(function(e){o.invokePaletteEvents(e,t)})),r&&r.addEventListener("input",(function(e){o.invokePaletteEvents(e,t)})),r&&r.addEventListener("keyup",(function(e){o.invokePaletteEvents(e,t)})),e.addEventListener("mousedown",(function(e){"Mac68K"!==window.navigator.platform&&"MacPPC"!==window.navigator.platform&&"MacIntel"!==window.navigator.platform||e.target.id!=(r&&r.id)&&e.preventDefault(),o.invokePaletteEvents(e,t)})),e.addEventListener("mousemove",(function(e){"Mac68K"!==window.navigator.platform&&"MacPPC"!==window.navigator.platform&&"MacIntel"!==window.navigator.platform||e.preventDefault(),o.invokePaletteEvents(e,t)})),e.addEventListener("mouseup",(function(e){o.invokePaletteEvents(e,t)})),e.addEventListener("mouseleave",(function(e){o.invokePaletteEvents(e,t)})),e.addEventListener("touchstart",(function(e){"Mac68K"!==window.navigator.platform&&"MacPPC"!==window.navigator.platform&&"MacIntel"!==window.navigator.platform||e.preventDefault(),o.invokePaletteEvents(e,t)})),e.addEventListener("touchmove",(function(e){"Mac68K"!==window.navigator.platform&&"MacPPC"!==window.navigator.platform&&"MacIntel"!==window.navigator.platform||e.preventDefault(),o.invokePaletteEvents(e,t)})),e.addEventListener("touchend",(function(e){o.invokePaletteEvents(e,t)})),setTimeout((function(){this.symbolPaletteDragAndDropModule=new h(e,window.symbolPaletteInstance)}),100),[2]}))}))},invokePaletteEvents:function(e,t){var i,n,r=window.symbolPaletteInstance,s=null,d=null;"touchstart"!=e.type&&"touchend"!=e.type&&e.target&&a(e.target,"e-acrdn-header")&&e.preventDefault(),"touchstart"==e.type&&(C=l(e.target)),"touchend"==e.type&&(C=!1);var h=this.palettegetMouseEvents(e);if(i=2===e.target.id.split("_").length?e.target.id.split("_")[0]:function(e,t,i){for(var n,r=e;r&&!o(r,i);){if(r.id&&-1!=r.id.indexOf(t)){n=r.id.split(t)[0];break}r=r.parentNode}return n}(e.target,"_Container","e-symbolpalette"),"keyup"==e.type||"click"==e.type||"input"==e.type){for(var c=0;c<(r||[]).length;c++){var g=r[c],m=e.target&&e.target.id&&e.target.id.includes("iconSearch")?"iconSearch":"textEnter";if(e.target&&g.id===e.target.id.split(m)[0]){n=g;break}}if(n&&n.id){var f=n.id;s=document.getElementById(f+"textEnter"),d=document.getElementById(f+"iconSearch")}"input"!==e.type&&"Escape"!==e.key||this.resetSearchIcon(d),"Enter"!==e.key&&"NumpadEnter"!==e.key||!s||""===s.value?"Backspace"!==e.key&&"Delete"!==e.key||!s||""===s.value||this.resetSearchIcon(d):this.toggleSearchIcon(d),"Escape"===e.key&&s&&(s.value=""),s&&(i=s.value)}if("mousemove"==e.type&&!u||"mousemove"!=e.type||!v)if(this.updateSymbolPaletteItemColor(e,"e-symbol-draggable","e-symbol-hover","e-symbol-selected"),"touchmove"==e.type&&i&&C||"touchmove"!=e.type)t.invokeMethodAsync("InvokePaletteEvents",h,i,"keyup"==h.type?d.classList.toString():"");else{var p=document.getElementById("previewID");p&&p.parentNode&&p.parentNode.removeChild(p)}"mousedown"==e.type&&this.updateSymbolPaletteItemColor(e,"e-symbol-draggable","e-symbol-hover","e-symbol-selected")},resetSearchIcon:function(e){e.classList.remove("e-close"),e.classList.add("e-search"),e.title="Start Search"},toggleSearchIcon:function(e){e.classList.contains("e-close")?this.resetSearchIcon(e):e.classList.contains("e-search")&&(e.classList.remove("e-search"),e.classList.add("e-close"),e.title="Clear text")},updateSymbolPaletteItemColor:function(e,t,i,n){if(e&&e.target&&e.type){var r=document.getElementById(e.target.id);if(r)if(Array.from(r.classList).find((function(e){return e===t}))){var a=e.target,o="mousemove"===e.type?i:n;a.classList.add(o);var l=document.getElementsByClassName(o);l&&0!==l.length&&Array.from(l).forEach((function(e){a!==e&&e.classList.remove(o)}))}}},palettegetMouseEvents:function(e){var t={};return-1!=e.type.indexOf("touch")?((t={altKey:e.altKey,shiftKey:e.shiftKey,ctrlKey:e.ctrlKey,detail:e.detail,metaKey:e.metaKey,type:e.type,key:e.key,keyCode:e.keyCode}).touches=this.getTouches(e),e.changedTouches&&e.changedTouches[0]&&(t.offsetX=e.changedTouches[0].clientX,t.offsetY=e.changedTouches[0].clientY),"touchmove"==e.type&&(!sf.base.Browser.isDevice||sf.base.Browser.isDevice&&(C||l(e.target)))&&(C=!0,e.preventDefault())):t={altKey:e.altKey,shiftKey:e.shiftKey,ctrlKey:e.ctrlKey,detail:e.detail,metaKey:e.metaKey,screenX:e.screenX,screenY:e.screenY,clientX:e.clientX,clientY:e.clientY,offsetX:e.offsetX,offsetY:e.offsetY,type:e.type,key:e.key,keyCode:e.keyCode},t},exportDiagram:function(t,i,n,r,a,o,l,s){return c(this,void 0,void 0,(function(){var d,h=this;return g(this,(function(u){return d=this,[2,new Promise((function(u,m){return c(h,void 0,void 0,(function(){var h,c,m,f,v,p,y,w;return g(this,(function(g){switch(g.label){case 0:return h="data",i=i,r=r,c=t.margin||{},t.region=t&&0==t.region?"PageSettings":1==t.region?"Content":"ClipBounds",t.fileName=o,t.mode=s?"Download":"Data",t.format=0===l?"JPEG":1===l?"PNG":2===l?"SVG":l,t.orientation=t&&0===t.orientation?"Landscape":"Portrait",m=n,f=new e(n.x,n.y,n.width,n.height),(v=t.clipBounds)&&(m.x=v.x?v.x:m.x,m.y=v.y?v.y:m.y,m.width=v.width||m.width,m.height=v.height||m.height),c={top:c.top,bottom:c.bottom,right:c.right,left:c.left},p=t.fileName||"diagram",[4,d.setCanvas(t,m,f,c,p,r,i)];case 1:return null!==(h=g.sent())&&(a||"Data"===t.mode&&"SVG"===t.format?u(h):(y={id:i+"_ExportImage",src:h},t.margin={top:0,bottom:0,right:0,left:0},(w=d.createHtmlElement("img",y)).onload=function(){var e=d.getMultipleImage(w,t,i,!0,r);u(JSON.stringify(e))})),[2]}}))}))}))]}))}))},setCanvas:function(t,i,n,r,a,o,l){return c(this,void 0,void 0,(function(){var s,d,h,c,u;return g(this,(function(g){switch(g.label){case 0:return t.clipBounds=i,d=new e(n.x,n.y,n.width,n.height),"scaleX","scaleY","scaleOffsetX","scaleOffsetY",h=new e(i.x,i.y,i.width,i.height),this.setScaleValueforCanvas(t,i,o),[4,this.exportAsImage({clipBounds:i,margin:r,region:t.region,scaleX:t.scaleX,scaleY:t.scaleY,scaleOffsetX:t.scaleOffsetX,scaleOffsetY:t.scaleOffsetY},a,l,t,d,h,o,"Download"===t.mode)];case 1:return c=g.sent(),"SVG"!==t.format&&(u=s="JPEG"===t.format?c.toDataURL("image/jpeg"):"PNG"===t.format?c.toDataURL("image/png"):"BMP"===t.format?c.toDataURL("image/bmp"):c.toDataURL()),"Data"===t.mode?"SVG"===t.format?[2,c]:[2,s]:(this.canvasMultiplePage(t,c,r,u,a),[2,null])}}))}))},exportAsImage:function(e,t,i,n,r,a,o,l){return c(this,void 0,void 0,(function(){var s;return g(this,(function(d){switch(d.label){case 0:return[4,this.imageExport(e,t,i,n,r,a,o,l)];case 1:return(s=d.sent())instanceof Promise?(s.then((function(e){return e})),[2,s]):[2,s]}}))}))},imageExport:function(e,t,i,n,r,a,o,l){return c(this,void 0,void 0,(function(){var l;return g(this,(function(s){return l=this,document.getElementById(i),[2,new Promise((function(s,d){var h=e.clipBounds,c=e.margin,g=r.x,u=r.y,m=r.width,f=r.height,v=document.createElementNS("http://www.w3.org/2000/svg","svg");if("SVG"===n.format&&"ClipBounds"!==n.region)var p={x:String(a.x),y:String(a.y),width:String(a.width+c.right),height:String(a.height+c.bottom)};else p={x:String(a.x),y:String(a.y),width:String(a.width),height:String(a.height)};"SVG"===n.format&&v.setAttribute("transform","translate("+c.left+", "+c.top+")"),l.setAttributeSvg(v,p),p={x:String(g),y:String(u),width:String(m+c.left+c.right),height:String(f+c.top+c.bottom)};var y,w,b=document.getElementById(i+"_backgroundLayer_svg").cloneNode(!0),x=document.getElementById(i+"_diagramLayer_svg").cloneNode(!0);if(b){for(var E=0;E<b.childNodes.length;E++){if((k=b.childNodes[E]).id===i+"_backgroundImageLayer"&&(w=!0),k.id===i+"_backgroundLayer"){y=k;for(var S=k.childNodes.length-1;S>=0;S--){(B=k.childNodes[S]).id!==i+"_backgroundLayerrect"&&k.removeChild(B)}}}if(y&&b.removeChild(y),"PageSettings"!==n.region&&l.setAttributeSvg(b,p),v.appendChild(b),y){for(S=y.childNodes.length-1;S>=0;S--){var B=y.childNodes[S];w||B.setAttribute("fill","transparent"===o.background.background?"white":o.background.background)}v.appendChild(y)}if("PageSettings"!==n.region){l.setTransform(b,a);for(E=0;E<b.childNodes.length;E++){(k=b.childNodes[E]).id!==i+"_backgroundImageLayer"&&k.id!==i+"_backgroundLayer"||l.setTransform(k,a)}}}v.appendChild(x);for(E=0;E<x.childNodes.length;E++){var k;(k=x.childNodes[E]).id===i+"_diagramlayer"&&l.setTransform(k,a)}var C=window.URL.createObjectURL(new Blob([(new window.XMLSerializer).serializeToString(v)],{type:"image/svg+xml"}));if("SVG"===n.format){var I;"Data"===n.mode&&"SVG"===n.format&&(I=(new window.XMLSerializer).serializeToString(v),s(I));var P=[],A=(new window.XMLSerializer).serializeToString(v);P.push(A);var D,N=0;if("Download"===n.mode)for(N=0;N<P.length;N++)D=new Blob([P[N]],{type:"application/octet-stream"}),"msie"===sf.base.Browser.info.name?window.navigator.msSaveOrOpenBlob(D,t+".svg"):l.triggerDownload("SVG",t,C)}else{var L=document.createElement("canvas"),T=L.getContext("2d"),M=new Image;M.onload=function(){L.height="Content"==e.region?M.height:h.height,L.width="Content"==e.region?M.width:h.width,o.background.imageSource?T.fillStyle="transparent":T.fillStyle="transparent"===o.background.background?"white":o.background.background,T.fillRect(0,0,L.width,L.height),n.fitToPage?T.drawImage(M,c.left,c.top,L.width-(c.left+c.right),L.height-(c.top+c.bottom)):T.drawImage(M,0,0,L.width,L.height),window.URL.revokeObjectURL(C),s(L)},M.src=C}}))]}))}))},triggerDownload:function(e,t,i){var n=document.createElement("a");n.download=t+"."+e.toLocaleLowerCase(),n.href=i,n.click()},canvasMultiplePage:function(e,t,i,n,r){var a=this,o=[],l=n.substring(n.indexOf(":")+1,n.indexOf(";")),s=l.substring(l.indexOf("/")+1),d=(s="jpeg"===s?void 0:s.toUpperCase())||"JPG";if(e.fitToPage)o=[n],this.exportImage(o,r,d,n);else{if(e.pageHeight=e.pageHeight?e.pageHeight:(void 0).height,e.pageWidth=e.pageWidth?e.pageWidth:(void 0).width,e.pageHeight=e.pageHeight?e.pageHeight:t.width,e.pageWidth=e.pageWidth?e.pageWidth:t.height,i=e.margin||{},e.orientation&&("Landscape"===e.orientation&&e.pageHeight>e.pageWidth||"Portrait"===e.orientation&&e.pageWidth>e.pageHeight)){var h=e.pageWidth;e.pageWidth=e.pageHeight,e.pageHeight=h}e.margin={top:isNaN(i.top)?0:i.top,bottom:isNaN(i.bottom)?0:i.bottom,left:isNaN(i.left)?0:i.left,right:isNaN(i.right)?0:i.right};var c={id:void 0+"_printImage",src:n},g=this.createHtmlElement("img",c);g.onload=function(){o=a.getMultipleImage(g,e,!0),a.exportImage(o,r,d,n)}}},exportImage:function(e,t,i,n){for(var r=[],a=e instanceof HTMLElement?0:e.length,o=0;o<a;o++){for(var l=e[o].replace(/^data:[a-z]*;,/,"").split(","),s=atob(l[1]),d=new ArrayBuffer(s.length),h=new Uint8Array(d),c=0;c<s.length;c++)h[c]=s.charCodeAt(c);r.push(d)}for(var g=0;g<r.length;g++){var u=new Blob([r[g]],{type:"application/octet-stream"});if("msie"===sf.base.Browser.info.name)window.navigator.msSaveOrOpenBlob(u,t+"."+i);else{var m=window.URL.createObjectURL(u),f=document.createElement("a");f.download=t+"."+i.toLocaleLowerCase(),f.href=m,f.click()}}},setScaleValueforCanvas:function(e,t,i){var n="scaleX";e[n]=1,e.scaleY=1,e.scaleOffsetX=0,e.scaleOffsetY=0,e.pageHeight=e.pageHeight||i.height,e.pageWidth=e.pageWidth||i.width;var r=e.orientation||i.orientation;if(r||(r="Portrait"),"Portrait"===r){if(e.pageWidth>e.pageHeight){var a=e.pageHeight;e.pageHeight=e.pageWidth,e.pageWidth=a}}else if(e.pageHeight>e.pageWidth){a=e.pageWidth;e.pageWidth=e.pageHeight,e.pageHeight=a}e.pageWidth&&e.pageHeight&&e.fitToPage&&(e.stretch="Meet");var o=e.pageHeight||t.height,l=e.pageWidth||t.width;"Stretch"!==e.stretch&&"Meet"!==e.stretch&&"Slice"!==e.stretch||(e[n]=l/t.width,e.scaleY=o/t.height,"Meet"===e.stretch?(e[n]=e.scaleY=Math.min(e[n],e.scaleY),e.scaleOffsetY=(e.pageHeight-t.height*e[n])/2,e.scaleOffsetX=(e.pageWidth-t.width*e[n])/2):"Slice"===e.stretch&&(e[n]=e.scaleY=Math.max(e[n],e.scaleY)),t.width=l,t.height=o),t.x*=e[n],t.y*=e.scaleY},setTransform:function(e,t){e.setAttribute("transform","translate("+-t.x+", "+-t.y+")")},getPrintCanvasStyle:function(e,i){var n=0,r=0,a=new t;if(n=e.width,r=e.height,(i.pageHeight||i.pageWidth)&&(r=i.pageHeight?i.pageHeight:r,n=i.pageWidth?i.pageWidth:n),i.orientation&&("Landscape"===i.orientation&&r>n||"Portrait"===i.orientation&&n>r)){var o=n;n=r,r=o}return a.height=r,a.width=n,a},getMultipleImage:function(e,t,i){var n=[],r=this.createHtmlElement("div",{}),a=this.getPrintCanvasStyle(e,t),o=t.margin,l=o.left,s=o.top,d=o.right,h=o.bottom,c=0,g=a.width+c,u=a.height+0,m=0,f=0;if(t&&!t.fitToPage){r.style.height="auto",r.style.width="auto";var v=e.height,p=e.width,y=0,w=0,b=void 0,x=0,E=0,S=void 0,B=void 0;do{do{x=a.width,E=a.height,m+a.width>=p&&(x=p-m),f+a.height>=v&&(E=v-f),(S=(B=this.createCanvas(void 0+"_multiplePrint",a.width,a.height)).getContext("2d")).fillStyle="white",S.fillRect(0,0,a.width,a.height),S.drawImage(e,c+m,0+f,x,E,l,s,x-d-l,E-h-s),m+a.width>=p&&(m-=m-p),b="PNG"===t.format?B.toDataURL("image/png"):"JPEG"===t.format?B.toDataURL("image/jpeg"):B.toDataURL(),S.restore(),m+=g,i?n.push(b):this.printImage(r,b,y+""+w,g+"px;",u+"px;"),y++}while(m<p);w++,y=c=m=0,f+a.height>=v&&(f-=f-v),f+=u}while(f<v)}else{var k=this.getPrintCanvasStyle(e,t),C=k.width,I=k.height;(S=(B=this.createCanvas(void 0+"_diagram",C,I)).getContext("2d")).drawImage(e,0+l,0+s,e.width-(d+l),e.height-(s+h),0+l,0+s,C-(d+l),I-(s+h));b="PNG"===t.format?B.toDataURL("image/png"):"JPEG"===t.format?B.toDataURL("image/jpeg"):B.toDataURL();S.restore(),i?n.push(b):this.printImage(r,b,0)}return i?n:r},printImage:function(e,t,i,n,a){var o={class:"e-diagram-print-page",style:"width:"+n+"height:"+a},l=this.createHtmlElement("img",o),s=this.createHtmlElement("div",o);r(l,o={id:void 0+"_multiplePrint_img"+i,style:"float:left",src:t}),s.appendChild(l),e.appendChild(s)},print:function(e,t,i,n){return c(this,void 0,void 0,(function(){var r,a;return g(this,(function(o){switch(o.label){case 0:return r={width:i.width,height:i.height},e.mode="Data",n=n,t=t,[4,this.exportDiagram(e,t,i,n,!0,void 0,void 0,!1)];case 1:return a=o.sent(),e.fitToPage&&(e.pageWidth=r.width,e.pageHeight=r.height),this.printImages(a,e),[2,a]}}))}))},onBeforeWindowUnload:function(){d&&!d.closed&&(d.close(),window.removeEventListener("beforeunload",this.onBeforeWindowUnload))},onBeforePrintWindowUnload:function(){d&&(d.removeEventListener("beforeunload",this.onBeforePrintWindowUnload),window.removeEventListener("beforeunload",this.onBeforeWindowUnload))},printImages:function(e,t){var i=this,n={id:void 0+"_printImage",src:e};t.margin={top:0,bottom:0,right:0,left:0};var r=this.createHtmlElement("img",n);r.onload=function(){var e=i.getMultipleImage(r,t),n=window.open("");d=n,null!=n&&e instanceof HTMLElement&&(n.document.write("<html><head><style> body{margin:0px;}  @media print { .e-diagram-print-page{page-break-after: left; }.e-diagram-print-page:last-child {page-break-after: avoid;}}</style><title></title></head>"),n.addEventListener("load",(function(e){setTimeout((function(){n.window.print()}),3e3)})),n.document.write("<center>"+e.innerHTML+"</center>"),n.document.close(),d.addEventListener("beforeunload",i.onBeforePrintWindowUnload),window.addEventListener("beforeunload",i.onBeforeWindowUnload))}},exportImages:function(e,t){var i=this,n=t&&t.region?t.region:"Content",a=t.margin||{};a={top:isNaN(a.top)?0:a.top,bottom:isNaN(a.bottom)?0:a.bottom,left:isNaN(a.left)?0:a.left,right:isNaN(a.right)?0:a.right};var o=this.getDiagramBounds(n,{});t.clipBounds&&(o.x=isNaN(t.clipBounds.x)?o.x:t.clipBounds.x,o.y=isNaN(t.clipBounds.y)?o.y:t.clipBounds.y,o.width=t.clipBounds.width||o.width,o.height=t.clipBounds.height||o.height);var l=document.createElement("img");r(l,{src:e});var s=this;l.onload=function(){var n=i.createCanvas(s.Diagram.id+"innerImage",o.width+(a.left+a.right),o.height+(a.top+a.bottom)),r=n.getContext("2d");if(r.fillStyle=s.diagram.pageSettings.background.color,r.fillRect(0,0,o.width+(a.left+a.right),o.height+(a.top+a.bottom)),r.drawImage(l,0,0,o.width,o.height,a.left,a.top,o.width,o.height),e=n.toDataURL(),t.printOptions)s.printImages(e,t);else{r.restore();var d=t.fileName||"diagram";i.canvasMultiplePage(t,n,a,e,d)}}},overviewModelBounds:function(){var e,t,i=document.getElementById("overview"),n=i.clientWidth,r=i.clientHeight,a=i.getBoundingClientRect(),o=window.screenX<0?-1*window.screenX:window.screenX,l=window.screenY<0?-1*window.screenY:window.screenY;if(0===n){var s=Math.floor(window.innerWidth-o-Math.floor(a.left));n=s>0?s:Math.floor(window.innerWidth)}if(0===r){var d=Math.floor(window.innerHeight-l-Math.floor(a.top));r=d>0?d:Math.floor(window.innerHeight)}return n>0&&(e=r),r>0&&(t=n),{height:e,width:t}},invokeOverviewEvents:function(e){var t=document.getElementById("overview"),i=this;t&&(t.addEventListener("mousedown",(function(t){i.overviewMouseEventArgs(t,e)})),t.addEventListener("mouseup",(function(t){i.overviewMouseEventArgs(t,e)})),t.addEventListener("mousemove",(function(t){i.overviewMouseEventArgs(t,e)})),t.addEventListener("mouseleave",(function(t){i.overviewMouseEventArgs(t,e)})),t.addEventListener("touchstart",(function(t){i.overviewMouseEventArgs(t,e)})),t.addEventListener("touchmove",(function(t){i.overviewMouseEventArgs(t,e)})),t.addEventListener("touchend",(function(t){i.overviewMouseEventArgs(t,e)})))},diagramOverview:function(e,t,i,n,r){var a=document.getElementById(e+"_diagramLayer_svg").cloneNode(!0),o=document.createElementNS("http://www.w3.org/2000/svg","svg");n.scale=1;var l=document.getElementById(e).clientHeight,s=document.getElementById(e).clientWidth,d=Math.max(Number(t.width),s),h=Math.max(Number(t.height),l).toString(),c=d.toString();i>=1&&(a.setAttribute("style","pointer-events: all;height:"+h+"px;width:"+c+"px;"),a.children[0].setAttribute("transform","translate("+-t.x+","+-t.y+"),scale("+n.scale+")")),o.setAttribute("height",h),o.setAttribute("width",c),o.appendChild(a);var g=window.URL.createObjectURL(new Blob([(new window.XMLSerializer).serializeToString(o)],{type:"image/svg+xml"})),u=document.getElementById("overview_diagramlayer"),m=u.getContext("2d"),f=new Image;f.onload=function(){m.fillStyle="transparent",m.setTransform(1,0,0,1,0,0),m.clearRect(0,0,u.width,u.height),m.setTransform(r,0,0,r,0,0),m.drawImage(f,0,0),window.URL.revokeObjectURL(g)},f.src=g;for(var v=document.getElementById(e+"_htmlLayer_div").cloneNode(!0),p=document.getElementById("overview_htmllayer_div");p.firstChild;)p.firstChild.remove();for(var y=0;y<v.children.length;y++){var w=v.children[y].cloneNode(!0);p.appendChild(w)}},overviewMouseEventArgs:function(e,t){var i=document.getElementById("overview"),n={altKey:e.altKey,shiftKey:e.shiftKey,ctrlKey:e.ctrlKey,detail:e.detail,metaKey:e.metaKey,screenX:e.screenX,screenY:e.screenY,clientX:e.clientX,clientY:e.clientY,offsetX:e.offsetX,offsetY:e.offsetY,type:e.type,key:e.key,keyCode:e.keyCode,button:e.button,targetId:e.target.id,overviewGetBoundingClientRect:i.getBoundingClientRect(),targetCssClass:e.target.classList.value,touches:this.getTouches(e),offsetLeft:i.offsetLeft,offsetTop:i.offsetTop};t.invokeMethodAsync("InvokeOverviewEvents",n)}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfdiagramcomponent');})})();