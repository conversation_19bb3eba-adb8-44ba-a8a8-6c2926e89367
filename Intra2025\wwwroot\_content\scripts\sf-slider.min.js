/*!*  filename: sf-slider.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[54],{"./bundles/sf-slider.js":function(t,e,i){"use strict";i.r(e);i("./modules/sf-slider.js")},"./modules/sf-slider.js":function(t,e){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Slider=function(){"use strict";var t="e-handle-focused",e="e-handle-active",i="Horizontal",s=function(){function s(t,e,i,s){this.activeHandle=1,this.initialRendering=!0,this.isClicked=!1,this.isSliderMove=!1,this.isDragRange=!1,this.isTabPressed=!1,this.isKeyPressed=!1,this.transition={handle:"left .4s cubic-bezier(.25, .8, .25, 1), right .4s cubic-bezier(.25, .8, .25, 1), top .4s cubic-bezier(.25, .8, .25, 1) , bottom .4s cubic-bezier(.25, .8, .25, 1)",rangeBar:"all .4s cubic-bezier(.25, .8, .25, 1)",scaleTransform:"transform .4s cubic-bezier(.25, .8, .25, 1)"},this.isDestroyed=!1,this.element=e,this.dataId=t,window.sfBlazor.setCompInstance(this),this.dotNetRef=i,this.options=s,this.initialRendering=!0,null===this.options.Value&&(this.options.Value="Range"!==this.options.Type?this.options.Min:[this.options.Min,this.options.Max]),this.options.CustomValues&&(this.options.Min=0,this.options.Max=s.CustomValues.length-1)}return s.prototype.initialize=function(){if(this.sliderContainer=this.element.parentElement,this.sliderTrack=this.element.querySelector(".e-slider-track"),this.hiddenInput=this.element.parentElement.querySelector(".e-slider-input"),this.setElementWidth(this.options.Width),this.setHandler(),this.setZindex(),this.isMaterial="material"===this.getTheme(this.sliderContainer),this.isMaterial3="Material3"===this.getTheme(this.sliderContainer),sf.base.isNullOrUndefined(this.materialHandle)&&(this.isMaterial||this.isMaterial3)&&null!==this.options.Tooltip&&"Range"!==this.options.Type){this.materialHandle=document.createElement("div"),this.materialHandle.className="e-handle e-material-handle";var t=this.element.querySelector(".e-material-handle");sf.base.isNullOrUndefined(t)||this.element.removeChild(t),this.element.appendChild(this.materialHandle)}return this.setValue(!1),this.updateColorRangeBarPos(),this.initialRendering&&this.options.Enabled&&!this.options.ReadOnly&&(this.wireEvents(),this.initialRendering=!1),this.getTheme(this.sliderContainer)},s.prototype.rangeBarMousedown=function(t){var e,i;if("mousedown"===t.type&&t.preventDefault(),this.changedEventValue=this.options.Value,"Range"===this.options.Type&&t.target===this.rangeBar){var s=void 0,n=void 0;"mousedown"===t.type?(s=(e=[t.clientX,t.clientY])[0],n=e[1]):"touchstart"===t.type&&(s=(i=[t.changedTouches[0].clientX,t.changedTouches[0].clientY])[0],n=i[1]),"Horizontal"===this.options.Orientation?(this.firstPartRemain=s-this.rangeBar.getBoundingClientRect().left,this.secondPartRemain=this.rangeBar.getBoundingClientRect().right-s):(this.firstPartRemain=n-this.rangeBar.getBoundingClientRect().top,this.secondPartRemain=this.rangeBar.getBoundingClientRect().bottom-n),this.minDiff=this.handleVal2-this.handleVal1;var a=this.element.querySelector(".e-tab-handle");a&&a.classList.remove("e-tab-handle"),sf.base.EventHandler.add(document,"mousemove touchmove",this.dragRangeBarMove,this),sf.base.EventHandler.add(document,"mouseup touchend",this.dragRangeBarUp,this)}},s.prototype.dragRangeBarUp=function(t){this.triggerChangeEvent(this.changedEventValue,t,!0),sf.base.EventHandler.remove(document,"mousemove touchmove",this.dragRangeBarMove),sf.base.EventHandler.remove(document,"mouseup touchend",this.dragRangeBarUp),this.isDragRange=!0},s.prototype.triggerChangeEvent=function(t,e,i){if(null!==this.options.Events){var s="touchend"===e.type||e.currentTarget===document;this.options.Events.onChange.hasDelegate&&s&&!this.options.IsImmediateValue&&this.isValueChange(t)&&this.invokeEvent(t,!1),(s||i)&&this.options.Events.valueChange.hasDelegate&&this.isValueChange(t)&&(this.invokeEvent(t,!0),this.changedEventValue=this.options.Value)}},s.prototype.invokeEvent=function(t,e){this.dotNetRef.invokeMethodAsync("TriggerEvent",{PreviousValue:t,Value:this.options.Value,isValueChanged:e})},s.prototype.handleValueAdjust=function(t,e,i){1===i?(this.handleVal1=e,this.handleVal2=this.handleVal1+this.minDiff):2===i&&(this.handleVal2=e,this.handleVal1=this.handleVal2-this.minDiff),this.handlePos1=this.checkHandlePosition(this.handleVal1),this.handlePos2=this.checkHandlePosition(this.handleVal2)},s.prototype.getLimitValueAndPosition=function(t,e,i,s){return sf.base.isNullOrUndefined(e)&&(e=this.options.Min,sf.base.isNullOrUndefined(t)&&s&&(t=e)),sf.base.isNullOrUndefined(i)&&(i=this.options.Max,sf.base.isNullOrUndefined(t)&&s&&(t=i)),t<e&&(t=e),t>i&&(t=i),[t,this.checkHandlePosition(t)]},s.prototype.dragRangeBarMove=function(t){var e,i,s,n,a;"touchmove"!==t.type&&t.preventDefault(),this.isDragRange=!0,this.rangeBar.style.transition="none",this.firstHandle.style.transition="none",this.secondHandle.style.transition="none","mousemove"===t.type?(n=(e=[t.clientX,t.clientY])[0],a=e[1]):(n=(i=[t.changedTouches[0].clientX,t.changedTouches[0].clientY])[0],a=i[1]),"mousemove"===t.type?s={x:t.clientX,y:t.clientY}:"touchmove"!==t.type&&"touchstart"!==t.type||(s={x:t.changedTouches[0].clientX,y:t.changedTouches[0].clientY});var o=this.options.Limits;if(sf.base.isNullOrUndefined(o)||(!o.enabled||!o.startHandleFixed)&&(!o.enabled||!o.endHandleFixed)){if(s=this.options.EnableRtl?{x:n+this.secondPartRemain,y:a+this.secondPartRemain}:{x:n-this.firstPartRemain,y:a+this.secondPartRemain},this.options.Min>this.options.Max?(this.handlePos2=this.xyToPosition(s),this.handleVal2=this.positionToValue(this.handlePos2)):(this.handlePos1=this.xyToPosition(s),this.handleVal1=this.positionToValue(this.handlePos1)),s=this.options.EnableRtl?{x:n-this.firstPartRemain,y:a-this.firstPartRemain}:{x:n+this.secondPartRemain,y:a-this.firstPartRemain},this.options.Min>this.options.Max?(this.handlePos1=this.xyToPosition(s),this.handleVal1=this.positionToValue(this.handlePos1)):(this.handlePos2=this.xyToPosition(s),this.handleVal2=this.positionToValue(this.handlePos2)),!sf.base.isNullOrUndefined(this.options.Limits)&&this.options.Limits.enabled){var h=this.getLimitValueAndPosition(this.handleVal1,o.minStart,o.minEnd);this.handleVal1=h[0],this.handlePos1=h[1],this.handleVal1===o.minEnd&&this.handleValueAdjust(this.handleVal1,o.minEnd,1),this.handleVal1===o.minStart&&this.handleValueAdjust(this.handleVal1,o.minStart,1),h=this.getLimitValueAndPosition(this.handleVal2,o.maxStart,o.maxEnd),this.handleVal2=h[0],this.handlePos2=h[1],this.handleVal2===o.maxStart&&this.handleValueAdjust(this.handleVal2,o.maxStart,2),this.handleVal2===o.maxEnd&&this.handleValueAdjust(this.handleVal2,o.maxEnd,2)}this.handleVal2===(this.options.Min>this.options.Max?this.options.Min:this.options.Max)&&this.handleValueAdjust(this.handleVal2,this.options.Min>this.options.Max?this.options.Min:this.options.Max,2),this.handleVal1===(this.options.Min>this.options.Max?this.options.Max:this.options.Min)&&this.handleValueAdjust(this.handleVal1,this.options.Min>this.options.Max?this.options.Max:this.options.Min,1)}var l=this.options.IsImmediateValue?this.previousHandleVal:this.changedEventValue?this.changedEventValue:this.previousHandleVal;this.setHandlePosition(t),this.updateValue(),"Default"!==this.options.Type&&(this.rangeBar.style.transition=this.transition.rangeBar,this.setRangeBarPosition()),null!==this.options.Events&&this.options.Events.onChange.hasDelegate&&this.isValueChange(l)&&this.options.IsImmediateValue&&this.invokeEvent(l,!1)},s.prototype.wireEvents=function(){sf.base.EventHandler.add(this.element,"click",this.clickHandler,this),sf.base.EventHandler.add(this.element,"keyup",this.keyUp,this),sf.base.EventHandler.add(this.element,"focusout",this.focusOut,this),"Range"===this.options.Type&&sf.base.EventHandler.add(this.rangeBar,"mousedown touchstart",this.rangeBarMousedown,this),this.onResize=this.reposition.bind(this),window.addEventListener("resize",this.onResize),this.wireFirstHandleEventArgs(),"Range"===this.options.Type&&this.wireSecondHandleEventArgs()},s.prototype.wireFirstHandleEventArgs=function(){sf.base.EventHandler.add(this.firstHandle,"mousedown touchstart",this.handleFocus,this),sf.base.EventHandler.add(this.firstHandle,"mouseup touchend",this.sliderBarUp,this)},s.prototype.wireSecondHandleEventArgs=function(){sf.base.EventHandler.add(this.secondHandle,"mousedown touchstart",this.handleFocus,this),sf.base.EventHandler.add(this.secondHandle,"mouseup touchend",this.sliderBarUp,this)},s.prototype.unWireEvents=function(){sf.base.EventHandler.remove(this.element,"click",this.clickHandler),sf.base.EventHandler.remove(this.element,"keyup",this.keyUp),sf.base.EventHandler.remove(this.element,"focusout",this.focusOut),"Range"===this.options.Type&&sf.base.EventHandler.remove(this.rangeBar,"mousedown touchstart",this.rangeBarMousedown),window.removeEventListener("resize",this.onResize),this.unWireFirstHandleEventArgs(),"Range"===this.options.Type&&this.unWireSecondHandleEventArgs()},s.prototype.unWireFirstHandleEventArgs=function(){sf.base.EventHandler.remove(this.firstHandle,"mousedown touchstart",this.handleFocus),sf.base.EventHandler.remove(this.firstHandle,"mouseup touchend",this.sliderBarUp)},s.prototype.unWireSecondHandleEventArgs=function(){sf.base.EventHandler.remove(this.secondHandle,"mousedown touchstart",this.handleFocus),sf.base.EventHandler.remove(this.secondHandle,"mouseup touchend",this.sliderBarUp)},s.prototype.isValueChange=function(t){return"Range"===this.options.Type?t[0]!==this.options.Value[0]||t[1]!==this.options.Value[1]:t!==this.options.Value},s.prototype.focusOut=function(){var t=this;setTimeout((function(){t.isDestroyed||(null!==t.options.Tooltip&&t.options.Tooltip.isVisible&&("Range"===t.options.Type||sf.base.isNullOrUndefined(t.materialHandle)||(t.materialHandle.style.transform="scale(1)"),t.dotNetRef.invokeMethodAsync("CloseTooltip")),t.isTabPressed&&t.handleFocusOut())}),10)},s.prototype.reposition=function(){this.initialRendering=!1,this.initialize()},s.prototype.handleValueUpdate=function(){return"Range"===this.options.Type?1===this.activeHandle?this.handleVal1:this.handleVal2:this.handleVal1},s.prototype.handleFocusOut=function(){if(this.firstHandle.classList.contains(t)&&this.firstHandle.classList.remove(t),this.firstHandle.classList.contains(e)&&this.firstHandle.classList.remove(e),this.firstHandle.classList.contains("e-tab-handle")&&this.firstHandle.classList.remove("e-tab-handle"),"Range"===this.options.Type&&(this.secondHandle.classList.contains(t)&&this.secondHandle.classList.remove(t),this.secondHandle.classList.contains(e)&&this.secondHandle.classList.remove(e),this.secondHandle.classList.contains("e-tab-handle")&&this.secondHandle.classList.remove("e-tab-handle")),this.isTabPressed){this.isTabPressed=!1;var i=sf.base.select(".e-tab-handle",this.element);i&&sf.base.removeClass([i],"e-tab-handle")}},s.prototype.keyUp=function(i){var s=i.target;if(9===i.keyCode&&s.classList.contains("e-handle")){if(this.isTabPressed=!0,!s.classList.contains("e-tab-handle")){var n=sf.base.select(".e-tab-handle",this.element);if(n&&sf.base.removeClass([n],"e-tab-handle"),sf.base.addClass([s],"e-tab-handle"),"Range"===this.options.Type){var a=s.previousElementSibling;a&&a.classList?this.activeHandle=a.classList.contains("e-handle")?2:1:this.activeHandle=1}}this.changedEventValue=this.previousHandleVal,s===this.firstHandle?sf.base.addClass([this.firstHandle],[t,e,"e-tab-handle"]):sf.base.addClass([this.secondHandle],[t,e,"e-tab-handle"])}},s.prototype.handleFocus=function(i){this.element.focus(),this.getHandlePosition(i),this.changedEventValue=this.previousHandleVal,this.sliderBarClick(),i.currentTarget===this.firstHandle?(this.firstHandle.classList.add(t),this.firstHandle.classList.add(e),this.firstHandle.classList.add("e-tab-handle")):(this.secondHandle.classList.add(t),this.secondHandle.classList.add(e),this.secondHandle.classList.add("e-tab-handle")),sf.base.EventHandler.add(document,"mousemove touchmove",this.sliderBarMove,this),sf.base.EventHandler.add(document,"mouseup touchend",this.sliderBarUp,this)},s.prototype.sliderBarMove=function(t){if("touchmove"===t.type&&t.target.parentElement===this.element||"mousemove"===t.type){"touchmove"!==t.type&&t.preventDefault(),this.isSliderMove=!0;var e=void 0;"mousemove"===t.type?e={x:t.clientX,y:t.clientY}:"touchmove"!==t.type&&"touchstart"!==t.type||(e={x:t.changedTouches[0].clientX,y:t.changedTouches[0].clientY}),this.handlePos=this.xyToPosition(e),this.handleVal=this.positionToValue(this.handlePos),this.handlePos=this.checkHandlePosition(this.handleVal),this.firstHandle.style.transition=this.transition.scaleTransform,"Range"===this.options.Type&&(this.secondHandle.style.transition=this.transition.scaleTransform),this.rangeBar&&(this.rangeBar.style.transition="none"),"Range"===this.options.Type?-1===this.previousHandleVal.indexOf(this.handleVal)&&(1===this.activeHandle&&this.handleVal<=this.handleVal2||2===this.activeHandle&&this.handleVal>=this.handleVal1)&&(null!=this.options.Events&&this.options.Events.onChange.hasDelegate&&this.options.IsImmediateValue?this.callChangeEvent(!1):this.sliderBarClick()):this.previousHandleVal!==this.handleVal&&(null!==this.options.Events&&this.options.Events.onChange.hasDelegate&&this.options.IsImmediateValue?this.callChangeEvent(!1):this.sliderBarClick())}},s.prototype.callChangeEvent=function(t){var e=this.options.IsImmediateValue?this.previousHandleVal:this.changedEventValue?this.changedEventValue:this.previousHandleVal;this.sliderBarClick(),this.isValueChange(e)&&this.invokeEvent(e,t)},s.prototype.updateHandleAttributes=function(t,e){"aria-valuenow"===t?"Range"===this.options.Type?(this.firstHandle.setAttribute(t,e[0]),this.secondHandle.setAttribute(t,e[1])):this.firstHandle.setAttribute(t,e[0]):(this.firstHandle.setAttribute(t,e[0]),"Range"===this.options.Type&&this.secondHandle.setAttribute(t,e[0]))},s.prototype.getTheme=function(t){return window.getComputedStyle(t,":after").getPropertyValue("content").replace(/['"]+/g,"")},s.prototype.setZindex=function(){sf.base.isNullOrUndefined(this.options.Ticks)||"None"===this.options.Ticks.placement||(this.element.getElementsByTagName("ul")[0].style.zIndex="-1",this.element.style.zIndex="8"),"material"!==this.getTheme(this.sliderContainer)&&"Material3"!==this.getTheme(this.sliderContainer)||sf.base.isNullOrUndefined(this.options.Ticks)||"Both"!==this.options.Ticks.placement||(this.element.style.zIndex="8"),this.firstHandle.style.zIndex="9","Range"===this.options.Type&&(this.secondHandle.style.zIndex="10")},s.prototype.sliderBarUp=function(t){this.handleFocusOut(),sf.base.EventHandler.remove(document,"mousemove touchmove",this.sliderBarMove),sf.base.EventHandler.remove(document,"mouseup touchend",this.sliderBarUp),this.isSliderMove=!1,this.transitionNode(),this.triggerChangeEvent(this.changedEventValue,t,!1)},s.prototype.transitionNode=function(){this.firstHandle.style.transition="none","Range"===this.options.Type&&(this.secondHandle.style.transition="none"),this.rangeBar&&(this.rangeBar.style.transition="none")},s.prototype.clickHandler=function(t){if(this.isDragRange)this.isDragRange=!1;else{t.preventDefault();var e=this.changedEventValue;if(this.getHandlePosition(t),null!=this.options.Events&&this.options.Events.onChange.hasDelegate||null!=this.options.Events&&this.options.Events.valueChange.hasDelegate?(this.isClicked=!0,this.sliderBarClick(),this.options.Events.onChange.hasDelegate&&this.callChangeEvent(!1),e=this.isSliderMove?this.previousHandleVal:this.changedEventValue?this.changedEventValue:this.previousHandleVal,this.triggerChangeEvent(e,t,!0)):this.sliderBarClick(),this.hiddenInput&&this.hiddenInput.name&&this.hiddenInput.name.includes("imageEditor")&&this.changedEventValue!==this.previousHandleVal){var i=new Event("custom-changed",{bubbles:!0,cancelable:!0});this.hiddenInput.dispatchEvent(i)}this.isClicked&&(this.previousHandleVal=this.options.Value),this.changedEventValue=this.previousHandleVal,this.isClicked=!1}},s.prototype.updateNewHandleValue=function(t,e,i){return(i[0]||i[1])&&(t<i[0]?(t=i[0],e=this.checkHandlePosition(t)):t>i[1]&&(t=i[1],e=this.checkHandlePosition(t))),[t,e]},s.prototype.sliderBarClick=function(){if("Range"!==this.options.Type||1===this.activeHandle&&this.handleVal1<=this.handleVal2||2===this.activeHandle&&this.handleVal1<=this.handleVal2){if(this.firstHandle.style.transition=this.transition.handle,"Range"===this.options.Type&&(this.secondHandle.style.transition=this.transition.handle),this.rangeBar&&(this.rangeBar.style.transition=this.transition.rangeBar),this.options.Limits){var t=void 0;if(1!==this.activeHandle||this.options.Limits.startHandleFixed){if(2===this.activeHandle&&!this.options.Limits.endHandleFixed){t=[this.options.Limits.maxStart,this.options.Limits.maxEnd];e=this.updateNewHandleValue(this.handleVal,this.handlePos,t);this.updateHandleValue(this.secondHandle,e[0],e[1])}}else{t=[this.options.Limits.minStart,this.options.Limits.minEnd];var e=this.updateNewHandleValue(this.handleVal,this.handlePos,t);this.updateHandleValue(this.firstHandle,e[0],e[1],!0),!this.isMaterial&&!this.isMaterial3||sf.base.isNullOrUndefined(this.materialHandle)||"Range"===this.options.Type||this.updateHandleValue(this.materialHandle,e[0],e[1],1===this.activeHandle)}}else{var i=1===this.activeHandle?this.firstHandle:this.secondHandle;this.updateHandleValue(i,this.handleVal,this.handlePos,1===this.activeHandle),!this.isMaterial&&!this.isMaterial3||sf.base.isNullOrUndefined(this.materialHandle)||"Range"===this.options.Type||this.updateHandleValue(this.materialHandle,this.handleVal,this.handlePos,1===this.activeHandle)}"Default"!==this.options.Type&&this.setRangeBarPosition(),this.updateValue()}},s.prototype.updateHandleValue=function(t,e,i,s){void 0===s&&(s=!1),this.applyHandlePosition(t,i),s?(this.handleVal1=e,this.handlePos1=i):(this.handleVal2=e,this.handlePos2=i),this.updateAriaValue()},s.prototype.updateAriaValue=function(){"Range"!==this.options.Type?this.options.CustomValues?this.updateHandleAttributes("aria-valuenow",[this.options.CustomValues[this.handleVal1].toString()]):this.updateHandleAttributes("aria-valuenow",[this.handleVal1.toString()]):this.options.CustomValues?this.updateHandleAttributes("aria-valuenow",[this.options.CustomValues[this.handleVal1].toString(),this.options.CustomValues[this.handleVal2].toString()]):this.updateHandleAttributes("aria-valuenow",[this.handleVal1.toString(),this.handleVal2.toString()])},s.prototype.positionToValue=function(t){var e,s=parseFloat(sf.base.formatUnit(this.options.Max))-parseFloat(sf.base.formatUnit(this.options.Min));return e=this.options.Orientation===i?t/this.element.getBoundingClientRect().width*s:t/this.element.getBoundingClientRect().height*s,this.add(e,parseFloat(this.options.Min.toString()),!0)},s.prototype.add=function(t,e,i){var s=Math.pow(10,3);return i?(Math.round(t*s)+Math.round(e*s))/s:(Math.round(t*s)-Math.round(e*s))/s},s.prototype.setElementWidth=function(t){sf.base.isNullOrUndefined(t)||("number"==typeof t?this.sliderContainer.style.width=sf.base.formatUnit(t):"string"==typeof t&&(this.sliderContainer.style.width=t.match(/px|%|em/)?t:sf.base.formatUnit(t)))},s.prototype.xyToPosition=function(t){var e=this.element.getBoundingClientRect();if(this.options.Min===this.options.Max)return 100;if(this.options.Orientation===i){var s=t.x-e.left,n=this.element.offsetWidth/100;this.posvalue=s/n}else{var a=t.y-e.top;n=this.element.offsetHeight/100;this.posvalue=100-a/n}var o=this.stepValueCalculation(this.posvalue);return o<0?o=0:o>100&&(o=100),this.options.EnableRtl&&this.options.Orientation===i&&(o=100-o),this.options.Orientation===i?e.width*(o/100):e.height*(o/100)},s.prototype.stepValueCalculation=function(t){0===this.options.Step&&(this.options.Step=1);var e=parseFloat(sf.base.formatUnit(this.options.Step))/((parseFloat(sf.base.formatUnit(this.options.Max))-parseFloat(sf.base.formatUnit(this.options.Min)))/100),i=t%Math.abs(e);return 0!==i&&(e/2>i?t-=i:t+=Math.abs(e)-i),t},s.prototype.setHandler=function(){this.createFirstHandle(),this.createSecondHandle()},s.prototype.createSecondHandle=function(){this.secondHandle=this.element.querySelector(".e-handle-second")},s.prototype.createFirstHandle=function(){this.firstHandle=this.element.querySelector(".e-handle-first")},s.prototype.setValue=function(t){void 0===t&&(t=!0);var e="Range"!==this.options.Type?this.options.Value:this.options.Value[0];if(this.handleVal1=sf.base.isNullOrUndefined(this.options.Value)?this.checkHandleValue(parseFloat(this.options.Min.toString())):this.checkHandleValue(parseFloat(e.toString())),this.handlePos1=this.checkHandlePosition(this.handleVal1),null!=this.options.Limits){var i=this.updateNewHandleValue(this.handleVal1,this.handlePos1,[this.options.Limits.minStart,this.options.Limits.minEnd]);this.handleVal1=i[0],this.handlePos1=i[1]}if("Range"===this.options.Type&&(this.handleVal2=sf.base.isNullOrUndefined(this.options.Value)?this.checkHandleValue(parseFloat(this.options.Max.toString())):this.checkHandleValue(parseFloat(this.options.Value[1].toString())),this.handlePos2=this.checkHandlePosition(this.handleVal2),null!=this.options.Limits)){i=this.updateNewHandleValue(this.handleVal2,this.handlePos2,[this.options.Limits.maxStart,this.options.Limits.maxEnd]);this.handleVal2=i[0],this.handlePos2=i[1]}this.setHandlePosition(null),"Default"!==this.options.Type&&this.setRangeBarPosition(),null!==this.options.Ticks&&"None"!==this.options.Ticks.placement&&this.setTickValuePosition(),this.updateValue(t)},s.prototype.updateColorRangeBarPos=function(){if(null!=this.options.ColorRange)for(var t=this.sliderTrack.children,e=0;e<this.options.ColorRange.length;e++)this.options.Orientation===i?(this.options.EnableRtl||(t[e].style.left=this.options.EnableRtl?this.checkHandlePosition(this.options.Max-this.options.ColorRange[e].end)+"px":this.checkHandlePosition(this.options.ColorRange[e].start)+"px"),t[e].style.width=this.checkHandlePosition(this.options.ColorRange[e].end)-this.checkHandlePosition(this.options.ColorRange[e].start)+"px"):(t[e].style.bottom=this.checkHandlePosition(this.options.ColorRange[e].start)+"px",t[e].style.height=this.checkHandlePosition(this.options.ColorRange[e].end)-this.checkHandlePosition(this.options.ColorRange[e].start)+"px"),t[e].style.removeProperty("display")},s.prototype.setTickValuePosition=function(){this.firstChild=this.element.querySelector("ul").children[0];var t,e,s=this.firstChild.getBoundingClientRect(),n=this.options.Ticks.smallStep,a=Math.abs(parseFloat(sf.base.formatUnit(this.options.Max))-parseFloat(sf.base.formatUnit(this.options.Min)))/n;this.firstChild.children.length>0&&(t=this.firstChild.children[0].getBoundingClientRect());var o,h=[this.element.querySelectorAll(".e-tick.e-large .e-tick-value")];o="Both"===this.options.Ticks.placement?[].slice.call(h[0],2):[].slice.call(h[0],1);for(var l=this.options.Orientation!==i?2*s.height:2*s.width,d=0;d<this.firstChild.children.length;d++)this.options.Orientation===i?this.options.EnableRtl?this.firstChild.children[d].style.left=(l-this.firstChild.children[d].getBoundingClientRect().width)/2+"px":this.firstChild.children[d].style.left=-t.width/2+"px":this.firstChild.children[d].style.top=-t.width/2+"px";for(d=0;d<o.length;d++)e=o[d].getBoundingClientRect(),this.options.Orientation===i?(sf.base.setStyleAttribute(o[d],{left:(l-e.width)/2+"px"}),this.options.EnableRtl&&o[d].parentElement.classList.contains("e-last-tick")&&sf.base.setStyleAttribute(o[d],{left:-e.width/2+"px"})):sf.base.setStyleAttribute(o[d],{top:(l-e.height)/2+"px"});0===a&&this.firstChild.classList.remove("e-last-tick"),null!=this.firstChild&&(this.options.Orientation===i?this.firstChild.children[0].style.left:t.height),null!=o[0]&&(this.options.Orientation===i?o[0].style.left:e.height),null!=this.firstChild&&null!=o[0]&&(this.options.Orientation===i?(this.firstChild.children[0].style.left,o[0].style.left):(t.height,e.height)),this.element.querySelector("ul").style.removeProperty("visibility")},s.prototype.setRangeBarPosition=function(){if(this.rangeBar=this.element.querySelector(".e-range"),!this.initialRendering)if(this.options.Orientation===i)"MinRange"===this.options.Type?(this.options.EnableRtl?this.rangeBar.style.right="0px":this.rangeBar.style.left="0px",sf.base.setStyleAttribute(this.rangeBar,{width:sf.base.isNullOrUndefined(this.handlePos1)?0:this.handlePos1+"px"})):(this.options.EnableRtl?this.rangeBar.style.right=this.handlePos1+"px":this.rangeBar.style.left=this.handlePos1+"px",sf.base.setStyleAttribute(this.rangeBar,{width:this.handlePos2-this.handlePos1+"px"}));else if("MinRange"===this.options.Type){var t=this.element.getBoundingClientRect();this.rangeBar.style.bottom=this.options.Min>this.options.Max?this.handlePos1+"px":"0px",sf.base.setStyleAttribute(this.rangeBar,{height:sf.base.isNullOrUndefined(this.handlePos1)?0:this.options.Min>this.options.Max?t.height-this.handlePos1+"px":this.handlePos1+"px"})}else this.rangeBar.style.bottom=this.options.Min>this.options.Max?this.handlePos2+"px":this.handlePos1+"px",sf.base.setStyleAttribute(this.rangeBar,{height:this.options.Min>this.options.Max?this.handlePos1-this.handlePos2+"px":this.handlePos2-this.handlePos1+"px"})},s.prototype.setHandlePosition=function(t){this.updateHandleAttributes("aria-valuemin",[this.options.Min.toString()]),this.updateHandleAttributes("aria-valuemax",[this.options.Max.toString()]);var e=[this.handlePos1,this.handlePos2];this.updateHandlePosition(this.firstHandle,e[0]),!this.isMaterial&&!this.isMaterial3||"Range"===this.options.Type||sf.base.isNullOrUndefined(this.materialHandle)||this.updateHandlePosition(this.materialHandle,e[0]),"Range"===this.options.Type&&this.updateHandlePosition(this.secondHandle,e[1]),this.updateAriaValue()},s.prototype.getHandlePosition=function(t){var e;if("mousedown"!==t.type&&"mouseup"!==t.type||t.preventDefault(),"mousedown"===t.type||"mouseup"===t.type||"click"===t.type?e={x:t.clientX,y:t.clientY}:"touchend"!==t.type&&"touchstart"!==t.type||(e={x:t.changedTouches[0].clientX,y:t.changedTouches[0].clientY}),this.handlePos=this.xyToPosition(e),this.handleVal=this.positionToValue(this.handlePos),this.handleVal2<this.handleVal)this.activeHandle=2;else if(this.handleVal1>this.handleVal)this.activeHandle=1;else{var i=this.handleVal>this.handleVal1?this.handleVal-this.handleVal1:this.handleVal1-this.handleVal,s=this.handleVal>this.handleVal2?this.handleVal-this.handleVal2:this.handleVal2-this.handleVal;this.activeHandle=i>s?2:1}},s.prototype.updateHandlePosition=function(t,e){this.applyHandlePosition(t,e),t.style.removeProperty("visibility")},s.prototype.applyHandlePosition=function(t,e){this.isKeyPressed?this.isClicked&&(t.style.transition="none"):t.style.transition=this.transition.handle,(!this.initialRendering||(this.isMaterial||this.isMaterial3)&&"Range"!==this.options.Type)&&(this.options.Orientation===i?this.options.EnableRtl?t.style.right=e+"px":t.style.left=e+"px":t.style.bottom=e+"px")},s.prototype.checkHandleValue=function(t){if(this.options.Min===this.options.Max)return parseFloat(sf.base.formatUnit(this.options.Max));var e=this.handleStartEnd();return t<e.start?t=e.start:t>e.end&&(t=e.end),t},s.prototype.checkHandlePosition=function(t){var e,s=this.element.getBoundingClientRect();return t=100*(t-parseFloat(sf.base.formatUnit(this.options.Min)))/(parseFloat(sf.base.formatUnit(this.options.Max))-parseFloat(sf.base.formatUnit(this.options.Min))),e=this.options.Orientation===i?s.width*(t/100):s.height*(t/100),parseFloat(sf.base.formatUnit(this.options.Max))===parseFloat(sf.base.formatUnit(this.options.Min))&&(e=this.options.Orientation===i?s.width:s.height),e},s.prototype.handleStartEnd=function(){return this.options.Min>this.options.Max?{start:this.options.Max,end:this.options.Min}:{start:this.options.Min,end:this.options.Max}},s.prototype.updateValue=function(t){var e=this;if(void 0===t&&(t=!0),"Range"===this.options.Type?this.options.Value=[this.handleVal1,this.handleVal2]:this.options.Value=this.handleVal1,this.isClicked||(this.previousHandleVal=this.options.Value),t&&(null!==this.options.Tooltip?setTimeout((function(){e.dotNetRef.invokeMethodAsync("UpdateValue",e.options.Value,e.activeHandle)}),300):(!this.isMaterial&&!this.isMaterial3||null===this.options.Tooltip||sf.base.isNullOrUndefined(this.materialHandle)||"Range"===this.options.Type||(this.materialHandle.style.transform="scale(0)"),this.dotNetRef.invokeMethodAsync("UpdateValue",this.options.Value,this.activeHandle)),this.hiddenInput&&this.hiddenInput.name&&this.hiddenInput.name.includes("imageEditor"))){this.hiddenInput.value=this.options.Value;var i=new Event("custom-change",{bubbles:!0,cancelable:!0});this.hiddenInput.dispatchEvent(i)}},s.prototype.updateLimitData=function(t){this.options.Value=this.tempValue?this.tempValue:this.options.Value,this.options.Limits=t,this.setValue()},s.prototype.updateTicksData=function(t){this.options.Ticks=t,this.setTickValuePosition()},s.prototype.updateSliderValue=function(t){var e=this.options.Value;this.options.Value=t,this.setValue(),this.invokeEvent(e,!1)},s.prototype.updateStepValue=function(t){this.options.Step=t},s.prototype.destroy=function(){this.element.style.display="none",this.unWireEvents(),this.element=null,this.isDestroyed=!0},s.prototype.updateTooltipPosition=function(t){var e=document.getElementById(t);!sf.base.isNullOrUndefined(e)&&"Range"!==this.options.Type&&sf.base.isNullOrUndefined(this.options.Tooltip.format)&&(e.style.transform=this.options.Orientation==i?"Before"===this.options.Tooltip.placement?"rotate(45deg)":"rotate(225deg)":"Before"===this.options.Tooltip.placement?"rotate(-45deg)":"rotate(-225deg)")},s.prototype.propertyChanges=function(t){void 0!==t.Enabled&&(this.options.Enabled=t.Enabled),void 0!==t.ShowButtons&&(this.setValue(!1),this.updateColorRangeBarPos()),void 0!==t.ReadOnly&&(this.options.ReadOnly=t.ReadOnly),void 0!==t.Value&&(this.options.Value=t.Value,this.tempValue=this.options.Value,this.setValue(!1)),void 0!==t.Max&&(this.options.Max=t.Max),void 0!==t.Step&&(this.options.Step=t.Step),void 0!==t.Min&&(this.options.Min=t.Min),void 0!==t.IsImmediateValue&&(this.options.IsImmediateValue=t.IsImmediateValue),this.options.Enabled&&!this.options.ReadOnly?(this.unWireEvents(),sf.base.isNullOrUndefined(t.Max)&&this.wireEvents()):this.unWireEvents()},s}();return{initialize:function(t,e,i,n){e&&new s(t,e,i,n);var a=window.sfBlazor.getCompInstance(t);return e&&!sf.base.isNullOrUndefined(a)?a.initialize():null},updateLimitData:function(t,e){var i=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(i)||i.updateLimitData(e)},updateTicksData:function(t,e){var i=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(i)||i.updateTicksData(e)},updateSliderValue:function(t,e){var i=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(i)||i.updateSliderValue(e)},updateSTepValue:function(t,e){var i=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(i)||i.updateStepValue(e)},updatedProperties:function(t,e){var i=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(i)||i.propertyChanges(e)},destroy:function(t){var e=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(e)||e.destroy()},reposition:function(t){var e=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(e)||e.reposition()},updateTooltipPosition:function(t,e){var i=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(i)||i.updateTooltipPosition(e)}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfslider');})})();