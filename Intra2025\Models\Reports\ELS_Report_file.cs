﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Intra2025.Models.Reports.Report
{
    [Table("els_report_file")]
    public class ELS_REPORT_FILEC
    {
        [Key]
        public int Sno { get; set; }

        public string? Bbssno { get; set; }

        [MaxLength(500)]
        public string Filedesc { get; set; } = string.Empty;

        [MaxLength(200)]
        public string Filename { get; set; } = string.Empty;

        [MaxLength(500)]
        public string Filepath { get; set; } = string.Empty;

        [MaxLength(20)]
        public string Postdepartid { get; set; } = string.Empty;

        public DateTime? Createdate { get; set; }

        [MaxLength(20)]
        public string Createuserid { get; set; } = string.Empty;

        //[ForeignKey("Sno")]
        //public ELS_REPORTC Report { get; set; }
    }
}
