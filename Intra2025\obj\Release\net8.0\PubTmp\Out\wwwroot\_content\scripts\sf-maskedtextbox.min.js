/*!*  filename: sf-maskedtextbox.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[38],{"./bundles/sf-maskedtextbox.js":function(e,t,s){"use strict";s.r(t);s("./modules/sf-maskedtextbox.js")},"./modules/sf-maskedtextbox.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.MaskedTextBox=function(){"use strict";var e=function(){function e(e,t,s,i,n){this.ismultipledelete=!1,this.isClicked=!1,this.previousVal="",this.inputData=null,this.isFocusInput=!1,this.isKeyDown=!1,window.sfBlazor=window.sfBlazor,this.dataId=e,this.wrapperElement=t,this.isClicked=!1,this.sIndex=0,this.eIndex=0,this.element=s,this.options=n,window.sfBlazor.setCompInstance(this),this.dotNetRef=i,this.previousVal=this.options.maskedValue}return e.prototype.initialize=function(){sf.base.EventHandler.add(this.element,"focus",this.focusHandler,this),sf.base.EventHandler.add(this.element,"blur",this.focusOutHandler,this),sf.base.EventHandler.add(this.element,"paste",this.pasteHandler,this),sf.base.EventHandler.add(this.element,"cut",this.cutHandler,this),sf.base.EventHandler.add(this.element,"mousedown",this.mouseDownHandler,this),sf.base.EventHandler.add(this.element,"mouseup",this.mouseUpHandler,this),sf.base.EventHandler.add(this.element,"keydown",this.keyDownHandler,this),sf.base.EventHandler.add(this.element,"input",this.inputHandler,this),sf.base.EventHandler.add(this.element,"select",this.selectionHandler,this)},e.prototype.focusOutHandler=function(e){var t=this.wrapperElement.querySelector(".e-clear-icon");null!==t&&t.classList.add("e-clear-icon-hide")},e.prototype.mouseDownHandler=function(e){this.isClicked=!0},e.prototype.mouseUpHandler=function(e){this.isClicked=!1,sf.base.Browser.isDevice&&(this.ismultipledelete=!1,this.sIndex=this.eIndex=0)},e.prototype.selectionHandler=function(e){var t=this.element;t.selectionStart!==t.selectionEnd&&sf.base.Browser.isDevice&&(this.sIndex=t.selectionStart,this.eIndex=t.selectionEnd)},e.prototype.keyDownHandler=function(e){var t=this.element;this.previousVal=t.value,this.isKeyDown=!0,sf.base.Browser.isDevice||(t.selectionStart!==t.selectionEnd?(this.sIndex=t.selectionStart,this.eIndex=t.selectionEnd):(this.ismultipledelete=!1,this.sIndex=this.eIndex=0))},e.prototype.inputHandler=function(e){if(this.options.mask){var t=this.element,s=t.selectionStart,i=t.selectionEnd;if(e.data&&"insertText"!==e.inputType)t.value=this.previousVal,t.selectionStart=t.selectionEnd=s-1;else if((e.data||e.inputType)&&this.isKeyDown){var n=e.data?e.data:"deleteContentBackward"===e.inputType?"Backspace":"deleteContentForward"===e.inputType?"Delete":null;if(this.sIndex!==this.eIndex?(this.ismultipledelete=!0,s=this.sIndex,i=this.eIndex,this.sIndex=this.eIndex=0):i=s=e.data?s-1:"deleteContentBackward"===e.inputType?s+1:s,t.value=this.previousVal,t.selectionStart=t.selectionEnd=s,this.ismultipledelete){this.ismultipledelete=!1;var a=this.multipleDeletion(this.previousVal,s,i);t.value=a,this.previousVal=t.value,t.selectionStart=t.selectionEnd=s,null!==n&&"Backspace"!==n&&"Delete"!==n&&this.updateMask(n,s)}else this.updateMask(n,s);this.previousVal=t.value,this.updateServerValue()}else this.inputData=t.value,t.value=this.previousVal,t.selectionStart=t.selectionEnd=t.selectionStart,this.pasteHandler(e);this.isKeyDown=!1}},e.prototype.multipleDeletion=function(e,t,s){for(var i=e,n=t;n<s;n++)i=i.substring(0,n)+this.options.promptMask[n]+i.substring(n+1);return i},e.prototype.updateServerValue=function(){var e=this,t=this.element;setTimeout((function(){e.dotNetRef.invokeMethodAsync("UpdateInputValue",t.value,!1)}),20)},e.prototype.updateMask=function(e,t){var s=this.element;if(!this.ismultipledelete){var i=this.previousVal,n="";t>=0&&t<this.options.customRegExpCollec.length&&(n=1===this.options.customRegExpCollec[t].length?this.getRegex(this.options.customRegExpCollec[t]):this.options.customRegExpCollec[t]);var a=new RegExp(n);if("Backspace"===e||"Delete"===e)"Backspace"===e?(this.options.promptMask[t-1]===this.options.promptCharacter?s.value=i.substring(0,t-1)+this.options.promptCharacter+i.substring(t):s.value=i.substring(0,t-1)+this.options.promptMask[t-1]+i.substring(t),s.selectionStart=s.selectionEnd=t-1):(this.options.promptMask[t]===this.options.promptCharacter?s.value=i.substring(0,t)+this.options.promptCharacter+i.substring(t+1):s.value=i.substring(0,t)+this.options.promptMask[t]+i.substring(t+1),s.selectionStart=s.selectionEnd=t);else if(t<this.options.promptMask.length)if(this.options.promptMask[t]===this.options.promptCharacter){if(a.test(e)){var o=this.getLetterCase(e,t);s.value=i.substring(0,t)+o+i.substring(t+1),s.selectionStart=s.selectionEnd=t+1}}else s.selectionStart=s.selectionEnd=t+1,this.updateMask(e,s.selectionStart)}},e.prototype.getRegex=function(e){var t="";switch(e){case"0":t="\\d";break;case"9":t="[0-9 ]";break;case"L":t="[A-Za-z]";break;case"A":t="[A-Za-z0-9]";break;case"a":t="[A-Za-z0-9 ]";break;case"C":t="[^]+";break;case"#":t="[0-9 +-]";break;case"?":t="[A-Za-z ]";break;case"&":t="[^ ]+";break;default:t="("===e||"+"===e||")"===e?"\\"+e:e}return t},e.prototype.getLetterCase=function(e,t){for(var s=!1,i=!1,n=!1,a=!1,o=this.options.hiddenMask,l=[],r=[],p=0;p<o.length;p++)"<"!==o[p]&&!s||">"===o[p]||"|"===o[p]?">"!==o[p]&&!i||"<"===o[p]||"|"===o[p]?"|"!==o[p]&&!n||"<"===o[p]||">"===o[p]?a||(l[p]=!1,r[p]=!1):(n=a=!0,i=s=!1,l[p]=!1,r[p]=!1,"|"===o[p]&&(o=o.replace(o[p],""))):(i=a=!0,s=n=!1,l[p]=!1,r[p]=!0,">"===o[p]&&(o=o.replace(o[p],""))):(s=a=!0,i=n=!1,l[p]=!0,r[p]=!1,"<"===o[p]&&(o=o.replace(o[p],"")));return r[t]?e.toUpperCase():l[t]?e.toLowerCase():e},e.prototype.focusHandler=function(e){var t=this,s=this.element,i=0,n=this.stripValue(s.value),a=!1,o=!1;if(null!==this.options.mask&&(null!==n&&""!==n||"Always"===this.options.floatLabelType||null===this.options.placeHolder||""===this.options.placeHolder||(s.value=this.options.maskedValue),setTimeout((function(){if(s.selectionStart===t.options.mask.length||s.value[s.selectionStart]===t.options.promptCharacter)a=!0;else for(var e=s.selectionStart;e<t.options.mask.length;e++){if(s.value[e]===t.options.promptCharacter){a=!0;break}if(s.value[e]!==t.options.mask[e]){a=!1;break}}})),setTimeout((function(){var e=s.selectionStart-1;if(e===t.options.mask.length-1||s.value[e]===t.options.promptCharacter)o=!0;else for(var i=e;i>=0;i--){if(s.value[i]===t.options.promptCharacter){o=!0;break}if(s.value[i]!==t.options.mask[i]){o=!1;break}}})),this.isClicked||this.isFocusInput||"Always"!==this.options.floatLabelType&&(null===n||""===n)&&null!==this.options.placeHolder&&""!==this.options.placeHolder)){for(this.isFocusInput=!0,i=0;i<this.options.mask.length;i++)if(s.value[i]===this.options.promptCharacter){setTimeout((function(){(a||o)&&(s.selectionEnd=i,s.selectionStart=i)}),110);break}this.isClicked=!1}},e.prototype.stripValue=function(e){var t="";if(null!==this.options.mask&&null!=e&&""!==e)for(var s=0;s<this.options.mask.length;s++)this.options.mask[s]!==e[s]&&(t+=e[s]);return t},e.prototype.pasteHandler=function(e){var t=this;if(null!==this.options.mask&&!this.options.readonly&&this.options.enabled){var s=this.element,i=void 0;if(void 0===e.clipboardData||this.inputData?(i=this.inputData,s.selectionStart=s.selectionEnd=0,this.inputData=null):i=e.clipboardData.getData("text/plain"),null!==i){var n={Readonly:!1,Enabled:!0,Value:s.value,selectionEnd:s.selectionEnd,selectionStart:s.selectionStart,IsMultipleDelete:this.ismultipledelete,PasteValue:i};e.preventDefault(),this.dotNetRef.invokeMethodAsync("UpdatePasteValue",n).then((function(e){s.value=e.inputValue,s.selectionStart=s.selectionEnd=e.cursorPosition,t.previousVal=s.value}))}}},e.prototype.cutHandler=function(e){if(this.options.mask){var t=this.element,s=t.selectionStart,i=t.selectionEnd;if(s!==i&&null!==this.options.mask){this.ismultipledelete=!0;var n=t.value.substring(s,i);e.clipboardData.setData("text",n),window.clipboardData.setData("text",n),t.value=this.multipleDeletion(t.value,s,i),t.selectionStart=t.selectionEnd=s,this.previousVal=t.value,this.updateServerValue()}}},e.prototype.propertyChange=function(e){var t=this.options.mask!==e.mask||this.options.promptCharacter!==e.promptCharacter;this.options=e,this.previousVal=this.options.maskedValue,t&&e.maskValue&&(this.element.value=e.maskValue,this.element.setSelectionRange(e.selectionStart,e.selectionStart))},e}();return{initialize:function(t,s,i,n,a){i&&new e(t,s,i,n,a).initialize()},propertyChange:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.propertyChange(t)},focusIn:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||(t.isFocusInput=!0,t.element.focus())},focusOut:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.element.blur()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfmaskedtextbox');})})();