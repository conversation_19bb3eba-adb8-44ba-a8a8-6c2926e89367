/*!*  filename: sf-lineargauge.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[34],{"./bundles/sf-lineargauge.js":function(e,t,i){"use strict";i.r(t);i("./modules/sf-lineargauge.js")},"./modules/sf-lineargauge.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.LinearGauge=function(){"use strict";var e=function(){function e(e,t,i,n){this.dragMillisecond=0,this.tooltipTimer=0,this.isTouch=!1,this.isResized=!1,this.allowAnimation=!1,window.sfBlazor=window.sfBlazor,this.valueToCoefficient=function(e,t){var i=(e-t.Minimum)/t.Delta;return i="Vertical"===t.Orientation?t.IsInversed&&"Thermometer"!==t.ContainerType?i:1-i:t.IsInversed&&"Thermometer"!==t.ContainerType?1-i:i},this.barAnimationProgress=function(e,t,i,n,o,r,s){var a=n<o?n+t*r:n-t*r,d=this.valueToCoefficient(a,e)*e.RectHeight+e.RectY,l=this.valueToCoefficient(e.Minimum,e)*e.RectHeight+e.RectY,u=Math.abs(d-l);"Vertical"===e.Orientation?(e.PointerBoundY="Thermometer"===e.ContainerType?d:e.IsInversed?l:d,e.PointerBoundHeight=u,"Bar"===e.PointerType&&"Normal"===e.ContainerType&&(i.setAttribute("y",e.PointerBoundY.toString()),i.setAttribute("height",e.PointerBoundHeight.toString()),i.setAttribute("x",e.PointerBoundX.toString()),i.setAttribute("width",e.PointerWidth.toString())),"path"===i.tagName&&i.setAttribute("d",s?"":this.getPathContainer(e))):(e.PointerBoundX="Thermometer"===e.ContainerType?l:e.IsInversed?d:l,e.PointerBoundWidth=u,"Bar"===e.PointerType&&"Normal"===e.ContainerType&&(i.setAttribute("x",e.PointerBoundX.toString()),i.setAttribute("width",e.PointerBoundWidth.toString()),i.setAttribute("y",e.PointerBoundY.toString()),i.setAttribute("height",e.PointerHeight.toString())),"path"===i.tagName&&i.setAttribute("d",s?"":this.getPathContainer(e)))},this.markerAnimationProgress=function(e,t,i,n,o,r,s,a){var d=n<o?n+t*r:n-t*r,l=e.MarkerType;if("InvertedTriangle"!=e.MarkerType&&"Triangle"!=e.MarkerType||(l=("Outside"==e.Position&&!e.OpposedPosition||"Inside"==e.Position&&e.OpposedPosition||"Cross"==e.Position)&&"Triangle"==e.MarkerType?"InvertedTriangle":("Inside"==e.Position&&!e.OpposedPosition||"Outside"==e.Position&&e.OpposedPosition)&&"InvertedTriangle"==e.MarkerType?"Triangle":e.MarkerType),s||a)for(var u=0;u<i.childNodes.length;u++){var h=i.childNodes[u];if(h.id===e.AnimationId)if("Vertical"===e.Orientation){var g=e.PointerBoundX;if(e.PointerBoundY=this.valueToCoefficient(d,e)*e.RectHeight+e.RectY,"Circle"===e.MarkerType){var c=(e.PointerWidth+e.PointerHeight)/4,p=e.OpposedPosition?"Near"==e.Placement?g-c:g+c:"Far"!=e.Placement?g-c:g+c;h.setAttribute("cy",e.PointerBoundY.toString()),h.setAttribute("cx",p.toString())}else if("Image"===e.MarkerType||"Rectangle"===e.MarkerType){var m=!e.OpposedPosition&&"Far"!==e.Placement||e.OpposedPosition&&"Near"===e.Placement?g-("Image"===e.MarkerType?e.PointerWidth/2:e.PointerWidth):g;h.setAttribute("y",(e.PointerBoundY-e.PointerHeight/2).toString()),h.setAttribute("x",m.toString())}else if("Text"===e.MarkerType){m=!e.OpposedPosition&&"Far"!==e.Placement||e.OpposedPosition&&"Near"===e.Placement?"Inside"===e.Position&&!e.OpposedPosition||e.OpposedPosition&&"Near"===e.Placement&&"Outside"===e.Position?g-e.PointerBoundWidth/2:g-e.PointerBoundWidth:g;h.setAttribute("y",e.PointerBoundY.toString()),h.setAttribute("x",m.toString())}else h.setAttribute("d",this.calculateShapes(e,l))}else{e.PointerBoundX=this.valueToCoefficient(d,e)*e.RectHeight+e.RectY;var f=e.PointerBoundY;if("Circle"===e.MarkerType){c=(e.PointerWidth+e.PointerHeight)/4;var v=e.OpposedPosition?"Near"==e.Placement?f-c:f+c:"Far"==e.Placement?f+c:f-c;h.setAttribute("cx",e.PointerBoundX.toString()),h.setAttribute("cy",v.toString())}else if("Image"===e.MarkerType||"Rectangle"===e.MarkerType){var y=e.PointerHeight,P=e.OpposedPosition?"Near"===e.Placement?f-y/2:f+y/2:"Far"===e.Placement?f+y/2:f-y/2;h.setAttribute("x",(e.PointerBoundX-e.PointerWidth/2).toString()),h.setAttribute("y",P.toString())}else if("Text"===e.MarkerType){P=e.OpposedPosition?"Near"==e.Placement?"Outside"==e.Position?f+e.PointerBoundHeight+e.PointerBoundHeight/4:"Cross"==e.Position?f+e.PointerBoundHeight/2:f-e.PointerBoundHeight/4:"Cross"==e.Position?f+e.PointerBoundHeight+e.PointerBoundHeight/4:"Outside"==e.Position?f+2*e.PointerBoundHeight+e.PointerBoundHeight/4:f+e.PointerBoundHeight/2:"Far"==e.Placement?"Cross"==e.Position?f+e.PointerBoundHeight+e.PointerBoundHeight/4:"Inside"==e.Position?f+2*e.PointerBoundHeight+e.PointerBoundHeight/4:f+e.PointerBoundHeight/2:"Cross"==e.Position?f+e.PointerBoundHeight/2:"Inside"==e.Position?f+e.PointerBoundHeight+e.PointerBoundHeight/2:f-e.PointerBoundHeight/4;h.setAttribute("x",e.PointerBoundX.toString()),h.setAttribute("y",P.toString())}else h.setAttribute("d",this.calculateShapes(e,l))}}},this.id=e,this.element=t,this.dotNetRef=n,this.options=i,this.dataId=e,window.sfBlazor.setCompInstance(this)}return e.prototype.destroy=function(){this.dotNetRef=null,this.unWireEvents()},e.prototype.unWireEvents=function(){
/*! Bind the Event handler */
sf.base.EventHandler.remove(this.element,sf.base.Browser.touchStartEvent,this.gaugeOnMouseDown),sf.base.EventHandler.remove(this.element,sf.base.Browser.touchMoveEvent,this.gaugeOnMouseMove),sf.base.EventHandler.remove(this.element,sf.base.Browser.touchEndEvent,this.gaugeOnMouseEnd),sf.base.EventHandler.remove(this.element,sf.base.Browser.touchCancelEvent,this.gaugeOnMouseEnd),sf.base.EventHandler.remove(this.element,"click",this.gaugeOnMouseClick),sf.base.EventHandler.remove(this.element,"mouseleave",this.gaugeOnMouseLeave),window.removeEventListener(sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize",this.gaugeOnResize.bind(this))},e.prototype.render=function(){this.wireEvents()},e.prototype.wireEvents=function(){
/*! Bind the Event handler */
sf.base.EventHandler.add(this.element,sf.base.Browser.touchStartEvent,this.gaugeOnMouseDown,this),sf.base.EventHandler.add(this.element,sf.base.Browser.touchMoveEvent,this.gaugeOnMouseMove,this),sf.base.EventHandler.add(this.element,sf.base.Browser.touchEndEvent,this.gaugeOnMouseEnd,this),sf.base.EventHandler.add(this.element,sf.base.Browser.touchCancelEvent,this.gaugeOnMouseEnd,this),sf.base.EventHandler.add(this.element,"click",this.gaugeOnMouseClick,this),sf.base.EventHandler.add(this.element,"mouseleave",this.gaugeOnMouseLeave,this),window.addEventListener(sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize",this.gaugeOnResize.bind(this))},e.prototype.gaugeOnResize=function(){var e,t;if(!sf.base.isNullOrUndefined(this.element)){var i=document.getElementById(this.element.id+"_svg");if(!sf.base.isNullOrUndefined(i)){i.style.display="none";var n=this.element.getBoundingClientRect();e=n.width,t=n.height,i.style.removeProperty("display")}}this.previousHeight===t&&this.previousWidth===e||(this.previousHeight=t,this.previousWidth=e,this.isResized=!0,this.allowAnimation=!1,sf.base.isNullOrUndefined(this.dotNetRef)||this.dotNetRef.invokeMethodAsync("TriggerResizeEvent",e,t))},e.prototype.gaugeOnMouseClick=function(e){var t=e.target.id;(t.indexOf("Bar")>-1||t.indexOf("Marker")>-1)&&(this.pointerCheck=!1)},e.prototype.gaugeOnMouseLeave=function(e){if(!sf.base.isNullOrUndefined(this.dotNetRef)){var t=this.calculateSelectedElementIndex(e);this.dotNetRef.invokeMethodAsync("TriggerMouseLeaveEvent",e.x,e.y,t.axisIndex,t.pointerIndex,t.rangeIndex,t.annotationIndex)}},e.prototype.gaugeOnMouseDown=function(e){var t=e.target.id,i=0,n=0;if("touchstart"===e.type?(this.isTouch=!0,i=e.touches[0].clientX,n=e.touches[0].clientY):(this.isTouch=!1,i=e.pageX,n=e.pageY),t.indexOf("Bar")>-1||t.indexOf("Marker")>-1){this.pointerCheck=!0,this.pointerId=t;var o=parseInt(t.split("_AxisIndex_")[1].split("_")[0],10),r=parseInt(t.split("_AxisIndex_")[1].split("_")[2],10);sf.base.isNullOrUndefined(this.enableDragCollection)||!this.enableDragCollection[o][r]||sf.base.isNullOrUndefined(this.dotNetRef)||(e.preventDefault(),this.dotNetRef.invokeMethodAsync("TriggerDragStart",o,r))}if(!sf.base.isNullOrUndefined(this.dotNetRef)){var s=this.calculateSelectedElementIndex(e);this.dotNetRef.invokeMethodAsync("TriggerMouseDownEvent",i,n,s.axisIndex,s.pointerIndex,s.rangeIndex,s.annotationIndex)}this.isTouch&&this.tooltipRenderer(t,i,n)},e.prototype.gaugeOnMouseMove=function(e){var t=new Date,i=this.pointerCheck?this.pointerId:e.target.id,n=0,o=0;if("touchmove"===e.type?(n=e.touches[0].clientX,o=e.touches[0].clientY):(n=e.clientX,o=e.clientY),i.indexOf("Bar")>-1||i.indexOf("Marker")>-1){var r=this.svgClient(i),s=parseInt(i.split("_AxisIndex_")[1].split("_")[0],10),a=parseInt(i.split("_AxisIndex_")[1].split("_")[2],10);this.pointerCheck&&(0==this.dragMillisecond||Math.abs(t.getMilliseconds()-this.dragMillisecond)>80)&&!sf.base.isNullOrUndefined(this.enableDragCollection)&&this.enableDragCollection[s][a]&&!sf.base.isNullOrUndefined(this.dotNetRef)?(this.dragMillisecond=t.getMilliseconds(),document.getElementById(this.id+"_svg").setAttribute("cursor","grabbing"),this.dotNetRef.invokeMethodAsync("TriggerDrag",i,s,a,n-r.left,o-r.top)):this.pointerCheck||sf.base.isNullOrUndefined(this.enableDragCollection)||!this.enableDragCollection[s][a]||document.getElementById(this.id+"_svg").setAttribute("cursor","pointer")}else sf.base.isNullOrUndefined(document.getElementById(this.id+"_svg"))||document.getElementById(this.id+"_svg").setAttribute("cursor","auto");this.tooltipRenderer(i,n,o)},e.prototype.tooltipRenderer=function(e,t,i){var n=this;if(e===this.element.id+"_LinearGaugeTitle"&&event.target.textContent.indexOf("...")>-1)sf.base.isNullOrUndefined(this.dotNetRef)||(clearTimeout(this.tooltipTimer),this.dotNetRef.invokeMethodAsync("TriggerTooltip",e,0,0,0,0,{},{},{},{}),this.isTouch&&(this.tooltipTimer=setTimeout((function(){null!=document.getElementById(n.element.id+"_Tooltip")&&n.dotNetRef.invokeMethodAsync("TriggerTooltip","",0,0,0,0,{},{},{},{})}),1500)));else if(e.indexOf("Bar")>-1||e.indexOf("Marker")>-1||e.indexOf("Range")>-1){var o=this.svgClient(e),r=parseInt(e.split("_AxisIndex_")[1].split("_")[0],10),s=parseInt(e.split("_AxisIndex_")[1].split("_")[2],10),a=e.split("_")[0],d=document.getElementById(a).getBoundingClientRect(),l={Bottom:d.bottom,Height:d.height,Left:d.left,Right:d.right,Top:d.top,Width:d.width,X:d.x,Y:d.y},u=document.getElementById(a+"_AxisLine_"+r).getBoundingClientRect(),h={Bottom:u.bottom,Height:u.height,Left:u.left,Right:u.right,Top:u.top,Width:u.width,X:u.x,Y:u.y},g=document.getElementById(a+"_MajorTicksLine_"+r).getBoundingClientRect(),c={Bottom:g.bottom,Height:g.height,Left:g.left,Right:g.right,Top:g.top,Width:g.width,X:g.x,Y:g.y},p=document.getElementById(e).getBoundingClientRect(),m={Bottom:p.bottom,Height:p.height,Left:p.left,Right:p.right,Top:p.top,Width:p.width,X:p.x,Y:p.y};sf.base.isNullOrUndefined(this.dotNetRef)||(clearTimeout(this.tooltipTimer),this.dotNetRef.invokeMethodAsync("TriggerTooltip",e,r,s,t-o.left,i-o.top,l,h,c,m),this.isTouch&&(this.tooltipTimer=setTimeout((function(){null!=document.getElementById(n.element.id+"_Tooltip")&&n.dotNetRef.invokeMethodAsync("TriggerTooltip","",0,0,0,0,{},{},{},{})}),1500)))}else{null!=document.getElementById(this.element.id+"_Tooltip")&&this.dotNetRef.invokeMethodAsync("TriggerTooltip","",0,0,0,0,{},{},{},{})}},e.prototype.gaugeOnMouseEnd=function(e){var t=this.pointerCheck?this.pointerId:e.target.id,i=0,n=0;if("touchend"===e.type){var o=e;i=o.changedTouches[0].pageX,n=o.changedTouches[0].pageY}else i=e.clientX,n=e.clientY;if(this.pointerCheck=!1,t.indexOf("Bar")>-1||t.indexOf("Marker")>-1){this.pointerCheck=!1,this.dragMillisecond=0,this.pointerId="";var r=this.svgClient(t),s=t.split("_AxisIndex_")[0].split("_")[0],a=parseInt(t.split("_AxisIndex_")[1].split("_")[0],10),d=parseInt(t.split("_AxisIndex_")[1].split("_")[2],10);sf.base.isNullOrUndefined(this.enableDragCollection)||!this.enableDragCollection[a][d]||sf.base.isNullOrUndefined(this.dotNetRef)||(document.getElementById(this.id+"_svg").setAttribute("cursor","pointer"),this.dotNetRef.invokeMethodAsync("TriggerDragEnd",a,d,s,t,i-r.left,n-r.top))}if(!sf.base.isNullOrUndefined(this.dotNetRef)){var l=this.calculateSelectedElementIndex(e);this.dotNetRef.invokeMethodAsync("TriggerMouseUpEvent",i,n,l.axisIndex,l.pointerIndex,l.rangeIndex,l.annotationIndex)}},e.prototype.calculateSelectedElementIndex=function(e){var t=e.target.id,i=t.indexOf("Bar")>-1||t.indexOf("Marker")>-1,n=-1,o=-1,r=-1,s=-1;if(t.indexOf("Range")>-1||i)n=t.indexOf("_AxisIndex_")>-1?parseInt(t.split("_AxisIndex_")[1].split("_")[0],10):-1,o=i?parseInt(t.split("_AxisIndex_")[1].split("_")[2],10):-1,r=t.indexOf("Range")>-1?parseInt(t.split("_Range_")[1],10):-1;else if(!sf.base.isNullOrUndefined(e.target.closest(".e-lineargauge-annotation"))){var a=e.target.closest(".e-lineargauge-annotation").parentElement;n=a.id.indexOf("Annotation")>-1?parseInt(a.id.split("_")[2]):-1,s=a.id.indexOf("Annotation")>-1?parseInt(a.id.split("_")[a.id.split("_").length-1]):-1}return{axisIndex:n,pointerIndex:o,rangeIndex:r,annotationIndex:s}},e.prototype.svgClient=function(e){return document.getElementById(e.split("_AxisIndex_")[0]+"_svg").getBoundingClientRect()},e.prototype.pointerAnimation=function(e,t,i,n,o,r){var s=this,a=window.sfBlazor.getCompInstance(t),d=document.getElementById(e.AnimationId),l=e.PreviousPointerValue,u=e.PointerValue;l=l===u?e.Minimum:l;var h,g=Math.abs(l-u);new sf.base.Animation({}).animate(d.parentElement,{name:"Linear",duration:e.Duration,progress:function(t){d.style.visibility="visible",s.isResized||!o&&!a.allowAnimation?d.parentElement.removeAttribute("style"):(h=(t.timeStamp-t.delay)/e.Duration,"Bar"!=e.PointerType?s.markerAnimationProgress(e,h,d.parentElement,l,u,g,o,a.allowAnimation):s.barAnimationProgress(e,h,d,l,u,g,!1))},end:function(c){s.isResized||!o&&!a.allowAnimation?d.parentElement.removeAttribute("style"):(h=1,"Bar"!=e.PointerType?s.markerAnimationProgress(e,h,d.parentElement,l,u,g,o,a.allowAnimation):s.barAnimationProgress(e,h,d,l,u,g,"RoundedRectangle"===e.ContainerType&&e.Minimum===e.PointerValue)),i&&n>0&&(r?a.annotationAnimation(t,n):a.cancelAnimation())}}),i||(this.isResized=!1)},e.prototype.annotationAnimation=function(e,t){var i=window.sfBlazor.getCompInstance(this.element.id),n=document.getElementById(e+"_AnnotationGroup");document.getElementById(e+"_create_Animation");if("0"===n.style.opacity){var o=0;new sf.base.Animation({}).animate(n,{duration:t,progress:function(e){e.timeStamp>e.delay&&(o=(e.timeStamp-e.delay)/e.duration,n.style.opacity=(1*o).toString())},end:function(){n.style.opacity=1..toString(),i.cancelAnimation()}})}},e.prototype.animationProcess=function(e,t,i,n,o,r,s){var a=document.getElementById(e+"_Axis_Collections"),d=document.getElementById(e+"_create_Animation"),l=window.sfBlazor.getCompInstance(e),u=0,h=document.querySelectorAll("style."+e+"_Animation"),g=!sf.base.isNullOrUndefined(document.getElementById(e+"_AnnotationGroup"))&&document.getElementById(e+"_AnnotationGroup").children.length>0;new sf.base.Animation({}).animate(a,{duration:t,progress:function(t){u=(t.timeStamp-t.delay)/t.duration,h[0].innerHTML="."+e+"_Animation{opacity:"+1*u+"}"},end:function(){if(h[0].innerHTML="."+e+"_Animation{opacity: 1}",l&&l.element){for(var n=JSON.parse(o.animationSettings),s=0;s<r;s++){var a=document.getElementById(e+"_PointersGroup_"+s);sf.base.isNullOrUndefined(a)||(a.style.visibility="visible"),n.length>0&&l.pointerAnimation(n[s],e,i,t,!1,g)}i&&r<1&&(g?l.annotationAnimation(e,t):l.cancelAnimation())}}}),setTimeout((function(){sf.base.isNullOrUndefined(d)||d.remove()}),s+100),this.isResized=!1},e.prototype.cancelAnimation=function(){this.allowAnimation=!1,this.dotNetRef.invokeMethodAsync("EndAnimation")},e.prototype.getPathContainer=function(e){var t=" ",i=e.CornerRadius,n=e.PointerBoundWidth,o=e.PointerBoundHeight,r=e.Orientation,s=e.IsInversed,a=e.PointerType,d=e.PointerBoundX,l=e.PointerBoundY,u=0,h=0;switch(e.ContainerType){case"RoundedRectangle":var g=d+n-i,c=l+o-i,p=i+l,m=i+d;"Bar"===a&&("Vertical"===r&&0!==o||"Horizontal"===r&&0!==n)&&(m>d+n/2&&(m=g=d+n/2),p>l+o/2&&(p=c=l+o/2)),"Bar"===a&&("Vertical"===r&&0===o||"Horizontal"===r&&0===n)&&(n<i/2&&!s?g=g+i+i/2:n<i/2&&s&&(m=d-Math.ceil(i/4)),o<i/2&&!s?p=l-Math.ceil(i/4):o<i/2&&s&&(c=c+i+i/2)),t="M "+d+" "+p+" Q "+d+" "+l+" "+m+" "+l+" L "+g+" "+l+" Q "+(d+n)+" "+l+" "+(d+n)+" "+p+" L "+(d+n)+" "+c+" Q "+(d+n)+" "+(l+o)+" "+g+" "+(l+o)+"  L "+m+" "+(l+o)+" Q "+d+" "+(l+o)+" "+d+" "+c+" L "+d+" "+p+" z";break;case"Thermometer":var f="Vertical"===r?n:o;if(u=f+f/2/Math.PI,h=f/2,"Vertical"===r){var v=e.ContainerWidth+e.ContainerWidth/2/Math.PI-u,y="Bar"===a?l+v:l,P="Bar"===a?l+(h-h/Math.PI):l;t="M"+d+" "+(y+o)+" A "+u+" "+u+", 0, 1, 0, "+(d+n)+" "+(y+o)+" L "+(d+n)+" "+P+" A "+h+" "+h+", 0, 1, 0, "+d+" "+P+" z "}else{var B="Bar"!==a||e.IsInversed?d:d-(e.ContainerWidth+e.ContainerWidth/2/Math.PI-u),b="Bar"===a?n-(h-h/Math.PI):n;t="M"+B+" "+l+" A "+u+" "+u+", 0, 1, 0, "+B+" "+(l+o)+" L "+(("Bar"===a?d:B)+b)+" "+(l+o)+" A "+h+" "+h+", 0, 1, 0, "+(("Bar"===a?d:B)+b)+" "+l+" z "}}return t},e.prototype.calculateShapes=function(e,t){var i,n=e.PointerWidth,o=e.PointerHeight,r=e.PointerBoundX,s=e.PointerBoundY,a=e.Placement,d=e.OpposedPosition,l=e.Orientation;switch(t){case"Diamond":i="M "+(r="Horizontal"===l?r-n/2:!d&&"Far"!==a||d&&"Near"===a?r-n:r)+" "+(s="Vertical"===l?s:d?"Near"===a?s-o/2:s+o/2:"Far"===a?s+o/2:s-o/2)+" L "+(r+n/2)+" "+(s-o/2)+" L "+(r+n)+" "+s+" L "+(r+n/2)+" "+(s+o/2)+" L "+r+" "+s+" z";break;case"Triangle":i="Vertical"===l?"M "+r+" "+s+" L"+(r-n)+" "+(s-o/2)+"L"+(r-n)+" "+(s+o/2)+" Z":"M "+r+" "+s+" L"+(r+n/2)+" "+(s-o)+"L"+(r-n/2)+" "+(s-o)+" Z";break;case"InvertedTriangle":i="Vertical"===l?"M "+r+" "+s+" L"+(r+n)+" "+(s-o/2)+"L"+(r+n)+" "+(s+o/2)+" Z":"M "+r+" "+s+" L"+(r+n/2)+" "+(s+o)+"L"+(r-n/2)+" "+(s+o)+" Z";break;case"Arrow":i="Vertical"===l?"M "+r+" "+s+" L"+(r-n/2)+" "+(s-o/2)+" L"+(r-n/2)+" "+(s-o/2+o/4)+" L"+(r-n)+" "+(s-o/2+o/4)+" L"+(r-n)+" "+(s+o/2-o/4)+" L"+(r-n/2)+" "+(s+o/2-o/4)+" L"+(r-n/2)+" "+(s+o/2)+"z":"M "+r+" "+s+" L"+(r+n/2)+" "+(s-o/2)+" L"+(r+n/2-n/4)+" "+(s-o/2)+" L"+(r+n/2-n/4)+" "+(s-o)+" L"+(r-n/2+n/4)+" "+(s-o)+" L"+(r-n/2+n/4)+" "+(s-o/2)+" L"+(r-n/2)+" "+(s-o/2)+"z";break;case"InvertedArrow":i="Vertical"===l?"M "+r+" "+s+"L"+(r+n/2)+" "+(s-o/2)+" L"+(r+n/2)+" "+(s-o/2+o/4)+" L"+(r+n)+" "+(s-o/2+o/4)+" L"+(r+n)+" "+(s+o/2-o/4)+" L"+(r+n/2)+" "+(s+o/2-o/4)+" L"+(r+n/2)+" "+(s+o/2)+"z":"M "+r+" "+s+" L"+(r+n/2)+" "+(s+o/2)+" L"+(r+n/2-n/4)+" "+(s+o/2)+" L"+(r+n/2-n/4)+" "+(s+o)+" L"+(r-n/2+n/4)+" "+(s+o)+" L"+(r-n/2+n/4)+" "+(s+o/2)+" L"+(r-n/2)+" "+(s+o/2)+"z"}return i},e}();return{initialize:function(t,i,n,o,r){return new e(t.id,t,i,n).render(),sf.base.isNullOrUndefined(o)||t.setAttribute("style",o),r&&this.createAnimationProcess(t.id,r),this.getElementSize(t.id)},getElementSize:function(e){var t,i,n=document.getElementById(e);if(null!=n){var o=n.getBoundingClientRect();t=o.width,i=o.height}return{width:t,height:i,isIE:sf.base.Browser.isIE}},pointerAnimationProcess:function(e,t,i){var n=window.sfBlazor.getCompInstance(e);if(n&&n.element){n.isResized=!1;for(var o=JSON.parse(t.animationSettings),r=0;r<i;r++)n.pointerAnimation(o[r],e,!1,0,!0)}},createAnimationProcess:function(e,t){var i=document.getElementById(e+"_create_Animation");if(t&&sf.base.isNullOrUndefined(i)){var n=document.createElement("style");n.className=e+"_Animation",n.innerText="."+e+"_Animation{opacity: 0}",n.id=e+"_create_Animation",document.body.appendChild(n)}},animationProcess:function(e,t,i,n,o,r,s){var a=window.sfBlazor.getCompInstance(e);if(a&&a.element){a.allowAnimation=i;var d=JSON.parse(n.axisAnimation);a.animationProcess(e,t,i,d,o,r,s)}},cancelAnimation:function(e){var t=window.sfBlazor.getCompInstance(e);t&&t.element&&(t.allowAnimation=!1)},setPathAttribute:function(e,t,i,n,o,r){var s=document.getElementById(e);""===t?s.setAttribute("d",i):"Circle"==t?"Horizontal"==r?s.setAttribute("cx",n.toString()):s.setAttribute("cy",o.toString()):"Rectangle"==t||"Image"==t?"Horizontal"==r?s.setAttribute("x",n.toString()):s.setAttribute("y",o.toString()):(s.setAttribute("x",n.toString()),s.setAttribute("y",o.toString()))},updateCollection:function(e,t){var i=window.sfBlazor.getCompInstance(e);i&&i.element&&(i.enableDragCollection=JSON.parse(t.enableDragCollection))},getElementBounds:function(e){var t=document.getElementById(e);if(t){var i=t.getBoundingClientRect();return{width:i.width,height:i.height,top:i.top,bottom:i.bottom,left:i.left,right:i.right}}return null},dispose:function(e){var t=window.sfBlazor.getCompInstance(e);t&&t.element&&t.destroy()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sflineargauge');})})();