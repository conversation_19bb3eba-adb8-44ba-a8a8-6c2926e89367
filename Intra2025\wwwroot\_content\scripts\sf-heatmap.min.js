/*!*  filename: sf-heatmap.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[30],{"./bundles/sf-heatmap.js":function(t,e,i){"use strict";i.r(e);i("./modules/sf-heatmap.js")},"./modules/sf-heatmap.js":function(t,e){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.HeatMap=function(){"use strict";var t=function(){function t(t,e){this.tapCount=0,this.isDoubleClick=!1,this.previousTargetID="",this.isSelectionRectCreated=!1,this.mouseClick=!1,this.checkCtrlKey=!1,this.tooltipRender=!0,this.isCellTapHold=!1,this.rect=document.createElementNS("http://www.w3.org/2000/svg","rect"),this.svg=document.createElementNS("http://www.w3.org/2000/svg","svg"),this.resizeTimer=0,this.previousCellID="",window.sfBlazor=window.sfBlazor,this.element=t,this.dataId=t.id,window.sfBlazor.setCompInstance(this),this.dotnetRef=e}return t.prototype.initialize=function(t){var e=this;this.enableMultiSelect=t.EnableMultiSelect,this.allowSelection=t.AllowSelection,this.isRectType=t.IsRectType;var i=sf.base.Browser.isPointer?"pointerleave":"mouseleave";sf.base.EventHandler.add(this.element,"click touchend",this.heatMapMouseClick,this),sf.base.EventHandler.add(this.element,"dblclick touchend",this.heatMapDoubleMouseClick,this),sf.base.EventHandler.add(this.element,"mousedown touchstart",this.heatMapMouseDown,this),sf.base.EventHandler.add(this.element,"mouseup touchend",this.heatMapMouseUp,this),sf.base.EventHandler.add(this.element,sf.base.Browser.touchStartEvent,this.heatMapMouseMove,this),sf.base.EventHandler.add(this.element,i,this.heatMapMouseLeave,this),sf.base.EventHandler.add(this.element,sf.base.Browser.touchMoveEvent,this.heatMapMouseMove,this);var o=sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize";sf.base.EventHandler.add(window,o,this.resizeBound.bind(this),this);var l=this.getElementBounds(this.element);return this.touchInstance=new sf.base.Touch(this.element,{tapHold:function(t){e.isCellTapHold=!0,e.isSelectionRectCreated=!0,e.cellTap(t)}}),"width:"+l.width+", height:"+l.height+", parentHeight:"+l.parentHeight},t.prototype.cellTap=function(t){this.allowSelection&&this.dotnetRef.invokeMethodAsync("HeatMapMouseClick",this.getEventArgs(t.originalEvent),this.getElementBounds(this.element),this.isCellTapHold,this.isSelectionRectCreated,!1)},t.prototype.heatMapMouseClick=function(t){var e=this;this.singleClickTimeout=setTimeout((function(){e.isDoubleClick||e.dotnetRef.invokeMethodAsync("HeatMapMouseClick",e.getEventArgs(t),e.getElementBounds(e.element),e.isCellTapHold,e.isSelectionRectCreated,!1)}),200),this.isSelectionRectCreated=!1},t.prototype.heatMapDoubleMouseClick=function(t){var e=this;if("touchend"===t.type){this.tapCount=this.tapCount>1?0:this.tapCount+1;var i=this;sf.base.isNullOrUndefined(this.tapCountTimer)&&(this.tapCountTimer=setTimeout((function(){window.clearTimeout(i.tapCountTimer),i.tapCountTimer=null,i.tapCount=0}),1e3))}(this.tapCount>1&&this.previousTargetID===t.target.id||"dblclick"===t.type)&&(this.isDoubleClick=!0,setTimeout((function(){e.isDoubleClick=!1}),1e3),this.dotnetRef.invokeMethodAsync("HeatMapMouseClick",this.getEventArgs(t),this.getElementBounds(this.element),this.isCellTapHold,this.isSelectionRectCreated,!0),this.isSelectionRectCreated=!1,this.previousTargetID="",clearTimeout(this.singleClickTimeout)),this.previousTargetID=t.target.id},t.prototype.heatMapMouseUp=function(t){var e=this;this.startRectValue=null;var i=document.getElementById(this.element.id+"_selectedCells"),o=null;i&&(this.mouseClick=!1,o=i.getBoundingClientRect(),i.remove(),this.isSelectionRectCreated&&!t.ctrlKey&&"touchend"===t.type&&(this.isCellTapHold=!0)),"touchend"===t.type&&this.tooltipfadeOut(t),this.tooltipRender=!0,this.initialX!=(t.changedTouches?t.changedTouches[0].clientX:t.clientX)&&null==o||"touchend"==t.type&&this.isCellTapHold?this.isCellTapHold=!0:this.isCellTapHold=!1,this.allowSelection&&this.isRectType&&("touchend"===t.type?setTimeout((function(){e.dotnetRef.invokeMethodAsync("HeatMapMouseUp",e.getEventArgs(t),o,e.getElementBounds(e.element),e.isCellTapHold,e.checkCtrlKey)}),200):this.dotnetRef.invokeMethodAsync("HeatMapMouseUp",this.getEventArgs(t),o,this.getElementBounds(this.element),this.isCellTapHold,this.checkCtrlKey))},t.prototype.heatMapMouseDown=function(t){if(this.allowSelection&&this.isRectType&&(-1!=t.target.id.indexOf(this.element.id+"_HeatMapRect_")||-1!=t.target.id.indexOf(this.element.id+"_HeatMapRectLabels_"))){""!=this.previousCellID&&"touchstart"!=t.type&&"touchmove"!=t.type&&this.selectionHighLight(t.target.id),this.mouseClick=!0,this.initialX=t.changedTouches?t.changedTouches[0].clientX:t.clientX,this.initialY=t.changedTouches?t.changedTouches[0].clientY:t.clientX;var e=void 0;null!=document.querySelector("#selectedCellStyles")&&0!=document.querySelector("#selectedCellStyles").length?(e=document.getElementById("selectedCellStyles")).innerHTML=".selectedCell {opacity: 0.3;}":((e=document.createElement("style")).id="selectedCellStyles",e.innerHTML=".selectedCell {opacity: 0.3;}"),document.body.appendChild(e)}},t.prototype.heatMapMouseLeave=function(t){var e=document.getElementById(this.element.id+"_selectedCells"),i=null;this.mouseClick&&(this.previousCellID=""),e&&(i=e.getBoundingClientRect(),e.remove(),this.startRectValue=null,this.mouseClick=!1),this.tooltipRender=!0,this.initialX=null,this.initialY=null,this.dotnetRef.invokeMethodAsync("HeatMapMouseLeave",this.getEventArgs(t),i,this.getElementBounds(this.element),this.isCellTapHold,this.checkCtrlKey)},t.prototype.heatMapMouseMove=function(t){var e=!0;if("touchstart"!=t.type&&"touchmove"!=t.type||t.preventDefault(),this.allowSelection&&this.enableMultiSelect&&this.isRectType&&!sf.base.isNullOrUndefined(this.initialX)&&!sf.base.isNullOrUndefined(this.initialY)&&this.mouseClick){var i=this.getEventArgs(t),o=0,l=0,s=void 0,n=void 0;"touchstart"===i.type||"touchmove"===i.type?(s=i.changedTouches.clientX,n=i.changedTouches.clientY):(s=i.clientX,n=i.clientY);var a=this.getElementBounds(document.getElementById(this.element.id+"_Container_RectGroup"));if(this.initialX>a.left&&this.initialY>a.top){var r=document.getElementById(this.element.id+"_svg").getBoundingClientRect();sf.base.isNullOrUndefined(this.startRectValue)&&(this.startRectValue=this.svgPoint(this.svg,s,n),this.checkCtrlKey=t.ctrlKey);var h=this.svgPoint(this.svg,s,n);if(h.x<a.right&&h.x>a.left&&h.y<a.bottom&&h.y>a.top&&(l=Math.abs((h.x<a.right?h.x:a.right)-this.startRectValue.x),o=Math.abs((h.y<a.bottom?h.y:a.bottom)-this.startRectValue.y),h.x=h.x>this.startRectValue.x?this.startRectValue.x:h.x,h.y=h.y>this.startRectValue.y?this.startRectValue.y:h.y,"touchstart"!==i.type&&"touchmove"!==i.type||(this.tooltipRender=!0,this.tooltipfadeOut(t)),this.enableMultiSelect&&(h.x=h.x-r.x,h.y=h.y-r.y,this.rect.setAttributeNS(null,"x",h.x.toString()),this.rect.setAttributeNS(null,"y",h.y.toString()),this.rect.setAttributeNS(null,"id",this.element.id+"_selectedCells"),this.rect.setAttributeNS(null,"width",l.toString()),this.rect.setAttributeNS(null,"height",o.toString()),this.rect.setAttributeNS(null,"fill","#87ceeb"),this.rect.setAttributeNS(null,"stroke-dasharray","0"),this.rect.setAttributeNS(null,"opacity","0.5"),this.rect.setAttributeNS(null,"stroke","#0000ff"),document.getElementById(this.element.id+"_Container_RectGroup").appendChild(this.rect),l>0||o>0))){this.isSelectionRectCreated=!0,this.tooltipRender=!1;var c=document.getElementById(this.element.id+"_tooltip_svg");if(c&&c.remove(),e=!1,!t.ctrlKey&&(-1!=t.target.id.indexOf(this.element.id+"_HeatMapRect_")||-1!=t.target.id.indexOf(this.element.id+"_HeatMapRectLabels_"))){""!=this.previousCellID&&this.selectionHighLight(t.target.id);for(var d=document.getElementById(this.element.id+"_Container_RectGroup"),u=document.getElementById(this.element.id+"_Container_TextGroup"),p=0;p<d.childNodes.length;p++){var m=d.childNodes[p];!sf.base.isNullOrUndefined(m.id)&&m.id.indexOf("HeatMapRect")>-1&&m.classList.add("selectedCell")}for(p=0;p<u.childNodes.length;p++){m=u.childNodes[p];!sf.base.isNullOrUndefined(m.id)&&m.id.indexOf("_HeatMapRectLabels_")>-1&&(m.removeAttribute("opacity"),m.setAttribute("style","opacity:0.3"),m.classList.add("selectedCell"))}}}}}!this.allowSelection||this.enableMultiSelect||!this.mouseClick||"touchmove"!=t.type&&"mousemove"!=t.type||""!=this.previousCellID&&"0.3"==document.getElementById(this.previousCellID).getAttribute("opacity")&&(this.previousCellID=""),this.dotnetRef.invokeMethodAsync("HeatMapMouseMove",this.getEventArgs(t),this.getElementBounds(this.element),e,sf.base.Browser.isDevice)},t.prototype.svgPoint=function(t,e,i){var o=this.svg.createSVGPoint();return o.x=e,o.y=i,t.getScreenCTM()?o.matrixTransform(t.getScreenCTM().inverse()):o},t.prototype.selectionHighLight=function(t){if(-1!=t.indexOf(this.element.id+"_HeatMapRectLabels_")){var e=t.replace(this.element.id+"_HeatMapRectLabels_",this.element.id+"_HeatMapRect_"),i=parseInt(e.split("_")[2]);i++;var o=e.slice(0,e.lastIndexOf("_")+1)+i.toString();this.previousCellID=o,document.getElementById(this.previousCellID)&&this.setOpacity(document.getElementById(this.previousCellID),"1")}else document.getElementById(this.previousCellID)&&this.setOpacity(document.getElementById(this.previousCellID),"1"),this.previousCellID=t},t.prototype.tooltipfadeOut=function(t){var e=this;if(this.tooltipTimer&&(window.clearTimeout(this.tooltipTimer),this.tooltipTimer=null),"touchstart"===t.type||"touchend"===t.type){var i=window.sfBlazor.getCompInstance(this.element.id);this.tooltipTimer=setTimeout((function(){sf.base.isNullOrUndefined(i.tooltip)||sf.base.isNullOrUndefined(i.tooltip.element)||sf.base.isNullOrUndefined(i.tooltip.element.firstElementChild)||i.tooltip.fadeOut(),e.dotnetRef.invokeMethodAsync("RemoveTooltip",!0,t.target.id),""!=e.previousCellID&&(e.previousCellID=""),e.selectionHighLight(t.target.id),window.clearTimeout(e.tooltipTimer),e.tooltipTimer=null}),1500),t&&"touchmove"===t.type&&t.preventDefault()}},t.prototype.resizeBound=function(){var t,e,i=this;if(!sf.base.isNullOrUndefined(this.element)&&!sf.base.isNullOrUndefined(document.getElementById(this.element.id+"_svg"))){var o=document.getElementById(this.element.id+"_svg");o.style.display="none";var l=this.getElementBounds(this.element);t=l.width,e=l.height,o.style.removeProperty("display")}this.previousHeight===e&&this.previousWidth===t||(this.previousHeight=e,this.previousWidth=t,this.resizeTimer&&clearTimeout(this.resizeTimer),this.element&&(this.resizeTimer=setTimeout((function(){return i.dotnetRef.invokeMethodAsync("ResizeBound",e,t)}),500)))},t.prototype.setMouseXY=function(t,e,i){var o=document.getElementById(i).getBoundingClientRect(),l=document.getElementById(i).getBoundingClientRect();this.mouseY=e-l.top-Math.max(o.top-l.top,0),this.mouseX=t-l.left-Math.max(o.left-l.left,0)},t.prototype.renderTooltip=function(t,e,i,o,l,s,n,a,r,h,c,d,u){if(this.tooltipRender){var p=document.getElementById(e+"_svg"),m=!(p&&parseInt(p.getAttribute("opacity"),10)>0),g="string"==typeof i?JSON.parse(i):i;n||s||this.updateCellHighlight(i.id,l),sf.base.isNullOrUndefined(u)||(g.inverted=!u,g.enableAnimation=!1),c&&"TailwindDark"==c&&(g.fill="#F9FAFB"),c&&"Bootstrap5Dark"==c&&(g.fill="#E9ECEF");var f=window.sfBlazor.getCompInstance(t);g.content=g.xLabel+g.yLabel+g.value,m&&!sf.base.isNullOrUndefined(f)&&(g.content=g.displayText,g.location={x:g.x+g.width/2,y:g.y+g.height/2},g.areaBounds={height:o.height+o.y,width:o.width,x:o.x},g.opacity="Tailwind"==c||"TailwindDark"===c||"Bootstrap5"===c||"Bootstrap5Dark"===c?1:.75,g.theme=c,g.fill=r,g.header=d,f.tooltip=new sf.svgbase.Tooltip(g),f.tooltip.enableAnimation=!1,f.tooltip.border.width=sf.base.isNullOrUndefined(h)?f.tooltip.border.width:h.width,f.tooltip.border.color=sf.base.isNullOrUndefined(h)?f.tooltip.border.color:h.color,f.tooltip.appendTo("#"+e)),sf.base.isNullOrUndefined(f.tooltip)||(sf.base.isNullOrUndefined(a)||(f.tooltip.textStyle.color=a.color||f.tooltip.textStyle.color,f.tooltip.textStyle.fontFamily=a.fontFamily||f.tooltip.textStyle.fontFamily,f.tooltip.textStyle.fontStyle=a.fontStyle||f.tooltip.textStyle.fontStyle,f.tooltip.textStyle.fontWeight=a.fontWeight||f.tooltip.textStyle.fontWeight,f.tooltip.textStyle.size=a.size||f.tooltip.textStyle.size),c&&"Tailwind"==c&&(f.tooltip.textStyle.color="#FFFFFF",f.tooltip.textStyle.fontWeight="500",f.tooltip.textStyle.size="12px",f.tooltip.textStyle.fontFamily="Inter"),c&&"TailwindDark"==c&&(f.tooltip.textStyle.color="#1F2937",f.tooltip.textStyle.fontWeight="500",f.tooltip.textStyle.size="12px",f.tooltip.textStyle.fontFamily="Inter"),c&&"Bootstrap5"==c&&(f.tooltip.border.width=0,f.tooltip.border.color="",f.tooltip.fill="",f.tooltip.opacity=.75,f.tooltip.textStyle.opacity=1,f.tooltip.textStyle.color=null,f.tooltip.textStyle.fontWeight="Normal",f.tooltip.textStyle.size="13px",f.tooltip.textStyle.fontFamily="Segoe UI"),c&&"Bootstrap5Dark"==c&&(f.tooltip.textStyle.color="#212529",f.tooltip.textStyle.fontWeight="500",f.tooltip.textStyle.size="14px",f.tooltip.textStyle.fontFamily="system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', 'Liberation Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'"),c&&"Material3"==c&&(f.tooltip.textStyle.color="#F4EFF4",f.tooltip.textStyle.fontWeight="400",f.tooltip.textStyle.size="14px",f.tooltip.textStyle.fontFamily="Roboto"),c&&"Material3Dark"==c&&(f.tooltip.textStyle.color="#313033",f.tooltip.textStyle.fontWeight="400",f.tooltip.textStyle.size="14px",f.tooltip.textStyle.fontFamily="Roboto"),g.toolTipX=g.x+g.width/2,g.toolTipY=g.y+g.height/2,g.content=g.displayText,f.tooltip.location=new sf.svgbase.TooltipLocation(g.toolTipX,g.toolTipY),f.tooltip.content=[g.content],f.tooltip.dataBind())}},t.prototype.removeOpacity=function(t,e){for(var i=0;i<e.length;i++){var o=e[i];if(!sf.base.isNullOrUndefined(o)&&o.indexOf("HeatMapRect")>-1){document.getElementById(o).classList.remove("selectedCell");var l=parseInt(o.replace(t+"_HeatMapRect_",""),10),s=document.getElementById(t+"_HeatMapRectLabels_"+(l-1));s.classList.remove("selectedCell"),s.setAttribute("style","opacity: 1")}}},t.prototype.setSelectionProperty=function(t,e,i){this.allowSelection=t,this.enableMultiSelect=e,this.isRectType=i},t.prototype.updateCellHighlight=function(t,e){this.previousCellID!==t&&(""!=this.previousCellID&&this.setOpacity(document.getElementById(this.previousCellID),"1"),e&&(this.setOpacity(document.getElementById(t),"0.65"),this.previousCellID=t))},t.prototype.setOpacity=function(t,e){t.setAttribute("opacity",e)},t.prototype.getElementBounds=function(t){var e,i,o,l,s,n,a;if(null!=t){var r=t.getBoundingClientRect();e=r.width,i=r.height,a=t.parentElement?t.parentElement.offsetHeight:r.height,n=r.left,o=r.top,s=r.right,l=r.bottom}return{width:Math.ceil(e),height:Math.ceil(i),parentHeight:Math.ceil(a),left:Math.ceil(n),top:Math.ceil(o),right:Math.ceil(s),bottom:Math.ceil(l)}},t.prototype.stringToNumber=function(t,e){return sf.base.isNullOrUndefined(t)?null:-1!==t.indexOf("%")?e/100*parseInt(t,10):parseInt(t,10)},t.prototype.getEventArgs=function(t){var e=t.changedTouches?t.changedTouches[0].clientX:t.clientX,i=t.changedTouches?t.changedTouches[0].clientY:t.clientY,o=sf.base.isNullOrUndefined(t.currentTarget)?t.target.id:t.currentTarget.id;this.setMouseXY(e,i,o);var l=t.touches,s=[];if(t.type.indexOf("touch")>-1)for(var n=0,a=l.length;n<a;n++)s.push({pageX:l[n].clientX,pageY:l[n].clientY,pointerId:t.pointerId||0});var r=t.type.indexOf("touchend")>-1&&!sf.base.isNullOrUndefined(document.elementFromPoint(t.changedTouches[0].clientX,t.changedTouches[0].clientY))?document.elementFromPoint(t.changedTouches[0].clientX,t.changedTouches[0].clientY).id:t.target.id;return{type:t.type,clientX:t.clientX,clientY:t.clientY,mouseX:this.mouseX,mouseY:this.mouseY,pointerType:t.pointerType,target:r,changedTouches:{clientX:t.changedTouches?t.changedTouches[0].clientX:0,clientY:t.changedTouches?t.changedTouches[0].clientY:0},touches:s,pointerId:t.pointerId,ctrlKey:t.ctrlKey}},t.prototype.destroy=function(){var t=sf.base.Browser.isPointer?"pointerleave":"mouseleave";sf.base.EventHandler.remove(this.element,"click",this.heatMapMouseClick),sf.base.EventHandler.remove(this.element,"dblclick touchend",this.heatMapDoubleMouseClick),sf.base.EventHandler.remove(this.element,"mousedown touchstart",this.heatMapMouseDown),sf.base.EventHandler.remove(this.element,"mouseup touchend",this.heatMapMouseUp),sf.base.EventHandler.remove(this.element,sf.base.Browser.touchStartEvent,this.heatMapMouseMove),sf.base.EventHandler.remove(this.element,t,this.heatMapMouseLeave),sf.base.EventHandler.remove(this.element,sf.base.Browser.touchMoveEvent,this.heatMapMouseMove);var e=sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize";sf.base.EventHandler.remove(window,e,this.resizeBound.bind(this)),this.element=null,this.svg=null,this.startRectValue=null,this.touchInstance.destroy(),this.touchInstance=null,this.svg=null},t}();return{initialize:function(e,i,o){new t(e,i);var l=window.sfBlazor.getCompInstance(e.id);return l?l.initialize(o):null},removeOpacity:function(t,e){var i=window.sfBlazor.getCompInstance(t);i&&i.element&&i.removeOpacity(t,e)},getElementSize:function(t,e){var i=window.sfBlazor.getCompInstance(e);if(i&&i.element){var o=i.getElementBounds(t);return"width:"+o.width+", height:"+o.height+", parentHeight:"+o.parentHeight}return null},setSelectionProperty:function(t,e,i,o){var l=window.sfBlazor.getCompInstance(t);l&&l.element&&l.setSelectionProperty(e,i,o)},renderTooltipTemplate:function(t,e,i){var o=window.sfBlazor.getCompInstance(t);if(o&&o.element){var l=document.getElementById(t+"_Tooltip"),s=document.getElementById(t+"_HeatmapBorder");if(l&&s){var n,a=Number(s.getAttribute("x")),r=Number(s.getAttribute("y")),h=Number(s.getAttribute("width")),c=l.getBoundingClientRect(),d=void 0;i-c.height<r?(d=e+c.width/2>h?(h-c.width).toString()+"px":e-c.width/2<a?l.style.left=a.toString()+"px":(e-c.width/2).toString()+"px",n=(i+5).toString()+"px"):e+c.width/2>h?(d=(h-c.width).toString()+"px",n=(i-c.height-5).toString()+"px"):e-c.width/2<a?(d=a.toString()+"px",n=(i-c.height-5).toString()+"px"):(d=(e-c.width/2).toString()+"px",n=(i-c.height-5).toString()+"px"),l.style.left=d||"0",l.style.top=n||"0",l.style.visibility="visible"}}},renderTooltip:function(t,e,i,o,l,s,n,a,r,h,c,d,u,p){var m=window.sfBlazor.getCompInstance(t);m&&m.element&&m.renderTooltip(t,e,o,l,s,n,a,r,h,c,d,u,p)},fadeOut:function(t,e,i){var o=window.sfBlazor.getCompInstance(t);if(o&&o.element){if(sf.base.isNullOrUndefined(o)||!sf.base.isNullOrUndefined(o)&&sf.base.isNullOrUndefined(o.tooltip))return;var l=document.getElementById(o.dataId+"_tooltip_svg");""==o.previousCellID||e||(document.getElementById(o.previousCellID)&&o.setOpacity(document.getElementById(o.previousCellID),"1"),o.previousCellID=""),i||l&&o.tooltip.fadeOut()}},destroy:function(t){var e=window.sfBlazor.getCompInstance(t);e.element&&e&&e.destroy()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfheatmap');})})();