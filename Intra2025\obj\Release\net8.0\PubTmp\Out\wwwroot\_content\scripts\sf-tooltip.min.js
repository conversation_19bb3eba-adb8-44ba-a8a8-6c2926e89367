/*!*  filename: sf-tooltip.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[69],{"./bundles/sf-tooltip.js":function(t,e,i){"use strict";i.r(e);i("./modules/sf-tooltip.js")},"./modules/sf-tooltip.js":function(t,e){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Tooltip=function(){"use strict";var t=function(){return(t=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var s in e=arguments[i])Object.prototype.hasOwnProperty.call(e,s)&&(t[s]=e[s]);return t}).apply(this,arguments)},e=function(){function t(t,e,i,o,s){this.popupObj=null,this.tipClass="e-tip-bottom",this.tooltipPositionX="Center",this.tooltipPositionY="Top",this.isContinuousOpen=!1,this.isRestrictUpdate=!1,this.showTimer=0,this.hideTimer=0,this.contentTargetValue=null,this.contentEvent=null,this.contentAnimation=null,this.beforeCloseAnimation=null,this.isPopupHidden=!0,this.isMultiTarget=!1,this.mouseAction=!1,this.mouseMoveEvent=null,this.mouseMoveTarget=null,this.containerElement=null,this.isBodyContainer=!0,this.element=e,this.dataId=t,this.properties=o,this.dotnetRef=i,sf.base.isNullOrUndefined(this.element)||(this.ctrlId=this.element.id,window.sfBlazor.setCompInstance(this),this.eventList=s)}return t.prototype.getTriggerList=function(t){return"Auto"===t&&(t=sf.base.Browser.isDevice?"Hover":"Hover Focus"),t.split(" ")},t.prototype.formatPosition=function(){var t,e;0===this.properties.position.indexOf("Top")||0===this.properties.position.indexOf("Bottom")?(t=this.properties.position.split(/(?=[A-Z])/),this.tooltipPositionY=t[0],this.tooltipPositionX=t[1]):(e=this.properties.position.split(/(?=[A-Z])/),this.tooltipPositionX=e[0],this.tooltipPositionY=e[1])},t.prototype.getTargetList=function(t){if(!t||""===t)return[this.element];var e=[].slice.call(sf.base.selectAll(t,this.element));return e.length>0?e:[].slice.call(sf.base.selectAll(t,document))},t.prototype.wireEvents=function(t){var e=this,i=this.getTriggerList(t),o=this.getTargetList(this.properties.target);i.forEach((function(t){"Custom"!==t&&o.forEach((function(i){switch(t){case"Focus":sf.base.EventHandler.add(i,"focus",e.targetHover,e);break;case"Click":sf.base.EventHandler.add(i,sf.base.Browser.touchStartEvent,e.targetClick,e);break;case"Hover":sf.base.Browser.isDevice?(e.touchModule=new sf.base.Touch(i,{tapHoldThreshold:500,tapHold:e.tapHoldHandler.bind(e)}),sf.base.EventHandler.add(i,sf.base.Browser.touchEndEvent,e.touchEndHandler,e)):(sf.base.EventHandler.add(i,"mouseover",e.targetHover,e),e.properties.isSticky||sf.base.EventHandler.add(i,"mouseleave",e.onMouseOut,e))}}))})),sf.base.EventHandler.add(document,"touchend",this.touchEnd,this),sf.base.EventHandler.add(document,"scroll wheel",this.scrollHandler.bind(this),{passive:!0}),sf.base.EventHandler.add(document,"keydown",this.keyDown,this),window.addEventListener("resize",this.onWindowResize.bind(this))},t.prototype.onWindowResize=function(){this.isHidden()||this.reposition(this.findTarget())},t.prototype.wireMouseEvents=function(t,e){this.tooltipEle&&(this.properties.isSticky||("focus"===t.type&&sf.base.EventHandler.add(e,"blur",this.onMouseOut,this),this.properties.closeDelay&&(sf.base.EventHandler.add(this.tooltipEle,"mouseenter",this.tooltipHover,this),sf.base.EventHandler.add(this.tooltipEle,"mouseleave",this.tooltipMouseOut,this))),this.properties.mouseTrail&&sf.base.EventHandler.add(e,"mousemove touchstart mouseenter",this.onMouseMove,this))},t.prototype.unWireEvents=function(t){var e=this,i=this.getTriggerList(t),o=this.getTargetList(this.properties.target);i.forEach((function(t){"Custom"!==t&&o.forEach((function(i){switch(t){case"Focus":sf.base.EventHandler.remove(i,"focus",e.targetHover);break;case"Click":sf.base.EventHandler.remove(i,sf.base.Browser.touchStartEvent,e.targetClick);break;case"Hover":sf.base.Browser.isDevice?(e.touchModule&&e.touchModule.destroy(),sf.base.EventHandler.remove(i,sf.base.Browser.touchEndEvent,e.touchEndHandler)):(sf.base.EventHandler.remove(i,"mouseover",e.targetHover),e.properties.isSticky||sf.base.EventHandler.remove(i,"mouseleave",e.onMouseOut))}}))})),sf.base.EventHandler.remove(document,"touchend",this.touchEnd),sf.base.EventHandler.remove(document,"scroll wheel",this.scrollHandler),sf.base.EventHandler.remove(document,"keydown",this.keyDown),window.removeEventListener("resize",this.onWindowResize.bind(this))},t.prototype.unWireMouseEvents=function(t){var e=this;this.properties.isSticky||(this.getTriggerList(this.properties.opensOn).forEach((function(i){"Focus"===i&&sf.base.EventHandler.remove(t,"blur",e.onMouseOut)})),this.properties.closeDelay&&(sf.base.EventHandler.remove(t,"mouseenter",this.tooltipHover),sf.base.EventHandler.remove(t,"mouseleave",this.tooltipMouseOut)));this.properties.mouseTrail&&sf.base.EventHandler.remove(t,"mousemove touchstart mouseenter",this.onMouseMove)},t.prototype.findTarget=function(){return sf.base.select('[data-tooltip-id= "'+this.ctrlId+'_content"]',document)},t.prototype.addDescribedBy=function(t,e){var i=(sf.base.getAttributeOrDefault(t,"aria-describedby",null)||"").split(/\s+/);i.indexOf(e)<0&&i.push(e),sf.base.attributes(t,{"aria-describedby":i.join(" ").trim(),"data-tooltip-id":e})},t.prototype.removeDescribedBy=function(t){var e=sf.base.getAttributeOrDefault(t,"data-tooltip-id",null),i=(sf.base.getAttributeOrDefault(t,"aria-describedby",null)||"").split(/\s+/),o=i.indexOf(e);-1!==o&&i.splice(o,1),t.removeAttribute("data-tooltip-id");var s=i.join(" ").trim();s?sf.base.attributes(t,{"aria-describedby":s}):t.removeAttribute("aria-describedby")},t.prototype.clear=function(){if(this.isPopupHidden){if(this.popupObj&&this.popupObj.destroy(),this.tooltipEle){sf.base.removeClass([this.tooltipEle],"e-popup-close"),sf.base.addClass([this.tooltipEle],"e-popup-open"),this.tooltipEle.style.display="none";var t=document.getElementById(this.ctrlId+"_content_placeholder");t&&t.appendChild(this.tooltipEle),document.body.contains(this.element)&&this.dotnetRef.invokeMethodAsync("CreateTooltip",!1).catch((function(){}))}this.tooltipEle=null,this.popupObj=null}},t.prototype.tapHoldHandler=function(t){this.targetHover(t.originalEvent)},t.prototype.touchEndHandler=function(){this.properties.isSticky||this.hideTooltip(this.properties.animation.close)},t.prototype.targetClick=function(t){var e=this.properties.target?sf.base.closest(t.target,this.properties.target):this.element;e&&(null===sf.base.getAttributeOrDefault(e,"data-tooltip-id",null)?this.targetHover(t):this.properties.isSticky||this.hideTooltip(this.properties.animation.close,t,e))},t.prototype.restoreElement=function(t){this.unWireMouseEvents(t),sf.base.isNullOrUndefined(sf.base.getAttributeOrDefault(t,"data-content",null))||t.removeAttribute("data-content"),this.removeDescribedBy(t)},t.prototype.checkForOpen=function(t,e,i){if(sf.base.isNullOrUndefined(e)||sf.base.isNullOrUndefined(i))return!1;var o=this.properties.target?sf.base.closest(i.target,this.properties.target):this.element;if(sf.base.isNullOrUndefined(o))return!1;var s=!0;return"Hover"===t?s=sf.base.matches(o,":hover"):"Auto"===t?s=sf.base.matches(o,":hover")||sf.base.matches(o,":focus"):"Focus"===t?s=sf.base.matches(o,":focus"):"Click"!==t&&"Custom"!==t||(s=null===sf.base.getAttributeOrDefault(o,"data-tooltip-id",null)),s},t.prototype.targetHover=function(t){var e=this;if(this.checkForOpen(this.properties.opensOn,this.element,t)){var i=this.properties.target?sf.base.closest(t.target,this.properties.target):this.element,o=i.getAttribute("title");if(i.title="",""!==o&&null!==o&&sf.base.attributes(i,{"sf-tooltip":o}),!sf.base.isNullOrUndefined(i)&&null===sf.base.getAttributeOrDefault(i,"data-tooltip-id",null)){if(this.isMultiTarget=this.getTargetList(this.properties.target).length>1,this.properties.target&&this.isMultiTarget&&t&&!sf.base.isNullOrUndefined(t.target)&&!sf.base.isNullOrUndefined(t.relatedTarget))if(sf.base.closest(t.target,this.properties.target)==sf.base.closest(t.relatedTarget,this.properties.target))return;[].slice.call(sf.base.selectAll('[data-tooltip-id= "'+this.ctrlId+'_content"]',document)).forEach((function(t){e.restoreElement(t)})),this.mouseAction=!1,this.showTooltip(i,this.properties.animation.open,t)}}},t.prototype.isHidden=function(){return!this.tooltipEle||!this.tooltipEle.classList.contains("e-popup-open")},t.prototype.mouseMoveBeforeOpen=function(t){this.mouseMoveEvent=t},t.prototype.mouseMoveBeforeRemove=function(){this.mouseMoveTarget&&sf.base.EventHandler.remove(this.mouseMoveTarget,"mousemove touchstart",this.mouseMoveBeforeOpen)},t.prototype.showTooltip=function(t,e,i){var o=this;this.mouseAction=!1,this.clear(),clearTimeout(this.showTimer),clearTimeout(this.hideTimer),this.properties.mouseTrail&&this.properties.openDelay&&(this.mouseMoveBeforeRemove(),this.mouseMoveTarget=t,sf.base.EventHandler.add(this.mouseMoveTarget,"mousemove touchstart",this.mouseMoveBeforeOpen,this));this.showTimer=setTimeout((function(){o.isContinuousOpen=!sf.base.isNullOrUndefined(o.tooltipEle),o.tooltipEventArgs={type:i?i.type.toString():null,cancel:!1,target:o.getDomObject("target",t),event:i||null,hasText:o.hasText(),element:o.getDomObject("tooltipElement",o.tooltipEle),isInteracted:!sf.base.isNullOrUndefined(i),name:"beforeRender",left:i?o.getXYValue(i,"x"):null,top:i?o.getXYValue(i,"y"):null},o.contentTargetValue=t,o.contentEvent=i,o.contentAnimation=e,o.isRestrictUpdate=o.eventList.beforeRender&&!o.isHidden(),o.eventList.beforeRender?o.triggerEvent("TriggerBeforeRenderEvent",o.tooltipEventArgs):o.beforeRenderCallBack(!1)}),this.properties.openDelay)},t.prototype.triggerEvent=function(t,e){document.body.contains(this.element)&&this.dotnetRef.invokeMethodAsync(t,e).catch((function(){}))},t.prototype.beforeRenderCallBack=function(t){t?(this.isPopupHidden=!0,this.clear(),this.mouseMoveBeforeRemove()):(this.isPopupHidden=!1,sf.base.isNullOrUndefined(this.tooltipEle)?document.body.contains(this.element)&&this.dotnetRef.invokeMethodAsync("CreateTooltip",!0).catch((function(){})):this.isContinuousOpen&&!this.isRestrictUpdate?this.contentUpdated():this.isRestrictUpdate=!1)},t.prototype.checkCollision=function(t,e,i){var o={left:e,top:i,position:this.properties.position,horizontal:this.tooltipPositionX,vertical:this.tooltipPositionY},s=sf.popups.isCollide(this.tooltipEle,this.checkCollideTarget(),e,i);return s.length>0&&(o.horizontal=s.indexOf("left")>=0?"Right":s.indexOf("right")>=0?"Left":this.tooltipPositionX,o.vertical=s.indexOf("top")>=0?"Bottom":s.indexOf("bottom")>=0?"Top":this.tooltipPositionY),o},t.prototype.checkCollideTarget=function(){return!this.properties.windowCollision&&this.properties.target?this.element:null},t.prototype.calculateElementPosition=function(t,e){return[this.isBodyContainer?t.left+e.left:t.left-this.containerElement.offsetLeft+e.left+window.pageXOffset+this.containerElement.scrollLeft,this.isBodyContainer?t.top+e.top:t.top-this.containerElement.offsetTop+e.top+window.pageYOffset+this.containerElement.scrollTop]},t.prototype.getScalingFactor=function(t){if(!t)return{x:1,y:1};var e={x:1,y:1},i=t.closest('[style*="transform: scale"]');if(i&&i!=this.tooltipEle&&i.contains(this.tooltipEle)){var o=window.getComputedStyle(i).getPropertyValue("transform").match(/matrix\(([^)]+)\)/)[1].split(",").map(parseFloat);e.x=o[0],e.y=o[3]}return e},t.prototype.collisionFlipFit=function(t,e,i){var o=this.checkCollision(t,e,i),s=o.position;this.tooltipPositionY!==o.vertical&&(s=0===this.properties.position.indexOf("Bottom")||0===this.properties.position.indexOf("Top")?o.vertical+this.tooltipPositionX:this.tooltipPositionX+o.vertical),this.tooltipPositionX!==o.horizontal&&(0===s.indexOf("Left")&&(o.vertical="LeftTop"===s||"LeftCenter"===s?"Top":"Bottom",s=o.vertical+"Left"),0===s.indexOf("Right")&&(o.vertical="RightTop"===s||"RightCenter"===s?"Top":"Bottom",s=o.vertical+"Right"),o.horizontal=this.tooltipPositionX),this.tooltipEventArgs={type:null,cancel:!1,target:this.getDomObject("target",t),event:null,isInteracted:!1,hasText:this.hasText(),element:this.getDomObject("tooltipElement",this.tooltipEle),collidedPosition:s,name:"beforeCollision",left:null,top:null},this.isRestrictUpdate=this.eventList.beforeCollision&&!this.isHidden(),this.eventList.beforeCollision&&this.triggerEvent("TriggerBeforeCollisionEvent",this.tooltipEventArgs);var n=o.vertical,r=o.horizontal;if(o.position!==s){var l=sf.popups.calculatePosition(t,r,n,!this.isBodyContainer,this.isBodyContainer?null:this.containerElement.getBoundingClientRect());this.adjustArrow(t,s,r,n);var p=this.getScalingFactor(t),a=this.calculateTooltipOffset(s,p.x,p.y);a.top-=this.getOffSetPosition("TopBottom",s,this.properties.offsetY),a.left-=this.getOffSetPosition("RightLeft",s,this.properties.offsetX),o.position=s;var h=this.calculateElementPosition(l,a),f=h[0],c=h[1];o.left=f,o.top=c}else this.adjustArrow(t,s,r,n);var d={left:o.left,top:o.top},u=sf.popups.fit(this.tooltipEle,this.checkCollideTarget(),{X:!0,Y:!1},d).left;if(sf.base.setStyleAttribute(this.tooltipEle,{display:"block"}),this.properties.showTipPointer&&(0===s.indexOf("Bottom")||0===s.indexOf("Top"))){var b=sf.base.select(".e-arrow-tip",this.tooltipEle),m=parseInt(b.style.left,10)-(u-o.left);m<0?m=0:m+b.offsetWidth>this.tooltipEle.clientWidth&&(m=this.tooltipEle.clientWidth-b.offsetWidth),sf.base.setStyleAttribute(b,{left:m.toString()+"px"})}return sf.base.setStyleAttribute(this.tooltipEle,{display:""}),d.left=u,window.scrollX&&"BottomRight"==this.properties.position&&(d.left=this.element.getBoundingClientRect().left+window.scrollX-this.element.offsetWidth),d},t.prototype.getOffSetPosition=function(t,e,i){return-1!==t.indexOf(this.properties.position.split(/(?=[A-Z])/)[0])&&-1!==t.indexOf(e.split(/(?=[A-Z])/)[0])?2*i:0},t.prototype.hideTooltip=function(t,e,i){var o=this;clearTimeout(this.hideTimer),clearTimeout(this.showTimer);var s=function(){var s;o.checkForOpen(o.properties.opensOn,o.element,e)||(o.properties.closeDelay&&o.tooltipEle&&o.isTooltipOpen||(s=e?o.properties.target?i||e.target:o.element:sf.base.select('[data-tooltip-id= "'+o.ctrlId+'_content"]',document),o.tooltipEventArgs={type:e?e.type.toString():null,cancel:!1,target:o.getDomObject("target",s),event:e||null,element:o.getDomObject("tooltipElement",o.tooltipEle),hasText:o.hasText(),isInteracted:!sf.base.isNullOrUndefined(e),name:"beforeClose",collidedPosition:null,left:e?o.getXYValue(e,"x"):null,top:e?o.getXYValue(e,"y"):null},o.beforeCloseTarget=s,o.beforeCloseAnimation=t,o.isRestrictUpdate=o.eventList.beforeClose&&!o.isHidden(),o.eventList.beforeClose?o.triggerEvent("TriggerBeforeCloseEvent",o.tooltipEventArgs):o.beforeCloseCallBack(!1)))};!sf.base.isNullOrUndefined(this.tooltipEle)&&this.properties.closeDelay<=0?s():this.hideTimer=setTimeout(s,this.properties.closeDelay)},t.prototype.beforeCloseCallBack=function(t){if(t)this.isPopupHidden=!1;else{var e=this;e.mouseMoveBeforeRemove();this.popupObj?this.popupHide(this.beforeCloseAnimation,this.beforeCloseTarget):setTimeout((function(){e.isMultiTarget||e.popupHide(e.beforeCloseAnimation,e.beforeCloseTarget)}),200)}},t.prototype.popupHide=function(t,e){e&&this.restoreElement(e),this.isPopupHidden=!0;var i={name:"None"===t.effect&&"Enable"===sf.base.animationMode?"FadeOut":t.effect,duration:t.duration,delay:t.delay,timingFunction:"easeIn"};("None"===t.effect&&"Enable"!==sf.base.animationMode||"Disable"===sf.base.animationMode)&&(i=void 0),this.popupObj&&this.popupObj.hide(i)},t.prototype.calculateTooltipOffset=function(t,e,i){void 0===e&&(e=1),void 0===i&&(i=1);var o=this.tooltipEle.getBoundingClientRect(),s=sf.base.select(".e-arrow-tip",this.tooltipEle),n=s?s.getBoundingClientRect():null,r=s?n.width:0,l=s?n.height:0,p=this.properties.showTipPointer?0:8,a={top:0,left:0},h=l/2+2+(o.height-this.tooltipEle.clientHeight*i),f=r/2+2+(o.width-this.tooltipEle.clientWidth*e);switch(this.properties.mouseTrail&&(p+=2),t){case"RightTop":a.left+=r+p,a.top-=o.height-h;break;case"RightCenter":a.left+=r+p,a.top-=o.height/2;break;case"RightBottom":a.left+=r+p,a.top-=h;break;case"BottomRight":a.top+=l+p,a.left-=f;break;case"BottomCenter":a.top+=l+p,a.left-=o.width/2;break;case"BottomLeft":a.top+=l+p,a.left-=o.width-f;break;case"LeftBottom":a.left-=r+o.width+p,a.top-=h;break;case"LeftCenter":a.left-=r+o.width+p,a.top-=o.height/2;break;case"LeftTop":a.left-=r+o.width+p,a.top-=o.height-h;break;case"TopLeft":a.top-=o.height+l+p,a.left-=o.width-f;break;case"TopRight":a.top-=o.height+l+p,a.left-=f;break;default:a.top-=o.height+l+p,a.left-=o.width/2}return a.left+=this.properties.offsetX,a.top+=this.properties.offsetY,a},t.prototype.setTipClass=function(t){0===t.indexOf("Right")?this.tipClass="e-tip-left":0===t.indexOf("Bottom")?this.tipClass="e-tip-top":0===t.indexOf("Left")?this.tipClass="e-tip-right":this.tipClass="e-tip-bottom"},t.prototype.updateTipPosition=function(t){var e=sf.base.selectAll(".e-arrow-tip,.e-arrow-tip-outer,.e-arrow-tip-inner",this.tooltipEle),i=["e-tip-bottom","e-tip-top","e-tip-left","e-tip-right"];sf.base.removeClass(e,i),this.setTipClass(t),sf.base.addClass(e,this.tipClass)},t.prototype.adjustArrow=function(t,e,i,o){if(!1!==this.properties.showTipPointer){this.updateTipPosition(e),sf.base.setStyleAttribute(this.tooltipEle,{display:"block"});var s,n,r=this.tooltipEle.clientWidth,l=this.tooltipEle.clientHeight,p=sf.base.select(".e-arrow-tip",this.tooltipEle),a=sf.base.select(".e-arrow-tip-inner",this.tooltipEle),h=p.offsetWidth,f=p.offsetHeight;sf.base.setStyleAttribute(this.tooltipEle,{display:""});var c,d="End"===this.properties.tipPointerPosition,u="Start"===this.properties.tipPointerPosition;if("e-tip-bottom"===this.tipClass||"e-tip-top"===this.tipClass){if(n="e-tip-bottom"===this.tipClass?"99.9%":-(f-1)+"px",sf.base.setStyleAttribute(a,{top:"-"+("e-tip-bottom"===this.tipClass?f-2:f-6)+"px"}),t)s=(c="Center"!==i||r>t.offsetWidth||this.properties.mouseTrail)&&"Left"===i||!c&&d?r-h-2+"px":c&&"Right"===i||!c&&u?"2px":c&&(d||u)?d?t.offsetWidth+(this.tooltipEle.offsetWidth-t.offsetWidth)/2-h/2-2+"px":(this.tooltipEle.offsetWidth-t.offsetWidth)/2-h/2+2+"px":r/2-h/2+"px"}else s="e-tip-right"===this.tipClass?"99.9%":-(h-1)+"px",sf.base.setStyleAttribute(a,{left:(this.tipClass,-(h-2)+"px")}),n=(c="Center"!==o||l>t.offsetHeight||this.properties.mouseTrail)&&"Top"===o||!c&&d?l-f-2+"px":c&&"Bottom"===o||!c&&u?"2px":l/2-f/2+"px";window.scrollX&&"BottomRight"===this.properties.position&&(s=this.element.offsetLeft+"px"),sf.base.setStyleAttribute(p,{top:n,left:s})}},t.prototype.tooltipHover=function(){this.tooltipEle&&(this.isTooltipOpen=!0)},t.prototype.tooltipMouseOut=function(t){this.isTooltipOpen=!1,this.hideTooltip(this.properties.animation.close,t,this.findTarget())},t.prototype.onMouseOut=function(t){var e=t.relatedTarget;if(this.mouseAction=!0,e&&!this.properties.mouseTrail){var i=sf.base.closest(e,".e-tooltip-wrap.e-lib.e-popup");i?sf.base.EventHandler.add(i,"mouseleave",this.tooltipElementMouseOut,this):(this.hideTooltip(this.properties.animation.close,t,this.findTarget()),0===this.properties.closeDelay&&this.clear())}else this.hideTooltip(this.properties.animation.close,t,this.findTarget()),this.clear()},t.prototype.tooltipElementMouseOut=function(t){this.hideTooltip(this.properties.animation.close,t,this.findTarget()),sf.base.EventHandler.remove(this.element,"mouseleave",this.tooltipElementMouseOut),this.clear()},t.prototype.onMouseMove=function(t){if(this.tooltipEle){var e=0,i=0;t.type.indexOf("touch")>-1?(t.preventDefault(),e=t.touches[0].pageX,i=t.touches[0].pageY):(e=t.pageX,i=t.pageY),sf.base.Animation.stop(this.tooltipEle),sf.base.removeClass([this.tooltipEle],"e-popup-close"),sf.base.addClass([this.tooltipEle],"e-popup-open"),this.adjustArrow(t.target,this.properties.position,this.tooltipPositionX,this.tooltipPositionY);var o=this.getScalingFactor(t.target),s=this.calculateTooltipOffset(this.properties.position,o.x,o.y),n=e+s.left+this.properties.offsetX,r=i+s.top+this.properties.offsetY,l=this.checkCollision(t.target,n,r);if(this.tooltipPositionX!==l.horizontal||this.tooltipPositionY!==l.vertical){var p=0===this.properties.position.indexOf("Bottom")||0===this.properties.position.indexOf("Top")?l.vertical+l.horizontal:l.horizontal+l.vertical;l.position=p,this.adjustArrow(t.target,l.position,l.horizontal,l.vertical);var a=this.calculateTooltipOffset(l.position,o.x,o.y);l.left=e+a.left-this.properties.offsetX,l.top=i+a.top-this.properties.offsetY}sf.base.setStyleAttribute(this.tooltipEle,{left:l.left+"px",top:l.top+"px"})}},t.prototype.keyDown=function(t){this.tooltipEle&&27===t.keyCode&&this.hideTooltip(this.properties.animation.close)},t.prototype.touchEnd=function(t){this.tooltipEle&&null===sf.base.closest(t.target,".e-tooltip")&&!this.properties.isSticky&&this.hideTooltip(this.properties.animation.close)},t.prototype.scrollHandler=function(t){this.tooltipEle&&!this.properties.isSticky&&(sf.base.closest(t.target,".e-tooltip-wrap.e-lib.e-popup")||this.hideTooltip(this.properties.animation.close))},t.prototype.renderContent=function(t){var e=sf.base.getAttributeOrDefault(t,"sf-tooltip",null),i=sf.base.getAttributeOrDefault(t,"data-title",null);(!sf.base.isNullOrUndefined(e)&&t?(sf.base.attributes(t,{"data-content":e}),this.isMultiTarget=!0):!sf.base.isNullOrUndefined(i)&&t&&(sf.base.attributes(t,{"data-content":i}),this.isMultiTarget=!0),this.properties.content)||(sf.base.select(".e-tip-content",this.tooltipEle).innerText=t.getAttribute("data-content"))},t.prototype.setHeightWidth=function(t,e,i){this.tooltipEle&&sf.base.setStyleAttribute(this.tooltipEle,{height:e,width:t}),i&&"auto"!==this.tooltipEle.style.width&&(this.tooltipEle.style.maxWidth=t)},t.prototype.appendContainer=function(){"string"==typeof this.properties.container&&("body"===this.properties.container?this.containerElement=document.body:(this.isBodyContainer=!1,this.containerElement=sf.base.select(this.properties.container,document),sf.base.addClass([this.containerElement],"e-tooltip-popup-container"))),this.containerElement.appendChild(this.tooltipEle)},t.prototype.updateTarget=function(){if(this.contentTargetValue){sf.base.Browser.isDevice&&sf.base.addClass([this.tooltipEle],"e-bigger"),this.appendContainer(),sf.base.removeClass([this.tooltipEle],"e-hidden"),this.addDescribedBy(this.contentTargetValue,this.ctrlId+"_content"),this.renderContent(this.contentTargetValue),sf.base.addClass([this.tooltipEle],"e-popup-open"),this.renderPopup(this.contentTargetValue);var t=this.properties.position;this.adjustArrow(this.contentTargetValue,t,this.tooltipPositionX,this.tooltipPositionY),sf.base.Animation.stop(this.tooltipEle),this.reposition(this.contentTargetValue),this.afterContentRender()}},t.prototype.contentUpdated=function(){if(this.tooltipEle){if(!this.isContinuousOpen||this.isRestrictUpdate)return;this.updateTarget()}else this.ctrlId=this.element.id,this.tooltipEle=sf.base.select("#"+this.ctrlId+"_content",document),this.tooltipEle&&(this.setHeightWidth(sf.base.formatUnit(this.properties.width),sf.base.formatUnit(this.properties.height),this.contentTargetValue),this.updateTarget())},t.prototype.afterContentRender=function(){sf.base.removeClass([this.tooltipEle],"e-popup-open"),sf.base.addClass([this.tooltipEle],"e-popup-close"),this.tooltipEventArgs={type:this.contentEvent?this.contentEvent.type.toString():null,isInteracted:!sf.base.isNullOrUndefined(this.contentEvent),hasText:this.hasText(),target:this.getDomObject("target",this.contentTargetValue),name:"beforeOpen",cancel:!1,event:this.contentEvent?this.contentEvent:null,element:this.getDomObject("tooltipElement",this.tooltipEle),left:this.contentEvent?this.getXYValue(this.contentEvent,"x"):null,top:this.contentEvent?this.getXYValue(this.contentEvent,"y"):null},this.isRestrictUpdate=this.eventList.beforeOpen&&!this.isHidden(),this.eventList.beforeOpen?this.triggerEvent("TriggerBeforeOpenEvent",this.tooltipEventArgs):this.beforeOpenCallBack(!1)},t.prototype.beforeOpenCallBack=function(t){if(t)this.isPopupHidden=!0,this.contentTargetValue&&this.popupHide(this.properties.animation.close,this.contentTargetValue),this.mouseMoveBeforeRemove();else{if(sf.base.isNullOrUndefined(this.contentAnimation))return;var e={name:"None"===this.contentAnimation.effect&&"Enable"===sf.base.animationMode?"FadeIn":this.contentAnimation.effect,duration:this.contentAnimation.duration,delay:this.contentAnimation.delay,timingFunction:"easeOut"};("None"===this.contentAnimation.effect&&"Enable"!==sf.base.animationMode||"Disable"===sf.base.animationMode)&&(e=void 0),this.popupObj&&(this.popupObj.show(e,this.contentTargetValue),this.properties.openDelay&&this.properties.mouseTrail&&this.mouseMoveEvent&&this.onMouseMove(this.mouseMoveEvent))}this.contentEvent&&this.wireMouseEvents(this.contentEvent,this.contentTargetValue),this.contentTargetValue=this.contentEvent=this.contentAnimation=null},t.prototype.reposition=function(t){if(null!==t&&this.tooltipEle){var e=this.getTooltipPosition(t);this.popupObj&&(this.popupObj.position={X:e.left,Y:e.top},this.popupObj.dataBind())}},t.prototype.renderPopup=function(t){var e=this.properties.mouseTrail?{top:0,left:0}:this.getTooltipPosition(t);this.tooltipEle.classList.remove("e-lib"),this.popupObj=new sf.popups.Popup(this.tooltipEle,{height:this.properties.height,width:this.properties.width,position:{X:e.left,Y:e.top},enableRtl:this.properties.enableRtl,open:this.openPopupHandler.bind(this),close:this.closePopupHandler.bind(this)})},t.prototype.openPopupHandler=function(){this.tooltipEle&&this.tooltipEle.classList.contains("e-popup-close")&&this.isMultiTarget&&(sf.base.removeClass([this.tooltipEle],"e-popup-close"),sf.base.addClass([this.tooltipEle],"e-popup-open")),this.properties.mouseTrail||this.reposition(this.contentTargetValue?this.contentTargetValue:this.findTarget()),this.tooltipEventArgs.name="Opened",this.isRestrictUpdate=this.eventList.opened&&!this.isHidden(),this.eventList.opened&&this.triggerEvent("TriggerOpenedEvent",this.tooltipEventArgs),this.mouseAction&&!sf.base.isNullOrUndefined(this.tooltipEle)&&this.hideTooltip(this.properties.animation.close)},t.prototype.closePopupHandler=function(){this.clear(),this.tooltipEventArgs.name="Closed",this.isRestrictUpdate=this.eventList.closed&&!this.isHidden(),this.eventList.closed&&this.triggerEvent("TriggerClosedEvent",this.tooltipEventArgs)},t.prototype.getTooltipPosition=function(t){sf.base.setStyleAttribute(this.tooltipEle,{display:"block"}),this.formatPosition();var e=sf.popups.calculatePosition(t,this.tooltipPositionX,this.tooltipPositionY,!this.isBodyContainer,this.isBodyContainer?null:this.containerElement.getBoundingClientRect()),i=this.getScalingFactor(t),o=this.calculateTooltipOffset(this.properties.position,i.x,i.y),s=this.calculateElementPosition(e,o),n=this.collisionFlipFit(t,s[0],s[1]);return n.left=n.left/i.x,n.top=n.top/i.y,sf.base.setStyleAttribute(this.tooltipEle,{display:""}),n},t.prototype.getDomObject=function(t,e){return e?window.sfBlazor.getDomObject(t,e):null},t.prototype.hasText=function(){return!!this.tooltipEle&&""!==this.tooltipEle.innerText.trim()},t.prototype.getXYValue=function(t,e){var i,o=t.changedTouches;if(!(i="x"===e?o?o[0].clientX:t.clientX:o?o[0].clientY:t.clientY)&&"focus"===t.type&&t.target){var s=t.target.getBoundingClientRect();i=s?"x"===e?s.left:s.top:null}return Math.ceil(i)},t.prototype.destroy=function(){if(this.tooltipEle){var t=sf.base.select("#"+this.ctrlId+"_content_placeholder",document);t&&t.appendChild(this.tooltipEle)}this.popupObj&&this.popupObj.destroy(),sf.base.removeClass([this.element],"e-tooltip"),this.unWireEvents(this.properties.opensOn),this.unWireMouseEvents(this.element),this.tooltipEle=null,this.popupObj=null},t}();return{wireEvents:function(t,i,o,s,n){new e(t,i,o,s,n);var r=window.sfBlazor.getCompInstance(t);i&&!sf.base.isNullOrUndefined(r)&&(r.formatPosition(),r.wireEvents(s.opensOn))},contentUpdated:function(t){var e=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(e)||e.contentUpdated()},beforeRenderCallBack:function(t,e){var i=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(i)||i.beforeRenderCallBack(e)},beforeOpenCallBack:function(t,e){var i=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(i)||i.beforeOpenCallBack(e)},beforeCloseCallBack:function(t,e){var i=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(i)||i.beforeCloseCallBack(e)},showTooltip:function(t,e,i,o){var s=window.sfBlazor.getCompInstance(t);if(!sf.base.isNullOrUndefined(s)){if(null!==o&&""!==o){var n=sf.base.select(o,s.element),r=sf.base.select(o,document);n?e=n:r&&(e=r)}e&&0==e.offsetWidth||s.showTooltip(e,i,null)}},hideTooltip:function(t,e){var i=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(i)||i.hideTooltip(e)},destroy:function(t){var e=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(e)||e.destroy()},refresh:function(t){var e=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(e)||(e.isPopupHidden||e.hideTooltip(e.properties.animation.close),e.unWireEvents(e.properties.opensOn),e.wireEvents(e.properties.opensOn))},refreshPosition:function(t,e,i){var o=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(o)||(null===e&&(e=null!==i&&""!==i?sf.base.select(i,o.element):o.element),o.reposition(e))},updateProperties:function(e,i){var o=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(o))if(o.isRestrictUpdate=!0,(i.target||i.opensOn||i.isSticky)&&o.unWireEvents(o.properties.opensOn),o.properties=t({},o.properties,i),i.target||i.opensOn||i.isSticky)o.wireEvents(o.properties.opensOn),o.formatPosition();else{var s=o.findTarget();if(i.container&&(sf.base.isNullOrUndefined(this.containerElement)||sf.base.removeClass([this.containerElement],"e-tooltip-popup-container"),o.tooltipEle&&s&&o.appendContainer()),(i.height||i.width)&&o.setHeightWidth(sf.base.formatUnit(i.width),sf.base.formatUnit(i.height),s),i.position&&o.formatPosition(),o.tooltipEle&&s){if(i.position&&o.properties.showTipPointer){var n=sf.base.select(".e-arrow-tip-inner",o.tooltipEle),r=sf.base.select(".e-arrow-tip",o.tooltipEle);sf.base.removeClass([r],[o.tipClass]),o.setTipClass(i.position),sf.base.addClass([r],[o.tipClass]),sf.base.setStyleAttribute(n,{top:null,left:null})}o.reposition(s)}o.isRestrictUpdate=!1}}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sftooltip');})})();