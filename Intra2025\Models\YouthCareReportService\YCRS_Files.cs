﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Intra2025.Models
{
    // Youth & Child Care Reporting Service (YCRS)
    public class YCRS_Files
    {
        [Key]
        public int Sno { get; set; } //系統流水號

        public string? Id { get; set; }  //參照來源主表的Id

        [MaxLength(200)]
        public string? Filepath { get; set; } //檔案路徑
        [MaxLength(100)]
        public string? FileName { get; set; } //檔案名稱

        public bool IsMarkedForDeletion { get; set; } = false;  //標記「上傳檔案」是否刪除

        public DateTime Createdate { get; set; } //上傳日期

    }
}
