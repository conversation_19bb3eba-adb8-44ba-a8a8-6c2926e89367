{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "Intra2025.styles.css", "AssetFile": "Intra2025.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "500"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JnNWFbVt2vn5MuHlXmdnMOTDmF0Dvw7CEDeo0a9Dw3k=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 10 Dec 2024 05:18:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JnNWFbVt2vn5MuHlXmdnMOTDmF0Dvw7CEDeo0a9Dw3k="}]}, {"Route": "Intra2025.yivx7aakch.styles.css", "AssetFile": "Intra2025.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "500"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JnNWFbVt2vn5MuHlXmdnMOTDmF0Dvw7CEDeo0a9Dw3k=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 10 Dec 2024 05:18:16 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yivx7aakch"}, {"Name": "integrity", "Value": "sha256-JnNWFbVt2vn5MuHlXmdnMOTDmF0Dvw7CEDeo0a9Dw3k="}, {"Name": "label", "Value": "Intra2025.styles.css"}]}, {"Route": "_content/core/images/ui-icons_444444_256x240.6bswslor37.png", "AssetFile": "_content/core/images/ui-icons_444444_256x240.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6992"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"UqedaeFzvUx4Yw5BSiMvvgjbHymLX7hbnqahlqjy3RQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 10 Dec 2024 06:14:10 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6bswslor37"}, {"Name": "integrity", "Value": "sha256-UqedaeFzvUx4Yw5BSiMvvgjbHymLX7hbnqahlqjy3RQ="}, {"Name": "label", "Value": "_content/core/images/ui-icons_444444_256x240.png"}]}, {"Route": "_content/core/images/ui-icons_444444_256x240.png", "AssetFile": "_content/core/images/ui-icons_444444_256x240.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6992"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"UqedaeFzvUx4Yw5BSiMvvgjbHymLX7hbnqahlqjy3RQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 10 Dec 2024 06:14:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UqedaeFzvUx4Yw5BSiMvvgjbHymLX7hbnqahlqjy3RQ="}]}, {"Route": "_content/core/images/ui-icons_555555_256x240.bhce541dxt.png", "AssetFile": "_content/core/images/ui-icons_555555_256x240.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6988"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"rzuSnKvYqTdPeQA55gD0iSulbPy3v6voMzJjin/+4bE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 10 Dec 2024 06:14:10 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bhce541dxt"}, {"Name": "integrity", "Value": "sha256-rzuSnKvYqTdPeQA55gD0iSulbPy3v6voMzJjin/+4bE="}, {"Name": "label", "Value": "_content/core/images/ui-icons_555555_256x240.png"}]}, {"Route": "_content/core/images/ui-icons_555555_256x240.png", "AssetFile": "_content/core/images/ui-icons_555555_256x240.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6988"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"rzuSnKvYqTdPeQA55gD0iSulbPy3v6voMzJjin/+4bE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 10 Dec 2024 06:14:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rzuSnKvYqTdPeQA55gD0iSulbPy3v6voMzJjin/+4bE="}]}, {"Route": "_content/core/jquery-1.11.2.min.eo0bhlxbdl.js", "AssetFile": "_content/core/jquery-1.11.2.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "95931"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ls0pXSlb7AYs7evhd+VLnWsZ/AqEHcXBeMZUycz/CcA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 10 Dec 2024 06:14:10 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eo0bhlxbdl"}, {"Name": "integrity", "Value": "sha256-Ls0pXSlb7AYs7evhd+VLnWsZ/AqEHcXBeMZUycz/CcA="}, {"Name": "label", "Value": "_content/core/jquery-1.11.2.min.js"}]}, {"Route": "_content/core/jquery-1.11.2.min.js", "AssetFile": "_content/core/jquery-1.11.2.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "95931"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ls0pXSlb7AYs7evhd+VLnWsZ/AqEHcXBeMZUycz/CcA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 10 Dec 2024 06:14:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ls0pXSlb7AYs7evhd+VLnWsZ/AqEHcXBeMZUycz/CcA="}]}, {"Route": "_content/core/jquery-ui.css", "AssetFile": "_content/core/jquery-ui.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "37041"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"6+5cXAZpZ/uBxrQgN6GFa5TDRSWeuUPZN1xsKuqPMWk=\""}, {"Name": "Last-Modified", "Value": "Wed, 11 Dec 2024 01:08:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6+5cXAZpZ/uBxrQgN6GFa5TDRSWeuUPZN1xsKuqPMWk="}]}, {"Route": "_content/core/jquery-ui.min.8jap42rgh0.js", "AssetFile": "_content/core/jquery-ui.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "340692"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lH1ZpQVmVqYZVzqxeMio4DLC9X7Vz90IBW6nSiBOLwY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 10 Dec 2024 06:14:10 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8jap42rgh0"}, {"Name": "integrity", "Value": "sha256-lH1ZpQVmVqYZVzqxeMio4DLC9X7Vz90IBW6nSiBOLwY="}, {"Name": "label", "Value": "_content/core/jquery-ui.min.js"}]}, {"Route": "_content/core/jquery-ui.min.js", "AssetFile": "_content/core/jquery-ui.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "340692"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lH1ZpQVmVqYZVzqxeMio4DLC9X7Vz90IBW6nSiBOLwY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 10 Dec 2024 06:14:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lH1ZpQVmVqYZVzqxeMio4DLC9X7Vz90IBW6nSiBOLwY="}]}, {"Route": "_content/core/jquery-ui.pbbfqobfjo.css", "AssetFile": "_content/core/jquery-ui.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "37041"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"6+5cXAZpZ/uBxrQgN6GFa5TDRSWeuUPZN1xsKuqPMWk=\""}, {"Name": "Last-Modified", "Value": "Wed, 11 Dec 2024 01:08:10 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pbbfqobfjo"}, {"Name": "integrity", "Value": "sha256-6+5cXAZpZ/uBxrQgN6GFa5TDRSWeuUPZN1xsKuqPMWk="}, {"Name": "label", "Value": "_content/core/jquery-ui.css"}]}, {"Route": "_content/js/jquery-ui-datepicker-zh.dyu961vm2t.js", "AssetFile": "_content/js/jquery-ui-datepicker-zh.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6161"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9d7No+fJPSkAIhd6CUVaP9e0WDv6mIbMyp2oeb3qRqg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 10 Dec 2024 06:14:10 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dyu961vm2t"}, {"Name": "integrity", "Value": "sha256-9d7No+fJPSkAIhd6CUVaP9e0WDv6mIbMyp2oeb3qRqg="}, {"Name": "label", "Value": "_content/js/jquery-ui-datepicker-zh.js"}]}, {"Route": "_content/js/jquery-ui-datepicker-zh.js", "AssetFile": "_content/js/jquery-ui-datepicker-zh.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6161"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9d7No+fJPSkAIhd6CUVaP9e0WDv6mIbMyp2oeb3qRqg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 10 Dec 2024 06:14:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9d7No+fJPSkAIhd6CUVaP9e0WDv6mIbMyp2oeb3qRqg="}]}, {"Route": "_content/scripts/data.min.js", "AssetFile": "_content/scripts/data.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "84714"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ESCctFxImItyfMib+ebXHZpJXL2HE7kavCsN7s3aoHs=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ESCctFxImItyfMib+ebXHZpJXL2HE7kavCsN7s3aoHs="}]}, {"Route": "_content/scripts/data.min.xwvq8l0ywm.js", "AssetFile": "_content/scripts/data.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "84714"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ESCctFxImItyfMib+ebXHZpJXL2HE7kavCsN7s3aoHs=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xwvq8l0ywm"}, {"Name": "integrity", "Value": "sha256-ESCctFxImItyfMib+ebXHZpJXL2HE7kavCsN7s3aoHs="}, {"Name": "label", "Value": "_content/scripts/data.min.js"}]}, {"Route": "_content/scripts/drawings.min.js", "AssetFile": "_content/scripts/drawings.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "65634"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5qKOpZ5jo4LS4yrLnjH5QTWhoslY721mNuZJueLqpwQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5qKOpZ5jo4LS4yrLnjH5QTWhoslY721mNuZJueLqpwQ="}]}, {"Route": "_content/scripts/drawings.min.ubbzp0w6g8.js", "AssetFile": "_content/scripts/drawings.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "65634"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5qKOpZ5jo4LS4yrLnjH5QTWhoslY721mNuZJueLqpwQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ubbzp0w6g8"}, {"Name": "integrity", "Value": "sha256-5qKOpZ5jo4LS4yrLnjH5QTWhoslY721mNuZJueLqpwQ="}, {"Name": "label", "Value": "_content/scripts/drawings.min.js"}]}, {"Route": "_content/scripts/navigationsbase.min.js", "AssetFile": "_content/scripts/navigationsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19957"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lo6XsRoPBkbA1AO7QWc9w9IHURXtum7IgjXtdevJTF0=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lo6XsRoPBkbA1AO7QWc9w9IHURXtum7IgjXtdevJTF0="}]}, {"Route": "_content/scripts/navigationsbase.min.qonmkvqdhv.js", "AssetFile": "_content/scripts/navigationsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19957"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lo6XsRoPBkbA1AO7QWc9w9IHURXtum7IgjXtdevJTF0=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qonmkvqdhv"}, {"Name": "integrity", "Value": "sha256-lo6XsRoPBkbA1AO7QWc9w9IHURXtum7IgjXtdevJTF0="}, {"Name": "label", "Value": "_content/scripts/navigationsbase.min.js"}]}, {"Route": "_content/scripts/popup.min.7ysspdopse.js", "AssetFile": "_content/scripts/popup.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16251"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"j06ROtdgRCQj6sE5R4M82Lkgc4eNoVBRQFx5pmZtxzA=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ysspdopse"}, {"Name": "integrity", "Value": "sha256-j06ROtdgRCQj6sE5R4M82Lkgc4eNoVBRQFx5pmZtxzA="}, {"Name": "label", "Value": "_content/scripts/popup.min.js"}]}, {"Route": "_content/scripts/popup.min.js", "AssetFile": "_content/scripts/popup.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16251"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"j06ROtdgRCQj6sE5R4M82Lkgc4eNoVBRQFx5pmZtxzA=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j06ROtdgRCQj6sE5R4M82Lkgc4eNoVBRQFx5pmZtxzA="}]}, {"Route": "_content/scripts/popupsbase.min.js", "AssetFile": "_content/scripts/popupsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13506"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"99QH2n4MwyoCA7O0fpCIvBSckWka7RKZPf6oQztR7GI=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-99QH2n4MwyoCA7O0fpCIvBSckWka7RKZPf6oQztR7GI="}]}, {"Route": "_content/scripts/popupsbase.min.n06a8k4yyj.js", "AssetFile": "_content/scripts/popupsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "13506"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"99QH2n4MwyoCA7O0fpCIvBSckWka7RKZPf6oQztR7GI=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n06a8k4yyj"}, {"Name": "integrity", "Value": "sha256-99QH2n4MwyoCA7O0fpCIvBSckWka7RKZPf6oQztR7GI="}, {"Name": "label", "Value": "_content/scripts/popupsbase.min.js"}]}, {"Route": "_content/scripts/sf-accordion.min.2syd9uz43e.js", "AssetFile": "_content/scripts/sf-accordion.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12530"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UgIjdPMTEKBPKAThXpbkuY2oFCOTqWkEoNQWMpuffag=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2syd9uz43e"}, {"Name": "integrity", "Value": "sha256-UgIjdPMTEKBPKAThXpbkuY2oFCOTqWkEoNQWMpuffag="}, {"Name": "label", "Value": "_content/scripts/sf-accordion.min.js"}]}, {"Route": "_content/scripts/sf-accordion.min.js", "AssetFile": "_content/scripts/sf-accordion.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12530"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UgIjdPMTEKBPKAThXpbkuY2oFCOTqWkEoNQWMpuffag=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UgIjdPMTEKBPKAThXpbkuY2oFCOTqWkEoNQWMpuffag="}]}, {"Route": "_content/scripts/sf-accumulation-chart.min.7rxell4l3c.js", "AssetFile": "_content/scripts/sf-accumulation-chart.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19743"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nTSxSkdEY0+uSfw0puM3VQUd88DOfrJNr/QFqnoRiUg=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7rxell4l3c"}, {"Name": "integrity", "Value": "sha256-nTSxSkdEY0+uSfw0puM3VQUd88DOfrJNr/QFqnoRiUg="}, {"Name": "label", "Value": "_content/scripts/sf-accumulation-chart.min.js"}]}, {"Route": "_content/scripts/sf-accumulation-chart.min.js", "AssetFile": "_content/scripts/sf-accumulation-chart.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19743"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nTSxSkdEY0+uSfw0puM3VQUd88DOfrJNr/QFqnoRiUg=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nTSxSkdEY0+uSfw0puM3VQUd88DOfrJNr/QFqnoRiUg="}]}, {"Route": "_content/scripts/sf-barcode.min.js", "AssetFile": "_content/scripts/sf-barcode.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5319"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"d3tvh2A2Y7GlD7WHvaZ1SXV3x6IqsnfLL3kQTi4sDak=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d3tvh2A2Y7GlD7WHvaZ1SXV3x6IqsnfLL3kQTi4sDak="}]}, {"Route": "_content/scripts/sf-barcode.min.tbqde4abqe.js", "AssetFile": "_content/scripts/sf-barcode.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5319"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"d3tvh2A2Y7GlD7WHvaZ1SXV3x6IqsnfLL3kQTi4sDak=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tbqde4abqe"}, {"Name": "integrity", "Value": "sha256-d3tvh2A2Y7GlD7WHvaZ1SXV3x6IqsnfLL3kQTi4sDak="}, {"Name": "label", "Value": "_content/scripts/sf-barcode.min.js"}]}, {"Route": "_content/scripts/sf-breadcrumb.min.c70vyb5upx.js", "AssetFile": "_content/scripts/sf-breadcrumb.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4930"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OK95tKsZknbsJTboLATsZ2B8gWZXM9qexrRz9LWiUnY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c70vyb5upx"}, {"Name": "integrity", "Value": "sha256-OK95tKsZknbsJTboLATsZ2B8gWZXM9qexrRz9LWiUnY="}, {"Name": "label", "Value": "_content/scripts/sf-breadcrumb.min.js"}]}, {"Route": "_content/scripts/sf-breadcrumb.min.js", "AssetFile": "_content/scripts/sf-breadcrumb.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4930"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OK95tKsZknbsJTboLATsZ2B8gWZXM9qexrRz9LWiUnY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OK95tKsZknbsJTboLATsZ2B8gWZXM9qexrRz9LWiUnY="}]}, {"Route": "_content/scripts/sf-bullet-chart.min.js", "AssetFile": "_content/scripts/sf-bullet-chart.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7975"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"paU7lDENGqQ2+lxuuYQcWJStJJYFYP2Y7geg/HdBTIQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-paU7lDENGqQ2+lxuuYQcWJStJJYFYP2Y7geg/HdBTIQ="}]}, {"Route": "_content/scripts/sf-bullet-chart.min.x5lyqfln19.js", "AssetFile": "_content/scripts/sf-bullet-chart.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7975"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"paU7lDENGqQ2+lxuuYQcWJStJJYFYP2Y7geg/HdBTIQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x5lyqfln19"}, {"Name": "integrity", "Value": "sha256-paU7lDENGqQ2+lxuuYQcWJStJJYFYP2Y7geg/HdBTIQ="}, {"Name": "label", "Value": "_content/scripts/sf-bullet-chart.min.js"}]}, {"Route": "_content/scripts/sf-calendar.min.fbuqdk6i07.js", "AssetFile": "_content/scripts/sf-calendar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1994"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+/grmsgXyMtS6RVUBarsJ1Y/kwZTAJAl8JeUSmdhGtA=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fbuqdk6i07"}, {"Name": "integrity", "Value": "sha256-+/grmsgXyMtS6RVUBarsJ1Y/kwZTAJAl8JeUSmdhGtA="}, {"Name": "label", "Value": "_content/scripts/sf-calendar.min.js"}]}, {"Route": "_content/scripts/sf-calendar.min.js", "AssetFile": "_content/scripts/sf-calendar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1994"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+/grmsgXyMtS6RVUBarsJ1Y/kwZTAJAl8JeUSmdhGtA=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+/grmsgXyMtS6RVUBarsJ1Y/kwZTAJAl8JeUSmdhGtA="}]}, {"Route": "_content/scripts/sf-carousel.min.iqlib54np6.js", "AssetFile": "_content/scripts/sf-carousel.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5884"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dqLe46n/8FPBfz+ZXI5UdmzE2cMh7IghS1jtq/Xzq6c=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iqlib54np6"}, {"Name": "integrity", "Value": "sha256-dqLe46n/8FPBfz+ZXI5UdmzE2cMh7IghS1jtq/Xzq6c="}, {"Name": "label", "Value": "_content/scripts/sf-carousel.min.js"}]}, {"Route": "_content/scripts/sf-carousel.min.js", "AssetFile": "_content/scripts/sf-carousel.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5884"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dqLe46n/8FPBfz+ZXI5UdmzE2cMh7IghS1jtq/Xzq6c=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dqLe46n/8FPBfz+ZXI5UdmzE2cMh7IghS1jtq/Xzq6c="}]}, {"Route": "_content/scripts/sf-chart.min.js", "AssetFile": "_content/scripts/sf-chart.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "196064"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"M4MlaSYZobf6Ypzl6skwtk5JYpXV5l1BPqcK6EJ+g5s=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M4MlaSYZobf6Ypzl6skwtk5JYpXV5l1BPqcK6EJ+g5s="}]}, {"Route": "_content/scripts/sf-chart.min.plssletgq9.js", "AssetFile": "_content/scripts/sf-chart.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "196064"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"M4MlaSYZobf6Ypzl6skwtk5JYpXV5l1BPqcK6EJ+g5s=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "plssletgq9"}, {"Name": "integrity", "Value": "sha256-M4MlaSYZobf6Ypzl6skwtk5JYpXV5l1BPqcK6EJ+g5s="}, {"Name": "label", "Value": "_content/scripts/sf-chart.min.js"}]}, {"Route": "_content/scripts/sf-chart3D.min.agbtsifi4f.js", "AssetFile": "_content/scripts/sf-chart3D.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "65523"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OnNUV9bnhR8scHT18PZSuIvlH39uQMmhkNPa0L0O5bg=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "agbtsifi4f"}, {"Name": "integrity", "Value": "sha256-OnNUV9bnhR8scHT18PZSuIvlH39uQMmhkNPa0L0O5bg="}, {"Name": "label", "Value": "_content/scripts/sf-chart3D.min.js"}]}, {"Route": "_content/scripts/sf-chart3D.min.js", "AssetFile": "_content/scripts/sf-chart3D.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "65523"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OnNUV9bnhR8scHT18PZSuIvlH39uQMmhkNPa0L0O5bg=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OnNUV9bnhR8scHT18PZSuIvlH39uQMmhkNPa0L0O5bg="}]}, {"Route": "_content/scripts/sf-circulargauge.min.js", "AssetFile": "_content/scripts/sf-circulargauge.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "30671"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rAM7udywNwIPsJBGzlO6sjaaTwFqbZ/wRdDtiRDb0p4=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rAM7udywNwIPsJBGzlO6sjaaTwFqbZ/wRdDtiRDb0p4="}]}, {"Route": "_content/scripts/sf-circulargauge.min.kytsxnkjsj.js", "AssetFile": "_content/scripts/sf-circulargauge.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "30671"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rAM7udywNwIPsJBGzlO6sjaaTwFqbZ/wRdDtiRDb0p4=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kytsxnkjsj"}, {"Name": "integrity", "Value": "sha256-rAM7udywNwIPsJBGzlO6sjaaTwFqbZ/wRdDtiRDb0p4="}, {"Name": "label", "Value": "_content/scripts/sf-circulargauge.min.js"}]}, {"Route": "_content/scripts/sf-colorpicker.min.bvhuhzxw09.js", "AssetFile": "_content/scripts/sf-colorpicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6354"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CR11itKPEgaFdwsvXdMEv/vKrnqHKSsQu7VX0bH+y1s=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bvhuhzxw09"}, {"Name": "integrity", "Value": "sha256-CR11itKPEgaFdwsvXdMEv/vKrnqHKSsQu7VX0bH+y1s="}, {"Name": "label", "Value": "_content/scripts/sf-colorpicker.min.js"}]}, {"Route": "_content/scripts/sf-colorpicker.min.js", "AssetFile": "_content/scripts/sf-colorpicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6354"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CR11itKPEgaFdwsvXdMEv/vKrnqHKSsQu7VX0bH+y1s=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CR11itKPEgaFdwsvXdMEv/vKrnqHKSsQu7VX0bH+y1s="}]}, {"Route": "_content/scripts/sf-contextmenu.min.js", "AssetFile": "_content/scripts/sf-contextmenu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17086"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jbEFo1ILbBR5/3QfB9WdItd/jvWbIadRQ01+nwEA0xc=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jbEFo1ILbBR5/3QfB9WdItd/jvWbIadRQ01+nwEA0xc="}]}, {"Route": "_content/scripts/sf-contextmenu.min.lqp19k0c2n.js", "AssetFile": "_content/scripts/sf-contextmenu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "17086"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jbEFo1ILbBR5/3QfB9WdItd/jvWbIadRQ01+nwEA0xc=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lqp19k0c2n"}, {"Name": "integrity", "Value": "sha256-jbEFo1ILbBR5/3QfB9WdItd/jvWbIadRQ01+nwEA0xc="}, {"Name": "label", "Value": "_content/scripts/sf-contextmenu.min.js"}]}, {"Route": "_content/scripts/sf-dashboard-layout.min.dstp8y1g9z.js", "AssetFile": "_content/scripts/sf-dashboard-layout.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "46013"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DUORdEQmnx94F7IoWjHbv4jU5uOKGYzGaSneMvg6GC8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dstp8y1g9z"}, {"Name": "integrity", "Value": "sha256-DUORdEQmnx94F7IoWjHbv4jU5uOKGYzGaSneMvg6GC8="}, {"Name": "label", "Value": "_content/scripts/sf-dashboard-layout.min.js"}]}, {"Route": "_content/scripts/sf-dashboard-layout.min.js", "AssetFile": "_content/scripts/sf-dashboard-layout.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "46013"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DUORdEQmnx94F7IoWjHbv4jU5uOKGYzGaSneMvg6GC8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DUORdEQmnx94F7IoWjHbv4jU5uOKGYzGaSneMvg6GC8="}]}, {"Route": "_content/scripts/sf-datepicker.min.js", "AssetFile": "_content/scripts/sf-datepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "35481"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ATwTHL5aIh0HweTmvRDUQpujqMP1CaeUFdSWxWtlFIA=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ATwTHL5aIh0HweTmvRDUQpujqMP1CaeUFdSWxWtlFIA="}]}, {"Route": "_content/scripts/sf-datepicker.min.t9ez9kh65x.js", "AssetFile": "_content/scripts/sf-datepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "35481"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ATwTHL5aIh0HweTmvRDUQpujqMP1CaeUFdSWxWtlFIA=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t9ez9kh65x"}, {"Name": "integrity", "Value": "sha256-ATwTHL5aIh0HweTmvRDUQpujqMP1CaeUFdSWxWtlFIA="}, {"Name": "label", "Value": "_content/scripts/sf-datepicker.min.js"}]}, {"Route": "_content/scripts/sf-daterangepicker.min.js", "AssetFile": "_content/scripts/sf-daterangepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14775"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PachGiR/IGly/84ee5R1c0LDs6K6dkRQdOwVohPyP7I=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PachGiR/IGly/84ee5R1c0LDs6K6dkRQdOwVohPyP7I="}]}, {"Route": "_content/scripts/sf-daterangepicker.min.togfxddnnb.js", "AssetFile": "_content/scripts/sf-daterangepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14775"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PachGiR/IGly/84ee5R1c0LDs6K6dkRQdOwVohPyP7I=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "togfxddnnb"}, {"Name": "integrity", "Value": "sha256-PachGiR/IGly/84ee5R1c0LDs6K6dkRQdOwVohPyP7I="}, {"Name": "label", "Value": "_content/scripts/sf-daterangepicker.min.js"}]}, {"Route": "_content/scripts/sf-diagramcomponent.min.bx76q8yq8f.js", "AssetFile": "_content/scripts/sf-diagramcomponent.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "55164"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YXhVvL0J27P4wN1yAEPBqgIgTK+Fzi32dovpPakSJnU=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bx76q8yq8f"}, {"Name": "integrity", "Value": "sha256-YXhVvL0J27P4wN1yAEPBqgIgTK+Fzi32dovpPakSJnU="}, {"Name": "label", "Value": "_content/scripts/sf-diagramcomponent.min.js"}]}, {"Route": "_content/scripts/sf-diagramcomponent.min.js", "AssetFile": "_content/scripts/sf-diagramcomponent.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "55164"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YXhVvL0J27P4wN1yAEPBqgIgTK+Fzi32dovpPakSJnU=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YXhVvL0J27P4wN1yAEPBqgIgTK+Fzi32dovpPakSJnU="}]}, {"Route": "_content/scripts/sf-dialog.min.im7x7mvyys.js", "AssetFile": "_content/scripts/sf-dialog.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23128"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JmwRuvKIOqaFixXBsKhmM5ReF5GYONI/pwPsq4P8i2E=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "im7x7mvyys"}, {"Name": "integrity", "Value": "sha256-JmwRuvKIOqaFixXBsKhmM5ReF5GYONI/pwPsq4P8i2E="}, {"Name": "label", "Value": "_content/scripts/sf-dialog.min.js"}]}, {"Route": "_content/scripts/sf-dialog.min.js", "AssetFile": "_content/scripts/sf-dialog.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23128"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JmwRuvKIOqaFixXBsKhmM5ReF5GYONI/pwPsq4P8i2E=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JmwRuvKIOqaFixXBsKhmM5ReF5GYONI/pwPsq4P8i2E="}]}, {"Route": "_content/scripts/sf-drop-down-button.min.js", "AssetFile": "_content/scripts/sf-drop-down-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8267"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xNRQXaTgcY3wMj8WJuVL9ZLHTNtNoGHtvnyZiljHxnA=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xNRQXaTgcY3wMj8WJuVL9ZLHTNtNoGHtvnyZiljHxnA="}]}, {"Route": "_content/scripts/sf-drop-down-button.min.wvohb6568k.js", "AssetFile": "_content/scripts/sf-drop-down-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8267"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xNRQXaTgcY3wMj8WJuVL9ZLHTNtNoGHtvnyZiljHxnA=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wvohb6568k"}, {"Name": "integrity", "Value": "sha256-xNRQXaTgcY3wMj8WJuVL9ZLHTNtNoGHtvnyZiljHxnA="}, {"Name": "label", "Value": "_content/scripts/sf-drop-down-button.min.js"}]}, {"Route": "_content/scripts/sf-dropdownlist.min.jopo3r89gw.js", "AssetFile": "_content/scripts/sf-dropdownlist.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "34751"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"A4fGXs/21qgv7FvRlOcy0o2k0QTA6mBAlYAi7/Z1nU8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jopo3r89gw"}, {"Name": "integrity", "Value": "sha256-A4fGXs/21qgv7FvRlOcy0o2k0QTA6mBAlYAi7/Z1nU8="}, {"Name": "label", "Value": "_content/scripts/sf-dropdownlist.min.js"}]}, {"Route": "_content/scripts/sf-dropdownlist.min.js", "AssetFile": "_content/scripts/sf-dropdownlist.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "34751"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"A4fGXs/21qgv7FvRlOcy0o2k0QTA6mBAlYAi7/Z1nU8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-A4fGXs/21qgv7FvRlOcy0o2k0QTA6mBAlYAi7/Z1nU8="}]}, {"Route": "_content/scripts/sf-dropdowntree.min.cjjwmmzx72.js", "AssetFile": "_content/scripts/sf-dropdowntree.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26150"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6IgZIbMIplZToFOBP17MheifIYcLXjHIwZHMdD7hAGE=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cjjwmmzx72"}, {"Name": "integrity", "Value": "sha256-6IgZIbMIplZToFOBP17MheifIYcLXjHIwZHMdD7hAGE="}, {"Name": "label", "Value": "_content/scripts/sf-dropdowntree.min.js"}]}, {"Route": "_content/scripts/sf-dropdowntree.min.js", "AssetFile": "_content/scripts/sf-dropdowntree.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26150"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6IgZIbMIplZToFOBP17MheifIYcLXjHIwZHMdD7hAGE=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6IgZIbMIplZToFOBP17MheifIYcLXjHIwZHMdD7hAGE="}]}, {"Route": "_content/scripts/sf-filemanager.min.jb7lbkio52.js", "AssetFile": "_content/scripts/sf-filemanager.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24518"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sQzwxEZ+BvS3vhcGaFMNd8vVDTbTEv2vlgswhYOkYGk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jb7lbkio52"}, {"Name": "integrity", "Value": "sha256-sQzwxEZ+BvS3vhcGaFMNd8vVDTbTEv2vlgswhYOkYGk="}, {"Name": "label", "Value": "_content/scripts/sf-filemanager.min.js"}]}, {"Route": "_content/scripts/sf-filemanager.min.js", "AssetFile": "_content/scripts/sf-filemanager.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24518"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sQzwxEZ+BvS3vhcGaFMNd8vVDTbTEv2vlgswhYOkYGk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sQzwxEZ+BvS3vhcGaFMNd8vVDTbTEv2vlgswhYOkYGk="}]}, {"Route": "_content/scripts/sf-floating-action-button.min.js", "AssetFile": "_content/scripts/sf-floating-action-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2295"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xPwy4bfwis4vUCkJC+KnCL5jMdgjV0JuA85E92lJl0Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xPwy4bfwis4vUCkJC+KnCL5jMdgjV0JuA85E92lJl0Q="}]}, {"Route": "_content/scripts/sf-floating-action-button.min.sa489juu1z.js", "AssetFile": "_content/scripts/sf-floating-action-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2295"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xPwy4bfwis4vUCkJC+KnCL5jMdgjV0JuA85E92lJl0Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sa489juu1z"}, {"Name": "integrity", "Value": "sha256-xPwy4bfwis4vUCkJC+KnCL5jMdgjV0JuA85E92lJl0Q="}, {"Name": "label", "Value": "_content/scripts/sf-floating-action-button.min.js"}]}, {"Route": "_content/scripts/sf-gantt.min.js", "AssetFile": "_content/scripts/sf-gantt.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "72792"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"iuVtdZ20OxUXjOsf4EpY9E01EOsS2lYOpuO0osgyVD0=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iuVtdZ20OxUXjOsf4EpY9E01EOsS2lYOpuO0osgyVD0="}]}, {"Route": "_content/scripts/sf-gantt.min.m5kalfdrsf.js", "AssetFile": "_content/scripts/sf-gantt.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "72792"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"iuVtdZ20OxUXjOsf4EpY9E01EOsS2lYOpuO0osgyVD0=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m5kalfdrsf"}, {"Name": "integrity", "Value": "sha256-iuVtdZ20OxUXjOsf4EpY9E01EOsS2lYOpuO0osgyVD0="}, {"Name": "label", "Value": "_content/scripts/sf-gantt.min.js"}]}, {"Route": "_content/scripts/sf-grid.min.js", "AssetFile": "_content/scripts/sf-grid.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "272710"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"087UByy/+1Kohrl1uScHq481Hsodfwi2qV1AJYBX3ng=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-087UByy/+1Kohrl1uScHq481Hsodfwi2qV1AJYBX3ng="}]}, {"Route": "_content/scripts/sf-grid.min.lmsl5e1a5t.js", "AssetFile": "_content/scripts/sf-grid.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "272710"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"087UByy/+1Kohrl1uScHq481Hsodfwi2qV1AJYBX3ng=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lmsl5e1a5t"}, {"Name": "integrity", "Value": "sha256-087UByy/+1Kohrl1uScHq481Hsodfwi2qV1AJYBX3ng="}, {"Name": "label", "Value": "_content/scripts/sf-grid.min.js"}]}, {"Route": "_content/scripts/sf-heatmap.min.7be2y5r7pg.js", "AssetFile": "_content/scripts/sf-heatmap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19582"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DUXuukjJ106tAD6gr8zwwmEVPbyANVIWYwPtw6Htz2k=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7be2y5r7pg"}, {"Name": "integrity", "Value": "sha256-DUXuukjJ106tAD6gr8zwwmEVPbyANVIWYwPtw6Htz2k="}, {"Name": "label", "Value": "_content/scripts/sf-heatmap.min.js"}]}, {"Route": "_content/scripts/sf-heatmap.min.js", "AssetFile": "_content/scripts/sf-heatmap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19582"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DUXuukjJ106tAD6gr8zwwmEVPbyANVIWYwPtw6Htz2k=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DUXuukjJ106tAD6gr8zwwmEVPbyANVIWYwPtw6Htz2k="}]}, {"Route": "_content/scripts/sf-image-editor.min.8yp2ls3ebq.js", "AssetFile": "_content/scripts/sf-image-editor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "745075"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IDvSELQSqNbjm1FUbXAbklpp0jvGpheCGbHRoYEyOo0=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8yp2ls3ebq"}, {"Name": "integrity", "Value": "sha256-IDvSELQSqNbjm1FUbXAbklpp0jvGpheCGbHRoYEyOo0="}, {"Name": "label", "Value": "_content/scripts/sf-image-editor.min.js"}]}, {"Route": "_content/scripts/sf-image-editor.min.js", "AssetFile": "_content/scripts/sf-image-editor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "745075"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IDvSELQSqNbjm1FUbXAbklpp0jvGpheCGbHRoYEyOo0=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDvSELQSqNbjm1FUbXAbklpp0jvGpheCGbHRoYEyOo0="}]}, {"Route": "_content/scripts/sf-inplaceeditor.min.js", "AssetFile": "_content/scripts/sf-inplaceeditor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7539"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JpIRVKpIMxn7KM83FdWU/FeEZ3j37W5+Ihhn++lbVwk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JpIRVKpIMxn7KM83FdWU/FeEZ3j37W5+Ihhn++lbVwk="}]}, {"Route": "_content/scripts/sf-inplaceeditor.min.qkf4ydx6ba.js", "AssetFile": "_content/scripts/sf-inplaceeditor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7539"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JpIRVKpIMxn7KM83FdWU/FeEZ3j37W5+Ihhn++lbVwk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qkf4ydx6ba"}, {"Name": "integrity", "Value": "sha256-JpIRVKpIMxn7KM83FdWU/FeEZ3j37W5+Ihhn++lbVwk="}, {"Name": "label", "Value": "_content/scripts/sf-inplaceeditor.min.js"}]}, {"Route": "_content/scripts/sf-kanban.min.j0bsps21c5.js", "AssetFile": "_content/scripts/sf-kanban.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "43864"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wy+LzwtvYMBZlga5BxFBxh9w2kxVBtw8sfMqyCfXE6o=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j0bsps21c5"}, {"Name": "integrity", "Value": "sha256-wy+LzwtvYMBZlga5BxFBxh9w2kxVBtw8sfMqyCfXE6o="}, {"Name": "label", "Value": "_content/scripts/sf-kanban.min.js"}]}, {"Route": "_content/scripts/sf-kanban.min.js", "AssetFile": "_content/scripts/sf-kanban.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "43864"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wy+LzwtvYMBZlga5BxFBxh9w2kxVBtw8sfMqyCfXE6o=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wy+LzwtvYMBZlga5BxFBxh9w2kxVBtw8sfMqyCfXE6o="}]}, {"Route": "_content/scripts/sf-lineargauge.min.6uy6jxrmjw.js", "AssetFile": "_content/scripts/sf-lineargauge.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "21192"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3jzO7ggG9uUwgNWGLAGaXvqAuWCEuO29Y8YhwT+1JP0=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6uy6jxrmjw"}, {"Name": "integrity", "Value": "sha256-3jzO7ggG9uUwgNWGLAGaXvqAuWCEuO29Y8YhwT+1JP0="}, {"Name": "label", "Value": "_content/scripts/sf-lineargauge.min.js"}]}, {"Route": "_content/scripts/sf-lineargauge.min.js", "AssetFile": "_content/scripts/sf-lineargauge.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "21192"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3jzO7ggG9uUwgNWGLAGaXvqAuWCEuO29Y8YhwT+1JP0=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3jzO7ggG9uUwgNWGLAGaXvqAuWCEuO29Y8YhwT+1JP0="}]}, {"Route": "_content/scripts/sf-listbox.min.4k5ppyv4aq.js", "AssetFile": "_content/scripts/sf-listbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5843"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZFnqFWNDVT9oQpi8wZqY6BRLYeoNS4/E9spJujRGZVY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4k5ppyv4aq"}, {"Name": "integrity", "Value": "sha256-ZFnqFWNDVT9oQpi8wZqY6BRLYeoNS4/E9spJujRGZVY="}, {"Name": "label", "Value": "_content/scripts/sf-listbox.min.js"}]}, {"Route": "_content/scripts/sf-listbox.min.js", "AssetFile": "_content/scripts/sf-listbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5843"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZFnqFWNDVT9oQpi8wZqY6BRLYeoNS4/E9spJujRGZVY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZFnqFWNDVT9oQpi8wZqY6BRLYeoNS4/E9spJujRGZVY="}]}, {"Route": "_content/scripts/sf-listview.min.js", "AssetFile": "_content/scripts/sf-listview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "25939"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"d29hx8WR01OJv17FDgbmCaS7cfsI4U3L8zhQI7iMPC0=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d29hx8WR01OJv17FDgbmCaS7cfsI4U3L8zhQI7iMPC0="}]}, {"Route": "_content/scripts/sf-listview.min.p42bddzkv9.js", "AssetFile": "_content/scripts/sf-listview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "25939"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"d29hx8WR01OJv17FDgbmCaS7cfsI4U3L8zhQI7iMPC0=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "p42bddzkv9"}, {"Name": "integrity", "Value": "sha256-d29hx8WR01OJv17FDgbmCaS7cfsI4U3L8zhQI7iMPC0="}, {"Name": "label", "Value": "_content/scripts/sf-listview.min.js"}]}, {"Route": "_content/scripts/sf-maps.min.js", "AssetFile": "_content/scripts/sf-maps.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "123154"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zo+hTfCRRR/BdwWhbYB3w6jIfO3FBodtbvN2wEr1xjc=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zo+hTfCRRR/BdwWhbYB3w6jIfO3FBodtbvN2wEr1xjc="}]}, {"Route": "_content/scripts/sf-maps.min.qix269to88.js", "AssetFile": "_content/scripts/sf-maps.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "123154"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zo+hTfCRRR/BdwWhbYB3w6jIfO3FBodtbvN2wEr1xjc=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qix269to88"}, {"Name": "integrity", "Value": "sha256-zo+hTfCRRR/BdwWhbYB3w6jIfO3FBodtbvN2wEr1xjc="}, {"Name": "label", "Value": "_content/scripts/sf-maps.min.js"}]}, {"Route": "_content/scripts/sf-maskedtextbox.min.js", "AssetFile": "_content/scripts/sf-maskedtextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9315"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SligKCnyu9DCGo6TXmnbUp2hM0sQPRxsQ2+dSD96xuQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SligKCnyu9DCGo6TXmnbUp2hM0sQPRxsQ2+dSD96xuQ="}]}, {"Route": "_content/scripts/sf-maskedtextbox.min.uqjvqum98b.js", "AssetFile": "_content/scripts/sf-maskedtextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9315"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SligKCnyu9DCGo6TXmnbUp2hM0sQPRxsQ2+dSD96xuQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uqjvqum98b"}, {"Name": "integrity", "Value": "sha256-SligKCnyu9DCGo6TXmnbUp2hM0sQPRxsQ2+dSD96xuQ="}, {"Name": "label", "Value": "_content/scripts/sf-maskedtextbox.min.js"}]}, {"Route": "_content/scripts/sf-mention.min.js", "AssetFile": "_content/scripts/sf-mention.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "20149"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YQCxzB/zbWJHMAcTzY2hvevMW+b+4+l7a7okhA2Dpqw=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YQCxzB/zbWJHMAcTzY2hvevMW+b+4+l7a7okhA2Dpqw="}]}, {"Route": "_content/scripts/sf-mention.min.py4tu9a8jk.js", "AssetFile": "_content/scripts/sf-mention.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "20149"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YQCxzB/zbWJHMAcTzY2hvevMW+b+4+l7a7okhA2Dpqw=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "py4tu9a8jk"}, {"Name": "integrity", "Value": "sha256-YQCxzB/zbWJHMAcTzY2hvevMW+b+4+l7a7okhA2Dpqw="}, {"Name": "label", "Value": "_content/scripts/sf-mention.min.js"}]}, {"Route": "_content/scripts/sf-menu.min.js", "AssetFile": "_content/scripts/sf-menu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13680"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HyzgR+dKNzA6dUdp0hPBg33X2I+Dr4ULQp9PtZ80TYo=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HyzgR+dKNzA6dUdp0hPBg33X2I+Dr4ULQp9PtZ80TYo="}]}, {"Route": "_content/scripts/sf-menu.min.z39kxg07av.js", "AssetFile": "_content/scripts/sf-menu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "13680"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HyzgR+dKNzA6dUdp0hPBg33X2I+Dr4ULQp9PtZ80TYo=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z39kxg07av"}, {"Name": "integrity", "Value": "sha256-HyzgR+dKNzA6dUdp0hPBg33X2I+Dr4ULQp9PtZ80TYo="}, {"Name": "label", "Value": "_content/scripts/sf-menu.min.js"}]}, {"Route": "_content/scripts/sf-multiselect.min.bdrrv5sebb.js", "AssetFile": "_content/scripts/sf-multiselect.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "31621"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"T7YnzEICHukckRGfZz3v+PtrWG+mYVscs5fXRwGLRIw=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bdrrv5sebb"}, {"Name": "integrity", "Value": "sha256-T7YnzEICHukckRGfZz3v+PtrWG+mYVscs5fXRwGLRIw="}, {"Name": "label", "Value": "_content/scripts/sf-multiselect.min.js"}]}, {"Route": "_content/scripts/sf-multiselect.min.js", "AssetFile": "_content/scripts/sf-multiselect.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "31621"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"T7YnzEICHukckRGfZz3v+PtrWG+mYVscs5fXRwGLRIw=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T7YnzEICHukckRGfZz3v+PtrWG+mYVscs5fXRwGLRIw="}]}, {"Route": "_content/scripts/sf-numerictextbox.min.js", "AssetFile": "_content/scripts/sf-numerictextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12257"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"toxfh1sTRE3UShFW4KRM9Anp1lHYNktkOOiQOpOFwUw=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-toxfh1sTRE3UShFW4KRM9Anp1lHYNktkOOiQOpOFwUw="}]}, {"Route": "_content/scripts/sf-numerictextbox.min.qg3z7c64hc.js", "AssetFile": "_content/scripts/sf-numerictextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12257"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"toxfh1sTRE3UShFW4KRM9Anp1lHYNktkOOiQOpOFwUw=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qg3z7c64hc"}, {"Name": "integrity", "Value": "sha256-toxfh1sTRE3UShFW4KRM9Anp1lHYNktkOOiQOpOFwUw="}, {"Name": "label", "Value": "_content/scripts/sf-numerictextbox.min.js"}]}, {"Route": "_content/scripts/sf-otp-input.min.huernujoyn.js", "AssetFile": "_content/scripts/sf-otp-input.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1321"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"F52NgH8FuNDHapAWQAGGzo//Rv9q/HsyjBv4PgV406Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-F52NgH8FuNDHapAWQAGGzo//Rv9q/HsyjBv4PgV406Y="}, {"Name": "label", "Value": "_content/scripts/sf-otp-input.min.js"}]}, {"Route": "_content/scripts/sf-otp-input.min.js", "AssetFile": "_content/scripts/sf-otp-input.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1321"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"F52NgH8FuNDHapAWQAGGzo//Rv9q/HsyjBv4PgV406Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F52NgH8FuNDHapAWQAGGzo//Rv9q/HsyjBv4PgV406Y="}]}, {"Route": "_content/scripts/sf-pager.min.4aqgn62rwn.js", "AssetFile": "_content/scripts/sf-pager.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9931"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DBhkXCK+sph0mBywD3wxvyVHYXsQ2Pkc15ySDHFpkO0=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4aqgn62rwn"}, {"Name": "integrity", "Value": "sha256-DBhkXCK+sph0mBywD3wxvyVHYXsQ2Pkc15ySDHFpkO0="}, {"Name": "label", "Value": "_content/scripts/sf-pager.min.js"}]}, {"Route": "_content/scripts/sf-pager.min.js", "AssetFile": "_content/scripts/sf-pager.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9931"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DBhkXCK+sph0mBywD3wxvyVHYXsQ2Pkc15ySDHFpkO0=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DBhkXCK+sph0mBywD3wxvyVHYXsQ2Pkc15ySDHFpkO0="}]}, {"Route": "_content/scripts/sf-pdfviewer.min.js", "AssetFile": "_content/scripts/sf-pdfviewer.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1489177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HnNTmQk3Fl8O8aH+bpRBCvIl855BMs9rjisP2bZnRy8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HnNTmQk3Fl8O8aH+bpRBCvIl855BMs9rjisP2bZnRy8="}]}, {"Route": "_content/scripts/sf-pdfviewer.min.uchpbx408h.js", "AssetFile": "_content/scripts/sf-pdfviewer.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1489177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HnNTmQk3Fl8O8aH+bpRBCvIl855BMs9rjisP2bZnRy8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uchpbx408h"}, {"Name": "integrity", "Value": "sha256-HnNTmQk3Fl8O8aH+bpRBCvIl855BMs9rjisP2bZnRy8="}, {"Name": "label", "Value": "_content/scripts/sf-pdfviewer.min.js"}]}, {"Route": "_content/scripts/sf-pivotview.min.h7kf5z8ax9.js", "AssetFile": "_content/scripts/sf-pivotview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "102575"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DaV7WF3cZ1MgfXMYB7miHt12XhWQXYRv3Tlv4bMqNt8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h7kf5z8ax9"}, {"Name": "integrity", "Value": "sha256-DaV7WF3cZ1MgfXMYB7miHt12XhWQXYRv3Tlv4bMqNt8="}, {"Name": "label", "Value": "_content/scripts/sf-pivotview.min.js"}]}, {"Route": "_content/scripts/sf-pivotview.min.js", "AssetFile": "_content/scripts/sf-pivotview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "102575"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DaV7WF3cZ1MgfXMYB7miHt12XhWQXYRv3Tlv4bMqNt8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DaV7WF3cZ1MgfXMYB7miHt12XhWQXYRv3Tlv4bMqNt8="}]}, {"Route": "_content/scripts/sf-progressbar.min.d9udb7f76q.js", "AssetFile": "_content/scripts/sf-progressbar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15754"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jw6udhjBu67gXlNULLLzds+lBHxDc/EUKHVFrCI0cv8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d9udb7f76q"}, {"Name": "integrity", "Value": "sha256-jw6udhjBu67gXlNULLLzds+lBHxDc/EUKHVFrCI0cv8="}, {"Name": "label", "Value": "_content/scripts/sf-progressbar.min.js"}]}, {"Route": "_content/scripts/sf-progressbar.min.js", "AssetFile": "_content/scripts/sf-progressbar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15754"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jw6udhjBu67gXlNULLLzds+lBHxDc/EUKHVFrCI0cv8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jw6udhjBu67gXlNULLLzds+lBHxDc/EUKHVFrCI0cv8="}]}, {"Route": "_content/scripts/sf-range-navigator.min.js", "AssetFile": "_content/scripts/sf-range-navigator.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16360"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vhbIz3hea0qdovc8EvXRqJBKcUh3SrO34oyVvIk2v4I=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vhbIz3hea0qdovc8EvXRqJBKcUh3SrO34oyVvIk2v4I="}]}, {"Route": "_content/scripts/sf-range-navigator.min.ut14vyw3ov.js", "AssetFile": "_content/scripts/sf-range-navigator.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16360"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vhbIz3hea0qdovc8EvXRqJBKcUh3SrO34oyVvIk2v4I=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ut14vyw3ov"}, {"Name": "integrity", "Value": "sha256-vhbIz3hea0qdovc8EvXRqJBKcUh3SrO34oyVvIk2v4I="}, {"Name": "label", "Value": "_content/scripts/sf-range-navigator.min.js"}]}, {"Route": "_content/scripts/sf-rating.min.js", "AssetFile": "_content/scripts/sf-rating.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9228"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6JwUmMLliCdNSq6ySgAPtfv/h1/16hGVt31LGC3VPGU=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6JwUmMLliCdNSq6ySgAPtfv/h1/16hGVt31LGC3VPGU="}]}, {"Route": "_content/scripts/sf-rating.min.kxnn443af6.js", "AssetFile": "_content/scripts/sf-rating.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9228"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6JwUmMLliCdNSq6ySgAPtfv/h1/16hGVt31LGC3VPGU=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kxnn443af6"}, {"Name": "integrity", "Value": "sha256-6JwUmMLliCdNSq6ySgAPtfv/h1/16hGVt31LGC3VPGU="}, {"Name": "label", "Value": "_content/scripts/sf-rating.min.js"}]}, {"Route": "_content/scripts/sf-richtexteditor.min.js", "AssetFile": "_content/scripts/sf-richtexteditor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "703675"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1G0uMLLcQVAN50N6hqvkVp2yiLd5Sn4p+OaLyvDi5T8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1G0uMLLcQVAN50N6hqvkVp2yiLd5Sn4p+OaLyvDi5T8="}]}, {"Route": "_content/scripts/sf-richtexteditor.min.kgsfkvckjt.js", "AssetFile": "_content/scripts/sf-richtexteditor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "703675"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1G0uMLLcQVAN50N6hqvkVp2yiLd5Sn4p+OaLyvDi5T8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kgsfkvckjt"}, {"Name": "integrity", "Value": "sha256-1G0uMLLcQVAN50N6hqvkVp2yiLd5Sn4p+OaLyvDi5T8="}, {"Name": "label", "Value": "_content/scripts/sf-richtexteditor.min.js"}]}, {"Route": "_content/scripts/sf-schedule.min.js", "AssetFile": "_content/scripts/sf-schedule.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "246794"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vETUzX1w8u5GU5/7DBUeYlKd6sTMK+MJCXz5HL4mW1o=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vETUzX1w8u5GU5/7DBUeYlKd6sTMK+MJCXz5HL4mW1o="}]}, {"Route": "_content/scripts/sf-schedule.min.w3nb7xn8tk.js", "AssetFile": "_content/scripts/sf-schedule.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "246794"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vETUzX1w8u5GU5/7DBUeYlKd6sTMK+MJCXz5HL4mW1o=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w3nb7xn8tk"}, {"Name": "integrity", "Value": "sha256-vETUzX1w8u5GU5/7DBUeYlKd6sTMK+MJCXz5HL4mW1o="}, {"Name": "label", "Value": "_content/scripts/sf-schedule.min.js"}]}, {"Route": "_content/scripts/sf-sidebar.min.0ob9l88qty.js", "AssetFile": "_content/scripts/sf-sidebar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10662"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UPh1alFmVV8foftvwY3dpBlTXE98UUIdeM726K8e6vY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0ob9l88qty"}, {"Name": "integrity", "Value": "sha256-UPh1alFmVV8foftvwY3dpBlTXE98UUIdeM726K8e6vY="}, {"Name": "label", "Value": "_content/scripts/sf-sidebar.min.js"}]}, {"Route": "_content/scripts/sf-sidebar.min.js", "AssetFile": "_content/scripts/sf-sidebar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10662"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UPh1alFmVV8foftvwY3dpBlTXE98UUIdeM726K8e6vY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UPh1alFmVV8foftvwY3dpBlTXE98UUIdeM726K8e6vY="}]}, {"Route": "_content/scripts/sf-signature.min.hl0cba31yd.js", "AssetFile": "_content/scripts/sf-signature.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24003"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BVFulb0hVe4nbC07KQYhF7wNvhLWHFN8cfNrIK9XWtA=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hl0cba31yd"}, {"Name": "integrity", "Value": "sha256-BVFulb0hVe4nbC07KQYhF7wNvhLWHFN8cfNrIK9XWtA="}, {"Name": "label", "Value": "_content/scripts/sf-signature.min.js"}]}, {"Route": "_content/scripts/sf-signature.min.js", "AssetFile": "_content/scripts/sf-signature.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24003"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BVFulb0hVe4nbC07KQYhF7wNvhLWHFN8cfNrIK9XWtA=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BVFulb0hVe4nbC07KQYhF7wNvhLWHFN8cfNrIK9XWtA="}]}, {"Route": "_content/scripts/sf-slider.min.9dzu6u5fx7.js", "AssetFile": "_content/scripts/sf-slider.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "32013"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jRx9uQZqtKNKqnlqHEv0g4HhtGzyMX9fyL7vTQ7a8w8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9dzu6u5fx7"}, {"Name": "integrity", "Value": "sha256-jRx9uQZqtKNKqnlqHEv0g4HhtGzyMX9fyL7vTQ7a8w8="}, {"Name": "label", "Value": "_content/scripts/sf-slider.min.js"}]}, {"Route": "_content/scripts/sf-slider.min.js", "AssetFile": "_content/scripts/sf-slider.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "32013"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jRx9uQZqtKNKqnlqHEv0g4HhtGzyMX9fyL7vTQ7a8w8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jRx9uQZqtKNKqnlqHEv0g4HhtGzyMX9fyL7vTQ7a8w8="}]}, {"Route": "_content/scripts/sf-smith-chart.min.14z6auownm.js", "AssetFile": "_content/scripts/sf-smith-chart.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14279"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Qfm0GIQdyNZ1CTAWp83Za7wRllAf3Pej+tYVU26aPy4=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "14z6auownm"}, {"Name": "integrity", "Value": "sha256-Qfm0GIQdyNZ1CTAWp83Za7wRllAf3Pej+tYVU26aPy4="}, {"Name": "label", "Value": "_content/scripts/sf-smith-chart.min.js"}]}, {"Route": "_content/scripts/sf-smith-chart.min.js", "AssetFile": "_content/scripts/sf-smith-chart.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14279"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Qfm0GIQdyNZ1CTAWp83Za7wRllAf3Pej+tYVU26aPy4=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Qfm0GIQdyNZ1CTAWp83Za7wRllAf3Pej+tYVU26aPy4="}]}, {"Route": "_content/scripts/sf-sparkline.min.5vje85vfft.js", "AssetFile": "_content/scripts/sf-sparkline.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8929"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"77dXc7t9QwinShYhRyW0NfMKVNlfC2FSPnRPpO6SAIU=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5vje85vfft"}, {"Name": "integrity", "Value": "sha256-77dXc7t9QwinShYhRyW0NfMKVNlfC2FSPnRPpO6SAIU="}, {"Name": "label", "Value": "_content/scripts/sf-sparkline.min.js"}]}, {"Route": "_content/scripts/sf-sparkline.min.js", "AssetFile": "_content/scripts/sf-sparkline.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8929"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"77dXc7t9QwinShYhRyW0NfMKVNlfC2FSPnRPpO6SAIU=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-77dXc7t9QwinShYhRyW0NfMKVNlfC2FSPnRPpO6SAIU="}]}, {"Route": "_content/scripts/sf-speeddial.min.js", "AssetFile": "_content/scripts/sf-speeddial.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11930"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rHwIIhyEKY2ihv94pWdbPMtEaw+r/8qHDa3sXZOsjDI=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rHwIIhyEKY2ihv94pWdbPMtEaw+r/8qHDa3sXZOsjDI="}]}, {"Route": "_content/scripts/sf-speeddial.min.w15bl8sa0j.js", "AssetFile": "_content/scripts/sf-speeddial.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11930"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rHwIIhyEKY2ihv94pWdbPMtEaw+r/8qHDa3sXZOsjDI=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w15bl8sa0j"}, {"Name": "integrity", "Value": "sha256-rHwIIhyEKY2ihv94pWdbPMtEaw+r/8qHDa3sXZOsjDI="}, {"Name": "label", "Value": "_content/scripts/sf-speeddial.min.js"}]}, {"Route": "_content/scripts/sf-spinner.min.js", "AssetFile": "_content/scripts/sf-spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1254"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XqJAtyD2ZJ0Vm92Xq5KGwQCT3n1CfkikHs2/qZm5ul0=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XqJAtyD2ZJ0Vm92Xq5KGwQCT3n1CfkikHs2/qZm5ul0="}]}, {"Route": "_content/scripts/sf-spinner.min.u8mr1ids57.js", "AssetFile": "_content/scripts/sf-spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1254"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XqJAtyD2ZJ0Vm92Xq5KGwQCT3n1CfkikHs2/qZm5ul0=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u8mr1ids57"}, {"Name": "integrity", "Value": "sha256-XqJAtyD2ZJ0Vm92Xq5KGwQCT3n1CfkikHs2/qZm5ul0="}, {"Name": "label", "Value": "_content/scripts/sf-spinner.min.js"}]}, {"Route": "_content/scripts/sf-splitter.min.js", "AssetFile": "_content/scripts/sf-splitter.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "50244"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eyWmpsIIrgFRCevSb0jySArZpmrAQ8cMIccvnq2UTl0=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eyWmpsIIrgFRCevSb0jySArZpmrAQ8cMIccvnq2UTl0="}]}, {"Route": "_content/scripts/sf-splitter.min.zit4bzuu5o.js", "AssetFile": "_content/scripts/sf-splitter.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "50244"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eyWmpsIIrgFRCevSb0jySArZpmrAQ8cMIccvnq2UTl0=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zit4bzuu5o"}, {"Name": "integrity", "Value": "sha256-eyWmpsIIrgFRCevSb0jySArZpmrAQ8cMIccvnq2UTl0="}, {"Name": "label", "Value": "_content/scripts/sf-splitter.min.js"}]}, {"Route": "_content/scripts/sf-stepper.min.js", "AssetFile": "_content/scripts/sf-stepper.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14627"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P/y9j6YuMHKlVVBM5U8D4KIpw5UNZSyXiROtwFg0txM=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P/y9j6YuMHKlVVBM5U8D4KIpw5UNZSyXiROtwFg0txM="}]}, {"Route": "_content/scripts/sf-stepper.min.lbq26frs5z.js", "AssetFile": "_content/scripts/sf-stepper.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14627"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P/y9j6YuMHKlVVBM5U8D4KIpw5UNZSyXiROtwFg0txM=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lbq26frs5z"}, {"Name": "integrity", "Value": "sha256-P/y9j6YuMHKlVVBM5U8D4KIpw5UNZSyXiROtwFg0txM="}, {"Name": "label", "Value": "_content/scripts/sf-stepper.min.js"}]}, {"Route": "_content/scripts/sf-stock-chart.min.js", "AssetFile": "_content/scripts/sf-stock-chart.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10699"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N+n9zhdOXerQKo7sbizJ1di0o4Mw6KwKV8rOSg3Ir6w=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N+n9zhdOXerQKo7sbizJ1di0o4Mw6KwKV8rOSg3Ir6w="}]}, {"Route": "_content/scripts/sf-stock-chart.min.pso9x0v6xp.js", "AssetFile": "_content/scripts/sf-stock-chart.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10699"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N+n9zhdOXerQKo7sbizJ1di0o4Mw6KwKV8rOSg3Ir6w=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pso9x0v6xp"}, {"Name": "integrity", "Value": "sha256-N+n9zhdOXerQKo7sbizJ1di0o4Mw6KwKV8rOSg3Ir6w="}, {"Name": "label", "Value": "_content/scripts/sf-stock-chart.min.js"}]}, {"Route": "_content/scripts/sf-svg-export.min.js", "AssetFile": "_content/scripts/sf-svg-export.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14228"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Me3UYkArxYTj9mYM+rDM5CAD6CpPsi6ZpNS3Zv8Pwsg=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Me3UYkArxYTj9mYM+rDM5CAD6CpPsi6ZpNS3Zv8Pwsg="}]}, {"Route": "_content/scripts/sf-svg-export.min.n66a1x5rbl.js", "AssetFile": "_content/scripts/sf-svg-export.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14228"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Me3UYkArxYTj9mYM+rDM5CAD6CpPsi6ZpNS3Zv8Pwsg=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n66a1x5rbl"}, {"Name": "integrity", "Value": "sha256-Me3UYkArxYTj9mYM+rDM5CAD6CpPsi6ZpNS3Zv8Pwsg="}, {"Name": "label", "Value": "_content/scripts/sf-svg-export.min.js"}]}, {"Route": "_content/scripts/sf-tab.min.js", "AssetFile": "_content/scripts/sf-tab.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "33458"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UeSfBmw6Vr7dfWf+iZUrAKzF7ElDroPO0ullDRHMAwA=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UeSfBmw6Vr7dfWf+iZUrAKzF7ElDroPO0ullDRHMAwA="}]}, {"Route": "_content/scripts/sf-tab.min.v0hm0v4gid.js", "AssetFile": "_content/scripts/sf-tab.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "33458"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UeSfBmw6Vr7dfWf+iZUrAKzF7ElDroPO0ullDRHMAwA=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0hm0v4gid"}, {"Name": "integrity", "Value": "sha256-UeSfBmw6Vr7dfWf+iZUrAKzF7ElDroPO0ullDRHMAwA="}, {"Name": "label", "Value": "_content/scripts/sf-tab.min.js"}]}, {"Route": "_content/scripts/sf-textarea.min.insb8m6iuu.js", "AssetFile": "_content/scripts/sf-textarea.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1568"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zmWA0UOsyJFHYCS6a8bmBxH8ivG2Q6K0NerRveu1088=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "insb8m6iuu"}, {"Name": "integrity", "Value": "sha256-zmWA0UOsyJFHYCS6a8bmBxH8ivG2Q6K0NerRveu1088="}, {"Name": "label", "Value": "_content/scripts/sf-textarea.min.js"}]}, {"Route": "_content/scripts/sf-textarea.min.js", "AssetFile": "_content/scripts/sf-textarea.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1568"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zmWA0UOsyJFHYCS6a8bmBxH8ivG2Q6K0NerRveu1088=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zmWA0UOsyJFHYCS6a8bmBxH8ivG2Q6K0NerRveu1088="}]}, {"Route": "_content/scripts/sf-textbox.min.js", "AssetFile": "_content/scripts/sf-textbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3011"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tX43WI/Hb3AxN66to7l32JTK3rJcm1LkTXXTvxw1DWM=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tX43WI/Hb3AxN66to7l32JTK3rJcm1LkTXXTvxw1DWM="}]}, {"Route": "_content/scripts/sf-textbox.min.teyikf9532.js", "AssetFile": "_content/scripts/sf-textbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3011"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tX43WI/Hb3AxN66to7l32JTK3rJcm1LkTXXTvxw1DWM=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "teyikf9532"}, {"Name": "integrity", "Value": "sha256-tX43WI/Hb3AxN66to7l32JTK3rJcm1LkTXXTvxw1DWM="}, {"Name": "label", "Value": "_content/scripts/sf-textbox.min.js"}]}, {"Route": "_content/scripts/sf-timepicker.min.06usjhb1rz.js", "AssetFile": "_content/scripts/sf-timepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "31386"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SLFkQKjNzVDlf680XeZ9DKKqpPeOArN7e7ZksEzF500=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "06usjhb1rz"}, {"Name": "integrity", "Value": "sha256-SLFkQKjNzVDlf680XeZ9DKKqpPeOArN7e7ZksEzF500="}, {"Name": "label", "Value": "_content/scripts/sf-timepicker.min.js"}]}, {"Route": "_content/scripts/sf-timepicker.min.js", "AssetFile": "_content/scripts/sf-timepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "31386"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SLFkQKjNzVDlf680XeZ9DKKqpPeOArN7e7ZksEzF500=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SLFkQKjNzVDlf680XeZ9DKKqpPeOArN7e7ZksEzF500="}]}, {"Route": "_content/scripts/sf-toast.min.js", "AssetFile": "_content/scripts/sf-toast.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7744"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4YMTtZub9mNo+KZB+hEoI+eNm/q1hT1fkVz1HB+Cv5Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4YMTtZub9mNo+KZB+hEoI+eNm/q1hT1fkVz1HB+Cv5Y="}]}, {"Route": "_content/scripts/sf-toast.min.pqmv3rphqn.js", "AssetFile": "_content/scripts/sf-toast.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7744"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4YMTtZub9mNo+KZB+hEoI+eNm/q1hT1fkVz1HB+Cv5Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pqmv3rphqn"}, {"Name": "integrity", "Value": "sha256-4YMTtZub9mNo+KZB+hEoI+eNm/q1hT1fkVz1HB+Cv5Y="}, {"Name": "label", "Value": "_content/scripts/sf-toast.min.js"}]}, {"Route": "_content/scripts/sf-toolbar.min.js", "AssetFile": "_content/scripts/sf-toolbar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "40341"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PlEmgUIIBaGexs150nXWjmADNuvASHig5KF6+frv5FE=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PlEmgUIIBaGexs150nXWjmADNuvASHig5KF6+frv5FE="}]}, {"Route": "_content/scripts/sf-toolbar.min.y88syx5v2q.js", "AssetFile": "_content/scripts/sf-toolbar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "40341"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PlEmgUIIBaGexs150nXWjmADNuvASHig5KF6+frv5FE=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y88syx5v2q"}, {"Name": "integrity", "Value": "sha256-PlEmgUIIBaGexs150nXWjmADNuvASHig5KF6+frv5FE="}, {"Name": "label", "Value": "_content/scripts/sf-toolbar.min.js"}]}, {"Route": "_content/scripts/sf-tooltip.min.js", "AssetFile": "_content/scripts/sf-tooltip.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "30734"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8SEeAjZ2+xfyktQBwwKl2ZC95TK+KKZAQZ+K7cN307E=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8SEeAjZ2+xfyktQBwwKl2ZC95TK+KKZAQZ+K7cN307E="}]}, {"Route": "_content/scripts/sf-tooltip.min.rt2zl07dss.js", "AssetFile": "_content/scripts/sf-tooltip.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "30734"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8SEeAjZ2+xfyktQBwwKl2ZC95TK+KKZAQZ+K7cN307E=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rt2zl07dss"}, {"Name": "integrity", "Value": "sha256-8SEeAjZ2+xfyktQBwwKl2ZC95TK+KKZAQZ+K7cN307E="}, {"Name": "label", "Value": "_content/scripts/sf-tooltip.min.js"}]}, {"Route": "_content/scripts/sf-treegrid.min.js", "AssetFile": "_content/scripts/sf-treegrid.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "30788"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jpjaNt1FkjP3AUBN0O3N4h4cz1my/pMTLVYlvWi7B8c=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jpjaNt1FkjP3AUBN0O3N4h4cz1my/pMTLVYlvWi7B8c="}]}, {"Route": "_content/scripts/sf-treegrid.min.uxji2ohey3.js", "AssetFile": "_content/scripts/sf-treegrid.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "30788"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jpjaNt1FkjP3AUBN0O3N4h4cz1my/pMTLVYlvWi7B8c=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uxji2<PERSON>ey3"}, {"Name": "integrity", "Value": "sha256-jpjaNt1FkjP3AUBN0O3N4h4cz1my/pMTLVYlvWi7B8c="}, {"Name": "label", "Value": "_content/scripts/sf-treegrid.min.js"}]}, {"Route": "_content/scripts/sf-treemap.min.ep6wj4y17h.js", "AssetFile": "_content/scripts/sf-treemap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10460"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8swhKsyvJPIVTeJNaT0upVqDxdueRGHwKVckOTQ6uZQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ep6wj4y17h"}, {"Name": "integrity", "Value": "sha256-8swhKsyvJPIVTeJNaT0upVqDxdueRGHwKVckOTQ6uZQ="}, {"Name": "label", "Value": "_content/scripts/sf-treemap.min.js"}]}, {"Route": "_content/scripts/sf-treemap.min.js", "AssetFile": "_content/scripts/sf-treemap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10460"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8swhKsyvJPIVTeJNaT0upVqDxdueRGHwKVckOTQ6uZQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8swhKsyvJPIVTeJNaT0upVqDxdueRGHwKVckOTQ6uZQ="}]}, {"Route": "_content/scripts/sf-treeview.min.js", "AssetFile": "_content/scripts/sf-treeview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "49765"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qaPK49trseM/GdxKN41LefVYZ74F77JqaIDQ05YqwIY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qaPK49trseM/GdxKN41LefVYZ74F77JqaIDQ05YqwIY="}]}, {"Route": "_content/scripts/sf-treeview.min.xaqq0rtqt1.js", "AssetFile": "_content/scripts/sf-treeview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "49765"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qaPK49trseM/GdxKN41LefVYZ74F77JqaIDQ05YqwIY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xaqq0rtqt1"}, {"Name": "integrity", "Value": "sha256-qaPK49trseM/GdxKN41LefVYZ74F77JqaIDQ05YqwIY="}, {"Name": "label", "Value": "_content/scripts/sf-treeview.min.js"}]}, {"Route": "_content/scripts/sf-uploader.min.js", "AssetFile": "_content/scripts/sf-uploader.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "82563"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"l7lMi8nV76lJEXuEHYvwZ2qQOtpDvObrWrw7AMoNb+8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l7lMi8nV76lJEXuEHYvwZ2qQOtpDvObrWrw7AMoNb+8="}]}, {"Route": "_content/scripts/sf-uploader.min.rbnmiausmn.js", "AssetFile": "_content/scripts/sf-uploader.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "82563"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"l7lMi8nV76lJEXuEHYvwZ2qQOtpDvObrWrw7AMoNb+8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rbnmiausmn"}, {"Name": "integrity", "Value": "sha256-l7lMi8nV76lJEXuEHYvwZ2qQOtpDvObrWrw7AMoNb+8="}, {"Name": "label", "Value": "_content/scripts/sf-uploader.min.js"}]}, {"Route": "_content/scripts/sortable.min.9ka4b3gel8.js", "AssetFile": "_content/scripts/sortable.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10629"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eWU6pxuGsP4MNyxA4TARyedG9zOsG5lGYx2aNwON/eE=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9ka4b3gel8"}, {"Name": "integrity", "Value": "sha256-eWU6pxuGsP4MNyxA4TARyedG9zOsG5lGYx2aNwON/eE="}, {"Name": "label", "Value": "_content/scripts/sortable.min.js"}]}, {"Route": "_content/scripts/sortable.min.js", "AssetFile": "_content/scripts/sortable.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10629"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eWU6pxuGsP4MNyxA4TARyedG9zOsG5lGYx2aNwON/eE=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eWU6pxuGsP4MNyxA4TARyedG9zOsG5lGYx2aNwON/eE="}]}, {"Route": "_content/scripts/spinner.min.js", "AssetFile": "_content/scripts/spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10875"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"h74k4Bg7EBCx8pzMPaPcDU3sQvQch4k64uAiYYpK7mU=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h74k4Bg7EBCx8pzMPaPcDU3sQvQch4k64uAiYYpK7mU="}]}, {"Route": "_content/scripts/spinner.min.xua61h4c57.js", "AssetFile": "_content/scripts/spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10875"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"h74k4Bg7EBCx8pzMPaPcDU3sQvQch4k64uAiYYpK7mU=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xua61h4c57"}, {"Name": "integrity", "Value": "sha256-h74k4Bg7EBCx8pzMPaPcDU3sQvQch4k64uAiYYpK7mU="}, {"Name": "label", "Value": "_content/scripts/spinner.min.js"}]}, {"Route": "_content/scripts/splitbuttonsbase.min.js", "AssetFile": "_content/scripts/splitbuttonsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3669"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"o19T3fThBO7JaedmBDuUsVgWWbSgvYp54+D4SCx1obs=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o19T3fThBO7JaedmBDuUsVgWWbSgvYp54+D4SCx1obs="}]}, {"Route": "_content/scripts/splitbuttonsbase.min.xj1a658k5f.js", "AssetFile": "_content/scripts/splitbuttonsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3669"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"o19T3fThBO7JaedmBDuUsVgWWbSgvYp54+D4SCx1obs=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xj1a658k5f"}, {"Name": "integrity", "Value": "sha256-o19T3fThBO7JaedmBDuUsVgWWbSgvYp54+D4SCx1obs="}, {"Name": "label", "Value": "_content/scripts/splitbuttonsbase.min.js"}]}, {"Route": "_content/scripts/svgbase.min.js", "AssetFile": "_content/scripts/svgbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "49904"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HfuCPKsAsYzrFetVM++hiApQFekgpw3vRNj+aePxh7M=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HfuCPKsAsYzrFetVM++hiApQFekgpw3vRNj+aePxh7M="}]}, {"Route": "_content/scripts/svgbase.min.zknyd3obxc.js", "AssetFile": "_content/scripts/svgbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "49904"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HfuCPKsAsYzrFetVM++hiApQFekgpw3vRNj+aePxh7M=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:12:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zknyd3obxc"}, {"Name": "integrity", "Value": "sha256-HfuCPKsAsYzrFetVM++hiApQFekgpw3vRNj+aePxh7M="}, {"Name": "label", "Value": "_content/scripts/svgbase.min.js"}]}, {"Route": "_content/scripts/syncfusion-blazor-base.min.9wfcn8fwyp.js", "AssetFile": "_content/scripts/syncfusion-blazor-base.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "232813"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eWK7w5FhKHoD6qzFXSwDZ8FzjmxsX+uCQ3oOl3zcFz4=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9wfcn8fwyp"}, {"Name": "integrity", "Value": "sha256-eWK7w5FhKHoD6qzFXSwDZ8FzjmxsX+uCQ3oOl3zcFz4="}, {"Name": "label", "Value": "_content/scripts/syncfusion-blazor-base.min.js"}]}, {"Route": "_content/scripts/syncfusion-blazor-base.min.js", "AssetFile": "_content/scripts/syncfusion-blazor-base.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "232813"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eWK7w5FhKHoD6qzFXSwDZ8FzjmxsX+uCQ3oOl3zcFz4=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eWK7w5FhKHoD6qzFXSwDZ8FzjmxsX+uCQ3oOl3zcFz4="}]}, {"Route": "_content/scripts/syncfusion-blazor.min.js", "AssetFile": "_content/scripts/syncfusion-blazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3976620"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XSBowJgml3y3tJMyxkXXH1tKDJq5+CKey26T5CJygPw=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XSBowJgml3y3tJMyxkXXH1tKDJq5+CKey26T5CJygPw="}]}, {"Route": "_content/scripts/syncfusion-blazor.min.rep4azxr6y.js", "AssetFile": "_content/scripts/syncfusion-blazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3976620"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XSBowJgml3y3tJMyxkXXH1tKDJq5+CKey26T5CJygPw=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rep4azxr6y"}, {"Name": "integrity", "Value": "sha256-XSBowJgml3y3tJMyxkXXH1tKDJq5+CKey26T5CJygPw="}, {"Name": "label", "Value": "_content/scripts/syncfusion-blazor.min.js"}]}, {"Route": "_content/themes/bootstrap-dark.bcsws67y68.css", "AssetFile": "_content/themes/bootstrap-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3324152"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hURXJ8iFSEag3wSGifFRLo7d+P7zzXnbEeHuM7ier1A=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bcsws67y68"}, {"Name": "integrity", "Value": "sha256-hURXJ8iFSEag3wSGifFRLo7d+P7zzXnbEeHuM7ier1A="}, {"Name": "label", "Value": "_content/themes/bootstrap-dark.css"}]}, {"Route": "_content/themes/bootstrap-dark.css", "AssetFile": "_content/themes/bootstrap-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3324152"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hURXJ8iFSEag3wSGifFRLo7d+P7zzXnbEeHuM7ier1A=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hURXJ8iFSEag3wSGifFRLo7d+P7zzXnbEeHuM7ier1A="}]}, {"Route": "_content/themes/bootstrap.css", "AssetFile": "_content/themes/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3307632"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Zh0Nj+2qHkQ4EiaFakBoOxe/AwEMQV4VfnJ5Vfvc/Pg=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Zh0Nj+2qHkQ4EiaFakBoOxe/AwEMQV4VfnJ5Vfvc/Pg="}]}, {"Route": "_content/themes/bootstrap.qrwu8phw4d.css", "AssetFile": "_content/themes/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3307632"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Zh0Nj+2qHkQ4EiaFakBoOxe/AwEMQV4VfnJ5Vfvc/Pg=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qrwu8phw4d"}, {"Name": "integrity", "Value": "sha256-Zh0Nj+2qHkQ4EiaFakBoOxe/AwEMQV4VfnJ5Vfvc/Pg="}, {"Name": "label", "Value": "_content/themes/bootstrap.css"}]}, {"Route": "_content/themes/bootstrap4.7y8yw82vx4.css", "AssetFile": "_content/themes/bootstrap4.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3366189"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z4FKRLI3pRF3ED7cCGjMwlzqrSgW85vHHJal8FMGl1s=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7y8yw82vx4"}, {"Name": "integrity", "Value": "sha256-z4FKRLI3pRF3ED7cCGjMwlzqrSgW85vHHJal8FMGl1s="}, {"Name": "label", "Value": "_content/themes/bootstrap4.css"}]}, {"Route": "_content/themes/bootstrap4.css", "AssetFile": "_content/themes/bootstrap4.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3366189"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z4FKRLI3pRF3ED7cCGjMwlzqrSgW85vHHJal8FMGl1s=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z4FKRLI3pRF3ED7cCGjMwlzqrSgW85vHHJal8FMGl1s="}]}, {"Route": "_content/themes/bootstrap5-dark.45lgpaqrwi.css", "AssetFile": "_content/themes/bootstrap5-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3365084"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+K+orUhDUBnbxXNy7bbQfGPj7jYZjHHbA8nvfyAscY8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "45<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-+K+orUhDUBnbxXNy7bbQfGPj7jYZjHHbA8nvfyAscY8="}, {"Name": "label", "Value": "_content/themes/bootstrap5-dark.css"}]}, {"Route": "_content/themes/bootstrap5-dark.css", "AssetFile": "_content/themes/bootstrap5-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3365084"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+K+orUhDUBnbxXNy7bbQfGPj7jYZjHHbA8nvfyAscY8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+K+orUhDUBnbxXNy7bbQfGPj7jYZjHHbA8nvfyAscY8="}]}, {"Route": "_content/themes/bootstrap5.95j05ll0ew.css", "AssetFile": "_content/themes/bootstrap5.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3365117"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"E7bFLUlA0eOkVujXup1Vxb2s+TiHsxOiyjSKhSnvv2g=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "95j05ll0ew"}, {"Name": "integrity", "Value": "sha256-E7bFLUlA0eOkVujXup1Vxb2s+TiHsxOiyjSKhSnvv2g="}, {"Name": "label", "Value": "_content/themes/bootstrap5.css"}]}, {"Route": "_content/themes/bootstrap5.css", "AssetFile": "_content/themes/bootstrap5.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3365117"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"E7bFLUlA0eOkVujXup1Vxb2s+TiHsxOiyjSKhSnvv2g=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E7bFLUlA0eOkVujXup1Vxb2s+TiHsxOiyjSKhSnvv2g="}]}, {"Route": "_content/themes/customized/material-dark.997vthlmih.css", "AssetFile": "_content/themes/customized/material-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3961911"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fSdujPVfbvpc9CNbAU/DqeEbg8IOGdVAALU4XZu6ktY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "997vthlmih"}, {"Name": "integrity", "Value": "sha256-fSdujPVfbvpc9CNbAU/DqeEbg8IOGdVAALU4XZu6ktY="}, {"Name": "label", "Value": "_content/themes/customized/material-dark.css"}]}, {"Route": "_content/themes/customized/material-dark.css", "AssetFile": "_content/themes/customized/material-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3961911"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fSdujPVfbvpc9CNbAU/DqeEbg8IOGdVAALU4XZu6ktY=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fSdujPVfbvpc9CNbAU/DqeEbg8IOGdVAALU4XZu6ktY="}]}, {"Route": "_content/themes/customized/material.css", "AssetFile": "_content/themes/customized/material.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3907664"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OQh+RvWKEIKjKSMpdd9W+FbvWZmkpoO7HSAHQfC5nmM=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OQh+RvWKEIKjKSMpdd9W+FbvWZmkpoO7HSAHQfC5nmM="}]}, {"Route": "_content/themes/customized/material.zpi48ap3su.css", "AssetFile": "_content/themes/customized/material.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3907664"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OQh+RvWKEIKjKSMpdd9W+FbvWZmkpoO7HSAHQfC5nmM=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zpi48ap3su"}, {"Name": "integrity", "Value": "sha256-OQh+RvWKEIKjKSMpdd9W+FbvWZmkpoO7HSAHQfC5nmM="}, {"Name": "label", "Value": "_content/themes/customized/material.css"}]}, {"Route": "_content/themes/customized/tailwind-dark.css", "AssetFile": "_content/themes/customized/tailwind-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3297026"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cJrC3rLz0aCvng+J2YcU9u/D66a//kh8Ly4G3r34NT4=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cJrC3rLz0aCvng+J2YcU9u/D66a//kh8Ly4G3r34NT4="}]}, {"Route": "_content/themes/customized/tailwind-dark.sda4gepv51.css", "AssetFile": "_content/themes/customized/tailwind-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3297026"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cJrC3rLz0aCvng+J2YcU9u/D66a//kh8Ly4G3r34NT4=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sda4gepv51"}, {"Name": "integrity", "Value": "sha256-cJrC3rLz0aCvng+J2YcU9u/D66a//kh8Ly4G3r34NT4="}, {"Name": "label", "Value": "_content/themes/customized/tailwind-dark.css"}]}, {"Route": "_content/themes/customized/tailwind.1en6a1792r.css", "AssetFile": "_content/themes/customized/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3264705"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"6VlXR9SCBMwhd04Kg9hbb3MDDD8b5Ah5PuxjxOzcG14=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1en6a1792r"}, {"Name": "integrity", "Value": "sha256-6VlXR9SCBMwhd04Kg9hbb3MDDD8b5Ah5PuxjxOzcG14="}, {"Name": "label", "Value": "_content/themes/customized/tailwind.css"}]}, {"Route": "_content/themes/customized/tailwind.css", "AssetFile": "_content/themes/customized/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3264705"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"6VlXR9SCBMwhd04Kg9hbb3MDDD8b5Ah5PuxjxOzcG14=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6VlXR9SCBMwhd04Kg9hbb3MDDD8b5Ah5PuxjxOzcG14="}]}, {"Route": "_content/themes/fabric-dark.css", "AssetFile": "_content/themes/fabric-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3264469"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/IMXABI43mBt7ayRyP8Kb2bXjks6mbNw1wqxdbNFURM=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/IMXABI43mBt7ayRyP8Kb2bXjks6mbNw1wqxdbNFURM="}]}, {"Route": "_content/themes/fabric-dark.grqaeceerl.css", "AssetFile": "_content/themes/fabric-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3264469"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/IMXABI43mBt7ayRyP8Kb2bXjks6mbNw1wqxdbNFURM=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gr<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-/IMXABI43mBt7ayRyP8Kb2bXjks6mbNw1wqxdbNFURM="}, {"Name": "label", "Value": "_content/themes/fabric-dark.css"}]}, {"Route": "_content/themes/fabric.css", "AssetFile": "_content/themes/fabric.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3210848"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JFRFQsxbXfWOX2siadKmZnqmETf5yuGsA9N5zt5U4l4=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JFRFQsxbXfWOX2siadKmZnqmETf5yuGsA9N5zt5U4l4="}]}, {"Route": "_content/themes/fabric.otb8izkn2n.css", "AssetFile": "_content/themes/fabric.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3210848"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JFRFQsxbXfWOX2siadKmZnqmETf5yuGsA9N5zt5U4l4=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "otb8izkn2n"}, {"Name": "integrity", "Value": "sha256-JFRFQsxbXfWOX2siadKmZnqmETf5yuGsA9N5zt5U4l4="}, {"Name": "label", "Value": "_content/themes/fabric.css"}]}, {"Route": "_content/themes/fluent-dark.60b1pbm5ih.css", "AssetFile": "_content/themes/fluent-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3309908"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"9lLvUT1WBsYw1E54+uU0Qm9XnB0oWyp8k1bqEhIPhPM=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "60b1pbm5ih"}, {"Name": "integrity", "Value": "sha256-9lLvUT1WBsYw1E54+uU0Qm9XnB0oWyp8k1bqEhIPhPM="}, {"Name": "label", "Value": "_content/themes/fluent-dark.css"}]}, {"Route": "_content/themes/fluent-dark.css", "AssetFile": "_content/themes/fluent-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3309908"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"9lLvUT1WBsYw1E54+uU0Qm9XnB0oWyp8k1bqEhIPhPM=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9lLvUT1WBsYw1E54+uU0Qm9XnB0oWyp8k1bqEhIPhPM="}]}, {"Route": "_content/themes/fluent.6051a77scl.css", "AssetFile": "_content/themes/fluent.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3308049"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"avq795fa4KqbOOnR54ju/oeuBOHjKe8DwQ1mDLVvYNc=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6051a77scl"}, {"Name": "integrity", "Value": "sha256-avq795fa4KqbOOnR54ju/oeuBOHjKe8DwQ1mDLVvYNc="}, {"Name": "label", "Value": "_content/themes/fluent.css"}]}, {"Route": "_content/themes/fluent.css", "AssetFile": "_content/themes/fluent.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3308049"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"avq795fa4KqbOOnR54ju/oeuBOHjKe8DwQ1mDLVvYNc=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-avq795fa4KqbOOnR54ju/oeuBOHjKe8DwQ1mDLVvYNc="}]}, {"Route": "_content/themes/fluent2-dark.b1hozmx0gn.css", "AssetFile": "_content/themes/fluent2-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3815946"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"P5eJFXf1MdZQDmjIJAJ8u+19dsD//So38CxozrDQVNk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b1hozmx0gn"}, {"Name": "integrity", "Value": "sha256-P5eJFXf1MdZQDmjIJAJ8u+19dsD//So38CxozrDQVNk="}, {"Name": "label", "Value": "_content/themes/fluent2-dark.css"}]}, {"Route": "_content/themes/fluent2-dark.css", "AssetFile": "_content/themes/fluent2-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3815946"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"P5eJFXf1MdZQDmjIJAJ8u+19dsD//So38CxozrDQVNk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P5eJFXf1MdZQDmjIJAJ8u+19dsD//So38CxozrDQVNk="}]}, {"Route": "_content/themes/fluent2.7m1n3mk3yu.css", "AssetFile": "_content/themes/fluent2.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3854317"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EZ2WSkQOpuWpMYtDq1vB+BMkS+KDcucqDv5aEvC//dI=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7m1n3mk3yu"}, {"Name": "integrity", "Value": "sha256-EZ2WSkQOpuWpMYtDq1vB+BMkS+KDcucqDv5aEvC//dI="}, {"Name": "label", "Value": "_content/themes/fluent2.css"}]}, {"Route": "_content/themes/fluent2.css", "AssetFile": "_content/themes/fluent2.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3854317"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EZ2WSkQOpuWpMYtDq1vB+BMkS+KDcucqDv5aEvC//dI=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EZ2WSkQOpuWpMYtDq1vB+BMkS+KDcucqDv5aEvC//dI="}]}, {"Route": "_content/themes/highcontrast.css", "AssetFile": "_content/themes/highcontrast.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3224148"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NmxpZJ1X0zih7JnLGAK9x5mDjN1NEx+awxc/DQw8vUU=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NmxpZJ1X0zih7JnLGAK9x5mDjN1NEx+awxc/DQw8vUU="}]}, {"Route": "_content/themes/highcontrast.uv58ght8j2.css", "AssetFile": "_content/themes/highcontrast.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3224148"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NmxpZJ1X0zih7JnLGAK9x5mDjN1NEx+awxc/DQw8vUU=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uv58ght8j2"}, {"Name": "integrity", "Value": "sha256-NmxpZJ1X0zih7JnLGAK9x5mDjN1NEx+awxc/DQw8vUU="}, {"Name": "label", "Value": "_content/themes/highcontrast.css"}]}, {"Route": "_content/themes/material-dark.1y75lschnk.css", "AssetFile": "_content/themes/material-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3961979"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"edy1DyUXPTsNX0gh+CF32S2O6n74/h50SO0XlkLLXgk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1y75lschnk"}, {"Name": "integrity", "Value": "sha256-edy1DyUXPTsNX0gh+CF32S2O6n74/h50SO0XlkLLXgk="}, {"Name": "label", "Value": "_content/themes/material-dark.css"}]}, {"Route": "_content/themes/material-dark.css", "AssetFile": "_content/themes/material-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3961979"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"edy1DyUXPTsNX0gh+CF32S2O6n74/h50SO0XlkLLXgk=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-edy1DyUXPTsNX0gh+CF32S2O6n74/h50SO0XlkLLXgk="}]}, {"Route": "_content/themes/material.css", "AssetFile": "_content/themes/material.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3907732"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1NMOrdEN7gJNDetMgZTvPADHeYWz+TVs6CCgjpPZ7j8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1NMOrdEN7gJNDetMgZTvPADHeYWz+TVs6CCgjpPZ7j8="}]}, {"Route": "_content/themes/material.ovanlmfkzq.css", "AssetFile": "_content/themes/material.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3907732"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1NMOrdEN7gJNDetMgZTvPADHeYWz+TVs6CCgjpPZ7j8=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ovanlmfkzq"}, {"Name": "integrity", "Value": "sha256-1NMOrdEN7gJNDetMgZTvPADHeYWz+TVs6CCgjpPZ7j8="}, {"Name": "label", "Value": "_content/themes/material.css"}]}, {"Route": "_content/themes/material3-dark.css", "AssetFile": "_content/themes/material3-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3983506"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iFI2ogDfT6RuBfqsawr4tSqkXGtyw53amJNpX7ufmyU=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iFI2ogDfT6RuBfqsawr4tSqkXGtyw53amJNpX7ufmyU="}]}, {"Route": "_content/themes/material3-dark.k5gv3nl4t7.css", "AssetFile": "_content/themes/material3-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3983506"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iFI2ogDfT6RuBfqsawr4tSqkXGtyw53amJNpX7ufmyU=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k5gv3nl4t7"}, {"Name": "integrity", "Value": "sha256-iFI2ogDfT6RuBfqsawr4tSqkXGtyw53amJNpX7ufmyU="}, {"Name": "label", "Value": "_content/themes/material3-dark.css"}]}, {"Route": "_content/themes/material3.css", "AssetFile": "_content/themes/material3.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3984823"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"v24/cJ7yPtPrckXdXgemIHUd4CMVq8mwMyPvHukLVLQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-v24/cJ7yPtPrckXdXgemIHUd4CMVq8mwMyPvHukLVLQ="}]}, {"Route": "_content/themes/material3.xg6aaqeuyt.css", "AssetFile": "_content/themes/material3.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3984823"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"v24/cJ7yPtPrckXdXgemIHUd4CMVq8mwMyPvHukLVLQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xg6aaqeuyt"}, {"Name": "integrity", "Value": "sha256-v24/cJ7yPtPrckXdXgemIHUd4CMVq8mwMyPvHukLVLQ="}, {"Name": "label", "Value": "_content/themes/material3.css"}]}, {"Route": "_content/themes/tailwind-dark.2bboj1j9zj.css", "AssetFile": "_content/themes/tailwind-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3297136"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wqmPUgjuvMGkMyzyAW4oAeyPFvPlP9ppmr7seVj0q9g=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2bboj1j9zj"}, {"Name": "integrity", "Value": "sha256-wqmPUgjuvMGkMyzyAW4oAeyPFvPlP9ppmr7seVj0q9g="}, {"Name": "label", "Value": "_content/themes/tailwind-dark.css"}]}, {"Route": "_content/themes/tailwind-dark.css", "AssetFile": "_content/themes/tailwind-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3297136"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wqmPUgjuvMGkMyzyAW4oAeyPFvPlP9ppmr7seVj0q9g=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wqmPUgjuvMGkMyzyAW4oAeyPFvPlP9ppmr7seVj0q9g="}]}, {"Route": "_content/themes/tailwind.css", "AssetFile": "_content/themes/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3264815"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fk1hfGJzh+Kh93okf0hZ3l6/fRQTrETMjLMhJd1NhJo=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fk1hfGJzh+Kh93okf0hZ3l6/fRQTrETMjLMhJd1NhJo="}]}, {"Route": "_content/themes/tailwind.iod9dyhsig.css", "AssetFile": "_content/themes/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3264815"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fk1hfGJzh+Kh93okf0hZ3l6/fRQTrETMjLMhJd1NhJo=\""}, {"Name": "Last-Modified", "Value": "Mon, 08 Jul 2024 09:14:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iod9dyhsig"}, {"Name": "integrity", "Value": "sha256-fk1hfGJzh+Kh93okf0hZ3l6/fRQTrETMjLMhJd1NhJo="}, {"Name": "label", "Value": "_content/themes/tailwind.css"}]}, {"Route": "app.9tsjre4pz5.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2154"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Y3QRrYDjsQ+Bj4noKNSCcSRxgRtgAn0QL1o5m5BNepk=\""}, {"Name": "Last-Modified", "Value": "Fri, 19 Jul 2024 02:58:10 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9tsjre4pz5"}, {"Name": "integrity", "Value": "sha256-Y3QRrYDjsQ+Bj4noKNSCcSRxgRtgAn0QL1o5m5BNepk="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "app.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2154"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Y3QRrYDjsQ+Bj4noKNSCcSRxgRtgAn0QL1o5m5BNepk=\""}, {"Name": "Last-Modified", "Value": "Fri, 19 Jul 2024 02:58:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Y3QRrYDjsQ+Bj4noKNSCcSRxgRtgAn0QL1o5m5BNepk="}]}, {"Route": "bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Fri, 05 Jul 2024 05:46:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css"}]}, {"Route": "bootstrap/bootstrap.min.css", "AssetFile": "bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Fri, 05 Jul 2024 05:46:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 05 Jul 2024 05:46:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map"}]}, {"Route": "bootstrap/bootstrap.min.css.map", "AssetFile": "bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 05 Jul 2024 05:46:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "data/CaseBelong.081jllrvkd.json", "AssetFile": "data/CaseBelong.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1002"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"8KWvvzmuiMJOsLtrJly5FCRI4s/h2cZ0+dzlJuXAiwI=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Feb 2025 09:00:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "081jllrvkd"}, {"Name": "integrity", "Value": "sha256-8KWvvzmuiMJOsLtrJly5FCRI4s/h2cZ0+dzlJuXAiwI="}, {"Name": "label", "Value": "data/CaseBelong.json"}]}, {"Route": "data/CaseBelong.json", "AssetFile": "data/CaseBelong.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1002"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"8KWvvzmuiMJOsLtrJly5FCRI4s/h2cZ0+dzlJuXAiwI=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Feb 2025 09:00:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8KWvvzmuiMJOsLtrJly5FCRI4s/h2cZ0+dzlJuXAiwI="}]}, {"Route": "data/ChildCareKind.json", "AssetFile": "data/ChildCareKind.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1094"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"3uOWl7QAhK9jvf382lkLYs42UCdDwBNd3MKWsl0PYxM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 29 Apr 2025 05:26:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3uOWl7QAhK9jvf382lkLYs42UCdDwBNd3MKWsl0PYxM="}]}, {"Route": "data/ChildCareKind.mqq2ouqckq.json", "AssetFile": "data/ChildCareKind.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1094"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"3uOWl7QAhK9jvf382lkLYs42UCdDwBNd3MKWsl0PYxM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 29 Apr 2025 05:26:02 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mqq2ouqckq"}, {"Name": "integrity", "Value": "sha256-3uOWl7QAhK9jvf382lkLYs42UCdDwBNd3MKWsl0PYxM="}, {"Name": "label", "Value": "data/ChildCareKind.json"}]}, {"Route": "data/EmailList.itlke06i6b.json", "AssetFile": "data/EmailList.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "507"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"dlmiJE4AiiUgXf0MFsVl3T/TSnIBragmyGtleEQ41H4=\""}, {"Name": "Last-Modified", "Value": "Thu, 02 Jan 2025 01:21:16 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "itlke06i6b"}, {"Name": "integrity", "Value": "sha256-dlmiJE4AiiUgXf0MFsVl3T/TSnIBragmyGtleEQ41H4="}, {"Name": "label", "Value": "data/EmailList.json"}]}, {"Route": "data/EmailList.json", "AssetFile": "data/EmailList.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "507"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"dlmiJE4AiiUgXf0MFsVl3T/TSnIBragmyGtleEQ41H4=\""}, {"Name": "Last-Modified", "Value": "Thu, 02 Jan 2025 01:21:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dlmiJE4AiiUgXf0MFsVl3T/TSnIBragmyGtleEQ41H4="}]}, {"Route": "data/SMTP.db7hd3qomk.json", "AssetFile": "data/SMTP.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "103"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"SfhMR7kKW9kmciGa3RAdEwNWbnpGk023jGLI7KzswJI=\""}, {"Name": "Last-Modified", "Value": "Thu, 21 Nov 2024 00:13:14 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "db7hd3qomk"}, {"Name": "integrity", "Value": "sha256-SfhMR7kKW9kmciGa3RAdEwNWbnpGk023jGLI7KzswJI="}, {"Name": "label", "Value": "data/SMTP.json"}]}, {"Route": "data/SMTP.json", "AssetFile": "data/SMTP.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "103"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"SfhMR7kKW9kmciGa3RAdEwNWbnpGk023jGLI7KzswJI=\""}, {"Name": "Last-Modified", "Value": "Thu, 21 Nov 2024 00:13:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SfhMR7kKW9kmciGa3RAdEwNWbnpGk023jGLI7KzswJI="}]}, {"Route": "modals.css", "AssetFile": "modals.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2032"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O/PqngcmcAETWL0dXkHL1E5ATmcxP8KuqFU1PIdbbPI=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Oct 2024 05:42:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O/PqngcmcAETWL0dXkHL1E5ATmcxP8KuqFU1PIdbbPI="}]}, {"Route": "modals.vfom2vzevq.css", "AssetFile": "modals.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2032"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O/PqngcmcAETWL0dXkHL1E5ATmcxP8KuqFU1PIdbbPI=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Oct 2024 05:42:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfom2vzevq"}, {"Name": "integrity", "Value": "sha256-O/PqngcmcAETWL0dXkHL1E5ATmcxP8KuqFU1PIdbbPI="}, {"Name": "label", "Value": "modals.css"}]}, {"Route": "restrictData/sysconfig.ds5vopyc44.json", "AssetFile": "restrictData/sysconfig.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "129"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"DWtm6gk2jycODH+9EKwu4NSsNoUkyMiYaY4gLVlt5gA=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Dec 2024 07:28:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ds5vopyc44"}, {"Name": "integrity", "Value": "sha256-DWtm6gk2jycODH+9EKwu4NSsNoUkyMiYaY4gLVlt5gA="}, {"Name": "label", "Value": "restrictData/sysconfig.json"}]}, {"Route": "restrictData/sysconfig.json", "AssetFile": "restrictData/sysconfig.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "129"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"DWtm6gk2jycODH+9EKwu4NSsNoUkyMiYaY4gLVlt5gA=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Dec 2024 07:28:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DWtm6gk2jycODH+9EKwu4NSsNoUkyMiYaY4gLVlt5gA="}]}, {"Route": "sites.css", "AssetFile": "sites.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7435"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+yxxI79KdOsNbriNXf8gq4pd6hUT2Cxweuz0/ZKgcHw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 17 Dec 2024 06:46:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+yxxI79KdOsNbriNXf8gq4pd6hUT2Cxweuz0/ZKgcHw="}]}, {"Route": "sites.j6pi1nge1j.css", "AssetFile": "sites.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7435"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+yxxI79KdOsNbriNXf8gq4pd6hUT2Cxweuz0/ZKgcHw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 17 Dec 2024 06:46:46 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j6pi1nge1j"}, {"Name": "integrity", "Value": "sha256-+yxxI79KdOsNbriNXf8gq4pd6hUT2Cxweuz0/ZKgcHw="}, {"Name": "label", "Value": "sites.css"}]}, {"Route": "uploads/Reports/16b72f46-fe01-450d-bc13-9e711b2ef7e4_news638790166375446300.pdf", "AssetFile": "uploads/Reports/16b72f46-fe01-450d-bc13-9e711b2ef7e4_news638790166375446300.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "129735"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"qmmJ0+QUcu/qkiaDmvSOH/fHOavNufVCubbLeB3BvJE=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Apr 2025 07:05:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qmmJ0+QUcu/qkiaDmvSOH/fHOavNufVCubbLeB3BvJE="}]}, {"Route": "uploads/Reports/16b72f46-fe01-450d-bc13-9e711b2ef7e4_news638790166375446300.yp7hdmx02g.pdf", "AssetFile": "uploads/Reports/16b72f46-fe01-450d-bc13-9e711b2ef7e4_news638790166375446300.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "129735"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"qmmJ0+QUcu/qkiaDmvSOH/fHOavNufVCubbLeB3BvJE=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Apr 2025 07:05:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yp7hdmx02g"}, {"Name": "integrity", "Value": "sha256-qmmJ0+QUcu/qkiaDmvSOH/fHOavNufVCubbLeB3BvJE="}, {"Name": "label", "Value": "uploads/Reports/16b72f46-fe01-450d-bc13-9e711b2ef7e4_news638790166375446300.pdf"}]}]}