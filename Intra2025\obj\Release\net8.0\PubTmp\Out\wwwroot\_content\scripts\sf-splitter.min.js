/*!*  filename: sf-splitter.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[59],{"./bundles/sf-splitter.js":function(e,t,s){"use strict";s.r(t);s("./modules/sf-splitter.js")},"./modules/sf-splitter.js":function(e,t){function s(e){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Splitter=function(){"use strict";var e=function(){return(e=Object.assign||function(e){for(var t,s=1,i=arguments.length;s<i;s++)for(var n in t=arguments[s])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)},t="e-split-bar",i="e-static-pane",n="e-resize-handler",a="e-arrow-left",r="e-arrow-up",o="e-arrow-down",l="e-icon-hidden",h="msie",d="Horizontal",p="px",c=function(){function c(e){this.allPanes=[],this.allBars=[],this.previousCoordinates={},this.currentCoordinates={},this.updatePrePaneInPercentage=!1,this.updateNextPaneInPercentage=!1,this.panesDimensions=[],this.border=0,this.isToggleInvoke=!1,this.createdEnabled=!1,this.resizingEnabled=!1,this.expandedEnabled=!1,this.onExpandEnabled=!1,this.collapsedEnabled=!1,this.onCollapseEnabled=!1,this.onResizeStopEnabled=!1,this.onResizeStartEnabled=!1,this.iconsDelay=300,this.collapseFlag=!1,this.expandFlag=!0,this.expandingFlag=!0,this.enablePersistence=!1,this.isPreventResizeAction=!0,window.sfBlazor=window.sfBlazor,this.updateContext(e),window.sfBlazor&&(sf.base.isNullOrUndefined(window.sfBlazor.instances)&&(window.sfBlazor.instances=[]),window.sfBlazor.instances[this.dataId]=this)}return c.prototype.updateContext=function(e){sf.base.extend(this,this,e)},c.prototype.initialize=function(){this.id=this.element.id,this.addSeparator(!0),this.collapseFlag=!0,this.isCollapsed(),this.collapseFlag=!1,this.updateClass(),sf.base.EventHandler.add(document,"touchstart click",this.onDocumentClick,this),document.body.contains(this.element)&&this.createdEnabled&&this.dotNetRef.invokeMethodAsync("CreatedEvent",null),sf.base.EventHandler.add(this.element,"keydown",this.onMove,this),window.addEventListener("resize",this.reportWindowSize.bind(this),!0),sf.base.Browser.isDevice&&sf.base.addClass([this.element],"e-splitter-touch"),this.enableReversePanes&&this.setReversePane()},c.prototype.setReversePane=function(){this.allPanes=this.allPanes.reverse(),this.allBars=this.allBars.reverse(),sf.base.addClass([this.allBars[this.allBars.length-1]],"e-last-bar"),sf.base.removeClass([this.allBars[0]],"e-last-bar")},c.prototype.onDocumentClick=function(e){e.target.classList.contains(t)||sf.base.isNullOrUndefined(this.currentSeparator)||sf.base.removeClass([this.currentSeparator],["e-split-bar-hover","e-split-bar-active"])},c.prototype.updateClass=function(){if(sf.base.Browser.info.name===h){sf.base.addClass([this.element],"e-ie");for(var e=this.element.querySelectorAll(".e-splitter ."+n),t=0;t<e.length;t++){var s=sf.base.isNullOrUndefined(this.separatorSize)?1:this.separatorSize;sf.base.setStyleAttribute(e[t],{"padding-left":s/2+p,"padding-right":s/2+p})}}},c.prototype.reportWindowSize=function(){for(var e=this,t=this.allPanes.length,s=0;s<t;s++){if(!sf.base.isNullOrUndefined(this.paneSettings[s])&&sf.base.isNullOrUndefined(this.paneSettings[s].size)&&this.allPanes[s].classList.remove(i),t-1===s)this.element.querySelectorAll("."+i).length===t&&sf.base.removeClass([this.allPanes[s]],i)}setTimeout((function(){e.updateSplitterSize(!0)}),200)},c.prototype.onMove=function(e){if(this.allPanes.length>1&&this.element.children.length>1){this.getPaneDetails(),this.getPaneDimensions(!1);var s=this.getSeparatorIndex(this.currentSeparator),i=this.previousPane.classList.contains("e-collapsed"),n=this.previousPane.classList.contains("e-expanded"),a=this.nextPane.classList.contains("e-collapsed");(this.orientation!==d&&38===e.keyCode||this.orientation===d&&39===e.keyCode||this.orientation===d&&37===e.keyCode||this.orientation!==d&&40===e.keyCode)&&(!n&&!a&&!i||n&&!a)&&document.activeElement.classList.contains(t)&&this.paneSettings[s].resizable&&this.paneSettings[s+1].resizable?(e.preventDefault(),this.checkPaneSize(e),this.resizingEnabled&&this.dotNetRef.invokeMethodAsync("ResizingEvent",{event:this.getKeyBoardEvtArgs(e),index:[this.getPreviousPaneIndex(),this.getNextPaneIndex()],separator:this.getDomObject("separator",this.currentSeparator),paneSize:[this.prePaneDimenson,this.nextPaneDimension]})):13===e.keyCode&&this.paneSettings[s].collapsible&&document.activeElement.classList.contains(t)&&this.currentSeparator.classList.contains("e-split-bar-active")&&(this.previousPane.classList.contains("e-collapsed")?(this.expand(s),sf.base.addClass([this.currentSeparator],"e-split-bar-active")):(this.collapse(s),sf.base.addClass([this.currentSeparator],"e-split-bar-active")))}},c.prototype.addSeparator=function(e){var s;void 0===e&&(e=!1),this.allBars=[],this.allPanes=[];for(var i=0;i<this.element.children.length;i++)this.element.children[i].classList.contains("e-pane")&&this.allPanes.push(this.element.children[i]),this.element.children[i].classList.contains(t)&&this.allBars.push(this.element.children[i]);for(var a=this.allPanes.length,r=0;r<a;r++)if(r<a-1){if(s=this.allBars[r],this.updateIconClass(),sf.base.isNullOrUndefined(s)||(this.enableReversePanes?s.setAttribute("dir","ltr"):s.removeAttribute("dir"),(e||this.isVisible)&&(this.currentSeparator=s),this.isVisible=!1,this.addMouseActions(s),sf.base.EventHandler.add(s,"touchstart click",this.clickHandler,this),s.addEventListener("focus",(function(){document.activeElement.classList.contains(t)&&(s=document.activeElement,sf.base.addClass([s],"e-split-bar-active"))})),s.addEventListener("blur",(function(){sf.base.removeClass([s],"e-split-bar-active")}))),!sf.base.isNullOrUndefined(s))if(this.isResizable()){sf.base.EventHandler.add(s,"mousedown",this.onMouseDown,this);var o=sf.base.Browser.info.name===h?"pointerdown":"touchstart";sf.base.EventHandler.add(s,o,this.onMouseDown,this),this.updateResizablePanes(r)}else sf.base.addClass([sf.base.select("."+n,s)],"e-hide-handler")}else s&&sf.base.addClass([s],"e-last-bar"),a>1&&this.updateResizablePanes(r)},c.prototype.updateSeparator=function(){for(var e=0,t=0;t<this.allBars.length;t++){var s=this.allBars[t];sf.base.EventHandler.remove(s,"touchstart click",this.clickHandler),sf.base.EventHandler.remove(s,"mousedown",this.onMouseDown);var i=sf.base.Browser.info.name===h?"pointerdown":"touchstart";sf.base.EventHandler.remove(s,i,this.onMouseDown),e=""==this.allPanes[t].style.flexBasis?e:e+parseInt(this.allPanes[t].style.flexBasis),s.setAttribute("aria-valuenow",e.toString())}this.addSeparator()},c.prototype.updateResizablePanes=function(e){this.getPaneDetails(),this.isResizable(),sf.base.addClass([this.allPanes[e]],"e-resizable")},c.prototype.getPaneDetails=function(){var e=null,t=null;this.order=parseInt(this.currentSeparator.style.order,10),this.allPanes.length>1&&(e=this.getPrevPane(this.order),t=this.getNextPane(this.order)),e&&t&&(this.previousPane=e,this.nextPane=t,this.prevPaneIndex=this.getPreviousPaneIndex(),this.nextPaneIndex=this.getNextPaneIndex())},c.prototype.getOrderIndex=function(e,t){for(var s,i="pane"===t?this.allPanes:this.allBars,n=0;n<i.length;n++)parseInt(i[n].style.order)===e&&(s=n);return s},c.prototype.getPrevPane=function(e){return this.enableReversePanes?this.getOrderPane(e+1):this.getOrderPane(e-1)},c.prototype.getNextPane=function(e){return this.enableReversePanes?this.getOrderPane(e-1):this.getOrderPane(e+1)},c.prototype.getOrderPane=function(e){for(var t,s=0;s<this.element.children.length;s++)parseInt(this.element.children[s].style.order)===e&&(t=this.element.children[s]);return t},c.prototype.getPreviousPaneIndex=function(){var e=this.enableReversePanes?parseInt(this.currentSeparator.style.order,10)+1:parseInt(this.currentSeparator.style.order,10)-1;return this.getOrderIndex(e,"pane")},c.prototype.getNextPaneIndex=function(){var e=this.enableReversePanes?parseInt(this.currentSeparator.style.order,10)-1:parseInt(this.currentSeparator.style.order,10)+1;return this.getOrderIndex(e,"pane")},c.prototype.onMouseDown=function(e){var s=this;e.preventDefault();var i=e.target.classList.contains(t)?e.target:e.target.parentElement;if(this.splitterDetails(e),!i.classList.contains("e-navigate-arrow")){this.updateCurrentSeparator(i),sf.base.addClass([this.currentSeparator],"e-split-bar-active"),this.updateCursorPosition(e,"previous"),this.getPaneDetails();for(var n=this.element.querySelectorAll("iframe"),a=0;a<n.length;a++)n[a].style.pointerEvents="none";this.onResizeStartEnabled?(this.dotNetRef.invokeMethodAsync("ResizeStartEvent",{event:this.getMouseEvtArgs(e),index:[this.getPreviousPaneIndex(),this.getNextPaneIndex()],separator:this.getDomObject("separator",this.currentSeparator),cancel:!1}).then((function(e){s.isPreventResizeAction=e,e&&s.unwireResizeEvents()})),this.resizeEvent(e)):(this.isPreventResizeAction=!1,this.resizeEvent(e))}},c.prototype.getMouseEvtArgs=function(e){return{button:e.button,buttons:e.buttons,clientX:e.clientX,clientY:e.clientY,detail:e.detail,screenX:e.screenX,screenY:e.screenY,ctrlKey:e.ctrlKey,metaKey:e.metaKey,shiftKey:e.shiftKey,altKey:e.altKey,type:e.type}},c.prototype.getKeyBoardEvtArgs=function(e){return{key:e.key,code:e.code,location:e.location,repeat:e.repeat,ctrlKey:e.ctrlKey,metaKey:e.metaKey,shiftKey:e.shiftKey,altKey:e.altKey,type:e.type}},c.prototype.getDomObject=function(e,t){return null!=t?window.sfBlazor.getDomObject(e,t):null},c.prototype.resizeEvent=function(e){this.wireResizeEvents(),this.checkPaneSize(e)},c.prototype.checkPaneSize=function(e){var t,s,i=sf.base.isNullOrUndefined(this.separatorSize)?1:this.separatorSize;if(t=this.orientation===d?this.previousPane.offsetWidth:this.previousPane.offsetHeight,s=this.orientation===d?this.nextPane.offsetWidth:this.nextPane.offsetHeight,this.previousPane.style.flexBasis.indexOf("%")>0||this.nextPane.style.flexBasis.indexOf("%")>0){var n=this.updatePaneFlexBasis(this.previousPane),a=this.updatePaneFlexBasis(this.nextPane);this.totalPercent=n+a,this.totalWidth=this.convertPercentageToPixel(this.totalPercent+"%"),"keydown"!==e.type||sf.base.isNullOrUndefined(e.keyCode)||((39===e.keyCode||40===e.keyCode)&&s>0&&this.getMinInPixel(this.paneSettings[this.nextPaneIndex].min)<this.convertPercentageToPixel(a-1+"%")?(this.previousPane.style.flexBasis=n+1+"%",this.nextPane.style.flexBasis=a-1+"%"):(37===e.keyCode||38===e.keyCode)&&t>0&&this.getMinInPixel(this.paneSettings[this.prevPaneIndex].min)<this.convertPercentageToPixel(n-1+"%")&&(this.previousPane.style.flexBasis=n-1+"%",this.nextPane.style.flexBasis=a+1+"%"))}else this.totalWidth=this.orientation===d?this.previousPane.offsetWidth+this.nextPane.offsetWidth:this.previousPane.offsetHeight+this.nextPane.offsetHeight,"keydown"!==e.type||sf.base.isNullOrUndefined(e.keyCode)||((39===e.keyCode||40===e.keyCode)&&s>0&&this.getMinInPixel(this.paneSettings[this.nextPaneIndex].min)<s+i?(this.addStaticPaneClass(),this.previousPane.style.flexBasis=t+i+p,this.nextPane.style.flexBasis=s<i?"0px":s-i+p):(37===e.keyCode||38===e.keyCode)&&t>0&&this.getMinInPixel(this.paneSettings[this.prevPaneIndex].min)<t-i&&(this.addStaticPaneClass(),this.previousPane.style.flexBasis=t<i?"0px":t-i+p,this.nextPane.style.flexBasis=s+i+p))},c.prototype.getMinInPixel=function(e){if(sf.base.isNullOrUndefined(e))return 0;var t=this.convertPixelToNumber(e.toString());return e.indexOf("%")>0&&(t=this.convertPercentageToPixel(e)),this.convertPixelToNumber(t.toString())},c.prototype.addStaticPaneClass=function(){this.previousPane.classList.contains(i)||""===this.previousPane.style.flexBasis||sf.base.addClass([this.previousPane],i),this.nextPane.classList.contains(i)||""===this.nextPane.style.flexBasis||sf.base.addClass([this.nextPane],i)},c.prototype.convertPercentageToPixel=function(e,t){var s,i=e.toString();if(i.indexOf("%")>-1){s=parseFloat(i.slice(0,i.indexOf("%")));var n=void 0;n=sf.base.isNullOrUndefined(t)?this.orientation===d?this.element.offsetWidth:this.element.offsetHeight:this.panesDimensions[this.allPanes.indexOf(t)],s=Math.ceil(n*(s/100))}else s=parseInt(i,10);return s},c.prototype.updatePaneFlexBasis=function(e){var t;if(e.style.flexBasis.indexOf("%")>0)t=parseFloat(e.style.flexBasis.slice(0,e.style.flexBasis.indexOf("%")));else if(""!==e.style.flexBasis)t=this.convertPixelToPercentage(this.convertPixelToNumber(e.style.flexBasis));else{var s=this.orientation===d?e.offsetWidth:e.offsetHeight;t=this.convertPixelToPercentage(s)}return t},c.prototype.convertPixelToPercentage=function(e){return e/(this.orientation===d?this.element.offsetWidth:this.element.offsetHeight)*100},c.prototype.convertPixelToNumber=function(e){return(e=e.toString()).indexOf("p")>-1?parseFloat(e.slice(0,e.indexOf("p"))):parseFloat(e)},c.prototype.updateCurrentSeparator=function(e){this.currentSeparator=e.classList.contains(t)?e:e.parentElement},c.prototype.wireResizeEvents=function(){sf.base.EventHandler.add(document,"mousemove",this.onMouseMove,this),sf.base.EventHandler.add(document,"mouseup",this.onMouseUp,this);var e=sf.base.Browser.info.name===h?"pointermove":"touchmove",t=sf.base.Browser.info.name===h?"pointerup":"touchend";sf.base.EventHandler.add(document,e,this.onMouseMove,this),sf.base.EventHandler.add(document,t,this.onMouseUp,this)},c.prototype.unwireResizeEvents=function(){var e=sf.base.Browser.info.name===h?"pointermove":"touchmove",t=sf.base.Browser.info.name===h?"pointerup":"touchend";sf.base.EventHandler.remove(document,"mousemove",this.onMouseMove),sf.base.EventHandler.remove(document,"mouseup",this.onMouseUp),sf.base.EventHandler.remove(document,e,this.onMouseMove),sf.base.EventHandler.remove(document,t,this.onMouseUp),window.removeEventListener("resize",this.reportWindowSize.bind(this))},c.prototype.onMouseMove=function(e){if(this.isCursorMoved(e)&&!this.isPreventResizeAction){this.getPaneDetails(),this.getPaneDimensions(!1),this.resizingEnabled&&this.dotNetRef.invokeMethodAsync("ResizingEvent",{event:this.getMouseEvtArgs(e),index:[this.getPreviousPaneIndex(),this.getNextPaneIndex()],separator:this.getDomObject("separator",this.currentSeparator),paneSize:[this.prePaneDimenson,this.nextPaneDimension]});var t,n=this.validateDraggedPosition(this.getSeparatorPosition(e),this.prePaneDimenson,this.nextPaneDimension);if(this.getBorder(),t=this.orientation===d?this.element.getBoundingClientRect().left+n-this.currentSeparator.getBoundingClientRect().left+this.border:this.element.getBoundingClientRect().top+n-this.currentSeparator.getBoundingClientRect().top+this.border,this.nextPaneHeightWidth="string"===s(this.nextPaneHeightWidth)&&this.nextPaneHeightWidth.indexOf("p")>-1?this.convertPixelToNumber(this.nextPaneHeightWidth):parseInt(this.nextPaneHeightWidth,10),this.previousPaneHeightWidth="string"===s(this.previousPaneHeightWidth)&&this.previousPaneHeightWidth.indexOf("p")>-1?this.convertPixelToNumber(this.previousPaneHeightWidth):parseInt(this.previousPaneHeightWidth,10),this.prevPaneCurrentWidth=t+this.previousPaneHeightWidth,this.nextPaneCurrentWidth=this.nextPaneHeightWidth-t,this.validateMinMaxValues(),this.nextPaneCurrentWidth<0&&(this.nextPaneCurrentWidth=0),this.prevPaneCurrentWidth<0&&(this.prevPaneCurrentWidth=0),this.nextPaneCurrentWidth+this.prevPaneCurrentWidth>this.totalWidth&&(this.nextPaneCurrentWidth<this.prevPaneCurrentWidth?this.prevPaneCurrentWidth=this.prevPaneCurrentWidth-(this.nextPaneCurrentWidth+this.prevPaneCurrentWidth-this.totalWidth):this.nextPaneCurrentWidth=this.nextPaneCurrentWidth-(this.nextPaneCurrentWidth+this.prevPaneCurrentWidth-this.totalWidth)),this.nextPaneCurrentWidth+this.prevPaneCurrentWidth<this.totalWidth){var a=this.totalWidth-(this.nextPaneCurrentWidth+this.prevPaneCurrentWidth);this.nextPaneCurrentWidth=this.nextPaneCurrentWidth+a}this.calculateCurrentDimensions(),this.addStaticPaneClass();for(var r=0,o=0;o<this.paneSettings.length;o++)""===this.paneSettings[o].size&&(r+=1);var l=r===this.allPanes.length;if(""==this.previousPane.style.flexBasis&&""==this.nextPane.style.flexBasis){var h=this.allPanes.length%this.allBars.length;this.prevPaneIndex==h?(this.nextPane.style.flexBasis=this.nextPaneCurrentWidth,sf.base.addClass([this.nextPane],i)):this.nextPaneIndex==h?(this.previousPane.style.flexBasis=this.prevPaneCurrentWidth,sf.base.addClass([this.previousPane],i)):(this.nextPane.style.flexBasis=this.nextPaneCurrentWidth,sf.base.addClass([this.nextPane],i))}else l?(this.previousPane.style.flexBasis=this.prevPaneCurrentWidth,sf.base.addClass([this.previousPane],i),this.nextPane.style.flexBasis=this.nextPaneCurrentWidth,sf.base.addClass([this.nextPane],i)):(""!==this.previousPane.style.flexBasis&&(this.previousPane.style.flexBasis=this.prevPaneCurrentWidth),""!==this.nextPane.style.flexBasis&&(this.nextPane.style.flexBasis=this.nextPaneCurrentWidth));var p=""!==this.previousPane.style.flexBasis&&""!==this.nextPane.style.flexBasis;this.allPanes.length>2||!p||this.updateSplitterSize();for(var c=0,u=0;u<this.allBars.length;u++){var f=this.allBars[u];c=""==this.allPanes[u].style.flexBasis?c:c+parseInt(this.allPanes[u].style.flexBasis),f.setAttribute("aria-valuenow",c.toString())}}},c.prototype.updateSplitterSize=function(e){for(var t=0,s=[],i=0,n=this.element.children,a=0;a<n.length;a++)t+=this.orientation===d?n[a].offsetWidth:n[a].offsetHeight;for(var r=0;r<this.allBars.length;r++){var o=getComputedStyle(this.allBars[r]);t+=this.orientation===d?parseInt(o.marginLeft,10)+parseInt(o.marginLeft,10):parseInt(o.marginTop,10)+parseInt(o.marginBottom,10)}var l=this.orientation===d?this.element.offsetWidth-(2*this.border+t):this.element.offsetHeight-(2*this.border+t);for(a=0;a<this.allPanes.length&&!sf.base.isNullOrUndefined(this.paneSettings[a]);a++)(""===this.allPanes[a].innerText?this.paneSettings[a].size&&""===this.allPanes[a].innerText:this.paneSettings[a].size||""===this.allPanes[a].innerText)||(s[i]=a,i++);var h=l/s.length;for(r=0;r<s.length;r++)""!==this.allPanes[s[r]].style.flexBasis&&(this.allPanes[s[r]].style.flexBasis=this.orientation===d?this.allPanes[s[r]].offsetWidth+h+p:this.allPanes[s[r]].offsetHeight+h+p);if(2===this.allPanes.length&&e){var c=this.allPanes.length,u=void 0,f=void 0,P=void 0,g=void 0;for(a=0;a<c&&!sf.base.isNullOrUndefined(this.paneSettings[a]);a++)if(!sf.base.isNullOrUndefined(this.paneSettings[a].min)&&(f=this.convertPixelToNumber(this.paneSettings[a].min.toString()),this.paneSettings[a].min.indexOf("%")>0&&(f=this.convertPercentageToPixel(this.paneSettings[a].min)),u=this.convertPixelToNumber(f.toString()),(this.orientation===d?this.allPanes[a].offsetWidth:this.allPanes[a].offsetHeight)<u)){0===a?(P=this.allPanes[a],g=this.allPanes[a+1]):(P=this.allPanes[a],g=this.allPanes[a-1]);var v=u-(this.orientation===d?this.allPanes[a].offsetWidth:this.allPanes[a].offsetHeight),x=P.style.flexBasis.indexOf("%")>-1,b=this.orientation===d?P.offsetWidth:P.offsetHeight;P.style.flexBasis=x?this.convertPixelToPercentage(b+v)+"%":b+v+"px";var y=this.orientation===d?g.offsetWidth:g.offsetHeight;g.style.flexBasis=g.style.flexBasis.indexOf("%")>-1?this.convertPixelToPercentage(y-v)+"%":y-v+"px"}}},c.prototype.calcDragPosition=function(e,t){var s;return(s=(s=(this.orientation===d?this.currentCoordinates.x-e:this.currentCoordinates.y-e)/t)>1?1:s<0?0:s)*t},c.prototype.getSeparatorPosition=function(e){this.updateCursorPosition(e,"current");var t=this.orientation===d?this.element.getBoundingClientRect().left+window.scrollX:this.element.getBoundingClientRect().top+window.scrollY,s=this.orientation===d?this.element.offsetWidth:this.element.offsetHeight;return this.calcDragPosition(t,s)},c.prototype.getBorder=function(){var e=this.orientation===d?(this.element.offsetWidth-this.element.clientWidth)/2:(this.element.offsetHeight-this.element.clientHeight)/2;this.border="chrome"!==sf.base.Browser.info.name?0:e},c.prototype.validateMinMaxValues=function(){this.prevPaneCurrentWidth=this.validateMinRange(this.prevPaneIndex,this.prevPaneCurrentWidth,this.previousPane),this.nextPaneCurrentWidth=this.validateMinRange(this.nextPaneIndex,this.nextPaneCurrentWidth,this.nextPane),this.prevPaneCurrentWidth=this.validateMaxRange(this.prevPaneIndex,this.prevPaneCurrentWidth,this.previousPane),this.nextPaneCurrentWidth=this.validateMaxRange(this.nextPaneIndex,this.nextPaneCurrentWidth,this.nextPane)},c.prototype.validateMinRange=function(e,t,s){var i,n,a=null,r=0;return sf.base.isNullOrUndefined(this.paneSettings[e])||sf.base.isNullOrUndefined(this.paneSettings[e].min)||(a=this.paneSettings[e].min.toString()),sf.base.isNullOrUndefined(a)||(a.indexOf("%")>0&&(a=this.convertPercentageToPixel(a).toString()),t<(i=this.convertPixelToNumber(a))&&(r=t-i<=0?0:t-i,this.totalWidth=this.totalWidth-r,this.totalPercent=this.convertPixelToPercentage(this.totalWidth),n=i)),sf.base.isNullOrUndefined(n)?t:n},c.prototype.validateMaxRange=function(e,t,s){var i,n,a=null;return sf.base.isNullOrUndefined(this.paneSettings[e])||sf.base.isNullOrUndefined(this.paneSettings[e].max)||(a=this.paneSettings[e].max.toString()),sf.base.isNullOrUndefined(a)||(a.indexOf("%")>0&&(a=this.convertPercentageToPixel(a).toString()),t>(i=this.convertPixelToNumber(a))&&(this.totalWidth=this.totalWidth-(t-i),this.totalPercent=this.convertPixelToPercentage(this.totalWidth),n=i)),sf.base.isNullOrUndefined(n)?t:n},c.prototype.calculateCurrentDimensions=function(){this.updatePrePaneInPercentage||this.updateNextPaneInPercentage?(this.prevPaneCurrentWidth=Math.round(Number(Math.round(10*this.convertPixelToPercentage(this.prevPaneCurrentWidth))/10)),this.nextPaneCurrentWidth=Math.round(Number(Math.round(10*this.convertPixelToPercentage(this.nextPaneCurrentWidth))/10)),0===this.prevPaneCurrentWidth&&(this.nextPaneCurrentWidth=this.totalPercent),0===this.nextPaneCurrentWidth&&(this.prevPaneCurrentWidth=this.totalPercent),this.prevPaneCurrentWidth+this.nextPaneCurrentWidth!==this.totalPercent?this.equatePaneWidths():(this.prevPaneCurrentWidth=this.prevPaneCurrentWidth+"%",this.nextPaneCurrentWidth=this.nextPaneCurrentWidth+"%"),this.prevPaneCurrentWidth=this.updatePrePaneInPercentage?this.prevPaneCurrentWidth:this.convertPercentageToPixel(this.prevPaneCurrentWidth)+"px",this.nextPaneCurrentWidth=this.updateNextPaneInPercentage?this.nextPaneCurrentWidth:this.convertPercentageToPixel(this.nextPaneCurrentWidth)+"px",this.updatePrePaneInPercentage=!1,this.updateNextPaneInPercentage=!1):(this.prevPaneCurrentWidth=this.prevPaneCurrentWidth+"px",this.nextPaneCurrentWidth=this.nextPaneCurrentWidth+"px")},c.prototype.equatePaneWidths=function(){var e;this.prevPaneCurrentWidth+this.nextPaneCurrentWidth>this.totalPercent&&(e=this.prevPaneCurrentWidth+this.nextPaneCurrentWidth-this.totalPercent,this.prevPaneCurrentWidth=this.prevPaneCurrentWidth-e/2+"%",this.nextPaneCurrentWidth=this.nextPaneCurrentWidth-e/2+"%"),this.prevPaneCurrentWidth+this.nextPaneCurrentWidth<this.totalPercent&&(e=this.totalPercent-(this.prevPaneCurrentWidth+this.nextPaneCurrentWidth),this.prevPaneCurrentWidth=this.prevPaneCurrentWidth+e/2+"%",this.nextPaneCurrentWidth=this.nextPaneCurrentWidth+e/2+"%")},c.prototype.validateDraggedPosition=function(e,t,s){var i=this.orientation===d?this.currentSeparator.offsetLeft:this.currentSeparator.offsetTop,n=i-t,a=s+i,r=this.getMinMax(this.prevPaneIndex,"min"),o=this.getMinMax(this.nextPaneIndex,"min"),l=this.getMinMax(this.prevPaneIndex,"max"),h=this.getMinMax(this.nextPaneIndex,"max"),p=e;return e>a-o?p=a-o:e<n+r&&(p=n+r),sf.base.isNullOrUndefined(l)?sf.base.isNullOrUndefined(h)||e<a-h&&(p=a-h):e>n+l&&(p=n+l),p},c.prototype.getMinMax=function(e,t){var s="min"===t?0:null,i=null;return"min"===t?sf.base.isNullOrUndefined(this.paneSettings[e])||sf.base.isNullOrUndefined(this.paneSettings[e].min)||(i=this.paneSettings[e].min):sf.base.isNullOrUndefined(this.paneSettings[e])||sf.base.isNullOrUndefined(this.paneSettings[e].max)||(i=this.paneSettings[e].max),this.paneSettings.length>0&&!sf.base.isNullOrUndefined(this.paneSettings[e])&&!sf.base.isNullOrUndefined(i)?(i.indexOf("%")>0&&(i=this.convertPercentageToPixel(i).toString()),this.convertPixelToNumber(i)):s},c.prototype.isValidSize=function(e){var t;return!sf.base.isNullOrUndefined(this.paneSettings[e])&&!sf.base.isNullOrUndefined(this.paneSettings[e].size)&&this.paneSettings[e].size.indexOf("%")>-1&&(t=!0),t},c.prototype.getPaneDimensions=function(e){this.previousPaneHeightWidth=""===this.previousPane.style.flexBasis||e?this.getPaneHeight(this.previousPane):this.previousPane.style.flexBasis,this.nextPaneHeightWidth=""===this.nextPane.style.flexBasis||e?this.getPaneHeight(this.nextPane):this.nextPane.style.flexBasis,this.isValidSize(this.prevPaneIndex)&&(this.previousPaneHeightWidth=this.convertPercentageToPixel(this.previousPaneHeightWidth).toString(),this.updatePrePaneInPercentage=!0),this.isValidSize(this.nextPaneIndex)&&(this.nextPaneHeightWidth=this.convertPercentageToPixel(this.nextPaneHeightWidth).toString(),this.updateNextPaneInPercentage=!0),this.prePaneDimenson=this.convertPixelToNumber(this.previousPaneHeightWidth.toString()),this.nextPaneDimension=this.convertPixelToNumber(this.nextPaneHeightWidth.toString())},c.prototype.getPaneHeight=function(e){return this.orientation===d?e.offsetWidth.toString():e.offsetHeight.toString()},c.prototype.isCursorMoved=function(e){return"mouse"===this.getEventType(e.type)||!sf.base.isNullOrUndefined(e.pointerType)&&"mouse"===this.getEventType(e.pointerType)?this.checkCoordinates(e.pageX,e.pageY):sf.base.Browser.info.name!==h?this.checkCoordinates(e.touches[0].pageX,e.touches[0].pageY):this.checkCoordinates(e.pageX,e.pageY)},c.prototype.checkCoordinates=function(e,t){var s=!0;return e!==this.previousCoordinates.x&&t!==this.previousCoordinates.y||(s=!1),s},c.prototype.onMouseUp=function(e){if(this.isPreventResizeAction)this.unwireResizeEvents();else{sf.base.removeClass([this.currentSeparator],"e-split-bar-active"),this.unwireResizeEvents();for(var t=0;t<this.element.querySelectorAll("iframe").length;t++)this.element.querySelectorAll("iframe")[t].style.pointerEvents="auto";this.getPaneDimensions(!1),this.updatePersistData(),this.onResizeStopEnabled&&this.dotNetRef.invokeMethodAsync("SplitterResizeInfo",{event:this.getMouseEvtArgs(e),index:[this.getPreviousPaneIndex(),this.getNextPaneIndex()],separator:this.getDomObject("separator",this.currentSeparator),paneSize:[this.prePaneDimenson,this.nextPaneDimension],resizingPaneSizes:[this.previousPane.style.flexBasis,this.nextPane.style.flexBasis]})}},c.prototype.isMouseEvent=function(e){var t;return("mouse"===this.getEventType(e.type)||!sf.base.isNullOrUndefined(e.pointerType)&&"mouse"===this.getEventType(e.pointerType))&&(t=!0),t},c.prototype.getEventType=function(e){return e.indexOf("mouse")>-1?"mouse":"touch"},c.prototype.updateCursorPosition=function(e,t){if(this.isMouseEvent(e))this.changeCoordinates({x:e.pageX,y:e.pageY},t);else{var s=sf.base.Browser.info.name!==h?e.touches[0]:e;this.changeCoordinates({x:s.pageX,y:s.pageY},t)}},c.prototype.changeCoordinates=function(e,t){"previous"===t?this.previousCoordinates=e:this.currentCoordinates=e},c.prototype.isResizable=function(){var e,t=this.getNextPaneIndex(),s=this.getPreviousPaneIndex();return(!sf.base.isNullOrUndefined(this.paneSettings[s])&&this.paneSettings[s].resizable&&!sf.base.isNullOrUndefined(this.paneSettings[t])&&this.paneSettings[t].resizable||sf.base.isNullOrUndefined(this.paneSettings[t]))&&(e=!0),e},c.prototype.clickHandler=function(e){if(e.detail<=1){if(!e.target.classList.contains("e-navigate-arrow")){var s=sf.base.selectAll(".e-splitter > ."+t+".e-split-bar-hover");s.length>0&&sf.base.removeClass(s,"e-split-bar-hover"),sf.base.addClass([e.target],"e-split-bar-hover")}this.updateIconClass();var i=e.target;(i.classList.contains(a)||i.classList.contains(r))&&this.collapseAction(e),(i.classList.contains("e-arrow-right")||i.classList.contains(o))&&this.expandAction(e);for(var n=0,l=this.element.children,h=0;h<l.length;h++)n+=this.orientation===d?l[h].offsetWidth:l[h].offsetHeight;n>this.element.offsetWidth&&this.updateSplitterSize()}},c.prototype.collapseAction=function(e){if(this.splitterDetails(e),this.collapseFlag)this.collapsePane(e);else{var t={cancel:!1,event:e,index:[this.getPreviousPaneIndex(),this.getNextPaneIndex()]};this.onCollapseEnabled?this.dotNetRef.invokeMethodAsync("OnCollapseEvent",-1,t):sf.base.isNullOrUndefined(e)?this.collapseMethodEvent(-1,t):this.onCollapseEvent(t)}},c.prototype.onCollapseEvent=function(e){var t=this.orientation===d?a:r,s={target:this.allBars[e.index[0]].querySelector("."+t)};this.collapsePane(s),this.collapsedEnabled&&(this.getPaneDimensions(!0),this.dotNetRef.invokeMethodAsync("CollapsedEvent",{event:this.getMouseEvtArgs(e),index:[this.getPreviousPaneIndex(),this.getNextPaneIndex()],paneSize:[this.prePaneDimenson,this.nextPaneDimension]}))},c.prototype.collapsePane=function(e){var t=this.element.querySelectorAll(".e-collapsed").length,s=this.nextPane.nextElementSibling,n=this.previousPane.classList.contains(i)&&!this.previousPane.classList.contains("e-collapsed")&&!this.nextPane.classList.contains("e-collapsed")&&s.classList.contains("e-pane")&&!s.classList.contains(i)&&!s.classList.contains("e-collapsed")&&!(t===this.allPanes.length-2)||this.nextPane.classList.contains("e-collapsed")&&!this.previousPane.classList.contains(i)&&this.nextPane.classList.contains(i),a=["e-collapsed","e-pane-hidden"];this.nextPane.classList.contains("e-collapsed")?(sf.base.removeClass([this.previousPane],"e-expanded"),sf.base.removeClass([this.nextPane],a),this.collapseFlag||this.updatePaneSettings(this.nextPaneIndex,!1)):(sf.base.removeClass([this.previousPane],"e-expanded"),sf.base.removeClass([this.nextPane],a),sf.base.addClass([this.nextPane],"e-expanded"),sf.base.addClass([this.previousPane],a),this.collapseFlag||this.updatePaneSettings(this.prevPaneIndex,!0)),this.updateIconsOnCollapse(e),this.previousPane.setAttribute("aria-expanded","false"),this.nextPane.setAttribute("aria-expanded","true"),this.updateFlexGrow(this.checkStaticPanes()),n&&(sf.base.removeClass([this.nextPane],"e-expanded"),this.nextPane.style.flexGrow="")},c.prototype.updateIconsOnCollapse=function(e){this.splitterProperty(),this.previousPane.classList.contains("e-collapsed")&&!this.nextPane.classList.contains("e-collapsed")?(sf.base.addClass([e.target],l),this.paneSettings[this.prevPaneIndex].collapsible&&sf.base.removeClass([sf.base.select("."+this.arrow,this.currentSeparator)],l),this.resizableModel(this.currentBarIndex,!1),!this.previousPane.classList.contains("e-collapsed")||this.nextPane.classList.contains("e-collapsed")||this.paneSettings[this.prevPaneIndex].collapsible||this.hideTargetBarIcon(this.prevBar,this.rightArrow),this.previousPane.previousElementSibling&&!this.previousPane.previousElementSibling.classList.contains("e-collapsed")&&(this.previousPane.classList.contains("e-collapsed")&&this.paneSettings[this.prevPaneIndex].collapsible?this.showTargetBarIcon(this.prevBar,this.leftArrow):this.paneSettings[this.prevPaneIndex].collapsible||this.hideTargetBarIcon(this.prevBar,this.leftArrow)),sf.base.isNullOrUndefined(this.prevBar)||(this.resizableModel(this.currentBarIndex-1,!1),this.hideTargetBarIcon(this.prevBar,this.arrow)),this.paneSettings[this.prevPaneIndex].collapsible||this.hideTargetBarIcon(this.currentSeparator,this.rightArrow)):this.splitInstance.prevPaneCollapsed||this.splitInstance.nextPaneExpanded||(this.paneSettings[this.currentBarIndex].resizable&&this.resizableModel(this.currentBarIndex,!0),!this.splitInstance.nextPaneNextEle.classList.contains("e-collapsed")&&this.paneSettings[this.currentBarIndex+1].resizable&&this.resizableModel(this.currentBarIndex+1,!0),this.paneSettings[this.currentBarIndex].collapsible||sf.base.addClass([e.target],l),this.previousPane&&0===this.prevPaneIndex&&this.paneSettings[this.prevPaneIndex].collapsible&&this.showTargetBarIcon(this.currentSeparator,this.leftArrow),this.nextPane&&this.nextPaneIndex===this.allPanes.length-1&&this.paneSettings[this.nextPaneIndex].collapsible&&this.showTargetBarIcon(this.allBars[this.nextPaneIndex-1],this.rightArrow),!this.previousPane.classList.contains("e-collapsed")&&this.paneSettings[this.nextPaneIndex].collapsible&&this.showTargetBarIcon(this.currentSeparator,this.rightArrow),sf.base.isNullOrUndefined(this.nextBar)||(this.nextPane.nextElementSibling&&this.nextPane.nextElementSibling.classList.contains("e-collapsed")&&this.paneSettings[this.nextPaneIndex+1].collapsible||!this.nextPane.nextElementSibling.classList.contains("e-collapsed")&&this.paneSettings[this.nextPaneIndex].collapsible?this.showTargetBarIcon(this.nextBar,this.leftArrow):!this.paneSettings[this.splitInstance.nextPaneIndex+1].collapsible&&this.paneSettings[this.currentBarIndex]&&this.hideTargetBarIcon(this.nextBar,this.arrow)),this.nextPaneIndex===this.allPanes.length-1||!this.nextPane.nextElementSibling||this.nextPane.classList.contains("e-collapsed")||this.nextPane.nextElementSibling.classList.contains("e-collapsed")||this.paneSettings[this.nextPaneIndex+1].collapsible||this.hideTargetBarIcon(this.nextBar,this.rightArrow))},c.prototype.expandAction=function(e){if(this.expandingFlag){this.splitterDetails(e);var t=[this.getPreviousPaneIndex(),this.getNextPaneIndex()];this.expandFlag?this.onExpandEnabled?this.dotNetRef.invokeMethodAsync("OnExpandEvent",{cancel:!1,index:t,event:this.getMouseEvtArgs(e)}):this.onExpandEvent({event:e,index:t}):this.expandPane(e)}},c.prototype.onExpandEvent=function(e){var t=this.orientation===d?"e-arrow-right":o,s={target:this.allBars[e.index[0]].querySelector("."+t)};this.expandPane(s),this.expandedEnabled&&(this.getPaneDimensions(!0),this.dotNetRef.invokeMethodAsync("ExpandedEvent",{event:this.getMouseEvtArgs(e.event),index:[this.getPreviousPaneIndex(),this.getNextPaneIndex()],paneSize:[this.prePaneDimenson,this.nextPaneDimension]}))},c.prototype.getSeparatorIndex=function(e){var t=this.orientation===d?"e-split-bar-horizontal":"e-split-bar-vertical";this.allBars.length<1&&(this.allBars=sf.base.selectAll("."+t,this.element));var s=[].slice.call(this.allBars);return(s=this.enableReversePanes?s.reverse():s).indexOf(e)},c.prototype.updateBars=function(e){this.prevBar=this.allBars[e-1],this.nextBar=this.allBars[e+1]},c.prototype.splitterDetails=function(e){this.element.classList.contains("e-splitter-horizontal")?(this.orientation=d,this.arrow=e.target.classList.contains(a)?"e-arrow-right":a):(this.orientation="Vertical",this.arrow=e.target.classList.contains(r)?o:r),this.updateCurrentSeparator(e.target),this.currentIndex(e),this.updateBars(this.currentBarIndex),this.getPaneDetails()},c.prototype.currentIndex=function(e){this.currentBarIndex=this.getOrderIndex(parseInt(e.target.parentElement.style.order,10),"splitbar")},c.prototype.expandPane=function(e){var t=this.element.querySelectorAll(".e-collapsed").length,s=!this.previousPane.classList.contains("e-collapsed")&&this.previousPane.classList.contains(i)&&!this.nextPane.classList.contains("e-collapsed")&&!this.nextPane.classList.contains("e-expanded")&&this.nextPane.nextElementSibling.classList.contains("e-pane")&&!this.nextPane.nextElementSibling.classList.contains(i)&&!(t===this.allPanes.length-2),n=["e-collapsed","e-pane-hidden"];this.previousPane.classList.contains("e-collapsed")?(sf.base.removeClass([this.previousPane],n),sf.base.removeClass([this.nextPane],"e-expanded"),this.expandFlag&&this.updatePaneSettings(this.prevPaneIndex,!1)):(sf.base.removeClass([this.nextPane],"e-expanded"),sf.base.removeClass([this.previousPane],n),sf.base.addClass([this.previousPane],"e-expanded"),sf.base.addClass([this.nextPane],n),this.expandFlag&&this.updatePaneSettings(this.nextPaneIndex,!0)),this.updateIconsOnExpand(e),this.previousPane.setAttribute("aria-expanded","true"),this.nextPane.setAttribute("aria-expanded","false"),this.updateFlexGrow(this.checkStaticPanes()),s&&(sf.base.removeClass([this.previousPane],"e-expanded"),this.previousPane.style.flexGrow=""),this.expandingFlag=!0},c.prototype.checkStaticPanes=function(){for(var e=!0,t=0;t<this.allPanes.length;t++)!this.allPanes[t].classList.contains("e-collapsed")&&e&&(e=!!this.allPanes[t].classList.contains(i));return e},c.prototype.splitterProperty=function(){this.splitInstance={currentBarIndex:this.currentBarIndex,nextPaneCollapsible:this.nextPane.classList.contains("e-collapsible"),prevPaneCollapsible:this.previousPane.classList.contains("e-collapsible"),prevPaneExpanded:this.previousPane.classList.contains("e-expanded"),nextPaneExpanded:this.nextPane.classList.contains("e-expanded"),nextPaneCollapsed:this.nextPane.classList.contains("e-collapsed"),prevPaneCollapsed:this.previousPane.classList.contains("e-collapsed"),nextPaneIndex:this.getNextPaneIndex(),prevPaneIndex:this.getPreviousPaneIndex(),nextPaneNextEle:this.nextPane.nextElementSibling,prevPanePreEle:this.previousPane.previousElementSibling}},c.prototype.updateIconsOnExpand=function(e){this.splitterProperty(),sf.base.addClass([e.target],l),this.splitInstance.prevPaneExpanded||this.splitInstance.nextPaneCollapsed?this.splitInstance.prevPaneExpanded&&this.splitInstance.nextPaneCollapsed&&(this.resizableModel(this.currentBarIndex,!1),this.resizableModel(this.currentBarIndex+1,!1),this.paneSettings[this.nextPaneIndex].collapsible&&sf.base.removeClass([sf.base.select("."+this.arrow,this.currentSeparator)],l),sf.base.isNullOrUndefined(this.nextBar)||this.hideTargetBarIcon(this.nextBar,this.arrow),this.nextPane&&this.nextPaneIndex===this.allPanes.length-1&&!this.paneSettings[this.nextPaneIndex].collapsible&&this.splitInstance.nextPaneCollapsed&&this.hideTargetBarIcon(this.currentSeparator,this.arrow),this.nextPaneIndex!==this.allPanes.length-1&&this.nextPane.nextElementSibling&&this.nextPane.classList.contains("e-collapsed")&&!this.nextPane.nextElementSibling.classList.contains("e-collapsed")&&this.paneSettings[this.nextPaneIndex].collapsible&&this.showTargetBarIcon(this.nextBar,this.rightArrow)):(this.paneSettings[this.prevPaneIndex].collapsible&&sf.base.removeClass([sf.base.select("."+this.arrow,this.currentSeparator)],l),this.paneSettings[this.nextPaneIndex].collapsible&&sf.base.removeClass([e.target],l),this.paneSettings[this.currentBarIndex].resizable&&this.resizableModel(this.currentBarIndex,!0),sf.base.isNullOrUndefined(this.prevBar)||this.splitInstance.prevPanePreEle.classList.contains("e-collapsed")?(this.previousPane.previousElementSibling&&this.paneSettings[this.prevPaneIndex].collapsible&&this.previousPane.previousElementSibling.classList.contains("e-collapsed")&&this.paneSettings[this.prevPaneIndex-1].collapsible&&this.showTargetBarIcon(this.prevBar,this.rightArrow),this.paneSettings[this.currentBarIndex+1].collapsible||this.hideTargetBarIcon(this.currentSeparator,this.rightArrow)):(this.paneSettings[this.currentBarIndex-1].resizable&&this.resizableModel(this.currentBarIndex-1,!0),this.paneSettings[this.prevPaneIndex].collapsible&&this.showTargetBarIcon(this.prevBar,this.rightArrow),this.paneSettings[this.currentBarIndex-1].collapsible||this.hideTargetBarIcon(this.prevBar,this.arrow),this.paneSettings[this.currentBarIndex].collapsible&&!this.paneSettings[this.currentBarIndex+1].collapsible&&this.hideTargetBarIcon(this.currentSeparator,this.rightArrow)))},c.prototype.showResizer=function(e){!sf.base.isNullOrUndefined(this.previousPane)&&this.previousPane.classList.contains("e-resizable")&&!sf.base.isNullOrUndefined(this.nextPane)&&this.nextPane.classList.contains("e-resizable")&&sf.base.removeClass([sf.base.select("."+n,e)],"e-hide-handler")},c.prototype.resizableModel=function(e,t){var s,i=e;s=e===this.allBars.length?e-1:e,sf.base.EventHandler.remove(this.allBars[s],"mousedown",this.onMouseDown),t?(sf.base.EventHandler.add(this.allBars[s],"mousedown",this.onMouseDown,this),this.isResizable()&&(this.showResizer(this.allBars[s]),sf.base.removeClass([sf.base.select("."+n,this.allBars[s])],"e-hide-handler"),sf.base.addClass([this.allBars[s]],"e-resizable-split-bar"),e===this.allBars.length?sf.base.addClass([this.allPanes[e]],"e-resizable"):sf.base.addClass([this.allPanes[s]],"e-resizable"),this.updateResizablePanes(i))):(this.updateResizablePanes(i),sf.base.addClass([sf.base.select("."+n,this.allBars[s])],"e-hide-handler"),sf.base.removeClass([this.allBars[s]],"e-resizable-split-bar"),e===this.allBars.length?sf.base.removeClass([this.allPanes[e]],"e-resizable"):sf.base.removeClass([this.allPanes[s]],"e-resizable"))},c.prototype.hideTargetBarIcon=function(e,t){sf.base.addClass([sf.base.select("."+t,e)],l)},c.prototype.showTargetBarIcon=function(e,t){sf.base.removeClass([sf.base.select("."+t,e)],l)},c.prototype.updateFlexGrow=function(e){for(var t=this.allPanes,s=0;s<t.length;s++)t[s].style.flexGrow=t[s].classList.contains("e-expanded")?"1":t[s].classList.contains("e-collapsed")?"0":"",e&&!this.nextPane.classList.contains("e-collapsed")&&(this.nextPane.style.flexGrow="1")},c.prototype.updatePaneSettings=function(e,t){this.isToggleInvoke||(this.paneSettings[e].collapsed=t,this.updatePersistData(),this.dotNetRef.invokeMethodAsync("UpdateCollapsed",e,t))},c.prototype.addMouseActions=function(e){var t,s,i=this;e.addEventListener("mouseenter",(function(){t=setTimeout((function(){sf.base.addClass([e],["e-split-bar-hover"])}),i.iconsDelay)})),e.addEventListener("mouseleave",(function(){clearTimeout(t),sf.base.removeClass([e],["e-split-bar-hover"])})),e.addEventListener("mouseout",(function(){clearTimeout(s)})),e.addEventListener("mouseover",(function(){s=setTimeout((function(){sf.base.addClass([e],["e-split-bar-hover"])}),i.iconsDelay)}))},c.prototype.updateIconClass=function(){this.orientation===d?(this.leftArrow=a,this.rightArrow="e-arrow-right"):(this.leftArrow=r,this.rightArrow=o)},c.prototype.isCollapsed=function(e){if(!(!sf.base.isNullOrUndefined(e)&&this.paneSettings[e].collapsed&&this.allPanes.length>0&&!sf.base.isNullOrUndefined(this.allPanes[e])&&this.allPanes[e].classList.contains("e-collapsed"))){if(this.expandFlag=!1,sf.base.isNullOrUndefined(e)){for(var t=0;t<this.allPanes.length;t++)!sf.base.isNullOrUndefined(this.paneSettings[t])&&this.paneSettings[t].collapsed&&this.updateIsCollapsed(t,this.targetArrows().collapseArrow,this.targetArrows().lastBarArrow);for(t=this.allPanes.length-1;t>=0;t--)if(!sf.base.isNullOrUndefined(this.paneSettings[t])&&this.paneSettings[t].collapsed&&!this.allPanes[t].classList.contains("e-collapsed")){var s=this.orientation===d?"e-arrow-right":o;if(0!==t)this.collapseArrow(t-1,s).click();if(!this.nextPane.classList.contains("e-collapsed"))this.collapseArrow(t-1,s).click()}}else{this.collapseFlag=!0;var i=e===this.allBars.length,n=i?e-1:e,a={target:!i&&this.allPanes[e+1].classList.contains("e-collapsed")&&0!==e?this.collapseArrow(n-1,this.targetArrows().lastBarArrow):i?this.collapseArrow(n,this.targetArrows().lastBarArrow):this.collapseArrow(n,this.targetArrows().collapseArrow)};this.splitterDetails(a);var r={cancel:!1,index:[this.getPreviousPaneIndex(),this.getNextPaneIndex()]};this.onCollapseEnabled?this.dotNetRef.invokeMethodAsync("OnCollapseEvent",e,r):this.collapseMethodEvent(e,r)}this.expandFlag=!0}},c.prototype.collapseMethodEvent=function(e,t){this.expandFlag=!1;var s=[];s[0]=e;for(var i=1,n=0;n<this.allPanes.length;n++)this.allPanes[n].classList.contains("e-collapsed")&&(s[i]=n,i++);s=s.sort(),this.updateIsCollapsed(e,this.targetArrows().collapseArrow,this.targetArrows().lastBarArrow);for(n=0;n<s.length;n++)this.allPanes[s[n]].classList.contains("e-collapsed")||this.updateIsCollapsed(s[n],this.targetArrows().collapseArrow,this.targetArrows().lastBarArrow);for(n=s.length;n>0;n--)if(!this.allPanes[s[n-1]].classList.contains("e-collapsed")){var a=this.targetArrows();this.updateIsCollapsed(s[n-1],a.collapseArrow,a.lastBarArrow)}this.collapsedEnabled&&this.dotNetRef.invokeMethodAsync("CollapsedEvent",{event:this.getMouseEvtArgs(t),index:[this.getPreviousPaneIndex(),this.getNextPaneIndex()]}),this.collapseFlag=!1,this.expandFlag=!0},c.prototype.toggle=function(e,t){this.isToggleInvoke=!0;for(var s=0;s<this.paneSettings.length;s++)e.indexOf(s)>-1&&this.paneSettings[s].collapsed&&(this.collapsedOnchange(s),this.paneSettings[s].collapsed=!1);for(s=0;s<this.paneSettings.length;s++)t.indexOf(s)>-1&&!this.paneSettings[s].collapsed&&(this.isCollapsed(s),this.paneSettings[s].collapsed=!0);this.isToggleInvoke=!1,this.updatePersistData(),this.dotNetRef.invokeMethodAsync("UpdateSettings",this.paneSettings)},c.prototype.expand=function(e){this.collapsedOnchange(e),this.updatePaneSettings(e,!1)},c.prototype.collapse=function(e){this.isCollapsed(e),this.updatePaneSettings(e,!0)},c.prototype.collapsedOnchange=function(e){sf.base.isNullOrUndefined(this.paneSettings[e])||sf.base.isNullOrUndefined(this.paneSettings[e].collapsed)||!this.allPanes[e].classList.contains("e-collapsed")||this.updateIsCollapsed(e,this.targetArrows().lastBarArrow,this.targetArrows().collapseArrow)},c.prototype.collapsibleModelUpdate=function(e){var t,s,i;i=e===this.allBars.length?e-1:e,t=this.orientation===d?this.checkArrow(i,a):this.checkArrow(i,r),s=this.orientation===d?this.checkArrow(i,"e-arrow-right"):this.checkArrow(i,o),this.paneCollapsible(this.allPanes[e],e),this.updateCollapseIcons(i,s,t)},c.prototype.updateCollapseIcons=function(e,t,s){sf.base.isNullOrUndefined(this.paneSettings[e])||sf.base.isNullOrUndefined(this.paneSettings[e].collapsible)||(this.paneSettings[e].collapsible?sf.base.removeClass([s],[l]):sf.base.addClass([s],[l]),sf.base.isNullOrUndefined(this.paneSettings[e+1])||(this.paneSettings[e+1].collapsible?sf.base.removeClass([t],[l]):sf.base.addClass([t],[l])))},c.prototype.checkArrow=function(e,t){return this.allBars[e].querySelector(".e-navigate-arrow."+t)},c.prototype.paneCollapsible=function(e,t){this.paneSettings[t].collapsible?sf.base.addClass([e],"e-collapsible"):sf.base.removeClass([e],"e-collapsible")},c.prototype.propertyChanged=function(t,s){var n=e({},this.paneSettings);if(sf.base.extend(this,this,t),this.updateClass(),s)for(var a=0;a<=Object.keys(s).length;a++){var r=parseInt(Object.keys(s)[a],10);if(Object(s)[r])for(var o=0;o<Object(s)[r].length;o++)switch(Object(s)[r][o]){case"Visible":case"Resizable":this.isVisible=!0,this.resizableModel(r,this.paneSettings[r].resizable);break;case"Collapsible":this.collapsibleModelUpdate(r);break;case"Collapsed":n[r].collapsed!==t.paneSettings[r].collapsed&&(this.paneSettings[r].collapsed?this.isCollapsed(r):this.collapsedOnchange(r));break;case"Size":for(var l=0;l<this.allPanes.length;l++)n[l].size!==t.paneSettings[l].size?null==n[l].size?(this.allPanes[l].style.flexBasis=null,this.allPanes[l].classList.remove(i)):(this.paneSettings[l].size=t.paneSettings[l].size,this.allPanes[l].style.flexBasis=t.paneSettings[l].size):this.allPanes[l].classList.contains(i)||(this.allPanes[l].style.flexBasis=null)}}this.enablePersistence&&this.updatePersistData()},c.prototype.targetArrows=function(){return this.splitterProperty(),{collapseArrow:this.orientation===d?a:r,lastBarArrow:"Vertical"===this.orientation?o:"e-arrow-right"}},c.prototype.collapseArrow=function(e,t){return sf.base.selectAll("."+t,this.allBars[e])[0]},c.prototype.updateIsCollapsed=function(e,t,s){if(!sf.base.isNullOrUndefined(e)){var i=e===this.allBars.length,n=i?e-1:e;(!i&&this.allPanes[e+1].classList.contains("e-collapsed")&&0!==e?this.collapseArrow(n-1,s):i?this.collapseArrow(n,s):this.collapseArrow(n,t)).click()}},c.prototype.updatePersistData=function(){for(var e=[],t=this.element.querySelectorAll(".e-pane"),s=0;s<this.paneSettings.length;s++)e.push(JSON.parse(JSON.stringify(this.paneSettings[s]))),delete e[s].collapsible,delete e[s].content,delete e[s].cssClass,delete e[s].resizable,e[s].size=t[s].style.flexBasis;this.enablePersistence&&window.localStorage.setItem(this.id,JSON.stringify(e))},c.prototype.updatePanClass=function(e){this.allPanes[e].classList.contains("e-pane-hidden")&&this.allPanes[e].classList.remove("e-pane-hidden"),this.allPanes[e].classList.contains("e-collapsed")&&this.allPanes[e].classList.remove("e-collapsed")},c.prototype.destroy=function(){for(sf.base.EventHandler.remove(document,"touchstart click",this.onDocumentClick),sf.base.EventHandler.remove(this.element,"keydown",this.onMove),this.updatePersistData(),this.paneSettings=[],this.allBars=[],this.allPanes=[];this.element.attributes.length>0;)this.element.removeAttribute(this.element.attributes[0].name)},c}();return{initialize:function(e){if(e.dataId&&(new c(e),window.sfBlazor.instances[e.dataId].initialize()),e.enablePersistence)for(var t=e.element.querySelectorAll(".e-pane"),s=0;s<t.length;s++)t[s].style.flexBasis=e.paneSettings[s].size},collapse:function(e,t){e&&window.sfBlazor.instances[e].collapse(t)},expand:function(e,t){e&&window.sfBlazor.instances[e].expand(t)},toggle:function(e,t,s){e&&window.sfBlazor.instances[e].toggle(t,s)},resizeEvent:function(e,t){e&&window.sfBlazor.instances[e].resizeEvent(t)},onCollapseEvent:function(e,t){e&&window.sfBlazor.instances[e].onCollapseEvent(t)},collapseMethodEvent:function(e,t,s){e&&window.sfBlazor.instances[e].collapseMethodEvent(s,t)},onExpandEvent:function(e,t){e&&window.sfBlazor.instances[e].onExpandEvent(t)},destroy:function(e){e&&window.sfBlazor.instances[e].destroy()},setReversePane:function(e){e&&window.sfBlazor.instances[e].setReversePane()},updateSeparator:function(e){e.dataId&&(window.sfBlazor.instances[e.dataId].updateSeparator(),e.enableReversePanes&&window.sfBlazor.instances[e.dataId].setReversePane())},propertyChanged:function(e,t){e.dataId&&window.sfBlazor.instances[e.dataId].propertyChanged(e,t)},updatePanClass:function(e,t){e&&window.sfBlazor.instances[e].updatePanClass(t)}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfsplitter');})})();