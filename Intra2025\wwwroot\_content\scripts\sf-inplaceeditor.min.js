/*!*  filename: sf-inplaceeditor.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[32],{"./bundles/sf-inplaceeditor.js":function(e,t,n){"use strict";n.r(t);n("./modules/sf-inplaceeditor.js")},"./modules/sf-inplaceeditor.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.InPlaceEditor=function(){"use strict";var e="e-inplaceeditor",t="e-editable-open",n=function(){function n(e){this.submitOnEnter=!0,this.clearComponents=["AutoComplete","Mask","Text"],window.sfBlazor=window.sfBlazor,this.updateContext(e),window.sfBlazor&&(sf.base.isNullOrUndefined(window.sfBlazor.instances)&&(window.sfBlazor.instances=[]),window.sfBlazor.instances[this.dataId]=this,this.initialize())}return n.prototype.updateContext=function(e){sf.base.extend(this,this,e)},n.prototype.initialize=function(){this.onResizeScrollHandler=this.scrollResizeHandler.bind(this),this.wireEvents(),this.id=this.element.id,this.dotNetRef.invokeMethodAsync("Created",null)},n.prototype.wireEvents=function(){sf.base.EventHandler.add(this.element,"keydown",this.valueKeyDownHandler,this),sf.base.EventHandler.add(document,"scroll",this.scrollResizeHandler,this),window.addEventListener("resize",this.onResizeScrollHandler),Array.prototype.indexOf.call(this.clearComponents,this.type)>-1&&sf.base.EventHandler.add(this.element,"mousedown",this.mouseDownHandler,this)},n.prototype.scrollResizeHandler=function(){"Popup"!==this.mode||sf.base.Browser.isDevice||this.dotNetRef.invokeMethodAsync("CancelAction",null)},n.prototype.valueKeyDownHandler=function(n){"Tab"===n.code&&!0===n.shiftKey&&"BUTTON"!==n.target.tagName&&(this.element.setAttribute("tabindex","-1"),"Submit"===this.actionOnBlur?this.dotNetRef.invokeMethodAsync("SaveAction",!0):"Cancel"===this.actionOnBlur&&this.dotNetRef.invokeMethodAsync("CancelAction",!0)),"Enter"!==n.code||!n.target.classList.contains(e)||this.element.querySelector(".e-editable-value-container").classList.contains(t)||this.element.classList.contains("e-disable")||(n.preventDefault(),this.dotNetRef.invokeMethodAsync("RenderEditor",null))},n.prototype.mouseDownHandler=function(e){e.target.classList.contains("e-clear-icon")&&(this.isClearTarget=!0)},n.prototype.openEditor=function(e){if(this.updateContext(e),"Popup"===this.mode&&(this.popupContent=document.querySelector("#"+this.popupElement+"_content")),"Ignore"!==this.actionOnBlur&&sf.base.EventHandler.add(document,"mousedown",this.docClickHandler,this),this.submitOnEnter){var t="Popup"===this.mode?this.popupContent:this.element;sf.base.EventHandler.add(t,"keydown",this.enterKeyDownHandler,this)}this.dotNetRef.invokeMethodAsync("FocusEditor")},n.prototype.enterKeyDownHandler=function(n){sf.base.closest(n.target,".e-editable-component .e-richtexteditor")||(13===n.keyCode&&13===n.which&&sf.base.closest(n.target,".e-editable-component")?this.dotNetRef.invokeMethodAsync("SaveAction",!1):27===n.keyCode&&27===n.which?(sf.base.isNullOrUndefined(this.element)||sf.base.isNullOrUndefined(this.element.querySelector("input"))||"Inline"!==this.mode?sf.base.isNullOrUndefined(this.popupContent)||sf.base.isNullOrUndefined(this.popupContent.querySelector("input"))||"Popup"!==this.mode||this.popupContent.querySelector("input").blur():this.element.querySelector("input").blur(),this.dotNetRef.invokeMethodAsync("CancelAction",null)):"Enter"!==n.code||!n.target.classList.contains(e)||sf.base.isNullOrUndefined(this.element.querySelector(".e-editable-value-container"))||this.element.querySelector(".e-editable-value-container").classList.contains(t)||this.element.classList.contains("e-disable")||(n.preventDefault(),this.dotNetRef.invokeMethodAsync("RenderEditor",null)))},n.prototype.removeEditor=function(e){this.updateContext(e),this.enablePersistence&&window.localStorage.setItem(this.id,this.value),this.unWireEvents(),sf.base.EventHandler.remove(document,"mousedown",this.docClickHandler)},n.prototype.Destroy=function(e){var t=this;for(this.updateContext(e),this.unWireEvents(),sf.base.EventHandler.remove(document,"mousedown",this.docClickHandler),this.enablePersistence&&window.localStorage.setItem(this.id,this.value),"Popup"===this.mode&&this.destroyPopup(),["e-disable","e-rtl"].forEach((function(e){sf.base.removeClass([t.element],[e])}));this.element.firstElementChild;)this.element.removeChild(this.element.firstElementChild)},n.prototype.destroyPopup=function(){var e=document.querySelector("#"+this.popupElement);if(e){for(;e.attributes.length>0;)e.removeAttribute(e.attributes[0].name);for(var t=e.children,n=t.length-1;n>=0;n--)sf.base.detach(t[n])}},n.prototype.unWireEvents=function(){sf.base.EventHandler.remove(document,"scroll",this.scrollResizeHandler),window.removeEventListener("resize",this.onResizeScrollHandler),sf.base.EventHandler.remove(this.element,"keydown",this.valueKeyDownHandler),Array.prototype.indexOf.call(this.clearComponents,this.type)>-1&&sf.base.EventHandler.remove(this.element,"mousedown",this.mouseDownHandler);var e="Popup"===this.mode?this.popupContent:this.element;sf.base.EventHandler.remove(e,"keydown",this.enterKeyDownHandler)},n.prototype.validate=function(){return"Inline"===this.mode?!!this.element.querySelector(".validation-message"):!(!this.componentParent||!this.componentParent.querySelector(".validation-message"))},n.prototype.docClickHandler=function(t){var n=this,s=sf.base.closest(t.target,"."+e),o=sf.base.closest(t.target,".e-inplaceeditor-tip"),i=sf.base.closest(t.target,".e-editable-elements"),l=sf.base.closest(t.target,".e-rte-elements");if(this.isClearTarget||!sf.base.isNullOrUndefined(s)&&s.isEqualNode(this.element)||!sf.base.isNullOrUndefined(o)&&this.popupContent&&o.id.indexOf("tooltip")>-1||!sf.base.isNullOrUndefined(i)||!sf.base.isNullOrUndefined(l)||t.target.classList.contains("e-chips-close"))this.isClearTarget=!1;else{var r="Inline"===this.mode?this.element.querySelector(".e-editable-component"):this.element,a="Inline"===this.mode?this.element.querySelector(".e-editable-action-buttons"):this.componentParent;r.contains(t.target)||a&&a.contains(t.target)||sf.base.closest(t.target,".e-dropdown-popup.e-control.e-rte-elements")||sf.base.closest(t.target,".e-dropdown-popup.e-control.e-colorpicker-popup")||this.popupContent&&this.popupContent.contains(t.target)||setTimeout((function(){"Submit"!==n.actionOnBlur||n.element.querySelector(".validation-message")?"Cancel"===n.actionOnBlur&&n.dotNetRef.invokeMethodAsync("CancelAction",null):n.dotNetRef.invokeMethodAsync("SaveAction",!1)}))}},n}();return{initialize:function(e){e.dataId&&new n(e)},openEditor:function(e,t){e&&window.sfBlazor.instances[e].openEditor(t)},closeEditor:function(e,t,n){e&&(window.sfBlazor.instances[e].removeEditor(t),n||window.sfBlazor.instances[e].element.focus(),window.sfBlazor.instances[e].element.setAttribute("tabindex","0"))},destroy:function(e,t){e&&window.sfBlazor.instances[e].Destroy(t)},propertyChanged:function(e,t){e&&window.sfBlazor.instances[e].updateContext(t)},validate:function(e){return e&&window.sfBlazor.instances[e].validate()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfinplaceeditor');})})();