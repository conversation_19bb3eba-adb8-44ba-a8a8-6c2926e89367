﻿<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">支付作業登打系統</a>
        <button title="Navigation menu" class="navbar-toggler" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass nav-scrollable" @onclick="ToggleNavMenu">
    <nav class="flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="oi oi-home" aria-hidden="true"></span> 首頁
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="adminlist">
                <span class="oi oi-list-rich" aria-hidden="true"></span> 管理者清單
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="remitmt">
                <span class="oi oi-plus" aria-hidden="true"></span> 匯款彙整作業
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="remitedlist">
                <span class="oi oi-list" aria-hidden="true"></span> 匯款記錄查詢
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="payeemt">
                <span class="oi oi-person" aria-hidden="true"></span> 收款人資料維護
            </NavLink>
        </div>
    </nav>
</div>

@code {
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }
}

