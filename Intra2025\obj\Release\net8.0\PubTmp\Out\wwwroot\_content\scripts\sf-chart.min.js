/*!*  filename: sf-chart.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[13],{"./bundles/sf-chart.js":function(e,t,i){"use strict";i.r(t);i("./modules/sf-chart.js")},"./modules/sf-chart.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Chart=function(){"use strict";var e,t=(e=function(t,i){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])})(t,i)},function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}),i=function(){function e(e,t,i,o,s,r,d,c){void 0===s&&(s=!1),void 0===r&&(r=!1),window.sfBlazor=window.sfBlazor,this.mouseY=0,this.mouseX=0,this.isTouch=!1,this.eventInterval=80,this.mouseMoveRef=null,this.mouseMoveThreshold=null,this.chartOnMouseClickRef=null,this.chartRightClickRef=null,this.mouseLeaveRef=null,this.chartKeyDownRef=null,this.chartKeyUpRef=null,this.chartMouseWheelRef=null,this.chartOnMouseDownRef=null,this.mouseEndRef=null,this.chartMouseWheelThreshold=null,this.domMouseMoveRef=null,this.domMouseMoveThreshold=null,this.scrollbarMouseMoveThreshold=null,this.scrollbarMouseWheelThreshold=null,this.domMouseUpRef=null,this.longPressBound=null,this.touchObject=null,this.pinchZoomingEnable=!1,this.crosshairEnable=!1,this.toggleVisibility=!0,this.enableHighlight=!0,this.highlightMode="None",this.selectionMode="None",this.rectSeries=!0,this.isSeriesMode=!1,this.seriesTypes=[],this.redraw=!1,this.selectedDataIndexes=[],this.highlightDataIndexes=[],this.previousSelectedEle=[],this.highlightColor="",this.highlightPattern="None",this.selectionPattern="None",this.legendModule=[],this.allowMultiSelection=!1,this.drawType=[],this.previousHighlightedIndex=-1,this.tooltipBase=new n,this.markerExplodeBase=new a,this.crosshairBase=new l,this.userInteractionBase=new h,this.zoomBase=new p,this.zoomToolkitBase=new m,this.isChartPanning=!1,this.axes=[],this.scrollDownRef=null,this.wheelEventEndTimeout=null,this.isWheelScrolling=!1,this.isChartZoom=!1,this.isDragSelection=!1,this.isDisposed=!1,this.pinchStyle="opacity: 0; position: absolute; display: block; width: 100px; height: 100px; background: transparent; border: 2px solid blue;",this.pinchtarget=null,this.documentKeyHandler=function(e){e.altKey&&74===e.keyCode&&!sf.base.isNullOrUndefined(this.element)&&this.element.focus()},this.id=t,this.element=i,this.dotnetref=o,this.isZooming=s,this.isScrollbar=r,this.options=d,this.currentLegendIndex=0,this.currentPointIndex=0,this.currentSeriesIndex=0,this.currentAnnotationIndex=0,this.seriesPathElement=null,this.previousTargetId="",this.isZoomed=!1,this.selectionHighlightOptions(c),this.selectedDataIndexes=c.selectedDataIndexes||[],this.unSelected=t+"_ej2_deselected",this.dataId=e,window.sfBlazor.setCompInstance(this),this.isDisposed=!1,this.getTooltipData(),this.userInteractionBase.svgRenderer=new sf.svgbase.SvgRenderer(this.element.id),this.removeTooltipData()}return e.prototype.render=function(){this.unWireEvents(this.id,this.dotnetref),this.wireEvents(this.id,this.dotnetref)},e.prototype.destroy=function(){this.isDisposed=!0,this.unWireEvents(this.id,this.dotnetref),this.dotnetref.invokeMethodAsync("DisposeDotNetReference")},e.prototype.selectionHighlightOptions=function(e){this.enableHighlight=e.enableHighlight,this.pinchZoomingEnable=e.pinchZoomingEnable,this.toggleVisibility=e.toggleVisibility,this.highlightMode=e.highlightMode,this.selectionMode=e.selectionMode,this.seriesTypes=e.seriesTypes,this.highlightColor=e.highlightColor,this.highlightPattern=e.highlightPattern,this.selectionPattern=e.selectionPattern,this.allowMultiSelection=e.allowMultiSelection,this.drawType=e.drawType,this.oldMode=this.currentMode},e.prototype.getTooltipData=function(e,t,i,o){var s=document.getElementById(this.element.id+"_tooltip_data");if(s){this.userInteractionBase.chartData=s.getAttributeNames().map((function(e){return s.getAttribute(e)})),this.userInteractionBase.chartData.shift();var r=this.userInteractionBase.chartData.indexOf("display: block;");if(r>-1&&this.userInteractionBase.chartData.splice(r,1),this.userInteractionBase.isStockChart)for(var n=this.userInteractionBase.chartData.indexOf("");n>-1;)this.userInteractionBase.chartData.splice(n,1),n=this.userInteractionBase.chartData.indexOf("");sf.base.isNullOrUndefined(this.userInteractionBase.visibleSeries)||this.userInteractionBase.visibleSeries.forEach((function(e){e.visible=!!document.getElementById(e.id)})),this.userInteractionBase.chartData.length>0&&e&&t&&i&&o&&(this.getVisibleSeries(this,e,t,i,o),this.isDataLoaded=!0),sf.base.isNullOrUndefined(this.tooltipBase.tooltipElementSize)&&this.tooltipBase.tooltipModule&&this.tooltipBase.tooltipModule.template&&this.getElementSize(this.element.id+"_tooltip")}},e.prototype.removeTooltipData=function(){var e=document.getElementById(this.element.id+"_tooltip_data");e&&e.getAttributeNames().length>1&&this.dotnetref.invokeMethodAsync("RemoveTooltipData")},e.prototype.tooltipOptions=function(e,t){this.userInteractionBase.availableSize=t.availableSize,this.userInteractionBase.chartBorderWidth=t.borderWidth,this.userInteractionBase.disableTrackTooltip=t.disableTrackTooltip,this.userInteractionBase.axisClipRect=t.axisClipRect,this.userInteractionBase.isPointMouseDown=t.isPointMouseDown,this.userInteractionBase.isPointDragging=t.isPointDragging,this.userInteractionBase.isInverted=t.isInverted,this.userInteractionBase.chartAreaType=t.chartAreaType,this.tooltipBase.isTooltipMarker=t.isMarkerEnable,this.tooltipBase.tooltipModule=e,this.tooltipBase.tooltipFormat=sf.base.isNullOrUndefined(t.tooltipFormat)?t.tooltipFormat:this.getTooltipFormat(t.tooltipFormat),this.userInteractionBase.enableRTL=t.enableRTL,this.crosshairBase.crosshair=t.crosshair,this.crosshairEnable=t.crosshair.enable,this.markerExplodeBase.markerExploded=t.markerExplode,this.userInteractionBase.chartRadius=t.chartRadius,this.userInteractionBase.theme=t.theme,this.crosshairBase.themeStyleCrosshairLine=t.themeStyleCrosshairLine,this.crosshairBase.themeStyleCrosshairFill=t.themeStyleCrosshairFill,this.crosshairBase.themeStyleCrosshairLabel=t.themeStyleCrosshairLabel,this.crosshairBase.themeStyleCrosshairTextSize=t.themeStyleCrosshairTextSize,this.crosshairBase.themeStyleCrosshairFontFamily=t.themeStyleCrosshairFontFamily,this.crosshairBase.themeStyleCrosshairFontWeight=t.themeStyleCrosshairFontWeight,this.tooltipBase.tooltipModule.template=t.templateString?t.templateString:this.tooltipBase.tooltipModule.template,this.userInteractionBase.initialRect=t.initialRect,this.userInteractionBase.secondaryElementOffset=t.secondaryElementOffset,sf.base.isNullOrUndefined(this.tooltipBase.tooltipElementSize)&&this.tooltipBase.tooltipModule.template&&this.getElementSize(this.element.id+"_tooltip"),this.userInteractionBase.isStockChart=this.element.id.indexOf("_stockChart")>-1,this.tooltipBase.tooltipEventCalled=t.tooltipEventCalled,this.tooltipBase.sharedTooltipEventCalled=t.sharedTooltipEventCalled,this.tooltipBase.crosshairMouseMoveEventCalled=t.crosshairMouseMoveEventCalled,this.tooltipBase.seriesTooltipTop=t.seriesTooltipTop,this.userInteractionBase.useGrouping=t.useGrouping,this.userInteractionBase.focusable=t.focusable,this.tooltipBase.tooltipDuration=0!=this.tooltipBase.tooltipModule.duration?this.tooltipBase.tooltipModule.duration:this.tooltipBase.tooltipModule.shared?100:300,this.crosshairEnable?this.togglePointerEvents(this.element.id,!1):this.pinchZoomingEnable&&this.togglePointerEvents(this.element.id,!0)},e.prototype.getElementSize=function(e){var t=document.getElementById(e);t&&0===t.childNodes.length&&(t.appendChild(document.getElementById("tooltip_template")),this.tooltipBase.tooltipElementSize={width:t.offsetWidth,height:t.offsetHeight},t.removeChild(t.firstChild))},e.prototype.clipPathID=function(e){return this.element.id+"_ChartSeriesClipRect_"+e},e.prototype.markerClipPathId=function(e){return this.element.id+"_ChartMarkerClipRect_"+e},e.prototype.getVisibleSeries=function(e,t,i,o,s){e.userInteractionBase.visibleSeries=[],e.userInteractionBase.axes=s;var r=document.getElementById(e.element.id+"SeriesCollection").querySelectorAll("[id*='SeriesGroup']"),n=document.getElementById(e.element.id+"IndicatorCollection").querySelectorAll("[id*='_Group']"),a=document.getElementById(e.element.id+"TrendLineCollection").querySelectorAll("[id*='TrendLineSeriesGroup']"),l=[];r.length>0&&r.forEach((function(e){l.push(e)})),n.length>0&&n.forEach((function(e){l.push(e)})),a.length>0&&a.forEach((function(e){l.push(e)}));for(var h,d=function(s){var r=l[s].id,n=document.getElementById(r),a=sf.base.isNullOrUndefined(n)?null:n.getAttribute("data-point").split(/(?![^(]*\)),/);h={id:r,name:a[15],index:parseInt(a[4]),interior:a[2],visible:"True"===a[8],category:a[10],seriesType:a[11],type:a[3],drawType:a[12],marker:i[s],border:o[s],shape:a[13],points:sf.base.isNullOrUndefined(c.userInteractionBase.chartData[s])||""==c.userInteractionBase.chartData[s]?[]:JSON.parse(e.userInteractionBase.chartData[s]),clipRect:t[s],tooltipFormat:""!==a[14]?c.getTooltipFormat(a[14]):a[14],enableTooltip:"True"===a[16],dataEditSettings:"True"===a[17],chartIsTransposed:"True"===a[18],opacity:parseFloat(a[19]),x_Axis:c.userInteractionBase.axes.filter((function(e){return e.name===a[20]}))[0],y_Axis:c.userInteractionBase.axes.filter((function(e){return e.name===a[21]}))[0],axesCount:c.userInteractionBase.axes.length,volume:a[22],markerDataLabelFormat:a[23],xMin:parseInt(a[24]),xMax:parseInt(a[25]),emptyPointMode:a[26],focusable:"True"===a[27]},e.userInteractionBase.visibleSeries.push(h)},c=this,u=0;u<l.length;u++)d(u)},e.prototype.getTooltipFormat=function(e){return e=e.replace("tooltip","tT").replace("text","t").replace("high","h").replace("low","l").replace("open","o").replace("close","c").replace("volume","v").replace("size","sI").replace("percentage","p").replace("minimum","mI").replace("maximum","mX").replace("outliers","oL").replace("upperQuartile","uQ").replace("lowerQuartile","lQ").replace("median","m").replace("average","a")},e.prototype.calculateSecondaryOffset=function(e){var t=document.getElementById(e.replace("_stockChart_chart","")+"_svg").getBoundingClientRect(),i=document.getElementById(e).getBoundingClientRect();this.userInteractionBase.secondaryElementOffset&&(this.userInteractionBase.secondaryElementOffset.left=Math.max(t.left-i.left,0),this.userInteractionBase.secondaryElementOffset.top=Math.max(t.top-i.top,0))},e.prototype.unWireEvents=function(e,t){var i=document.getElementById(e);if(i){this.dotnetref=t,f.dotnetrefCollection=f.dotnetrefCollection.filter((function(t){return t.id!==e}));
/*! Find the Events type */
var o=sf.base.Browser.isPointer?"pointerleave":"mouseleave";
/*! Bind the Event handler */if(i.removeEventListener("mousemove",this.mouseMoveRef),i.removeEventListener("touchmove",this.mouseMoveRef),sf.base.EventHandler.remove(i,sf.base.Browser.touchStartEvent,this.chartOnMouseDownRef),sf.base.EventHandler.remove(i,sf.base.Browser.touchEndEvent,this.mouseEndRef),sf.base.EventHandler.remove(i,"click",this.chartOnMouseClickRef),sf.base.EventHandler.remove(i,"contextmenu",this.chartRightClickRef),sf.base.EventHandler.remove(i,o,this.mouseLeaveRef),sf.base.EventHandler.remove(i,"keydown",this.chartKeyDownRef),sf.base.EventHandler.remove(document.body,"keydown",this.documentKeyHandler),sf.base.EventHandler.remove(i,"keyup",this.chartKeyUpRef),this.isZooming||this.isScrollbar){var s="mozilla"===sf.base.Browser.info.name?sf.base.Browser.isPointer?"mousewheel":"DOMMouseScroll":"mousewheel";i.removeEventListener(s,this.chartMouseWheelRef)}if(this.isScrollbar&&(window.removeEventListener("mousedown",this.scrollDownRef),window.removeEventListener("touchstart",this.scrollDownRef),window.removeEventListener("mousemove",this.domMouseMoveRef),window.removeEventListener("touchmove",this.domMouseMoveRef),window.removeEventListener("mouseup",this.domMouseUpRef,!1),window.removeEventListener("touchend",this.domMouseUpRef,!1)),0==document.getElementsByClassName("e-chart").length){var r=sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize";sf.base.EventHandler.remove(window,r,f.resizeBound)}this.touchObject&&(this.touchObject.destroy(),this.touchObject=null)
/*! Apply the style for chart */}},e.prototype.wireEvents=function(e,t){var i=document.getElementById(e);if(i){this.dotnetref=t,f.dotnetrefCollection.push({id:e,dotnetref:t});
/*! Find the Events type */
var o=sf.base.Browser.isPointer?"pointerleave":"mouseleave";if(this.chartOnMouseDownRef=this.chartOnMouseDown.bind(this,t,e),this.mouseEndRef=this.mouseEnd.bind(this,t,e),this.mouseMoveRef=this.mouseMove.bind(this,t,e),this.chartOnMouseClickRef=this.chartOnMouseClick.bind(this,t,e),this.chartRightClickRef=this.chartRightClick.bind(this,t,e),this.chartKeyDownRef=this.chartOnKeyDown.bind(this,this.dotnetref,this.id),this.chartKeyUpRef=this.chartOnKeyUp.bind(this,this.dotnetref,this.id),this.mouseLeaveRef=this.mouseLeave.bind(this,t,e),
/*! Bind the Event handler */
i.addEventListener("mousemove",this.mouseMoveRef),i.addEventListener("touchmove",this.mouseMoveRef),sf.base.EventHandler.add(i,sf.base.Browser.touchStartEvent,this.chartOnMouseDownRef),sf.base.EventHandler.add(i,sf.base.Browser.touchEndEvent,this.mouseEndRef),sf.base.EventHandler.add(i,"click",this.chartOnMouseClickRef),sf.base.EventHandler.add(i,"contextmenu",this.chartRightClickRef),sf.base.EventHandler.add(i,o,this.mouseLeaveRef),sf.base.EventHandler.add(i,"keydown",this.chartKeyDownRef),sf.base.EventHandler.add(document.body,"keydown",this.documentKeyHandler,this),sf.base.EventHandler.add(i,"keyup",this.chartKeyUpRef),this.isZooming||this.isScrollbar){this.chartMouseWheelRef=this.chartMouseWheel.bind(this,t,e);var s="mozilla"===sf.base.Browser.info.name?sf.base.Browser.isPointer?"mousewheel":"DOMMouseScroll":"mousewheel";i.addEventListener(s,this.chartMouseWheelRef)}this.isScrollbar&&(this.scrollDownRef=this.scrollDown.bind(this,t,e),this.domMouseMoveRef=this.domMouseMove.bind(this,t,e),this.domMouseUpRef=this.domMouseUp.bind(this,t,e),window.addEventListener("mousedown",this.scrollDownRef),window.addEventListener("touchstart",this.scrollDownRef),window.addEventListener("mousemove",this.domMouseMoveRef),window.addEventListener("touchmove",this.domMouseMoveRef),window.addEventListener("mouseup",this.domMouseUpRef,!1),window.addEventListener("touchend",this.domMouseUpRef,!1)),f.resizeBound=f.chartResize.bind(this,f.dotnetrefCollection);var r=sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize";sf.base.EventHandler.add(window,r,f.resizeBound),this.longPressBound=this.longPress.bind(this,t,e),this.touchObject=new sf.base.Touch(i,{tapHold:this.longPressBound,tapHoldThreshold:500})}},e.prototype.getEventArgs=function(e,t){var i=e.changedTouches?e.changedTouches[0].clientX:e.clientX,o=e.changedTouches?e.changedTouches[0].clientY:e.clientY;this.setMouseXY(i,o,t);var s=e.touches,r=[];if(e.type.indexOf("touch")>-1)for(var n=0,a=s.length;n<a;n++)r.push({pageX:s[n].clientX,pageY:s[n].clientY,pointerId:e.pointerId||0});return{type:e.type,clientX:e.clientX,clientY:e.clientY,mouseX:this.mouseX,mouseY:this.mouseY,pointerType:e.pointerType,target:e.target.id,changedTouches:{clientX:e.changedTouches?e.changedTouches[0].clientX:0,clientY:e.changedTouches?e.changedTouches[0].clientY:0},touches:r,pointerId:e.pointerId}},e.prototype.getWheelArgs=function(e,t){return this.setMouseXY(e.clientX,e.clientY,t),{detail:e.detail,wheelDelta:e.wheelDelta,target:e.currentTarget?e.currentTarget.id:e.srcElement?e.srcElement.id:e.target?e.target.id:"",clientX:e.clientX,clientY:e.clientY,mouseX:this.mouseX,mouseY:this.mouseY,browserName:sf.base.Browser.info.name,isPointer:sf.base.Browser.isPointer}},e.prototype.setMouseXY=function(e,t,i){var o=document.getElementById(i.replace("_stockChart_chart","")+"_svg").getBoundingClientRect(),s=document.getElementById(i).getBoundingClientRect();this.userInteractionBase.secondaryElementOffset&&(this.userInteractionBase.secondaryElementOffset.left=Math.max(o.left-(this.userInteractionBase.isStockChart?document.getElementById(i.replace("_stockChart_chart","")).getBoundingClientRect().left:s.left),0),this.userInteractionBase.secondaryElementOffset.top=Math.max(o.top-s.top,0),this.mouseY=t-s.top-this.userInteractionBase.secondaryElementOffset.top,this.mouseX=e-s.left-this.userInteractionBase.secondaryElementOffset.left)},e.prototype.chartOnMouseDown=function(e,t,i){this.dotnetref=e;var o,s,r,n=i.target,a=window.sfBlazor.getCompInstance(this.dataId);if(n.id.indexOf("legend")>0&&(a.previousHighlightedIndex=-1),"touchstart"===i.type?(this.isTouch=!0,o=(r=i).changedTouches[0].clientX,s=r.changedTouches[0].clientY):(this.isTouch="touch"===i.pointerType||"2"===i.pointerType||this.isTouch,o=i.clientX,s=i.clientY),this.setMouseXY(o,s,t),i.type.indexOf("touch")>-1){var l=i.changedTouches?i.changedTouches[0].clientX:i.clientX,h=i.changedTouches?i.changedTouches[0].clientY:i.clientY,d=i.touches.length>1;a.pinchZoomingEnable?(this.pinchtarget=document.getElementById(t+"_Pinch_target"),this.pinchtarget.setAttribute("style",this.pinchStyle+" top: "+(h-50)+"px; left: "+(l-50)+"px;"),this.togglePointerEvents(t,!a.crosshairEnable||!a.userInteractionBase.startMove||d),i.preventDefault(),"mozilla"!==sf.base.Browser.info.name.toLowerCase()&&d&&i.stopImmediatePropagation()):a.crosshairEnable&&a.userInteractionBase.startMove?(this.togglePointerEvents(t,!1),i.preventDefault()):a.selectionMode.indexOf("Drag")>-1&&!(n.id.indexOf("ej2_drag_")>-1)&&i.preventDefault()}return this.zoomBase.zoomingModule&&(f.removeTooltipCrosshair(),n.id.indexOf("_Zooming_")>-1&&f.zoomToolkitMouseDown(this,n),n.id.indexOf("_Zooming_")>-1||n.id.indexOf("_scrollBar_")>-1||f.onZoomingMouseDown(this.getEventArgs(i,t),this)),this.userInteractionBase.isStockChart&&a.zoomBase.zoomSettings.enablePan&&("Logarithmic"===a.userInteractionBase.axes[0].valueType?document.getElementById(this.element.id).setAttribute("cursor","not-allowed"):document.getElementById(this.element.id).setAttribute("cursor","pointer")),e.invokeMethodAsync("OnZoomingMouseDown",this.getEventArgs(i,t)),!1},e.prototype.togglePointerEvents=function(e,t){t?this.setPointerEvents(e,"none"):this.setPointerEvents(e,"auto")},e.prototype.setPointerEvents=function(e,t){document.getElementById(e+"_sfchart_rect_style").innerHTML="#"+e+"_ChartAreaBorder, #"+e+"_ChartBorder {pointer-events: "+t+"; }"},e.prototype.chartMouseWheel=function(e,t,i){var o=this;return this.dotnetref=e,(this.zoomBase.zoomingModule||this.isScrollbar)&&(f.removeTooltipCrosshair(),f.zoomingMouseWheelHandler(i,this)),e.invokeMethodAsync("OnChartMouseWheel",this.getWheelArgs(i,t)),this.wheelEventEndTimeout&&clearTimeout(this.wheelEventEndTimeout),this.wheelEventEndTimeout=setTimeout((function(){e.invokeMethodAsync("UpdateChartData"),o.isWheelScrolling=!1}),100),this.isMouseWheelZoom&&i.preventDefault(),!1},e.prototype.mouseMove=function(e,t,i,o){void 0===o&&(o=!1);var s=i.target;if(!sf.base.isNullOrUndefined(document.getElementById(s.id))&&("None"!==this.highlightMode||this.enableHighlight)){if((!sf.base.isNullOrUndefined(s)||s.id.indexOf("Point")>-1||s.id.indexOf("Symbol")>-1)&&(s=s.id.indexOf("text")>-1?f.findDOMElement(s.id.replace("text","shape")):f.findDOMElement(s.id),!sf.base.isNullOrUndefined(s)&&s.hasAttribute("class")&&(s.getAttribute("class").indexOf("highlight")>-1||s.getAttribute("class").indexOf("selection")>-1)))return;"None"!==this.highlightMode&&f.calculateSelectedElements(i,this.dataId);for(var r=void 0,n=[this.id+"_chart_legend_text_",this.id+"_chart_legend_shape_marker_",this.id+"_chart_legend_shape_",this.id+"_chart_legend_g_"],a=0;a<n.length;a++)if(!sf.base.isNullOrUndefined(s)&&!s.id.indexOf(n[a])){r=parseInt(s.id.split(n[a])[1]);break}var l=window.sfBlazor.getCompInstance(this.dataId);!sf.base.isNullOrUndefined(s)&&s.id.indexOf("legend")>0&&(f.isTargetChanged(r,l)||"None"!==this.highlightMode)&&!sf.base.Browser.isDevice&&(this.legendVisible=!0,f.legendSelection(i,l.dataId)),sf.base.isNullOrUndefined(s)||s.id.indexOf("legend")>0||f.resetPreviousHighlightIndex(l),sf.base.isNullOrUndefined(s)||"None"!=this.highlightMode||s.id.indexOf("legend")>0||!(l.highlightDataIndexes.length>0)||f.removeSelectionStyles(this.dataId)}if(!o){var h,d,c;if("touchmove"===i.type){this.isTouch=!0,h=(c=i).changedTouches[0].clientX,d=c.changedTouches[0].clientY;l=window.sfBlazor.getCompInstance(this.dataId);this.pinchtarget&&l.pinchZoomingEnable&&(this.pinchtarget.setAttribute("style",this.pinchStyle+" top: "+(d-50)+"px; left: "+(h-50)+"px;"),i.preventDefault()),l.crosshairBase.crosshair.enable&&l.userInteractionBase.startMove&&i.preventDefault()}else this.isTouch="touch"===i.pointerType||"2"===i.pointerType||this.isTouch,h=i.clientX,d=i.clientY;this.dotnetref=e,document.getElementById(t.replace("_stockChart_chart","")+"_svg")&&(this.setMouseXY(h,d,t),!this.userInteractionBase.chartStartMove||!this.isTouch&&this.isChartZoom||(null!=this.tooltipBase.tooltipModule&&f.tooltipMousemovehandler(this),this.markerExplodeBase.markerExploded&&f.markerMove(this,!1),this.crosshairBase.crosshair&&this.crosshairBase.crosshair.enable&&f.crosshairMousemoveHandler(this)),this.isTouch||(!sf.base.isNullOrUndefined(s)&&s.id.indexOf("_Zooming_")>-1?f.zoomToolkitTooltip(this,s.id,i):f.zoomToolkitRemoveTooltip(this)),this.zoomBase.zoomingModule&&f.onZoomingMouseMove(this.getEventArgs(i,t),this),e.invokeMethodAsync("OnZoomingMouseMove",this.getEventArgs(i,t)),this.isTouch=!1)}},e.prototype.mouseEnd=function(e,t,i){var o,s,r=this;this.dotnetref=e;var n="touchmove"===i.type?i:null;if(o="touchmove"===i.type?n.changedTouches[0].clientX:i.clientX,s="touchmove"===i.type?n.changedTouches[0].clientY:i.clientY,this.isTouch=i.type.indexOf("touch")>-1||("touch"===i.pointerType||"2"===i.pointerType),document.getElementById(t.replace("_stockChart_chart","")+"_svg")){if(this.setMouseXY(o,s,t),this.isTouch){(this.tooltipBase.tooltipModule.enable&&!this.crosshairBase.crosshair.enable&&!f.isSelected()&&sf.svgbase.withInAreaBounds(this.mouseX,this.mouseY,this.userInteractionBase.axisClipRect)&&this.tooltipBase.tooltipModule.shared||this.tooltipBase.tooltipModule.shared)&&(f.tooltip(this),this.markerExplodeBase.markerExploded&&f.markerMove(this,!1)),this.clearTooltip&&clearTimeout(this.clearTooltip),this.clearTooltip=setTimeout((function(){f.removeTooltip(300,r)}),1e3);var a=window.sfBlazor.getCompInstance(this.dataId);this.clearCrosshair&&clearTimeout(this.clearCrosshair),this.clearCrosshair=setTimeout((function(){f.removeCrosshair(a,300)}),1e3),f.removeMarker(this)}this.userInteractionBase.startMove&&(f.removeCrosshair(this,2e3),this.userInteractionBase.startMove=!1);var l=window.sfBlazor.getCompInstance(this.dataId);"None"==this.highlightMode&&l.highlightDataIndexes.length>0&&f.removeSelectionStyles(this.dataId)}if("touchend"==i.type)if(this.pinchZoomingEnable){var h=i.touches.length>0;this.togglePointerEvents(t,!this.crosshairEnable||h)}else this.togglePointerEvents(t,!1);return this.zoomBase.zoomingModule&&(this.zoomBase.threshold=(new Date).getTime()+300,f.onZoomingMouseEnd(this.getEventArgs(i,t),this)),e.invokeMethodAsync("OnZoomingMouseEnd",this.getEventArgs(i,t)),this.isTouch=!1,!1},e.prototype.chartOnMouseClick=function(e,t,i){f.calculateSelectedElements(i,this.dataId);var o=i.target,s=window.sfBlazor.getCompInstance(this.dataId);o.id.indexOf("legend")>0&&!s.toggleVisibility&&(this.legendVisible=!0,f.legendSelection(i,s.dataId))},e.prototype.chartRightClick=function(e,t,i){return this.dotnetref=e,!window.sfBlazor.getCompInstance(this.dataId).crosshairBase.crosshair.enable||2!==i.buttons&&"touch"!==i.pointerType||(i.preventDefault(),i.stopPropagation()),e.invokeMethodAsync("OnChartMouseRightClick",this.getEventArgs(i,t)),!1},e.prototype.chartOnKeyDown=function(e,t,i){this.dotnetref=e;for(var o="",s=i.target,r=!1,n=s;n&&n.parentNode;){if(n.id&&n.id.indexOf("Annotation")>-1){r=!0;break}n=n.parentNode}if((this.isZoomed&&"Tab"==i.code||"Space"==i.code&&!r)&&i.preventDefault(),(this.options.showTooltip||this.crosshairBase.crosshair&&this.crosshairBase.crosshair.enable)&&("Tab"==i.code&&this.previousTargetId.indexOf("Series")>-1||"Escape"===i.code)&&(o="ESC"),!i.ctrlKey||"+"!==i.key&&"Equal"!==i.code&&"-"!==i.key&&"Minus"!==i.code?82===i.keyCode&&this.isZoomed?(i.preventDefault(),this.isZoomed=!1,o="R"):i.code.indexOf("Arrow")>-1&&(i.preventDefault(),o=this.isZoomed?i.code:""):(i.preventDefault(),this.isZoomed=this.options.enableZoom,f.fadeOut(this.element),o=this.isZoomed?i.code:""),i.ctrlKey&&"p"===i.key&&(i.preventDefault(),o="CtrlP"),""!=o&&this.userInteractionBase.focusable){if(this.zoomBase.zoomingModule){if("Equal"!=o&&"Minus"!=o||(this.zoomBase.zoomingModule.isZoomed=this.zoomBase.zoomingModule.performedUI=!0,this.zoomBase.zoomingModule.isPanning=this.zoomBase.isChartDrag=!1,"Equal"==o?f.zoomToolkitZoomIn(this,s.getAttribute("opacity")):f.zoomToolkitZoomOut(this,s.getAttribute("opacity")),f.performZoomRedraw(this,!0)),"ArrowUp"==o||"ArrowDown"==o||"ArrowLeft"==o||"ArrowRight"==o){var a="ArrowUp"==o?10:"ArrowDown"==o?-10:0,l="ArrowLeft"==o?-10:"ArrowRight"==o?10:0;this.zoomBase.zoomingModule.isPanning=this.zoomBase.isChartDrag=!0,f.doPan(this,this.zoomBase.axisCollections,l,a),f.performZoomRedraw(this,!0)}"R"==o&&f.zoomToolkitReset(this)}e.invokeMethodAsync("OnChartKeyboardNavigations",o,i.target.id)}return!1},e.prototype.processPointSelection=function(e,t,i,o){var s=document.getElementById(t);sf.base.isNullOrUndefined(s)||("Enter"===o?this.chartOnMouseClick(e,t,{target:document.getElementById(i),type:"click"}):"Tab"!==o&&"ArrowMove"!==o||this.mouseMove(e,t,{target:document.getElementById(i),type:"mousemove"},!0))},e.prototype.chartOnKeyUp=function(e,t,i){this.dotnetref=e;var o,s,r,n,a="",l=i.target,h=i.target.id,d=i.target,c=f.getElement(this.element.id+"_ChartTitle"),u=f.getElement(this.element.id+"_ChartSubTitle"),m=f.getElement(this.element.id+"SeriesCollection"),p=f.getElement(this.element.id+"_chart_legend_translate_g"),g=f.getElement(this.element.id+"_chart_legend_pageup"),v=f.getElement(this.element.id+"_Annotation_Collections"),b=f.getElement(this.element.id+"_Zooming_ZoomIn"),x=f.getElement(this.element.id+"_Zooming_ZoomOut"),B=f.getElement(this.element.id+"_Zooming_Reset"),y=f.getElement(this.element.id+"TrendLineCollection"),S=f.isAxisZoomed(this.userInteractionBase.axes),I=!1;if(!this.isZoomed&&82!==i.keyCode&&!S&&this.userInteractionBase.focusable){var z;if(c&&c.setAttribute("class","e-chart-focused"),u&&u.setAttribute("class","e-chart-focused"),m&&m.firstElementChild)if(z=m.firstElementChild.children[1])(H=z.getAttribute("class"))&&-1===H.indexOf("e-chart-focused")?H+=" e-chart-focused":H||(H="e-chart-focused"),z.setAttribute("class",H+" e-chart-focused");if(y&&y.firstElementChild)if(z=y.firstElementChild.children[1])(H=z.getAttribute("class"))&&-1===H.indexOf("e-chart-focused")?H+=" e-chart-focused":H||(H="e-chart-focused"),z.setAttribute("class",H+" e-chart-focused");if(p)if(z=p.firstElementChild)(H=z.getAttribute("class"))&&-1===H.indexOf("e-chart-focused")?H+=" e-chart-focused":H||(H="e-chart-focused"),z.setAttribute("class",H);if(v)if(z=v.firstElementChild)(H=z.getAttribute("class"))&&-1===H.indexOf("e-chart-focused")?H+=" e-chart-focused":H||(H="e-chart-focused"),z.setAttribute("class",H);if(g&&g.setAttribute("class","e-chart-focused"),"Tab"==i.code){if(""!=this.previousTargetId)if(this.previousTargetId.indexOf("Series")>-1&&-1==h.indexOf("Series")){if(o=f.getElement(this.element.id+(this.previousTargetId.indexOf("TrendLine")>-1?"TrendLineCollection":"SeriesCollection")),!sf.base.isNullOrUndefined(o.children[0])){var T=void 0;T=this.previousTargetId.indexOf("_PointIndex_")>-1?f.getElement(this.seriesPathElement.id):this.previousTargetId.indexOf("_Symbol")>-1?f.getElement(this.previousTargetId.indexOf("TrendLine")>-1?this.element.id+"_Series_"+this.currentSeriesIndex+"_TrendLine_"+this.currentSeriesIndex+"_Point_"+this.currentPointIndex+"_Symbol":this.element.id+"_Series_"+this.currentSeriesIndex+"_Point_"+this.currentPointIndex+"_Symbol"):this.previousTargetId.indexOf("_Point_")>-1?f.getElement(this.previousTargetId.indexOf("TrendLine")>-1?this.element.id+"_Series_"+this.currentSeriesIndex+"_TrendLine_"+this.currentSeriesIndex+"_Point_"+this.currentPointIndex:this.element.id+"_Series_"+this.currentSeriesIndex+"_Point_"+this.currentPointIndex):f.getElement(this.element.id+(this.previousTargetId.indexOf("TrendLine")>-1?"TrendlineSeriesGroup":"SeriesGroup")+this.currentSeriesIndex);var M=f.getFocusedSeries(this,o.firstElementChild.id);f.setTabIndex(T,f.getElement(M)),this.currentPointIndex=0,this.currentSeriesIndex=0,this.currentLegendIndex=0}}else this.previousTargetId.indexOf("_chart_legend_page")>-1&&-1==h.indexOf("_chart_legend_page")&&-1==h.indexOf("_chart_legend_g_")?f.setTabIndex(i.target,f.getElement(this.element.id+"_chart_legend_pageup")):this.previousTargetId.indexOf("_chart_legend_g_")>-1&&-1==h.indexOf("_chart_legend_g_")&&(o=f.getElement(this.element.id+"_chart_legend_translate_g"),f.setTabIndex(o.children[this.currentLegendIndex],o.firstElementChild));if(-1==this.previousTargetId.indexOf("Series")&&h.indexOf("Series")>-1){this.currentPointIndex=0;M=h.indexOf("TrendLine")>-1?y.firstElementChild.id:m.firstElementChild.id;h=f.getFocusedSeries(this,M),d=f.getElement(h)}this.previousTargetId=h,h.indexOf("SeriesGroup")>-1&&(this.currentSeriesIndex=+h.split("SeriesGroup")[1],d.removeAttribute("tabindex"),d.blur(),-1==d.children[1].id.indexOf("_Point_")&&(s=f.getElement(this.element.id+(d.children[1].id.indexOf("_TrendLine_")>-1?"TrendLineSymbolGroup":"SymbolGroup")+h.split("SeriesGroup")[1])),h=null!=s?s.children[1].id:d.children[1].id,"MultiColoredLine"!=(n=f.getCurrentSeries(this,h,this.currentSeriesIndex)).type&&(h=f.focusChild(null!=s?s.children[1]:d.children[1]))),n=f.getCurrentSeries(this,h,this.currentSeriesIndex),h.indexOf("Series")>-1&&(I=f.checkPointsLength(n)),h.indexOf("Series")>-1&&"MultiColoredLine"!=n.type&&I&&(this.currentPointIndex=parseInt(h.split("_Series_")[1].replace("_Symbol","").split("_Point_")[1])),a=this.enableHighlight||this.crosshairBase.crosshair&&this.crosshairBase.crosshair.enable||this.options.showTooltip||h.indexOf("_Series_")>-1&&null==s?"Tab":""}else if(i.code.indexOf("Arrow")>-1){if(i.preventDefault(),this.previousTargetId=h,h.indexOf("_chart_legend_page")>-1)"ArrowLeft"==i.code?(f.getElement(this.element.id+"_chart_legend_pagedown").removeAttribute("tabindex"),f.focusChild(f.getElement(this.element.id+"_chart_legend_pageup"))):"ArrowRight"==i.code&&(f.getElement(this.element.id+"_chart_legend_pageup").removeAttribute("tabindex"),f.focusChild(f.getElement(this.element.id+"_chart_legend_pagedown")));else if(h.indexOf("_chart_legend_")>-1){var w=d.parentElement.children;w[this.currentLegendIndex].removeAttribute("tabindex"),this.currentLegendIndex+="ArrowUp"==i.code||"ArrowRight"==i.code?1:-1,this.currentLegendIndex=f.getActualIndex(this.currentLegendIndex,w.length);var _=w[this.currentLegendIndex];f.focusChild(_),h=_.children[1].id,a=this.enableHighlight?"ArrowMove":""}else if(h.indexOf("_Annotation_")>-1){var A=d.parentElement.children;A[this.currentAnnotationIndex].removeAttribute("tabindex"),this.currentAnnotationIndex+="ArrowUp"==i.code||"ArrowRight"==i.code?1:-1,this.currentAnnotationIndex=f.getActualIndex(this.currentAnnotationIndex,A.length);var E=A[this.currentAnnotationIndex];f.focusChild(E),h=E.id,a=""}else if(h.indexOf("_Series_")>-1){o=-1==d.id.indexOf("_Series_")?f.getElement(this.element.id+"SeriesCollection"):d.parentElement.parentElement;var C=i.target;if(d.removeAttribute("tabindex"),d.blur(),"ArrowRight"==i.code||"ArrowLeft"==i.code){for(var O=[],R=0;R<o.children.length;R++)o.children[R].id.indexOf("SeriesGroup")>-1&&O.push(+o.children[R].id.split("SeriesGroup")[1]);var k=O.indexOf(this.currentSeriesIndex);for(this.currentSeriesIndex=O.indexOf(this.currentSeriesIndex)+("ArrowRight"==i.code?1:-1),this.currentSeriesIndex=O[f.getActualIndex(this.currentSeriesIndex,O.length)],n=f.getCurrentSeries(this,h,this.currentSeriesIndex);!n.focusable;)this.currentSeriesIndex=O.indexOf(this.currentSeriesIndex)+("ArrowRight"==i.code?1:-1),this.currentSeriesIndex=O[f.getActualIndex(this.currentSeriesIndex,O.length)],n=f.getCurrentSeries(this,h,this.currentSeriesIndex);o=f.getElement(this.element.id+(h.indexOf("_TrendLine_")>-1?"TrendLineSeriesGroup":"SeriesGroup")+this.currentSeriesIndex),s=f.getElement(this.element.id+(h.indexOf("_TrendLine_")>-1?"TrendLineSymbolGroup":"SymbolGroup")+this.currentSeriesIndex);var P=f.getElement(this.element.id+(h.indexOf("_TrendLine_")>-1?"TrendLineSeriesGroup":"SeriesGroup")+k),D=f.getElement(this.element.id+(h.indexOf("_TrendLine_")>-1?"TrendLineSymbolGroup":"SymbolGroup")+k);r=parseInt(h.split("_Series_")[1].replace("_Symbol","").split("_Point_")[1]);var L=this.seriesPathElement?parseInt(this.seriesPathElement.id.split("_Series_")[1].replace("_Symbol","").split("_Point_")[1]):0,N=-1==o.children[1].id.indexOf("_Point_")&&s?s:o,X=P&&-1==P.children[1].id.indexOf("_Point_")&&D?D:P;I=f.checkPointsLength(n),isNaN(r)||isNaN(L)?C=-1==o.children[1].id.indexOf("_Point_")&&s?s.children[this.currentPointIndex+1]:"MultiColoredArea"==n.type?f.getElement(h):o.children[this.currentPointIndex+1]:X&&N.childElementCount==X.childElementCount?(this.currentPointIndex="BoxAndWhisker"==n.type?f.getActualIndex(this.currentPointIndex,(o.childElementCount-1)/2):f.getActualIndex(this.currentPointIndex,s?s.childElementCount:o.childElementCount),C=-1==o.children[1].id.indexOf("_Point_")&&s?s.children[this.currentPointIndex+1]:o.children[this.currentPointIndex+1]):(this.currentPointIndex=r,C=f.getCurrentPointElement(this,N,i.code))}else{this.currentPointIndex+="ArrowUp"==i.code?1:-1,r=parseInt(h.split("_Series_")[1].replace("_Symbol","").split("_Point_")[1]),n=f.getCurrentSeries(this,h,this.currentSeriesIndex),o=f.getElement(this.element.id+(h.indexOf("_TrendLine_")>-1?"TrendLineSeriesGroup":"SeriesGroup")+this.currentSeriesIndex),s=f.getElement(this.element.id+(h.indexOf("_TrendLine_")>-1?"TrendLineSymbolGroup":"SymbolGroup")+this.currentSeriesIndex);N=-1==o.children[1].id.indexOf("_Point_")&&s?s:o;I=f.checkPointsLength(n),isNaN(r)||-1!=h.indexOf("_BoxPath")||n.points.length==N.children.length-1&&!I?h.indexOf("_Symbol")>-1?(this.currentPointIndex=f.getActualIndex(this.currentPointIndex,f.getElement(this.element.id+(h.indexOf("_TrendLine_")>-1?"TrendLineSymbolGroup":"SymbolGroup")+this.currentSeriesIndex).childElementCount-1),C=h.indexOf("_TrendLine_")>-1?f.getElement(this.element.id+"_Series_"+this.currentSeriesIndex+"_TrendLine_"+this.currentSeriesIndex+"_Point_"+this.currentPointIndex+"_Symbol"):f.getElement(this.element.id+"_Series_"+this.currentSeriesIndex+"_Point_"+this.currentPointIndex+"_Symbol")):h.indexOf("_Point_")>-1&&(this.currentPointIndex=f.getActualIndex(this.currentPointIndex,h.indexOf("_BoxPath")>-1?(f.getElement(this.element.id+"SeriesGroup"+this.currentSeriesIndex).childElementCount-1)/2:f.getElement(this.element.id+"SeriesGroup"+this.currentSeriesIndex).childElementCount-1),C=h.indexOf("_BoxPath")>-1?f.getElement(this.element.id+"_Series_"+this.currentSeriesIndex+"_Point_"+this.currentPointIndex+"_BoxPath"):f.getElement(this.element.id+"_Series_"+this.currentSeriesIndex+"_Point_"+this.currentPointIndex)):C=f.getCurrentPointElement(this,N,i.code)}h="MultiColoredLine"==n.type||"MultiColoredArea"==n.type?sf.base.isNullOrUndefined(C)?h:C.id:sf.base.isNullOrUndefined(C)?h.indexOf("_TrendLine_")>-1?f.focusChild(f.getElement(this.element.id+"_Series_"+this.currentSeriesIndex+"_TrendLine_"+this.currentSeriesIndex)):f.focusChild(sf.base.isNullOrUndefined(f.getElement(this.element.id+"_Series_"+this.currentSeriesIndex))?f.getRectSeriesElement(this,n):f.getElement(this.element.id+"_Series_"+this.currentSeriesIndex)):f.focusChild(C),a=this.crosshairBase.crosshair&&this.crosshairBase.crosshair.enable||this.options.showTooltip||null==s?"ArrowMove":""}}else"Enter"!=i.code&&"Space"!=i.code||!(h.indexOf("_chart_legend_")>-1||h.indexOf("_Point_")>-1)||(h=h.indexOf("_chart_legend_page")>-1?h:h.indexOf("_chart_legend_")>-1?d.children[1].id:h,a="Enter")}if(b&&b.setAttribute("class","e-chart-focused"),x&&x.setAttribute("class","e-chart-focused"),B&&B.setAttribute("class","e-chart-focused"),h.indexOf("_Zooming_")>-1&&this.zoomBase.zoomingModule&&("Enter"!=i.code&&"Space"!=i.code||(this.zoomBase.zoomingModule.isZoomed=this.zoomBase.zoomingModule.performedUI=!0,this.zoomBase.zoomingModule.isPanning=this.zoomBase.isChartDrag=!1,h.indexOf("_Zooming_ZoomIn")>-1?(f.zoomToolkitZoomIn(this,l.getAttribute("opacity")),a="Enter"):h.indexOf("_Zooming_ZoomOut")>-1?(f.zoomToolkitZoomOut(this,l.getAttribute("opacity")),a="Enter"):h.indexOf("_Zooming_Reset")>-1&&(f.zoomToolkitReset(this),a="Enter"),f.performZoomRedraw(this,!1))),""!=a){if("ArrowMove"==a||"Tab"==a)if(h.indexOf("_Point_")>-1||h.indexOf("_Series_")>-1||h.indexOf("_ChartSegmentClipRect_")>-1){var F=h.indexOf("_ChartSegmentClipRect_")>-1?parseInt(h.split("_ChartSegmentClipRect_")[1]):parseInt(h.split("_Series_")[1].split("_Point_")[0]);if(r=h.indexOf("_ChartSegmentClipRect_")>-1?NaN:parseInt(h.split("_Series_")[1].replace("_Symbol","").split("_Point_")[1]),n=f.getCurrentSeries(this,h,F),isNaN(r)||"MultiColoredLine"==n.type&&h.indexOf("_Series_")>-1){(I=f.checkPointsLength(n))?(this.currentPointIndex="ArrowUp"==i.code||"ArrowDown"==i.code?this.currentPointIndex:null!=this.seriesPathElement?parseInt(this.seriesPathElement.id.split("_Series_")[1].replace("_Symbol","").split("_PointIndex_")[1]):n.points[0].iX,r=f.getCurrentPointIndex(this,n,i.code)):(this.currentPointIndex=f.getActualIndex(this.currentPointIndex,n.points.length),r=this.currentPointIndex),h=h.indexOf("_TrendLine_")>-1?this.element.id+"_Series_"+F+"_TrendLine_"+F+"_PointIndex_"+this.currentPointIndex:this.element.id+"_Series_"+F+"_PointIndex_"+this.currentPointIndex,null!=this.seriesPathElement&&(f.removeElement(this.seriesPathElement.id),f.removeElement(this.element.id+"_Series_"+parseInt(this.seriesPathElement.id.split("_Series_")[1].split("_PointIndex_")[0])+"_Point_"+parseInt(this.seriesPathElement.id.split("_Series_")[1].replace("_Symbol","").split("_PointIndex_")[1])+"_Trackball_1"),this.seriesPathElement=null),this.seriesPathElement=this.userInteractionBase.svgRenderer.drawPath({id:h,"stroke-width":2,fill:"transparent",opacity:1,stroke:"transparent",d:"M "+n.points[r].s[0].x+" "+n.points[r].s[0].y}),f.getElement(this.element.id+(h.indexOf("_TrendLine_")>-1?"TrendLineSeriesGroup":"SeriesGroup")+F).appendChild(this.seriesPathElement),f.focusTarget(h);var Z=h.indexOf("_TrendLine_")>-1?f.getElement(this.element.id+"_Series_"+F+"_TrendLine_"+F):f.getElement(this.element.id+"_Series_"+F);if(Z){var H=Z.getAttribute("class"),Y=Z.getAttribute("tabindex");H&&Y&&(Z.removeAttribute("class"),Z.removeAttribute("tabindex"))}e.invokeMethodAsync("GetAccessibilityText",F,n.type.indexOf("Spline")>-1&&"Drop"==n.emptyPointMode?r:this.currentPointIndex,h)}var U=parseInt(h.split("_Series_")[1].replace("_Symbol","").split("_Point_")[1]);if(("Gap"==n.emptyPointMode||-1==n.type.indexOf("Spline")&&"Drop"==n.emptyPointMode)&&I&&!isNaN(U))for(var W=0;W<n.points.length;W++)r===n.points[W].iX&&(r=W,W=n.points.length);var V=void 0;"Polar"==n.type||"Radar"==n.type?(V=n.points[r].s[0],this.mouseX=V.x-(n.points[r].mK?n.points[r].mK.mW:0),this.mouseY=V.y-(n.points[r].mK?n.points[r].mK.mH:0)):(V=f.isRectSeries(n.type)?n.points[r].r[0]:n.points[r].s[0],this.mouseX=f.isRectSeries(n.type)?Math.ceil(V.x+n.clipRect.x):V.x+n.clipRect.x,this.mouseY=f.isRectSeries(n.type)?Math.ceil(V.y+n.clipRect.y):V.y+n.clipRect.y),f.tooltipMousemovehandler(this),f.crosshairMousemoveHandler(this),this.markerExplodeBase.markerExploded&&f.markerMove(this,!1)}else f.removeTooltip(1,this),f.removeCrosshair(this,1),f.removeMarker(this);"ESC"==a&&(f.removeTooltip(1,this),f.removeCrosshair(this,1),f.removeMarker(this)),this.processPointSelection(e,t,h,a),h.indexOf("_Series_")>-1&&this.highlightDataIndexes.length>0&&f.removeSelectionStyles(this.dataId),e.invokeMethodAsync("OnChartKeyboardNavigations",a,h)}return h.indexOf("_Series_")>-1&&this.highlightDataIndexes.length>0&&f.removeSelectionStyles(this.dataId),!1},e.prototype.mouseLeave=function(e,t,i){return null!=this.tooltipBase.tooltipModule&&f.removeTooltip(this.tooltipBase.tooltipModule.fadeOutDuration,this),null!=this.crosshairBase.crosshair&&f.removeCrosshair(this,1e3),this.dotnetref=e,this.zoomBase.zoomingModule&&(this.zoomBase.isChartDrag=this.userInteractionBase.isPointMouseDown=!1,f.mouseCancelHandler(this)),e.invokeMethodAsync("OnChartMouseLeave",this.getEventArgs(i,t)),!1},e.prototype.longPress=function(e,t,i){this.dotnetref=e;var o=i&&i.originalEvent.changedTouches?i.originalEvent.changedTouches[0].clientX:0,s=i&&i.originalEvent.changedTouches?i.originalEvent.changedTouches[0].clientY:0;this.setMouseXY(o,s,t);this.mouseX,this.mouseY;return this.userInteractionBase.startMove=!0,sf.svgbase.withInAreaBounds(this.mouseX,this.mouseY,this.userInteractionBase.axisClipRect)&&(f.markerMove(this,!1),this.tooltipBase.tooltipModule.enable&&f.tooltip(this),this.crosshairBase.crosshair.enable&&f.createCrosshair(this)),this.dotnetref.invokeMethodAsync("OnChartLongPress"),!1},e.prototype.getAxisScrollbar=function(e,t){var i="",o=[];return f.isExist(e,"_scrollBar_svg")?(o=e.split("_scrollBar_svg"),i=o[o.length-1]):(o=e.split("_"),i=o[o.length-1]),t.filter((function(e){return e.name===i}))[0]},e.prototype.scrollDown=function(e,t,i){var o=i.target.id;if(o.indexOf("scrollBar")>-1){var s=window.sfBlazor.getCompInstance(this.dataId),r=s.scrollbarBase;r.targetId=o;var n=this.getAxisScrollbar(o,r.axes);if(!sf.base.isNullOrUndefined(n)){r.axis=n;var a=r.scrollbarOptions[n.name];a.isVertical="Vertical"===n.orientation;var l=n.isAxisInverse,h=f.getScrollEventArgs(i,[s.id,s.id+"_scrollBar_svg"+n.name]);this.mouseX=h.mouseX,this.mouseY=h.mouseY,r.isResizeLeft=f.isExist(o,"_leftCircle_")||f.isExist(o,"_leftArrow_"),r.isResizeRight=f.isExist(o,"_rightCircle_")||f.isExist(o,"_rightArrow_"),r.previousXY=a.isVertical&&l?this.mouseY:a.isVertical?a.width-this.mouseY:l?a.width-this.mouseX:this.mouseX,r.previousWidth=a.thumbRectWidth,r.previousRectX=a.thumbRectX,r.startZoomPosition=n.zoomPosition,r.startZoomFactor=n.zoomFactor,r.startRange=n.visibleRange,r.scrollStarted=!0;n.zoomPosition,n.zoomFactor,n.visibleRange;if(s.dotnetref.invokeMethodAsync("TriggerScrollEvents",f.getArgs("ScrollStart",n.name,n.zoomPosition,n.zoomFactor,null)),f.isExist(o,"scrollBarThumb_")||f.isExist(o,"gripCircle"))r.isThumbDrag=!0,a.svgObject.style.cursor="-webkit-grabbing";else if(f.isExist(o,"scrollBarBackRect_")){var d=f.moveLength(r);if(a.thumbRectX=f.isWithIn(d,r)?d:a.thumbRectX,f.positionThumb(a.thumbRectX,a.thumbRectWidth,r),f.setZoomFactorPosition(r,a.thumbRectX,a.thumbRectWidth,!1),a.isLazyLoad){var c=a.thumbRectX>r.previousRectX?"RightMove":"LeftMove",u=f.calculateLazyRange(r,c);u&&s.dotnetref.invokeMethodAsync("TriggerScrollEvents",f.getArgs("ScrollStart",n.name,n.zoomPosition,n.zoomFactor,u.currentRange))}}}}return!1},e.prototype.domMouseMove=function(e,t,i){var o=i.target.id,s=window.sfBlazor.getCompInstance(this.dataId);if(!sf.base.isNullOrUndefined(s)&&!sf.base.isNullOrUndefined(o)&&o.indexOf(s.id)>-1){var r=s.scrollbarBase;if((o=!sf.base.isNullOrUndefined(r)&&r.scrollStarted?r.targetId:o).indexOf("scrollBar")>-1){var n=this.getAxisScrollbar(o,r.axes),a=r.scrollbarOptions[n.name],l=n.isAxisInverse;if(sf.base.isNullOrUndefined(a)||a&&!document.getElementById(a.svgObject.id))return null;var h=f.getScrollEventArgs(i,[s.id,s.id+"_scrollBar_svg"+n.name]);this.mouseX=h.mouseX,this.mouseY=h.mouseY,f.setCursor(i.target,this.dataId,n.name),f.setTheme(i.target,this.dataId,n.name);var d=a.isVertical&&l?a.width-this.mouseY:a.isVertical?this.mouseY:this.mouseX,c=(n.visibleRange,a.zoomPosition),u=a.zoomFactor,m=r.previousRectX-a.thumbRectX<0?"RightMove":"LeftMove",p=void 0;a.isLazyLoad&&(r.isThumbDrag||r.isResizeLeft||r.isResizeRight)&&(p=f.calculateLazyRange(r,m));var g=p?p.currentRange:null;if(r.isThumbDrag){r.isScrolling=r.isThumbDrag,d=a.isVertical||l?a.width-d:d;var v=a.thumbRectX+(d-r.previousXY);d>=v+a.thumbRectWidth?f.setCursor(i.target,this.dataId,n.name):a.svgObject.style.cursor="-webkit-grabbing",d>=0&&d<=v+a.thumbRectWidth&&(a.thumbRectX=f.isWithIn(v,r)?v:a.thumbRectX,f.positionThumb(a.thumbRectX,a.thumbRectWidth,r),r.previousXY=d,f.setZoomFactorPosition(r,v,a.thumbRectWidth,!1)),s.dotnetref.invokeMethodAsync("TriggerScrollEvents",f.getArgs("OnScrollChanged",n.name,c,u,g))}else(r.isResizeLeft||r.isResizeRight)&&(f.resizeThumb(r,this.mouseX,this.mouseY),s.dotnetref.invokeMethodAsync("TriggerScrollEvents",f.getArgs("OnScrollChanged",n.name,c,u,g)))}}return!1},e.prototype.domMouseUp=function(e,t,i){var o=i.target.id,s=window.sfBlazor.getCompInstance(this.dataId),r=s.scrollbarBase;if((o=!sf.base.isNullOrUndefined(r)&&r.scrollStarted?r.targetId:o).indexOf("scrollBar")>-1&&!sf.base.isNullOrUndefined(r.scrollbarOptions[r.axis.name])){var n=void 0,a=r.scrollbarOptions[r.axis.name];a.startX=a.thumbRectX;var l=this.getAxisScrollbar(o,r.axes),h=a.startX+a.thumbRectWidth+8+1;if(!r.isResizeLeft&&!r.isResizeRight||a.isLazyLoad||(l.zoomFactor=h>=a.width-1&&a.startX-8.5<=0?1:a.zoomFactor),a.isLazyLoad){var d=r.previousRectX-a.startX;if((d>0||d<0)&&r.isThumbDrag){var c=d<0?"RightMove":"LeftMove";a.startX="RightMove"===c?a.startX+Math.abs(d)<a.width-8?a.startX:a.width-8-a.thumbRectWidth:a.startX+a.thumbRectWidth-Math.abs(d)>8?a.startX:8,(n=f.calculateLazyRange(r,c))&&(s.dotnetref.invokeMethodAsync("TriggerScrollEvents",f.getArgs("OnScrollEnd",l.name,l.zoomPosition,l.zoomFactor,n.currentRange)),r.targetId=o,r.scrollStarted=!1)}(r.isResizeLeft||r.isResizeRight)&&(n=f.calculateLazyRange(r))&&(s.dotnetref.invokeMethodAsync("TriggerScrollEvents",f.getArgs("OnScrollEnd",l.name,l.zoomPosition,l.zoomFactor,n.currentRange)),r.targetId=o,r.scrollStarted=!1)}r.isThumbDrag=!1,r.isScrollEnd=!1,r.isScrolling=!1,r.scrollStarted&&!a.isLazyLoad&&(s.dotnetref.invokeMethodAsync("TriggerScrollEvents",f.getArgs("OnScrollChanged",l.name,l.zoomPosition,l.zoomFactor,n?n.currentRange:null)),r.targetId=o,r.scrollStarted=!1),s.dotnetref.invokeMethodAsync("UpdateChartData")}return r.isResizeLeft=!1,r.isResizeRight=!1,s.userInteractionBase.isStockChart&&(s.userInteractionBase.startMove=!1),!1},e}(),o=function(e,t){this.series=e,this.point=t},s=function(e,t){this.x=e,this.y=t},r=function(e,t,i){this.r=e,this.g=t,this.b=i},n=function(){this.currentPoints=[],this.previousPoints=[],this.valueX=0,this.valueY=0,this.tooltipTempList=[]},a=function(){this.markerCurrentPoints=[],this.markerPreviousPoints=[],this.trackBallClass="EJ2-TrackBall"},l=function(){this.crosshairX=0,this.crosshairY=0,this.rx=2,this.ry=2},h=function(){this.lierIndex=0,this.chartStartMove=!0,this.axes=[],this.isFirstRendered=!0,this.toolbarHeight=43},d=function(e,t,i,o,s){this.browserName=e,this.isPointer=t,this.isDevice=i,this.isTouch=o,this.isIos=s},c=function(e){function i(t,i,o,s,r,n,a,l,h){var d=e.call(this,t,i,o.width,o.color,s,h)||this;return d.y=r.y,d.x=r.x,d.height=r.height,d.width=r.width,d.rx=n||0,d.ry=a||0,d.transform=l||"",d.stroke=0!==o.width&&""!==d.stroke?o.color:"transparent",d}return t(i,e),i}(sf.svgbase.PathOption),u=function(e){this.isZoomStart=!0,this.touchStartList=[],this.touchMoveList=[],this.chart=e,this.browser=new d(sf.base.Browser.info.name,sf.base.Browser.isPointer,sf.base.Browser.isDevice,sf.base.Browser.isTouch,sf.base.Browser.isIos||sf.base.Browser.isIos7),this.isDevice=this.browser.isDevice,this.zooming=e.zoomBase.zoomSettings,this.elementId=e.element.id,this.zoomAxes=[],this.zoomingRect=new sf.svgbase.Rect(0,0,0,0),this.isZoomed=this.performedUI=this.zooming.enablePan&&this.zooming.enableSelectionZooming},m=function(){this.iconRectOverFill="transparent",this.iconRectSelectionFill="transparent"},p=function(){this.axisCollections=[],this.previousMouseMoveX=0,this.previousMouseMoveY=0,this.mouseDownX=0,this.mouseDownY=0,this.clipRectId="_ChartAreaClipRect__Rect",this.zoomToolkitId="_Zooming_KitCollection",this.zoomToolkitZoom="_Zooming_Zoom",this.zoomToolkitZoomIn="_Zooming_ZoomIn",this.zoomToolkitZoomOut="_Zooming_ZoomOut",this.zoomToolkitPan="_Zooming_Pan",this.zoomToolkitReset="_Zooming_Reset",this.chartZoomTip="EJ2_Chart_ZoomTip"},g=function(e){function i(t,i,o,s,r,n,a){var l=e.call(this,t,i,o.width,o.color,s)||this;return l.cy=n,l.cx=r,l.r=a,l}return t(i,e),i}(sf.svgbase.PathOption),f={dataId:"",initialize:function(e,t,o,s,r,n,a){var l=this,h=new i(e,t.id,t,o,s,r,n,a);if(this.dataId=e,t.id.indexOf("_stockChart_")>-1){var d=t.id.split("_stockChart_")[0],c=window.sfBlazor.StockChart.dotnetrefCollection.find((function(e){return e.id==d})).dotnetref;window.sfBlazor.StockChart.getTooltipPosition(c).then((function(e){l.stockChartTooltipPosition=e}))}this.isUpdatingClipRect=!1,h.render()},destroy:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.destroy()},getArgs:function(e,t,i,o,s){return{name:e,axisName:t,zoomPosition:isFinite(i)?i:1,zoomFactor:isFinite(o)?o:1,currentRangeMax:s?s.maximum.toString():"",currentRangeMin:s?s.minimum.toString():""}},getMouseXY:function(e,t,i){var o,s,r;e.type.indexOf("touch")>-1?(o=(r=e).changedTouches[0].clientX,s=r.changedTouches[0].clientY):(o=e.clientX,s=e.clientY);var n=document.getElementById(t+"_scrollBar_svg"+i).getBoundingClientRect();this.mouseX=o-Math.max(n.left,0),this.mouseY=s-Math.max(n.top,0)},isExist:function(e,t){return e.indexOf(t)>-1},moveLength:function(e){var t=e.previousXY,i=e.previousRectX,o=e.scrollbarOptions[e.axis.name],s=.1*(o.width-16);return s=t<i?i-(i-s>8?s:8):i+(i+o.thumbRectWidth+s<o.width-8?s:8),s},isWithIn:function(e,t){var i=t.scrollbarOptions[t.axis.name];return e-8>=0&&e+i.thumbRectWidth+8<=i.width},positionThumb:function(e,t,i){var o=i.scrollbarOptions[i.axis.name];o.slider.setAttribute("x",e.toString()),o.slider.setAttribute("width",t.toString()),o.leftCircleEle.setAttribute("cx",e.toString()),o.rightCircleEle.setAttribute("cx",(e+t).toString()),this.setArrowDirection(i),o.gripCircle.setAttribute("transform","translate("+(e+t/2+5*(o.isVertical?1:-1))+","+(o.isVertical?"10":"5")+") rotate("+(o.isVertical?"180":"0")+")")},setZoomFactorPosition:function(e,t,i,o){void 0===o&&(o=!0);var s=e.axis,r=e.scrollbarOptions[s.name];r.isScrollUI=!0;var n=r.height/2,a=t+i+n+1,l=n+.5,h=r.isVertical?s.rect.h:r.width;r.zoomFactor=(i+(a>=r.width?n+1:0))/h,s.zoomFactor=o&&!r.isLazyLoad?r.zoomFactor:s.zoomFactor,r.zoomPosition=a>h?1-s.zoomFactor:t<n+1?0:(t-(t-l<=0?l:0))/h,s.zoomPosition=r.zoomPosition<0?0:r.zoomPosition>.9?1:r.zoomPosition,r.isLazyLoad||this.dotnetref.invokeMethodAsync("ChartScrolled",s.name,s.zoomFactor,s.zoomPosition)},calculateLazyRange:function(e,t,i){var o,s,r,n,a,l=e.scrollbarOptions[e.axis.name],h=l.thumbRectX,d=l.thumbRectWidth,c=l.scrollRange,u=this.getStartEnd(l.previousStart,l.previousEnd,!1,e);return e.isResizeRight||"RightMove"===t?(o=(d=e.isResizeRight?d+16:d)/l.width,s="RightMove"===t?(h+8)/l.width:e.axis.zoomPosition,n=(r="RightMove"===t?c.start+s*c.delta:l.previousStart)+o*c.delta):e.isResizeLeft||"LeftMove"===t?(s=(h-8)/l.width,o=d/l.width,r=(r=c.start+s*c.delta)>=c.start?r:c.start,n="LeftMove"===t?r+o*c.delta:l.previousEnd):(e.isThumbDrag||e.isScrollWheel)&&(s="RightMove"===t||i>0?(h+8)/l.width:(h-8)/l.width,o=l.thumbRectWidth/l.width,n=(r=(r=c.start+s*c.delta)>=c.start?r:c.start)+o*c.delta),n&&(a={axis:e.axis,currentRange:this.getStartEnd(r,n,!0,e),previousAxisRange:u}),a},getStartEnd:function(e,t,i,o){var s=o.scrollbarOptions[o.axis.name],r=s.valueType;switch("DateTime"!==r&&"DateTimeCategory"!==r||!i?i&&(s.previousStart=Math.round(e),s.previousEnd=Math.ceil(t)):(s.previousStart=e,s.previousEnd=t),r){case"Double":case"Category":case"Logarithmic":e=Math.round(e),t=Math.ceil(t);break;case"DateTime":case"DateTimeCategory":e=e,t=t}return{minimum:e,maximum:t}},setCursor:function(e,t,i){var o=window.sfBlazor.getCompInstance(t).scrollbarBase;if(o){var s=e.id;s&&o.scrollbarOptions[i]&&(o.scrollbarOptions[i].svgObject.style.cursor=s.indexOf("scrollBarThumb_")>-1||s.indexOf("_gripCircle")>-1?"-webkit-grab":s.indexOf("Circle_")>-1||s.indexOf("Arrow_")>-1?o.scrollbarOptions[i].isVertical?"ns-resize":"ew-resize":"auto")}},setTheme:function(e,t,i){var o=window.sfBlazor.getCompInstance(t).scrollbarBase,s=o.scrollbarOptions[i];if(s){var r=e.id,n=r.indexOf("_leftCircle_")>-1||r.indexOf("_leftArrow_")>-1,a=r.indexOf("_rightCircle_")>-1||r.indexOf("_rightArrow_")>-1,l=o.scrollbarThemeStyle,h=s.leftArrowEle,d=s.rightArrowEle,c=s.leftCircleEle,u=s.rightCircleEle,m=this.isCurrentAxis(e,h);c.style.fill=n&&m?l.circleHover:l.circle,u.style.fill=a&&m?l.circleHover:l.circle,c.style.stroke=n&&m?l.circleHover:l.circle,u.style.stroke=a&&m?l.circleHover:l.circle,"HighContrastLight"===o.theme&&(h.style.fill=n&&m?l.arrowHover:l.arrow,h.style.stroke=n&&m?l.arrowHover:l.arrow,d.style.fill=a&&m?l.arrowHover:l.arrow,d.style.stroke=a&&m?l.arrowHover:l.arrow,c.style.stroke=n&&m?l.circleHover:l.circle,u.style.stroke=a&&m?l.circleHover:l.circle)}},isCurrentAxis:function(e,t){return e.id.split("_")[2]===t.id.split("_")[2]},resizeThumb:function(e,t,i){var o,s=e.scrollbarOptions[e.axis.name].height/2,r=2*s+10+14,n=e.previousRectX,a=e.axis.isAxisInverse;this.mouseX=t,this.mouseY=i;var l=e.scrollbarOptions[e.axis.name],h=l.isVertical&&a?this.mouseY:l.isVertical?l.width-this.mouseY:a?l.width-this.mouseX:this.mouseX,d=Math.abs(e.previousXY-h);if(e.isResizeLeft&&h>=0){var c=n+(h>e.previousXY?d:-d);o=c-s>=0?e.previousWidth+(h>e.previousXY?-d:d):e.previousWidth,c=c-s>=0?c:n,o>=r&&h<c+o&&(l.thumbRectX=e.previousRectX=c,l.thumbRectWidth=e.previousWidth=o,e.previousXY=h,this.positionThumb(c,o,e),this.setZoomFactorPosition(e,c,o))}else e.isResizeRight&&(o=h>=r+l.thumbRectX&&h<=l.width-s?h-l.thumbRectX:e.previousWidth,l.thumbRectWidth=e.previousWidth=o,e.previousXY=h,this.positionThumb(l.startX,o,e),this.setZoomFactorPosition(e,l.startX,o),l.isLazyLoad||this.setZoomFactorPosition(e,l.startX,o))},setHighlightSelectionOptions:function(e,t){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.selectionHighlightOptions(t)},showTooltip:function(e,t,i,o){void 0===i&&(i=!1);var s=window.sfBlazor.getCompInstance(o);if(i)for(var r=0,n=s.userInteractionBase.visibleSeries;r<n.length;r++)for(var a=n[r],l=0,h=a.points;l<h.length;l++){var d=h[l],c="DateTime"==a.x_Axis.valueType?d.xV:d.x,u=e;if("DateTime"==a.x_Axis.valueType&&(u=new Date(u).getTime()),e==c&&t===d.yV){s.mouseX=d.r[0].x+a.clipRect.x,s.mouseY=d.r[0].y+a.clipRect.y,f.tooltipMousemovehandler(s),f.markerMove(s,!1);break}}else s.mouseX=e,s.mouseY=t,f.tooltipMousemovehandler(s),f.markerMove(s,!1)},hideTooltip:function(e){var t=window.sfBlazor.getCompInstance(e);f.removeTooltip(sf.base.Browser.isDevice?2e3:1e3,t)},showCrosshair:function(e,t,i){var o=window.sfBlazor.getCompInstance(i);o.mouseX=e,o.mouseY=t,f.crosshairMousemoveHandler(o)},hideCrosshair:function(e){var t=window.sfBlazor.getCompInstance(e);f.removeCrosshair(t,sf.base.Browser.isDevice?2e3:1e3)},updateZoomingOptions:function(e,t){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||(i.zoomBase.zoomingModule.isZoomed=t,i.zoomBase.zoomingModule.isPanning=t)},setTooltipData:function(e,t,i,o,s,r,n,a,l){var h=window.sfBlazor.getCompInstance(e);h.tooltipBase.tooltipModule&&!h.userInteractionBase.isStockChart&&(this.removeTooltip(sf.base.Browser.isDevice?1e3:100,h),this.removeCrosshair(h,sf.base.Browser.isDevice?1e3:100)),r&&(null!=h.tooltipBase.tooltipModule.template&&(r.template=h.tooltipBase.tooltipModule.template),h.tooltipBase.tooltipModule=r),h.dateValuePairs=n,h.numberValuePairs=a,h.userInteractionBase.chartStartMove=!1,h.getTooltipData(t,i,o,s),h.userInteractionBase.chartStartMove=!0,h.zoomBase.axisCollections=s,h.userInteractionBase.axisClipRect=l,h.zoomBase.zoomingModule&&(!this.isUpdatingClipRect&&h.userInteractionBase.isStockChart?(this.isUpdatingClipRect=!0,this.updateClipRect(this.getElement(h.element.id+h.zoomBase.clipRectId),h),this.isUpdatingClipRect=!1):h.userInteractionBase.isStockChart||this.updateClipRect(this.getElement(h.element.id+h.zoomBase.clipRectId),h)),h.isDataLoaded&&setTimeout((function(){h.removeTooltipData()}),500),this.applyZoomingToolkit(h)},setTooltipOptions:function(e,t,i,o,s,r,n,a,l){var h=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(h)||(h.dateValuePairs=a,h.numberValuePairs=l,h.userInteractionBase.chartStartMove=!1,h.tooltipOptions(t,i),h.zoomBase.axisCollections=n,sf.base.isNullOrUndefined(h.userInteractionBase.visibleSeries)&&h.getVisibleSeries(h,o,s,r,n),h.userInteractionBase.chartStartMove=!0,h.zoomBase.isResized&&h.zoomBase.zoomingModule&&(h.zoomBase.isResized=!1,this.updateClipRect(this.getElement(h.element.id+h.zoomBase.clipRectId),h)))},setZoomOptions:function(e,t){var i=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(i)){var o=i.userInteractionBase.theme;i.zoomBase.zoomSettings=t,i.zoomBase.zoomingModule=new u(i),i.zoomToolkitBase.selectionColor="Material3Dark"==o?"#CAC4D0":"Material3"==o?"#49454E":"Bootstrap5"==o||"Bootstrap5Dark"==o?"#0D6EFD":"Tailwind"==o?"#4F46E5":"TailwindDark"==o?"#22D3EE":"Material"==o?"#E3165B":"MaterialDark"==o?"#00B0FF":"Bootstrap4"==o?"#007BFF":"Fluent"==o||"FluentDark"==o?"#0078D4":"Bootstrap"==o?"#317AB9":"BootstrapDark"==o?"#0070F0":"Fabric"==o?"#0078D6":"FabricDark"==o?"#0074CC":"HighContrast"==o||"HighContrastLight"==o?"#FFD939":"Fluent2"==o?"#424242":"Fluent2Dark"==o?"#D6D6D6":i.zoomToolkitBase.selectionColor,i.zoomToolkitBase.iconRectOverFill="Bootstrap4"==o?"#5A6268":"Material3"==o?"#EADDFF":"Material3Dark"==o?"#4F378B":"Fluent2"==o?"#EBEBEB":"Fluent2Dark"==o?"#383838":i.zoomToolkitBase.iconRectOverFill,i.zoomToolkitBase.iconRectSelectionFill="Bootstrap4"==o?"#5B6269":"Material3"==o?"#EADDFF":"Material3Dark"==o?"#4F378B":"Fluent2"==o?"#EBEBEB":"Fluent2Dark"==o?"#383838":i.zoomToolkitBase.iconRectSelectionFill,i.zoomToolkitBase.selectedId=i.zoomBase.zoomingModule.isPanning?i.element.id+"_Zooming_Pan_1":i.element.id+"_Zooming_Zoom_1",i.zoomToolkitBase.fillColor="Bootstrap4"==o?"#495057":"Tailwind"==o?"#6B7280":"TailwindDark"==o?"D1D5DB":"Fluent"==o?"#A19F9D":"FluentDark"==o?"#484644":"Fluent2"==o?"#424242":"Fluent2Dark"==o?"#D6D6D6":"#737373",!this.getElement(i.element.id+i.zoomBase.clipRectId)&&i.zoomBase.zoomingModule&&this.createClipRect(i),this.applyZoomingToolkit(i)}},setUIBooleanValues:function(e,t,i,o){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||(s.userInteractionBase.isPointDragging=o,s.userInteractionBase.isPointMouseDown=t,s.userInteractionBase.disableTrackTooltip=i)},setTooltipArgsData:function(e,t,i){var o=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(o)||(o.tooltipBase.formattedText=[],o.tooltipBase.argsData.headerText=t,o.tooltipBase.argsData.text=i,this.seriesTooltip(o,o.tooltipBase.currentPoints[0],!0))},setSharedTooltipArgsData:function(e,t,i){var o=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(o)||(o.tooltipBase.sharedArgsData.headerText=t,o.tooltipBase.sharedArgsData.text=i,this.groupedTooltip(o,o.tooltipBase.currentPoints,!0,o.tooltipBase.lastData))},invokeBlurEffect:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||this.blurEffect(t.element.id,t)},invokeRemoveSelectedElements:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||(t.selectedDataIndexes=[],t.styleId=t.element.id+"_ej2_chart_selection",this.removeSelectedElements(e))},redrawSelection:function(e){var t=window.sfBlazor.getCompInstance(e);t.isSeriesMode="Series"==t.oldMode;var i=t.selectedDataIndexes,o=t.highlightDataIndexes;t.styleId.indexOf("highlight")>-1&&t.highlightDataIndexes.length>0?(t.highlightDataIndexes=[],this.removeSelectedElements(e),i=o):(t.selectedDataIndexes=[],this.removeSelectedElements(e)),this.blurEffect(t.element.id,t),this.selectDataIndex(t.dataId,i)},removeSelectionStyles:function(e){var t=window.sfBlazor.getCompInstance(e);t.highlightDataIndexes=[];for(var i=0;i<t.seriesTypes.length;i++)this.removeLegendSelectionClass(document.getElementsByClassName(t.unSelected),t),this.removeLegendSelectionClass(document.getElementsByClassName(this.generateStyle(t.element.id+"SeriesGroup"+i,t)),t),this.removeLegendSelectionClass(this.getSeriesElements(i,t),t);for(i=0;i<t.seriesTypes.length;i++)this.removeSvgClass(this.findDOMElement(t.element.id+"_chart_legend_shape_"+i),this.findDOMElement(t.element.id+"_chart_legend_shape_"+i).getAttribute("class"))},removeLegendSelectionClass:function(e,t){for(var i=0,o=e;i<o.length;i++){var s=o[i];sf.base.isNullOrUndefined(s)||(this.removeSvgClass(s,s.getAttribute("class")),sf.base.isNullOrUndefined(t.highlightColor)||"None"!=t.highlightPattern||s.setAttribute("fill",t.highlightColor))}},removeSelectedElements:function(e){for(var t=window.sfBlazor.getCompInstance(e),i=0;i<t.seriesTypes.length;i++)this.removeStyles(this.getSeriesElements(i,t),t)},selectionChart:function(e,t){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||(i.currentMode=i.selectionMode,i.styleId=i.element.id+"_ej2_chart_selection",i.isSeriesMode=!1,this.selection(i,t,this.findElements(i,"",t)))},selectDataIndex:function(e,t){var i=this,o=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(o)||(this.isAlreadySelected({type:"click"},o),t.forEach((function(e){var t="#"+o.element.id+"_Series_"+e.series+"_Point_"+e.point;i.performSelection(e,o,o.element.querySelector(t))})))},focusTarget:function(e){var t,i;if(e.indexOf("_chart_legend_")>-1){i=(t=this.getElement(e).parentElement).getAttribute("class");var o=f.getElement(this.id+"_ChartAreaClipRect_");o&&o.parentNode.parentNode.appendChild(o.parentNode);var s=this.getElement(this.id+"SeriesCollection").firstElementChild,r=window.sfBlazor.getCompInstance(this.dataId),n=s?this.getFocusedSeries(r,s.id):null;this.setTabIndex(this.getElement(this.id+"_chart_legend_translate_g").firstElementChild,n?this.getElement(n):null);var a=this.getElement(this.id+"TrendLineCollection").firstElementChild,l=a?this.getFocusedSeries(r,a.id):null;l&&this.getElement(l).setAttribute("tabindex","0"),t.setAttribute("tabindex","0"),i&&-1===i.indexOf("e-chart-focused")?i+=" e-chart-focused":i||(i="e-chart-focused")}else t=this.getElement(e),i="e-chart-focused";t.setAttribute("tabindex","0"),t.setAttribute("class",i),t.focus()},focusChild:function(e){e.setAttribute("tabindex","0");var t=e.getAttribute("class");return e.setAttribute("tabindex","0"),t&&-1===t.indexOf("e-chart-focused")?t+=" e-chart-focused":t||(t="e-chart-focused"),e.setAttribute("class",t),e.focus(),e.id},getCurrentSeries:function(e,t,i){var o,s=[];return e.userInteractionBase.visibleSeries.forEach((function(e){(t.indexOf("TrendLine")>-1&&"TrendLine"==e.category||-1==t.indexOf("TrendLine")&&"TrendLine"!=e.category&&"Indicator"!=e.category)&&s.push(e)})),s.forEach((function(e){e.index===i&&e.points.length>0&&(o=e)})),o},getFocusedSeries:function(e,t){if(t.indexOf("SeriesGroup")>-1){var i=+t.split("SeriesGroup")[1],o=this.getCurrentSeries(e,t,i);if(!o||o.focusable)return t;var s=[];e.userInteractionBase.visibleSeries.forEach((function(e){(t.indexOf("TrendLine")>-1&&"TrendLine"==e.category||-1==t.indexOf("TrendLine")&&"TrendLine"!=e.category&&"Indicator"!=e.category)&&s.push(e)}));for(var r=0;r<s.length;r++)if(s[r].focusable)return s[r].id}},getCurrentPointElement:function(e,t,i){for(var o=t.children[t.children.length-1].id.indexOf("_Connector_")>-1?t.children.length-1:t.children.length,s="ArrowDown"!=i?1:o-1;"ArrowDown"!=i?s<o:s>=1;"ArrowDown"!=i?s++:s--){var r=parseInt(t.children[s].id.split("_Series_")[1].replace("_Symbol","").split("_Point_")[1]);if("ArrowDown"!=i&&r>=e.currentPointIndex)return e.currentPointIndex=r,t.children[s];if("ArrowDown"==i&&r<=e.currentPointIndex)return e.currentPointIndex=r,t.children[s]}return e.currentPointIndex="ArrowUp"!=i?parseInt(t.children[o-1].id.split("_Series_")[1].replace("_Symbol","").split("_Point_")[1]):parseInt(t.children[1].id.split("_Series_")[1].replace("_Symbol","").split("_Point_")[1]),t.children["ArrowUp"!=i?o-1:1]},checkPointsLength:function(e){for(var t=0;t<e.points.length;t++)if(t!=e.points[t].iX)return!0;return!1},getCurrentPointIndex:function(e,t,i){for(var o="ArrowDown"!=i?0:t.points.length-1;"ArrowDown"!=i?o<t.points.length:o>=0;"ArrowDown"!=i?o++:o--){if("ArrowDown"!=i&&t.points[o].iX>=e.currentPointIndex)return e.currentPointIndex=t.points[o].iX,o;if("ArrowDown"==i&&t.points[o].iX<=e.currentPointIndex)return e.currentPointIndex=t.points[o].iX,o}return e.currentPointIndex="ArrowUp"!=i?t.points[t.points.length-1].iX:t.points[0].iX,"ArrowUp"!=i?t.points.length-1:0},getRectSeriesElement:function(e,t){for(var i=0;i<t.points.length;i++)if(t.points[i].iX>=e.currentPointIndex)return e.currentPointIndex=t.points[i].iX,this.getElement(this.id+"_Series_"+t.index+"_Point_"+e.currentPointIndex);return this.getElement(this.id+"_Series_"+t.index+"_Point_"+(t.points.length-1))},getActualIndex:function(e,t){return e>t-1?0:e<0?t-1:e},setTabIndex:function(e,t){e&&e.removeAttribute("tabindex"),t&&t.setAttribute("tabindex","0")},eventInterval:80,dotnetref:{},getScrollEventArgs:function(e,t){var i=e.changedTouches?e.changedTouches[0].clientX:e.clientX,o=e.changedTouches?e.changedTouches[0].clientY:e.clientY,s=this.setScrollMouseXY(i,o,e.target.id,t),r=e.touches,n=[];if(e.type.indexOf("touch")>-1)for(var a=0,l=r.length;a<l;a++)n.push({pageX:r[a].clientX,pageY:r[a].clientY,pointerId:e.pointerId||0});var h=e.target.id;return h=h.indexOf("scrollBar")>-1?h:this.svgId,{type:e.type,clientX:e.clientX,clientY:e.clientY,mouseX:s.mouseX,mouseY:s.mouseY,pointerType:e.pointerType,target:h,changedTouches:{clientX:e.changedTouches?e.changedTouches[0].clientX:0,clientY:e.changedTouches?e.changedTouches[0].clientY:0},touches:n,pointerId:e.pointerId}},getScrollWheelArgs:function(e){},svgId:null,setScrollMouseXY:function(e,t,i,o){this.svgId=o[1];var s=o[0];this.dotnetref=this.dotnetrefCollection.find((function(e){return s===e.id})).dotnetref;var r=e,n=t,a=this.getElement(this.svgId);if(!sf.base.isNullOrUndefined(a)){var l=a.getBoundingClientRect();r=e-Math.max(l.left,0),n=t-Math.max(l.top,0)}return{mouseX:r,mouseY:n}},dotnetrefCollection:[],tooltipMousemovehandler:function(e){e.userInteractionBase.isPointMouseDown?this.removeTooltip(e.tooltipBase.tooltipModule.fadeOutDuration,e):e.userInteractionBase.disableTrackTooltip||this.isSelected()||!e.tooltipBase.tooltipModule.enable||(!e.tooltipBase.tooltipModule.shared&&(!e.isTouch||e.userInteractionBase.startMove)||sf.svgbase.withInAreaBounds(e.mouseX,e.mouseY,e.userInteractionBase.axisClipRect)&&e.tooltipBase.tooltipModule.shared&&(!e.isTouch||e.userInteractionBase.startMove)?this.tooltip(e):e.tooltipBase.tooltipModule.shared&&e.tooltipBase.isSharedRemove&&this.removeTooltip(e.tooltipBase.tooltipModule.fadeOutDuration,e))},dragStart:function(){this.isDragSelection=!0,this.tooltipVisibility(!1)},dragRemove:function(){this.isDragSelection=!1,this.tooltipVisibility(!0)},tooltipVisibility:function(e){var t=document.querySelector("[id*='_tooltip']");t&&(t.style.visibility=e?"":"hidden")},markerMove:function(e,t){var i=this;if(e.userInteractionBase.isPointMouseDown)this.removeMarker(e);else{var o,s,r;if(!e.userInteractionBase.disableTrackTooltip&&(!e.isTouch||e.userInteractionBase.startMove)){if(e.tooltipBase.tooltipModule.shared&&e.tooltipBase.tooltipModule.enable){if(!sf.svgbase.withInAreaBounds(e.mouseX,e.mouseY,e.userInteractionBase.axisClipRect))return;if(e.tooltipBase.tooltipModule.enable){for(var n="PolarAxes"==e.userInteractionBase.chartAreaType?this.getData(e):null,a=0,l=e.userInteractionBase.visibleSeries;a<l.length;a++){var h=l[a];h.enableTooltip&&"Indicator"!=h.category&&h.visible&&h.marker.aH&&("CartesianAxes"==e.userInteractionBase.chartAreaType&&h.visible?o=this.getClosestX(e,h):"PolarAxes"==e.userInteractionBase.chartAreaType&&h.visible&&n.point&&(o={point:h.points[n.point.iX],series:h}),o&&!sf.base.isNullOrUndefined(o.point)&&(this.isSeriesAxisZoomed(h)&&o.point.s.length&&!this.isPointInRect(o.point.s,e.userInteractionBase.axisClipRect,o.point.r,e.userInteractionBase.isStockChart)||e.markerExplodeBase.markerCurrentPoints.push(o)))}e.markerExplodeBase.markerCurrentPoints=this.getSharedPoints(e,e.markerExplodeBase.markerCurrentPoints)}}else if((r=(o=this.getData(e)).series)&&r.marker.aH){var d=e.markerExplodeBase.markerPreviousPoints.length>0?e.markerExplodeBase.markerPreviousPoints[0]:null;s=!sf.base.isNullOrUndefined(r)&&("Bubble"==r.type||"Scatter"==r.drawType||"Scatter"==r.type||"Candle"!=r.type&&"Hilo"!=r.type&&"HiloOpenClose"!=r.type&&r.marker.vS&&0!=r.marker.mW&&0!=r.marker.mH),o.lierIndex=e.userInteractionBase.lierIndex,o.point&&s&&(null==d||d.point!=o.point||d.lierIndex>3&&d.lierIndex!=e.userInteractionBase.lierIndex)&&e.markerExplodeBase.markerCurrentPoints.push(o),o.point&&s&&e.userInteractionBase.isPointMouseDown&&e.markerExplodeBase.markerCurrentPoints.push(o)}if(e.markerExplodeBase.markerCurrentPoints.length>0&&(0==e.markerExplodeBase.markerPreviousPoints.length||e.userInteractionBase.isPointMouseDown||e.markerExplodeBase.markerPreviousPoints.length>0&&(e.tooltipBase.tooltipModule.showNearestPoint&&e.tooltipBase.tooltipModule.shared||e.markerExplodeBase.markerPreviousPoints[0].point!=e.markerExplodeBase.markerCurrentPoints[0].point))){e.markerExplodeBase.markerPreviousPoints.length>0&&this.removeMarker(e);for(var c=0;c<e.markerExplodeBase.markerCurrentPoints.length;c++){var u=(n=e.markerExplodeBase.markerCurrentPoints[c]).series,m=n.point;if(m.mK=m.mK?m.mK:{vS:!1},n&&m||r&&"Candle"!=r.type&&"Hilo"!=r.type&&"HiloOpenClose"!=r.type){window.clearTimeout(e.markerExplodeBase.markerExplodeInterval),e.markerExplodeBase.isRemove=!0;for(var p=0;p<m.s.length;p++)this.isRectSeries(u.type)&&!m.mK.vS||this.trackBall(e,u,m,m.s[p],p)}}e.markerExplodeBase.markerPreviousPoints=[],e.markerExplodeBase.markerPreviousPoints=e.markerExplodeBase.markerPreviousPoints.concat(e.markerExplodeBase.markerCurrentPoints)}!e.tooltipBase.tooltipModule.enable&&(0==e.markerExplodeBase.markerCurrentPoints.length&&e.markerExplodeBase.isRemove||t&&e.markerExplodeBase.isRemove||e.userInteractionBase.axisClipRect&&!sf.svgbase.withInAreaBounds(e.mouseX,e.mouseY,e.userInteractionBase.axisClipRect))&&(e.markerExplodeBase.isRemove=!1,e.markerExplodeBase.markerExplodeInterval=+setTimeout((function(){i.removeMarker(e)}),2e3))}e.markerExplodeBase.markerCurrentPoints=[]}},trackBall:function(e,t,i,o,s,r){for(var n=i.mK,a=t.marker,l=e.element.id+"_Series_"+t.index+"_Point_"+i.iX+"_Trackball"+(s&&0!=s?"_"+s:""),h={width:(n.mW?n.mW:a.mW)+3,height:(n.mH?n.mH:a.mH)+3},d=n.b||t.border,c=d.cL&&"transparent"!=d.cL?d.cL:n.f?n.f:i.i?i.i:t.interior,u=this.convertHexToColor(this.colorNameToHex(c)),m=n.b?n.b.wT:a.b.wT,p="rgba("+u.r+","+u.g+","+u.b+",0.2)",g="CartesianAxes"==e.userInteractionBase.chartAreaType?"translate("+t.clipRect.x+","+t.clipRect.y+")":"",f="Bubble"==t.type||"Scatter"==t.type?"url(#"+e.clipPathID(t.index)+")":"url(#"+e.markerClipPathId(t.index)+")",v=n.sH?n.sH:a.sH,b=0;b<2;b++){var x=new sf.svgbase.PathOption(l+"_"+b,b>0?n.f||i.i||(r?t.interior:"#ffffff"):"transparent",m+(b>0?0:8),b>0?c:p,n.oP||a.oP,"",null),B=this.calculateShapes(o,h,v,x,a.iU,!1);this.drawTrackBall(e.element.id+"_svg",B.renderOption,B.functionName,e.markerExplodeBase.trackBallClass,f,g)}},colorNameToHex:function(e){e="transparent"===e?"white":e,document.body.appendChild(sf.base.createElement("text",{id:"chartmeasuretext"}));var t=document.getElementById("chartmeasuretext");t.style.color=e,e=window.getComputedStyle(t).color,sf.base.remove(t);var i=/^(rgb|hsl)(a?)[(]\s*([\d.]+\s*%?)\s*,\s*([\d.]+\s*%?)\s*,\s*([\d.]+\s*%?)\s*(?:,\s*([\d.]+)\s*)?[)]$/.exec(e);return this.convertToHexCode(new r(parseInt(i[3],10),parseInt(i[4],10),parseInt(i[5],10)))},convertToHexCode:function(e){return"#"+this.componentToHex(e.r)+this.componentToHex(e.g)+this.componentToHex(e.b)},componentToHex:function(e){var t=e.toString(16);return 1===t.length?"0"+t:t},convertHexToColor:function(e){var t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?new r(parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)):new r(255,255,255)},calculateShapes:function(e,t,i,o,s,r){var n,a,l,h="path",d=r,c=d&&"Circle"===i?t.width-2:t.width,u=d&&"Circle"===i?t.height-2:t.height,m=e.x,p=e.y,g=e.y+-u/2,f=e.x+-c/2;switch(i){case"Bubble":case"Circle":h="ellipse",sf.base.merge(o,{d:"",rx:c/2,ry:u/2,cx:m,cy:p});break;case"Cross":n="M "+f+" "+(p+-u/2)+" L "+(m+c/2)+" "+(p+u/2)+" M "+f+" "+(p+u/2)+" L "+(m+c/2)+" "+(p+-u/2),sf.base.merge(o,{d:n});break;case"Multiply":n="M "+(m-0)+" "+(p-0)+" L "+(m+0)+" "+(p+0)+" M "+(m-0)+" "+(p+0)+" L "+(m+0)+" "+(p-0),sf.base.merge(o,{d:n,stroke:o.fill});break;case"HorizontalLine":n="M "+f+" "+p+" L "+(m+c/2)+" "+p,sf.base.merge(o,{d:n});break;case"VerticalLine":n="M "+m+" "+(p+u/2)+" L "+m+" "+(p+-u/2),sf.base.merge(o,{d:n});break;case"Diamond":n="M "+f+" "+p+" L "+m+" "+(p+-u/2)+" L "+(m+c/2)+" "+p+" L "+m+" "+(p+u/2)+" L "+f+" "+p+" z",sf.base.merge(o,{d:n});break;case"ActualRect":n="M "+f+" "+(p+-u/8)+" L "+(m+0)+" "+(p+-u/8)+" L "+(m+0)+" "+(p+u/8)+" L "+f+" "+(p+u/8)+" L "+f+" "+(p+-u/8)+" z",sf.base.merge(o,{d:n});break;case"TargetRect":n="M "+(f+0)+" "+(p+-u/2)+" L "+(m+0)+" "+(p+-u/2)+" L "+(m+0)+" "+(p+u/2)+" L "+(f+0)+" "+(p+u/2)+" L "+(f+0)+" "+(p+-u/2)+" z",sf.base.merge(o,{d:n});break;case"Rectangle":case"Hilo":case"HiloOpenClose":case"Candle":case"Waterfall":case"BoxAndWhisker":case"StepArea":case"StackingStepArea":case"Square":case"Flag":n="M "+f+" "+(p+-u/2)+" L "+(m+c/2)+" "+(p+-u/2)+" L "+(m+c/2)+" "+(p+u/2)+" L "+f+" "+(p+u/2)+" L "+f+" "+(p+-u/2)+" z",sf.base.merge(o,{d:n});break;case"Pyramid":case"Triangle":n="M "+f+" "+(p+u/2)+" L "+m+" "+(p+-u/2)+" L "+(m+c/2)+" "+(p+u/2)+" L "+f+" "+(p+u/2)+" z",sf.base.merge(o,{d:n});break;case"Funnel":case"InvertedTriangle":n="M "+(m+c/2)+" "+(p-u/2)+" L "+m+" "+(p+u/2)+" L "+(m-c/2)+" "+(p-u/2)+" L "+(m+c/2)+" "+(p-u/2)+" z",sf.base.merge(o,{d:n});break;case"Pentagon":for(var v=0;v<=5;v++)a=c/2*Math.cos(Math.PI/180*(72*v)),l=u/2*Math.sin(Math.PI/180*(72*v)),n=0===v?"M "+(m+a)+" "+(p+l)+" ":n.concat("L "+(m+a)+" "+(p+l)+" ");n=n.concat("Z"),sf.base.merge(o,{d:n});break;case"Plus":n="M "+f+" "+p+" L "+(m+c/2)+" "+p+" M "+m+" "+(p+u/2)+" L "+m+" "+(p+-u/2),sf.base.merge(o,{d:n});break;case"Image":h="Image",sf.base.merge(o,{href:s,height:u,width:c,x:f,y:g})}return{renderOption:o,functionName:h}},removeMarker:function(e){this.removeHighLightedMarker(e),e.markerExplodeBase.markerPreviousPoints=[]},crosshairMousemoveHandler:function(e){!e.userInteractionBase.disableTrackTooltip&&e.crosshairBase.crosshair.enable&&(!sf.svgbase.withInAreaBounds(e.mouseX,e.mouseY,e.userInteractionBase.axisClipRect)||e.isTouch&&!e.userInteractionBase.startMove?this.removeCrosshair(e,1e3):this.createCrosshair(e))},removeCrosshair:function(e,t){var i=this.getElement(e.element.id+"_UserInteraction"),o=this.getElement(e.element.id+"_crosshair_axis");this.stopAnimation(e.crosshairBase.crosshairInterval),!e.userInteractionBase.isFirstRendered&&i&&"0"!==i.getAttribute("opacity")&&(e.crosshairBase.crosshairInterval=+setTimeout((function(){new sf.base.Animation({}).animate(i,{duration:200,progress:function(e){if(i.style.animation="",i.setAttribute("opacity",(1-e.timeStamp/e.duration).toString()),o)for(;o.firstChild;)o.removeChild(o.firstChild)},end:function(){i.setAttribute("opacity","0"),e.tooltipBase.tooltipModule&&(e.tooltipBase.valueX=null,e.tooltipBase.valueY=null)}})}),t))},createCrosshair:function(e){var t,i=e.userInteractionBase.axisClipRect,o="",s="",r=e.crosshairBase.crosshair,n=document.getElementById(e.element.id+"_UserInteraction");if(window.clearTimeout(e.crosshairBase.crosshairInterval),(!e.tooltipBase.tooltipModule.enable||sf.svgbase.withInAreaBounds(e.tooltipBase.valueX,e.tooltipBase.valueY,i))&&(e.crosshairBase.crosshairX=e.tooltipBase.tooltipModule.enable&&!e.userInteractionBase.isInverted?e.tooltipBase.valueX:e.mouseX,e.crosshairBase.crosshairY=e.tooltipBase.tooltipModule.enable&&e.userInteractionBase.isInverted?e.tooltipBase.valueY:e.mouseY,e.userInteractionBase.isFirstRendered?(n.setAttribute("opacity","1"),n.setAttribute("style","pointer-events:none"),e.userInteractionBase.isFirstRendered=!1):n.setAttribute("opacity","1"),("Both"===r.lineType||"Horizontal"===r.lineType||e.userInteractionBase.axes[1].crosshairTooltip.enable)&&(o+="M "+i.x+" "+e.crosshairBase.crosshairY+" L "+(i.x+i.width)+" "+e.crosshairBase.crosshairY),("Both"===r.lineType||"Vertical"===r.lineType||e.userInteractionBase.axes[0].crosshairTooltip.enable)&&(s+="M "+e.crosshairBase.crosshairX+" "+i.y+" L "+e.crosshairBase.crosshairX+" "+(i.y+i.height)),0!=n.childNodes.length)){t=new sf.svgbase.PathOption("_HorizontalLine","none",r.line.width,r.line.color||e.crosshairBase.themeStyleCrosshairLine,null,r.dashArray,o),this.renderCrosshairLine(t,n.childNodes[0]),t=new sf.svgbase.PathOption("_VerticalLine","none",r.line.width,r.line.color||e.crosshairBase.themeStyleCrosshairLine,null,r.dashArray,s);var a={AxisInfo:[]};this.renderCrosshairLine(t,n.childNodes[3]),this.renderAxisTooltip(e,i,n.childNodes[6],a),e.tooltipBase.crosshairMouseMoveEventCalled&&e.dotnetref.invokeMethodAsync("OnCrosshairMove",a)}},renderAxisTooltip:function(e,t,i,o){var s,r,n,a,l,h,d,c={text:"",value:null};if(i)for(;i.firstChild;)i.removeChild(i.firstChild);for(var u=e.userInteractionBase.axes.length-1;u>=0;u--)if(d=(s=e.userInteractionBase.axes[u]).placeNextToAxisLine?s.updatedRect:s.rect,s.crosshairTooltip.enable&&(e.crosshairBase.crosshairX<=d.x+d.w&&d.x<=e.crosshairBase.crosshairX&&0!=d.w||e.crosshairBase.crosshairY<=d.y+d.h&&d.y<=e.crosshairBase.crosshairY&&0!=d.h)){if(n=document.getElementById(e.element.id+"_axis_tooltip_"+u),a=document.getElementById(e.element.id+"_axis_tooltip_text_"+u),!(c=this.getAxisText(e,s)).text)continue;sf.base.isNullOrUndefined(o)||o.AxisInfo.push({AxisName:s.name,AxisLabel:c.text,Value:c.value}),r=this.tooltipLocation(e,c.text,s,t,new sf.svgbase.Rect(d.x,d.y,d.w,d.h)),null===n&&(n=e.userInteractionBase.svgRenderer.drawPath({id:e.element.id+"_axis_tooltip_"+u,fill:s.crosshairTooltip.fill||e.crosshairBase.themeStyleCrosshairFill})),a&&this.removeElement(a.id),i.appendChild(n),l=new sf.svgbase.TextOption(e.element.id+"_axis_tooltip_text_"+u,0,0,"start",c.text);var m={size:s.crosshairTooltip.textStyle.size||e.crosshairBase.themeStyleCrosshairTextSize,color:s.crosshairTooltip.textStyle.color||e.crosshairBase.themeStyleCrosshairLabel,fontFamily:s.crosshairTooltip.textStyle.fontFamily||e.crosshairBase.themeStyleCrosshairFontFamily,fontStyle:s.crosshairTooltip.textStyle.fontStyle,fontWeight:s.crosshairTooltip.textStyle.fontWeight||e.crosshairBase.themeStyleCrosshairFontWeight,opacity:s.crosshairTooltip.textStyle.opacity};if(a=sf.svgbase.textElement(l,m,m.color,i),h=this.findCrosshairDirection(e.crosshairBase.rx,e.crosshairBase.ry,r,e.crosshairBase.arrowLocation,8,e.crosshairBase.isTop,e.crosshairBase.isBottom,e.crosshairBase.isLeft,e.crosshairBase.crosshairX,e.crosshairBase.crosshairY),n.setAttribute("d",h),a.textContent=c.text,a.setAttribute("x",(r.x+5+(e.userInteractionBase.enableRTL?e.crosshairBase.elementSize.width:0)).toString()),a.setAttribute("y",(r.y+5+3*e.crosshairBase.elementSize.height/4).toString()),"Fluent"===e.userInteractionBase.theme||"FluentDark"===e.userInteractionBase.theme||"Fabric"===e.userInteractionBase.theme){n.setAttribute("stroke","#cccccc"),n.setAttribute("stroke-width","0.5")}else if(e.userInteractionBase.theme.indexOf("Fluent2")>-1){var p=e.element.id+"_shadow";n.setAttribute("box-shadow","0px 1.6px 3.6px 0px #00000021, 0px 0.3px 0.9px 0px #0000001A"),n.setAttribute("filter","url(#"+p+")");var g='<filter id="'+p+'" height="130%"><feGaussianBlur in="SourceAlpha" stdDeviation="3"/>';g+='<feOffset dx="-1" dy="3.6" result="offsetblur"/><feComponentTransfer><feFuncA type="linear" slope="0.2"/>',g+='</feComponentTransfer><feMerge><feMergeNode/><feMergeNode in="SourceGraphic"/></feMerge></filter>';var f=e.userInteractionBase.svgRenderer.createDefs();f.setAttribute("id",e.element.id+"SVG_tooltip_definition"),n.appendChild(f),f.innerHTML=g}}},getAxisText:function(e,t){var i,o={text:"",value:null};e.crosshairBase.isBottom=!1,e.crosshairBase.isTop=!1,e.crosshairBase.isLeft=!1,e.crosshairBase.isRight=!1;var s="Category"===t.valueType&&"BetweenTicks"===t.labelPlacement?.5:0,r=t.isAxisOppositePosition;if("Horizontal"===t.orientation?(i=this.getValueByPoint(Math.abs(e.crosshairBase.crosshairX-t.rect.x),t.rect.w,t.orientation,t.visibleRange,t.isAxisInverse)+s,e.crosshairBase.isBottom=!r,e.crosshairBase.isTop=r):(i=this.getValueByPoint(Math.abs(e.crosshairBase.crosshairY-t.rect.y),t.rect.h,t.orientation,t.visibleRange,t.isAxisInverse)+s,e.crosshairBase.isRight=r,e.crosshairBase.isLeft=!r),"DateTime"===t.valueType){var n=(new sf.base.Internationalization).getDateFormat({format:t.format?t.format:t.dateFormat,type:this.firstToLowerCase("DateTime")})(this.convertDateAndTime(new Date(i)));return o.text=this.getGlobalizedDate(n,e.dateValuePairs),o.value=new Date(i),o}if("Category"===t.valueType)return o.text=i<t.labels.length?t.labels[Math.floor(i)]:"",o.value=Math.floor(i),o;if("DateTimeCategory"===t.valueType)return o.text=this.getIndexedAxisLabel(t.labels[Math.round(i)],this.customFormat(t,e),e.dateValuePairs),o.value=Math.floor(i),o;if("Logarithmic"===t.valueType){var a=this.formatAxisValue(Math.pow(t.logBase,i),this.getaxisFormat(t).indexOf("{value}")>-1,t.labelFormat,void 0,e);return o.text=this.getGlobalizedNumber(a,e.numberValuePairs),o.value=Number(Math.pow(t.logBase,i)),o}var l=t.labelFormat&&null!==t.labelFormat.match("{value}");a=l?t.labelFormat.replace("{value}",this.formatAxisValue(i,l,t.labelFormat,3,e)):this.formatAxisValue(i,l,t.labelFormat,3,e);return o.text=this.getGlobalizedNumber(a,e.numberValuePairs),o.value=i,o},convertDateAndTime:function(e){return new Date(e.getTime()+60*e.getTimezoneOffset()*1e3)},getGlobalizedDate:function(e,t){return Object.keys(t).forEach((function(i){e=e.replaceAll?e.replaceAll(i,t[i]):e.replace(i,t[i])})),e},getGlobalizedNumber:function(e,t){for(var i=e.split(""),o=Object.keys(t),s=0,r=e.length;s<r;s++)for(var n=0,a=o.length;n<a;n++)if(i[s]==o[n]){i.splice(s,1,t[o[n]]);break}return i.join("")},firstToLowerCase:function(e){return e.substr(0,1).toLowerCase()+e.substr(1)},getaxisFormat:function(e){return e.labelFormat?0!=e.labelFormat.indexOf("p")||e.labelFormat.indexOf("{value}")>-1||!e.isStack100?e.labelFormat:"{value}%":e.isStack100?"{value}%":""},getIndexedAxisLabel:function(e,t,i){for(var o=e.split(","),s=0;s<o.length;s++)o[s]=this.getGlobalizedDate((new sf.base.Internationalization).getDateFormat({format:t})(this.convertDateAndTime(new Date(parseInt(o[s],10)))),i);return o.join(", ")},customFormat:function(e,t){return e.labelFormat?e.labelFormat:"Years"===e.actualIntervalType?"yyyy":this.getSkeleton(e,t)},getSkeleton:function(e,t){var i=e.rangeIntervalType;return e.format?e.format:"Years"===i||"Quarter"===i?"y":"Months"===i||"Weeks"===i?t.userInteractionBase.isStockChart&&"DateTimeCategory"===e.valueType?"MMM d":"MMMM d":"Days"===i?"MM/dd/yyyy":"Hours"===i?"HH:mm tt":"HH:mm:ss tt"},tooltipLocation:function(e,t,i,o,r){var n,a=o.x,l=o.y,h="Inside"===i.labelPosition,d=i.scrollbarSettingsEnable?i.scrollBarHeight:0,c=i.crosshairTooltip.textStyle;c.size=c.size||e.crosshairBase.themeStyleCrosshairTextSize,c.fontFamily=c.fontFamily||e.crosshairBase.themeStyleCrosshairFontFamily,c.fontWeight=c.fontWeight||e.crosshairBase.themeStyleCrosshairFontWeight,e.crosshairBase.elementSize=sf.svgbase.measureText(t,c);var u=i.isAxisOppositePosition;if("Horizontal"===i.orientation){var m=h?r.y-e.crosshairBase.elementSize.height-20:r.y+d,p=h?r.y-e.crosshairBase.elementSize.height-10:r.y+10;e.crosshairBase.arrowLocation=new s(e.crosshairBase.crosshairX,m),n=new sf.svgbase.Rect(e.crosshairBase.crosshairX-e.crosshairBase.elementSize.width/2-5,p+(h?0:d),e.crosshairBase.elementSize.width+10,e.crosshairBase.elementSize.height+10),u&&(n.y=h?r.y:r.y-(e.crosshairBase.elementSize.height+20)-d),n.x<a&&(n.x=a),n.x+n.width>a+o.width&&(n.x-=n.x+n.width-(a+o.width)),e.crosshairBase.arrowLocation.x+4>n.x+n.width-e.crosshairBase.rx&&(e.crosshairBase.arrowLocation.x=n.x+n.width-e.crosshairBase.rx-8),e.crosshairBase.arrowLocation.x-8<n.x+e.crosshairBase.rx&&(e.crosshairBase.arrowLocation.x=n.x+e.crosshairBase.rx+8)}else{d*=u?1:-1,e.crosshairBase.arrowLocation=new s(r.x,e.crosshairBase.crosshairY);var g=h?r.x-d:r.x-e.crosshairBase.elementSize.width-20;n=new sf.svgbase.Rect(g+d,e.crosshairBase.crosshairY-e.crosshairBase.elementSize.height/2-5,e.crosshairBase.elementSize.width+10,e.crosshairBase.elementSize.height+10),u?(n.x=h?r.x-e.crosshairBase.elementSize.width-8:r.x+8+d,n.x+n.width>e.userInteractionBase.availableSize.width&&(e.crosshairBase.arrowLocation.x-=n.x+n.width-e.userInteractionBase.availableSize.width,n.x-=n.x+n.width-e.userInteractionBase.availableSize.width)):n.x<0&&(e.crosshairBase.arrowLocation.x-=n.x,n.x=0),n.y<l&&(n.y=l),n.y+n.height>=l+o.height&&(n.y-=n.y+n.height-(l+o.height)),e.crosshairBase.arrowLocation.y+4>n.y+n.height-e.crosshairBase.ry&&(e.crosshairBase.arrowLocation.y=n.y+n.height-e.crosshairBase.ry-4),e.crosshairBase.arrowLocation.y-4<n.y+e.crosshairBase.ry&&(e.crosshairBase.arrowLocation.y=n.y+e.crosshairBase.ry+4)}return n},findCrosshairDirection:function(e,t,i,o,s,r,n,a,l,h){var d="",c=i.x,u=i.y,m=i.x+i.width,p=i.y+i.height;return r?(d=(d=(d=d.concat("M "+c+" "+(u+t)+" Q "+c+" "+u+" "+(c+e)+" "+u)).concat(" L "+(m-e)+" "+u+" Q "+m+" "+u+" "+m+" "+(u+t))).concat(" L "+m+" "+(p-t)+" Q "+m+" "+p+" "+(m-e)+" "+p),0!==s&&(d=(d=d.concat(" L "+(o.x+s/2)+" "+p)).concat(" L "+l+" "+(p+s)+" L "+(o.x-s/2)+" "+p)),d=o.x-s/2>c||0===s?d.concat(" L "+(c+e)+" "+p+" Q "+c+" "+p+" "+c+" "+(p-t)+" z"):d.concat(" L "+c+" "+(p+t)+" z")):d=n?(d=(d=(d=(d=(d=d.concat("M "+c+" "+(u+t)+" Q "+c+" "+u+" "+(c+e)+" "+u+" L "+(o.x-s/2)+" "+u)).concat(" L "+l+" "+o.y)).concat(" L "+(o.x+s/2)+" "+u)).concat(" L "+(m-e)+" "+u+" Q "+m+" "+u+" "+m+" "+(u+t))).concat(" L "+m+" "+(p-t)+" Q "+m+" "+p+" "+(m-e)+" "+p)).concat(" L "+(c+e)+" "+p+" Q "+c+" "+p+" "+c+" "+(p-t)+" z"):a?(d=(d=(d=(d=(d=d.concat("M "+c+" "+(u+t)+" Q "+c+" "+u+" "+(c+e)+" "+u)).concat(" L "+(m-e)+" "+u+" Q "+m+" "+u+" "+m+" "+(u+t)+" L "+m+" "+(o.y-s/2))).concat(" L "+(m+s)+" "+h)).concat(" L "+m+" "+(o.y+s/2))).concat(" L "+m+" "+(p-t)+" Q "+m+" "+p+" "+(m-e)+" "+p)).concat(" L "+(c+e)+" "+p+" Q "+c+" "+p+" "+c+" "+(p-t)+" z"):(d=(d=(d=(d=(d=d.concat("M "+(c+e)+" "+u+" Q "+c+" "+u+" "+c+" "+(u+t)+" L "+c+" "+(o.y-s/2))).concat(" L "+(c-s)+" "+h)).concat(" L "+c+" "+(o.y+s/2))).concat(" L "+c+" "+(p-t)+" Q "+c+" "+p+" "+(c+e)+" "+p)).concat(" L "+(m-e)+" "+p+" Q "+m+" "+p+" "+m+" "+(p-t))).concat(" L "+m+" "+(u+t)+" Q "+m+" "+u+" "+(m-e)+" "+u+" z"),d},renderCrosshairLine:function(e,t){for(var i=Object.keys(e),o="",s=0;s<i.length;s++)"id"!=(o="strokeWidth"===i[s]?"stroke-width":"strokeDashArray"===i[s]?"stroke-dashArray":"direction"===i[s]?"d":i[s])&&t.setAttribute(o,e[i[s]])},isSelected:function(){return!1},getTooltipElement:function(e){e.tooltipBase.header=null===e.tooltipBase.tooltipModule.header?e.tooltipBase.tooltipModule.shared?"<b>${point.x}</b>":"<b>${series.name}</b>":e.getTooltipFormat(e.tooltipBase.tooltipModule.header),e.tooltipBase.formattedText=[]},findData:function(e,t,i){return e.point&&(!t||t.point!==e.point||t.lierIndex>3&&t.lierIndex!=i||t.point===e.point)},renderSeriesTooltip:function(e,t){var i=this.getData(t);if(i.lierIndex=t.userInteractionBase.lierIndex,t.tooltipBase.currentPoints=[],this.findData(i,t.tooltipBase.previousPoints.length>0?t.tooltipBase.previousPoints[0]:null,t.userInteractionBase.lierIndex)){if(window.clearTimeout(t.tooltipBase.toolTipInterval),!i.series.dataEditSettings&&t.tooltipBase.previousPoints[0]&&i.point.iX===t.tooltipBase.previousPoints[0].point.iX&&i.series.index===t.tooltipBase.previousPoints[0].series.index)return t.tooltipBase.isRemove=!0,null;this.pushData(i,t)?this.triggerTooltipRender(i,e,this.getTooltipText(i,t),this.findHeader(i,t),t):this.removeTooltip(t.tooltipBase.tooltipModule.fadeOutDuration,t)}else if(!i.point&&t.tooltipBase.isRemove)this.removeTooltip(t.tooltipBase.tooltipModule.fadeOutDuration,t),t.tooltipBase.isRemove=!1;else for(var o=0,s=t.userInteractionBase.visibleSeries;o<s.length;o++){var r=s[o];r.visible&&"TrendLine"!==r.category&&(i=this.getClosestX(t,r)||i)}i&&i.point&&this.findMouseValue(i,t)},triggerTooltipRender:function(e,t,i,o,s){s.tooltipBase.argsData={data:{pointX:sf.base.isNullOrUndefined(e.point.x)?"":e.point.x.toString(),pointY:sf.base.isNullOrUndefined(e.point.y)?"":e.point.y.toString(),seriesIndex:e.series.index,seriesName:e.series.name,pointIndex:e.point.iX,pointText:e.point.t},headerText:o,point:e.point,series:{},text:i},s.tooltipBase.tooltipEventCalled?s.dotnetref.invokeMethodAsync("TooltipEventTriggered",s.tooltipBase.argsData):this.seriesTooltip(s,e,t)},seriesTooltip:function(e,t,i){var o=[];if(null!=t){"BoxAndWhisker"===t.series.type&&(i=!0),e.tooltipBase.header=e.tooltipBase.argsData.headerText,e.tooltipBase.formattedText=e.tooltipBase.formattedText.concat(e.tooltipBase.argsData.text);var r=new s(t.series.clipRect.x,t.series.clipRect.y);if(null!=e.tooltipBase.tooltipModule.template&&e.tooltipBase.currentPoints.length>0){e.tooltipBase.tooltipTempList=[];var n=t.point,a={x:sf.base.isNullOrUndefined(n.x)?"":this.formatPointValue(n,"x",!0,!1,t.series,e.userInteractionBase.isStockChart,e),y:sf.base.isNullOrUndefined(n.y)?"":this.formatPointValue(n,"y",!1,!0,t.series,e.userInteractionBase.isStockChart,e),text:n.tT.toString(),high:n.h?this.formatPointValue(n,"h",!1,!0,t.series,e.userInteractionBase.isStockChart,e):"",low:n.l?this.formatPointValue(n,"l",!1,!0,t.series,e.userInteractionBase.isStockChart,e):"",open:n.o?this.formatPointValue(n,"o",!1,!0,t.series,e.userInteractionBase.isStockChart,e):"",close:n.c?this.formatPointValue(n,"c",!1,!0,t.series,e.userInteractionBase.isStockChart,e):"",volume:n.v?this.formatPointValue(n,"v",!1,!0,t.series,e.userInteractionBase.isStockChart,e):"",pointX:n.x?n.x.toString():"",pointY:n.y?n.y.toString():"",seriesIndex:t.series.index,seriesName:t.series.name,pointIndex:t.point.iX,pointText:t.point.t};e.tooltipBase.tooltipTempList.push(a);var l=this.getSymbolLocation(t,e),h=this.isRectSeries(t.series.type)&&"Waterfall"!=t.series.type&&t.point&&parseInt(n.y?n.y.toString():"")<0,d=e.userInteractionBase.isInverted&&h,c=this.getTemplateLocation(e.userInteractionBase.axisClipRect,l,e.tooltipBase.tooltipElementSize.width,e.tooltipBase.tooltipElementSize.height,this.findMarkerHeight(e.tooltipBase.currentPoints[0],e),r,d,h),u=40;c.x=c.x>e.userInteractionBase.axisClipRect.width?e.userInteractionBase.axisClipRect.width-u:c.x,e.dotnetref.invokeMethodAsync("SetTooltipTemplateElementSizeAsync",c.x,c.y,e.tooltipBase.tooltipTempList),this.removeHighlight(e),this.highlightPoints(e),this.updatePreviousPoint(e,o)}else{var m=e.userInteractionBase.chartBorderWidth;u=3;this.createTooltipRenderer(e,i,this.getSymbolLocation(t,e),r,t.point,this.findShapes(e),this.findMarkerHeight(e.tooltipBase.currentPoints[0],e),new sf.svgbase.Rect(m,m,e.userInteractionBase.availableSize.width-u-2*m,e.userInteractionBase.availableSize.height-u-2*m),e.crosshairBase.crosshair.enable,o,null,"")}}else this.removeHighlight(e);e.tooltipBase.isRemove=!0},groupedTooltip:function(e,t,i,o){var r=t[t.length-1];if(null!=o&&e.tooltipBase.currentPoints.length>0){var n=[];e.tooltipBase.header=e.tooltipBase.sharedArgsData.headerText,e.tooltipBase.formattedText=e.tooltipBase.sharedArgsData.text,this.findMouseValue(o,e);var a=1==e.tooltipBase.currentPoints.length?new s(e.tooltipBase.currentPoints[0].series.clipRect.x,e.tooltipBase.currentPoints[0].series.clipRect.y):new s(0,0);if(null!=e.tooltipBase.tooltipModule.template&&e.tooltipBase.currentPoints.length>0){e.tooltipBase.tooltipTempList=[];for(var l=0;l<t.length;l++){var h=t[l].point,d={x:sf.base.isNullOrUndefined(h.x)?"":this.formatPointValue(h,"x",!0,!1,t[l].series,e.userInteractionBase.isStockChart,e),y:sf.base.isNullOrUndefined(h.y)?"":this.formatPointValue(h,"y",!1,!0,t[l].series,e.userInteractionBase.isStockChart,e),text:h.t.toString(),high:h.h?this.formatPointValue(h,"h",!1,!0,t[l].series,e.userInteractionBase.isStockChart,e):"",low:h.l?this.formatPointValue(h,"l",!1,!0,t[l].series,e.userInteractionBase.isStockChart,e):"",open:h.o?this.formatPointValue(h,"o",!1,!0,t[l].series,e.userInteractionBase.isStockChart,e):"",close:h.c?this.formatPointValue(h,"c",!1,!0,t[l].series,e.userInteractionBase.isStockChart,e):"",volume:h.v?this.formatPointValue(h,"v",!1,!0,t[l].series,e.userInteractionBase.isStockChart,e):"",pointX:h.x?h.x.toString():"",pointY:h.y?h.y.toString():"",seriesIndex:t[l].series.index,seriesName:t[l].series.name,pointIndex:t[l].point.iX,pointText:t[l].point.t};e.tooltipBase.tooltipTempList.push(d)}var c=this.findSharedLocation(e),u=e.tooltipBase.currentPoints[0].series,m=this.isRectSeries(u.type)&&"Waterfall"!=u.type&&r.point&&parseInt(r.point.y?r.point.y.toString():"")<0,p=e.userInteractionBase.isInverted&&m,g=this.getTemplateLocation(e.userInteractionBase.axisClipRect,c,0,0,this.findMarkerHeight(e.tooltipBase.currentPoints[0],e),a,p,m);e.dotnetref.invokeMethodAsync("SetTooltipTemplateElementSizeAsync",g.x,g.y,e.tooltipBase.tooltipTempList),this.setTooltipTemplateElementSize(e,c,a,p,m),this.removeHighlight(e),this.highlightPoints(e),this.updatePreviousPoint(e,n)}else{var f=e.userInteractionBase.chartBorderWidth;this.createTooltipRenderer(e,i,this.findSharedLocation(e),a,o.point,this.findShapes(e),this.findMarkerHeight(e.tooltipBase.currentPoints[0],e),new sf.svgbase.Rect(f,f,e.userInteractionBase.availableSize.width-3-2*f,e.userInteractionBase.availableSize.height-3-2*f),e.crosshairBase.crosshair.enable,n,null,"")}}e.tooltipBase.isSharedRemove=!0},setTooltipTemplateElementSize:function(e,t,i,o,s){var r=this.getTemplateSize(e.element.id+"_tooltip");if(r&&e.tooltipBase.currentPoints.length>0){var n=this.getTemplateLocation(e.userInteractionBase.axisClipRect,t,r.width,r.height,this.findMarkerHeight(e.tooltipBase.currentPoints[0],e),i,o,s);n.x+=e.userInteractionBase.secondaryElementOffset.left,n.y+=e.userInteractionBase.secondaryElementOffset.top;var a=document.getElementById(e.element.id+"_tooltip"),l=r.width+10,h=n.x+10;0==r.width&&t.x+10>e.userInteractionBase.axisClipRect.x+e.userInteractionBase.axisClipRect.width&&(n.x=e.userInteractionBase.axisClipRect.x+h-(l+20)),a.setAttribute("style","top:"+n.y.toString()+"px;left:"+n.x.toString()+"px;pointer-events:none; position:absolute;z-index: 1;visibility: visible;")}},isPointInRect:function(e,t,i,o){for(var s,r=0;r<e.length;r++){var n=e[r];sf.svgbase.withInAreaBounds(n.x+t.x,n.y+t.y,t)||sf.svgbase.withInAreaBounds(n.x-t.x,n.y-t.y,t)?s=!0:o&&(n.x<0||n.y<0)?s||(s=n.x<0?n.x+i[r].w>0:n.y+i[r].h>0):s=!1}return s},pushData:function(e,t){return!!e.series.enableTooltip&&(null!=t&&t.tooltipBase.currentPoints.push(e),!0)},renderGroupedTooltip:function(e,t){var i,o,s=this,r="PolarAxes"===t.userInteractionBase.chartAreaType?this.getData(t):null;this.stopAnimation(t.tooltipBase.toolTipInterval),t.tooltipBase.currentPoints=[];for(var n,a,l=[],h=[],d=[],c=0;c<t.userInteractionBase.visibleSeries.length;c++){var u=t.userInteractionBase.visibleSeries[c];u.enableTooltip&&u.visible&&0!=u.points.length&&("CartesianAxes"==t.userInteractionBase.chartAreaType&&u.visible&&u.points.length>0?i=this.getClosestX(t,u):"PolarAxes"===t.userInteractionBase.chartAreaType&&u.visible&&null!=r.point&&(i={point:u.points[r.point.iX],series:u}),null!=i&&i.point&&l.push(i))}l=this.getSharedPoints(t,l),l=this.sortPointsInfo(l);var m,p,g=Number.MAX_VALUE;l.forEach((function(e){a={pointX:sf.base.isNullOrUndefined(e.point.x)?"":e.point.x.toString(),pointY:sf.base.isNullOrUndefined(e.point.y)?"":e.point.y.toString(),seriesIndex:e.series.index,seriesName:e.series.name,pointIndex:e.point.iX,pointText:e.point.t},d.push(a),n=s.findHeader(e,t),t.tooltipBase.currentPoints.push(e),h.push(s.getTooltipText(e,t)),m=t.userInteractionBase.isInverted?t.mouseY-e.series.clipRect.y:t.mouseX-e.series.clipRect.x,e.point.s.length&&Math.abs(m-e.point.s[0].x)<g&&(s.isSeriesAxisZoomed(e.series)&&e.point.s.length&&!s.isPointInRect(e.point.s,t.userInteractionBase.axisClipRect,e.point.r,t.userInteractionBase.isStockChart)||(g=Math.abs(m-e.point.s[0].x),p=e)),o=t.tooltipBase.lastData="TrendLine"===e.series.category&&t.tooltipBase.tooltipModule.shared?o:p||o})),t.userInteractionBase.isStockChart&&h.push(""),t.tooltipBase.sharedArgsData={headerText:n,text:h,data:d},t.tooltipBase.sharedTooltipEventCalled?(t.dotnetref.invokeMethodAsync("SharedTooltipEventTriggered",t.tooltipBase.sharedArgsData),this.findMouseValue(o,t)):this.groupedTooltip(t,l,e,o)},createTooltipRenderer:function(e,t,i,o,r,n,a,l,h,d,c,u){var m,p=e.tooltipBase.currentPoints[0].series,g=this.isRectSeries(p.type);t&&null!=i&&(m=new sf.svgbase.Tooltip({opacity:e.tooltipBase.tooltipModule.opacity,header:e.tooltipBase.header,content:e.tooltipBase.formattedText,fill:e.tooltipBase.tooltipModule.fill,border:e.tooltipBase.tooltipModule.border,enableAnimation:e.tooltipBase.tooltipModule.enableAnimation,location:null!=i?new s(i.x+e.userInteractionBase.secondaryElementOffset.left,e.userInteractionBase.isStockChart?i.y:i.y+e.userInteractionBase.secondaryElementOffset.top):null,shared:e.tooltipBase.tooltipModule.shared,crosshair:h,shapes:n,clipBounds:"PolarAxes"==e.userInteractionBase.chartAreaType?new s(0,0):o,areaBounds:new sf.svgbase.Rect(l.x+e.userInteractionBase.secondaryElementOffset.left,l.y+(e&&e.userInteractionBase.isStockChart?e.tooltipBase.seriesTooltipTop:0)+(e.userInteractionBase.isStockChart?0:e.userInteractionBase.secondaryElementOffset.top),l.width,l.height),palette:this.findPalette(e),controlName:"Chart",controlInstance:e,template:u||e.tooltipBase.tooltipModule.template,data:c,theme:e.userInteractionBase.theme,offset:a,textStyle:e.tooltipBase.tooltipModule.textStyle,isNegative:!!(g&&"Waterfall"!=p.type&&r&&r.y)&&("BoxPlot"!=p.seriesType&&Number(r.y)<0),inverted:e.userInteractionBase.isInverted&&g,arrowPadding:e.tooltipBase.formattedText.length>1?0:7,availableSize:e.userInteractionBase.availableSize,duration:e.tooltipBase.tooltipDuration,isCanvas:!1,rx:4,ry:4,isTextWrap:e.tooltipBase.tooltipModule.enableTextWrap,enableRTL:e.userInteractionBase.enableRTL})),null!=m&&(this.removeHighlight(e),this.highlightPoints(e),this.updatePreviousPoint(e,d),m.textStyle.size=sf.base.isNullOrUndefined(m.textStyle.size)?"12px":m.textStyle.size,m.textStyle.fontWeight=sf.base.isNullOrUndefined(m.textStyle.fontWeight)?"400":m.textStyle.fontWeight,this.renderTooltip(m,e.element.id+"_tooltip",e))},tooltip:function(e){this.getTooltipElement(e),sf.base.isNullOrUndefined(e.userInteractionBase.visibleSeries)||(e.tooltipBase.tooltipModule.shared?this.renderGroupedTooltip(!0,e):this.renderSeriesTooltip(!0,e))},getData:function(e){var t,i,o,s,r=null,n=null;if(!sf.base.isNullOrUndefined(e.userInteractionBase.visibleSeries)){for(var a=!1,l=0;l<e.userInteractionBase.visibleSeries.length;l++)if("StackingArea"===e.userInteractionBase.visibleSeries[l].type||"StackingArea100"===e.userInteractionBase.visibleSeries[l].type){a=!0;break}if(a){var h=e.userInteractionBase.visibleSeries.length;for(l=0;l<h;l++){if(t="Scatter"===(n=e.userInteractionBase.visibleSeries[l]).type||"Scatter"===n.drawType||n.marker&&n.marker.vS?(n.marker.mH+5)/2:0,i="Scatter"===n.type||"Scatter"===n.drawType||n.marker&&n.marker.vS?(n.marker.mW+5)/2:0,o=e.mouseX,s=e.mouseY,n.dataEditSettings&&this.isRectSeries(n.seriesType))if("Bar"===n.type&&n.chartIsTransposed||!n.chartIsTransposed&&"Bar"!==n.type){var d=n.marker.mH/2;s=n.y_Axis.isAxisInverse?s-d:s+d}else{var c=n.marker.mW/2;o=n.y_Axis.isAxisInverse?o+c:o-c}if(n.visible&&!sf.base.isNullOrUndefined(n.clipRect)&&sf.svgbase.withInAreaBounds(o,s,new sf.svgbase.Rect(n.clipRect.x,n.clipRect.y,n.clipRect.w,n.clipRect.h),t,i)&&(r=this.getRectPoint(n,n.clipRect,o,s,e)),r)return{point:r,series:n}}}else for(l=(h=e.userInteractionBase.visibleSeries.length)-1;l>=0;l--){if(t="Scatter"===(n=e.userInteractionBase.visibleSeries[l]).type||"Scatter"===n.drawType||n.marker&&n.marker.vS?(n.marker.mH+5)/2:0,i="Scatter"===n.type||"Scatter"===n.drawType||n.marker&&n.marker.vS?(n.marker.mW+5)/2:0,o=e.mouseX,s=e.mouseY,n.dataEditSettings&&this.isRectSeries(n.seriesType))if("Bar"===n.type&&n.chartIsTransposed||!n.chartIsTransposed&&"Bar"!==n.type){d=n.marker.mH/2;s=n.y_Axis.isAxisInverse?s-d:s+d}else{c=n.marker.mW/2;o=n.y_Axis.isAxisInverse?o+c:o-c}if(n.visible&&!sf.base.isNullOrUndefined(n.clipRect)&&sf.svgbase.withInAreaBounds(o,s,new sf.svgbase.Rect(n.clipRect.x,n.clipRect.y,n.clipRect.w,n.clipRect.h),t,i)&&(r=this.getRectPoint(n,n.clipRect,o,s,e)),r)return{point:r,series:n}}}return{point:r,series:n}},getRectPoint:function(e,t,i,o,s){for(var r,n,a,l,h,d,c=0,u=this.isRectSeries(e.seriesType),m=0,p=e.points;m<p.length;m++){var g=p[m];if(g.rD||g.r&&g.r.length){if(g.rD&&"PolarAxes"===s.userInteractionBase.chartAreaType&&e.drawType.indexOf("Column")>-1&&(r=i-(e.clipRect.w/2+e.clipRect.x),n=o-(e.clipRect.h/2+e.clipRect.y),c=2*Math.PI*(g.rD.cP<0?1+g.rD.cP:g.rD.cP),a=(a=(Math.atan2(n,r)+.5*Math.PI-c)%(2*Math.PI))<0?2*Math.PI+a:a,a+=2*Math.PI*e.x_Axis.startAngle,l=g.rD.sA,l=(l-=c)<0?2*Math.PI+l:l,h=g.rD.eA,h=(h-=c)<0?2*Math.PI+h:h,d=Math.sqrt(Math.pow(Math.abs(r),2)+Math.pow(Math.abs(n),2)),a>=l&&a<=h&&(d>=g.rD.iR&&d<=g.rD.r||d<=g.rD.iR&&d>=g.rD.r)&&d<=s.userInteractionBase.chartRadius))return g;if(e.dataEditSettings&&u&&this.rectRegion(i,o,g,t,e,s))return g;if(this.checkRegionContainsPoint(g.r,t,i,o,s))return g}}return null},rectRegion:function(e,t,i,o,s,r){var n,a="Bar"===s.type,l=s.y_Axis.isAxisInverse,h=s.chartIsTransposed,d=0,c=0,u=n=20;return l&&h?a?(d=i.r[0].h-10,n=i.r[0].w):(c=-10,u=i.r[0].h):l||i.yV<0?a?(c=-10,u=i.r[0].h):(d=i.r[0].h-10,n=i.r[0].w):h?a?(d=-10,n=i.r[0].w):(c=i.r[0].w-10,u=i.r[0].h):a?(c=i.r[0].w-10,u=i.r[0].h):(d=-10,n=i.r[0].w),i.r.some((function(i){return sf.svgbase.withInAreaBounds(e,t,new sf.svgbase.Rect(("CartesianAxes"===r.userInteractionBase.chartAreaType?o.x:0)+i.x+c,("CartesianAxes"===r.userInteractionBase.chartAreaType?o.y:0)+i.y+d,n,u))}))},checkRegionContainsPoint:function(e,t,i,o,s){return e.some((function(e,r){return s.userInteractionBase.lierIndex=r,sf.svgbase.withInAreaBounds(i,o,new sf.svgbase.Rect(("CartesianAxes"===s.userInteractionBase.chartAreaType?t.x:0)+e.x,("CartesianAxes"===s.userInteractionBase.chartAreaType?t.y:0)+e.y,e.w,e.h))}))},getClosestX:function(e,t){if(null==t)return null;var i=t.clipRect;if(i&&e.mouseX<=i.x+i.w&&e.mouseX>=i.x){var o=this.getPointValue(e,t),s=this.getClosest(t,o);return null!=s&&null==s.y&&"BollingerBand"==t.name?null:{point:s,series:t}}return null},getPointValue:function(e,t){if(null!=t&&null!=t.clipRect){var i=e.userInteractionBase.isInverted?e.mouseY-t.clipRect.y:e.mouseX-t.clipRect.x,o=e.userInteractionBase.isInverted?t.clipRect.h:t.clipRect.w;return this.getValueByPoint(i,o,t.x_Axis.orientation,t.x_Axis.visibleRange,t.x_Axis.isAxisInverse)}return 0},getValueByPoint:function(e,t,i,o,s){return("Horizontal"==i&&!s||"Horizontal"!=i&&s?e/t:1-e/t)*o.delta+o.start},getClosest:function(e,t){var i;if(null==e)return i;var o=e.xMin+864e5;return(t>=e.xMin-.5&&t<=e.xMax+.5&&e.points.length>0||1==e.points.length&&e.xMin==e.xMax&&t>=e.xMin-.5&&t<=o+.5)&&(i=this.findClosest(this.sortPoints(e.points,["xV"]),t)),i},sortPoints:function(e,t,i){for(var o=sf.base.extend([],e,null),s=0;s<o.length;s++)for(var r=0;r<t.length;r++)o[s][t[r]]instanceof Date&&(o[s][t[r]]=o[s][t[r]].getTime());return o.sort((function(e,o){for(var s=0,r=0,n=0;n<t.length;n++)s+=e[t[n]],r+=o[t[n]];return!i&&s<r||i&&s>r?-1:s===r?0:1})),o},findClosest:function(e,t){var i=e.length;if(t<=e[0].xV)return e[0];if(t>=e[i-1].xV)return e[i-1];for(var o=0,s=i,r=0;o<s;){if(e[r=Math.round((o+s)/2)].xV==t)return e[r];if(t<e[r].xV){if(r>0&&t>e[r-1].xV)return this.getClosestValue(e[r-1],e[r],t);s=r}else{if(r<i-1&&t<e[r+1].xV)return this.getClosestValue(e[r],e[r+1],t);o=r+1}}return e[r]},getClosestValue:function(e,t,i){return i-e.xV>=t.xV-i?t:e},getSharedPoints:function(e,t){var i,o,s=t;if(!("CartesianAxes"==e.userInteractionBase.chartAreaType&&null!=t&&t.length>0)||e.tooltipBase.tooltipModule.showAllSeries)return t;for(var r=0,n=t;r<n.length;r++){var a=n[r];if(i=this.getPointValue(e,a.series),this.isClosest(i,a,o)&&(o=a.point.xV),this.isAxisValue(a.series))return t}return(t=t.filter((function(e){return o==e.point.xV}))).length<s.length&&e.tooltipBase.tooltipModule.showNearestPoint?s:t},isClosest:function(e,t,i){var o=t.series.x_Axis;return e>=o.visibleRange.start-.5&&e<=o.visibleRange.end+.5&&(isNaN(i)||Math.abs(t.point.xV-e)<Math.abs(i-e))},isAxisValue:function(e){return!sf.base.isNullOrUndefined(e)&&(e.axesCount>2&&"PrimaryXAxis"!=e.x_Axis.name)},sortPointsInfo:function(e){var t=[];return e.forEach((function(e){"Series"===e.series.category&&t.push(e)})),e.forEach((function(e){"Indicator"===e.series.category&&t.push(e)})),e.forEach((function(e){"TrendLine"===e.series.category&&t.push(e)})),t},findHeader:function(e,t){var i=t.tooltipBase.header;return sf.base.isNullOrUndefined(i)?"":""!==(i=this.parseTemplate(e.point,e.series,i,t)).replace(/<b>/g,"").replace(/<\/b>/g,"").trim()?i:""},getTooltipText:function(e,t){return this.parseTemplate(e.point,e.series,this.getFormat(t,e.series),t)},getIndicatorTooltipFormat:function(e,t){return"XY"===e.seriesType?e.name+" : <b>${point.y}</b>":t},parseTemplate:function(e,t,i,o){for(var s,r,n=0,a=Object.keys(e);n<a.length;n++){var l=a[n];s=new RegExp("${point."+l+"}","gm"),i.indexOf(s.source)>-1&&(i=i.replace(s.source,this.formatPointValue(e,l,"${point.x}"===s.source,"${point.h}"===s.source||"${point.o}"===s.source||"${point.c}"===s.source||"${point.l}"===s.source||"${point.y}"===s.source,t,o.userInteractionBase.isStockChart,o)))}for(var h=0,d=Object.keys(t);h<d.length;h++){l=d[h];s=new RegExp("${series."+l+"}","gm"),r=t[l],i.indexOf(s.source)>-1&&(i=i.replace(s.source,r))}return i},getTemplateLocation:function(e,t,i,o,r,n,a,l){var h=new s(t.x,t.y),d=i+10,c=o+10,u=n.x,m=n.y,p=e.x,g=e.y;return a?(((h=new s(h.x+u+r,h.y+m-o/2)).x+d+12>p+e.width||l)&&(h.x=(t.x>e.width?e.width:t.x)+u-r-(d+12)),h.x<p&&(h.x=(t.x<0?0:t.x)+u+r),h.y<=g&&(h.y=g),h.y+c>=g+e.height&&(h.y-=h.y+c-(g+e.height))):(((h=new s(h.x+u-i/2,h.y+m-o-12-r)).y<g||l)&&(h.y=(t.y<0?0:t.y)+m+r),h.y+c+12>g+e.height&&(h.y=(t.y>e.height?e.height:t.y)+m-o-12-r),h.x<p&&(h.x=p),h.x+d>p+e.width&&(h.x-=h.x+d-(p+e.width))),{x:h.x,y:h.y}},formatPointValue:function(e,t,i,o,s,r,n){var a,l,h,d=i?s.x_Axis:s.y_Axis,c=d.format?d.format:d.dateFormat,u=d.labelFormat;if("DateTime"==d.valueType&&i)a=(new sf.base.Internationalization).getDateFormat({format:c||"MM/dd/yyyy",type:this.firstToLowerCase("DateTime")})(this.convertDateAndTime(new Date(e.xV))),a=this.getGlobalizedDate(a,n.dateValuePairs);else if("DateTimeCategory"===d.valueType&&i){var m=d.isUniversalDateTime?this.convertDateAndTime(new Date(e[t])):new Date(e[t]);a=(new sf.base.Internationalization).getDateFormat({format:c||u||"MM/dd/yyyy HH:mm:ss tt",type:this.firstToLowerCase("DateTime")})(m),a=this.getGlobalizedDate(a,n.dateValuePairs)}else if("Category"!==d.valueType&&i)a=(l=u&&null!==u.match("{value}"))?u.replace("{value}",this.formatAxisValue(e[t],l,u,void 0,n)):this.formatAxisValue(e[t],l,u,void 0,n);else if(o&&!sf.base.isNullOrUndefined(e[t])){if(l=u&&null!==u.match("{value}"),"outliers"===t)h=this.formatAxisValue(e[t][this.lierIndex-4],l,u,void 0,n);else{var p=e[t],g=s.markerDataLabelFormat?parseInt(s.markerDataLabelFormat.substring(1)):"Logarithmic"===d.valueType?10:2,f=r?Math.max(2,(p-Math.round(p)).toString().length):g;h=this.formatAxisValue(e[t],l,u,f,n)}r&&"Indicator"===s.category&&"AccumulationDistribution"===s.name&&(h=h.replace(/\B(?=(\d{3})+(?!\d))/g,",")),h=this.getGlobalizedNumber(h,n.numberValuePairs),a=l?u.replace("{value}",h):h}else a=this.getGlobalizedNumber(e[t].toString(),n.numberValuePairs);return a},getFormat:function(e,t){var i=e.tooltipBase.tooltipModule.shared&&!t.name?"":"<br/>";if(t.tooltipFormat)return"XY"===t.seriesType&&"Indicator"===t.category?this.getIndicatorTooltipFormat(t,e,e.tooltipBase.tooltipFormat):t.tooltipFormat;if(e.tooltipBase.tooltipFormat)return"XY"===t.seriesType&&"Indicator"===t.category?this.getIndicatorTooltipFormat(t,e,e.tooltipBase.tooltipFormat):e.tooltipBase.tooltipFormat;var o="Histogram"===e.seriesTypes[t.index]?"${point.mI}-${point.mX}":"${point.x}",s=e.tooltipBase.tooltipModule.shared&&(!e.userInteractionBase.isStockChart||t.seriesType.indexOf("HighLow")>-1)?"${series.name}":o;switch(t.seriesType){case"XY":return"Indicator"===t.category?this.getIndicatorTooltipFormat(t,e,e.tooltipBase.tooltipFormat):s+" : "+("Bubble"===e.seriesTypes[t.index]?"<b>${point.y}</b>  Size : <b>${point.sI}</b>":"<b>${point.y}</b>");case"HighLow":return s+i+"High : <b>${point.h}</b><br/>Low : <b>${point.l}</b>";case"HighLowOpenClose":e.userInteractionBase.isStockChart&&t.volume;return s+i+"High : <b>${point.h}</b><br/>Low : <b>${point.l}</b><br/>Open : <b>${point.o}</b><br/>Close : <b>${point.c}</b>";case"BoxPlot":return s+i+(e.userInteractionBase.lierIndex>3?"Outliers : <b>${point.oL}</b>":"Maximum : <b>${point.mX}</b><br/>Q3 : <b>${point.uQ}</b><br/>Median : <b>${point.m}</b><br/>Q1 : <b>${point.lQ}</b><br/>Minimum : <b>${point.mI}</b>");default:return""}},formatAxisValue:function(e,t,i,o,s){void 0===o&&(o=2);var r=Number(e);return(new sf.base.Internationalization).getNumberFormat({format:t?"":i,useGrouping:s.userInteractionBase.useGrouping,minimumFractionDigits:2,maximumFractionDigits:o>20?20:o})(r)},findShapes:function(e){if(!e.tooltipBase.tooltipModule.enableMarker)return[];for(var t=[],i=0,o=e.tooltipBase.currentPoints;i<o.length;i++){var s=o[i];t.push(s.series.shape)}return t},findMarkerHeight:function(e,t){var i=e.series;return("TrendLine"===i.category?f.getElement(t.id+"_Series_"+i.index+"_TrendLine_"+i.index+"_PointIndex_"+e.point.iX):f.getElement(t.id+"_Series_"+i.index+"_PointIndex_"+e.point.iX))?this.isRectSeries(i.type)&&"Scatter"!==i.type&&"Scatter"!==i.type||"Candle"===i.seriesType||"Hilo"===i.seriesType||"HiloOpenClose"===i.seriesType?0:(i.marker.mH+5)/2+2*i.marker.b.wT:(i.marker.vS||t.tooltipBase.tooltipModule.shared&&(!this.isRectSeries(i.type)||i.marker.vS)||"Scatter"===i.type||"Scatter"===i.type)&&"Candle"!==i.seriesType&&"Hilo"!==i.seriesType&&"HiloOpenClose"!==i.seriesType?(i.marker.mH+5)/2+2*i.marker.b.wT:0},findSharedLocation:function(e){return e.userInteractionBase.isStockChart?this.stockChartTooltipPosition&&"Nearest"===this.stockChartTooltipPosition?new s(e.tooltipBase.valueX,e.tooltipBase.valueY+e.tooltipBase.seriesTooltipTop+5):new s(e.userInteractionBase.axisClipRect.x+5,e.userInteractionBase.axisClipRect.y+e.tooltipBase.seriesTooltipTop+5):e.tooltipBase.currentPoints.length>1?new s(e.tooltipBase.valueX,e.tooltipBase.valueY):this.getSymbolLocation(e.tooltipBase.currentPoints[0],e)},valueToCoefficient:function(e,t){var i=(e-t.visibleRange.start)/t.visibleRange.delta;return t.isAxisInverse?1-i:i},valueToPolarCoefficient:function(e,t,i){var o,s,r=i.visibleRange;return"Category"!==t?(o=r.end-("DateTime"===t?i.dataTimeInterval:i.visibleInterval)-r.start,s=i.visibleLabelCount-1,o=0===o?1:o):(o=1===i.visibleLabelCount?1:i.visibleLabelCount-1-0,s=i.visibleLabelCount),i.isAxisInverse?(e-r.start)/o*(1-1/s):1-(e-r.start)/o*(1-1/s)},findMouseValue:function(e,t){if(e){var i=e.series.x_Axis;t.userInteractionBase.isInverted?(t.tooltipBase.valueY=(1-this.valueToCoefficient(e.point.xV,i))*i.rect.h+i.rect.y,t.tooltipBase.valueX=t.mouseX):("PolarAxes"===t.userInteractionBase.chartAreaType?t.tooltipBase.valueX=this.valueToPolarCoefficient(e.point.xV,e.series.x_Axis.valueType,i)*i.rect.w+i.rect.x:t.tooltipBase.valueX="TrendLine"===e.series.category&&t.tooltipBase.tooltipModule.shared?t.tooltipBase.valueX:this.valueToCoefficient(e.point.xV,i)*i.rect.w+i.rect.x,t.tooltipBase.valueY=t.mouseY)}},getSymbolLocation:function(e,t){var i=t.userInteractionBase.isStockChart?new s(0,t.tooltipBase.seriesTooltipTop):new s(0,0);if("BoxAndWhisker"!==e.series.type){if(0==e.point.s.length)return null;i=new s(e.point.s[0].x,e.point.s[0].y)}switch(e.series.type){case"BoxAndWhisker":return this.getBoxLocation(e,t);case"Waterfall":return this.getWaterfallRegion(e,i,t);case"RangeArea":case"SplineRangeArea":case"RangeColumn":case"RangeStepArea":return this.getRangeArea(e,i);default:return i}},getBoxLocation:function(e,t){return t.userInteractionBase.lierIndex>3?e.point.s[t.userInteractionBase.lierIndex-4]:{x:e.point.r[0].x+e.point.r[0].w/2,y:e.point.r[0].y+e.point.r[0].h/2}},getWaterfallRegion:function(e,t){return this.inverted?t.x=Number(e.point.y)<0?t.x+e.point.r[0].w:t.x:t.y=Number(e.point.y)<0?t.y-e.point.r[0].h:t.y,t},getRangeArea:function(e,t){return e.point.r.length>0&&e.point.r[0]&&(this.inverted?t.x=e.point.r[0].x+e.point.r[0].w/2:t.y=e.point.r[0].y+e.point.r[0].h/2),t},findPalette:function(e){for(var t=[],i=0,o=e.tooltipBase.currentPoints;i<o.length;i++){var s=o[i];t.push(this.findColor(s,s.series))}return t},findColor:function(e,t){return!this.isRectSeries(t.seriesType)||"Candle"!==t.seriesType&&"Hilo"!==t.seriesType&&"HiloOpenClose"!==t.seriesType?""!=e.point.i?e.point.i:""!=t.marker.f?t.marker.f:t.interior:e.point.i},renderTooltip:function(e,t,i){var o=document.getElementById(t+"_svg"),s=e,r=i;!(o&&parseInt(o.getAttribute("opacity"),10)>0)&&!sf.base.isNullOrUndefined(r)?(r.tooltip=new sf.svgbase.Tooltip(s),r.tooltip.enableRTL=s.enableRTL,r.tooltip.appendTo("#"+t)):sf.base.isNullOrUndefined(r.tooltip)||(r.tooltip.location=new sf.svgbase.TooltipLocation(s.location.x,s.location.y),r.tooltip.content=s.content,r.tooltip.header=s.header,r.tooltip.offset=s.offset,r.tooltip.palette=s.palette,r.tooltip.shapes=s.shapes,r.tooltip.data=s.data,r.tooltip.template=s.template,r.tooltip.textStyle.color=s.textStyle.color||r.tooltip.textStyle.color,r.tooltip.textStyle.fontFamily=s.textStyle.fontFamily||r.tooltip.textStyle.fontFamily,r.tooltip.textStyle.fontStyle=s.textStyle.fontStyle||r.tooltip.textStyle.fontStyle,r.tooltip.textStyle.fontWeight=s.textStyle.fontWeight||r.tooltip.textStyle.fontWeight,r.tooltip.textStyle.opacity=s.textStyle.opacity||r.tooltip.textStyle.opacity,r.tooltip.textStyle.size=s.textStyle.size||r.tooltip.textStyle.size,r.tooltip.isNegative=s.isNegative,r.tooltip.clipBounds=new sf.svgbase.TooltipLocation(s.clipBounds.x,s.clipBounds.y),r.tooltip.arrowPadding=s.arrowPadding,r.tooltip.dataBind())},removeTooltip:function(e,t){var i=this,o=this.getElement(t.element.id+"_tooltip");this.stopAnimation(t.tooltipBase.toolTipInterval),o&&null==t.tooltipBase.tooltipModule.template&&t.tooltipBase.previousPoints.length>0?t.tooltipBase.toolTipInterval=+setTimeout((function(){t.tooltipBase.tooltipModule&&i.fadeOut(t.dataId)}),e):o&&null!=t.tooltipBase.tooltipModule.template&&t.tooltipBase.previousPoints.length>0&&(t.tooltipBase.toolTipInterval=+setTimeout((function(){null!=t.tooltipBase.tooltipModule.template&&(t.dotnetref.invokeMethodAsync("RemoveTemplateTooltip"),t.tooltipBase.valueX=0,t.tooltipBase.valueY=0,t.tooltipBase.currentPoints=[],i.removeHighlight(t),i.removeMarker(t),t.tooltipBase.previousPoints=[])}),e))},stopAnimation:function(e){this.stopTimer(e)},stopTimer:function(e){window.clearInterval(e)},removeHighlight:function(e){for(var t=0,i=e.tooltipBase.previousPoints;t<i.length;t++){var o=i[t],s="Polar"!=o.series.type?o.series.type:o.series.drawType;this.isRectSeries(s)&&this.highlightPoint(e,o.series,o.point.iX,!1)}},highlightPoint:function(e,t,i,o){var s=this.getElement(e.element.id+"_Series_"+t.index+"_Point_"+i+("BoxPlot"==t.seriesType?"_BoxPath":""));sf.base.isNullOrUndefined(s)||(""===e.highlightColor||sf.base.isNullOrUndefined(e.highlightColor)?s.setAttribute("opacity",(o?e.userInteractionBase.theme.indexOf("Fluent2")>-1?t.opacity/2:t.opacity/1.25:t.opacity).toString()):s.setAttribute("fill",o&&"transparent"!=e.highlightColor?e.highlightColor:t.interior))},highlightPoints:function(e){for(var t=0,i=e.tooltipBase.currentPoints;t<i.length;t++){var o=i[t],s="Polar"!=o.series.type?o.series.type:o.series.drawType;this.isRectSeries(s)&&"Series"==o.series.category&&this.highlightPoint(e,o.series,o.point.iX,!0)}},updatePreviousPoint:function(e,t){t.length>0&&(e.tooltipBase.currentPoints=e.tooltipBase.currentPoints.concat(t)),e.tooltipBase.previousPoints=[],e.tooltipBase.previousPoints=e.tooltipBase.previousPoints.concat(e.tooltipBase.currentPoints)},fadeOut:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||!sf.base.isNullOrUndefined(t)&&sf.base.isNullOrUndefined(t.tooltip)||(this.removeTooltipCommentElement(t),t.tooltipBase.valueX=0,t.tooltipBase.valueY=0,t.tooltipBase.currentPoints=[],this.removeHighlight(t),this.removeMarker(t),t.tooltipBase.previousPoints=[],t.tooltip.fadeOut())},removeTooltipCommentElement:function(e){var t=e.tooltip.element?e.tooltip.element.id:null,i=t?document.getElementById(t):null;if(i&&!e.isRemoveCommentElement&&i.childNodes.length>1){for(var o=i.childNodes,s=[],r=0;r<o.length;r++)(o[r].nodeName.match("#comment")||o[r].nodeName.match("#text"))&&s.push(o[r]);for(var n=0,a=s;n<a.length;n++){var l=a[n];sf.base.remove(l),e.isRemoveCommentElement=!0}}},isSeriesAxisZoomed:function(e){var t=!1;return 1===e.x_Axis.zoomFactor&&1===e.y_Axis.zoomFactor&&0===e.y_Axis.zoomPosition&&0===e.x_Axis.zoomPosition||(t=!0),t},getParentElementBoundsById:function(e){var t=document.getElementById(e);if(t){var i=document.getElementById(e+"_svg");i&&(i.style.display="none");var o=document.getElementById(e+"_tooltip_data");o&&(o.style.display="none"),t.style.width="100%",t.style.height="100%";var s=t.getBoundingClientRect(),r=s.width||t.clientWidth||t.offsetWidth,n=s.height||t.clientHeight||t.offsetHeight;return i&&(i.style.display=""),o&&(o.style.display="block"),{width:r,height:n,left:s.left,top:s.top,right:s.right,bottom:s.bottom}}return{width:0,height:0,left:0,top:0,right:0,bottom:0}},getElementBoundsById:function(e,t){void 0===t&&(t=!0),t&&(this.id=e);var i=document.getElementById(e);if(i){var o=i.getBoundingClientRect();return{width:i.clientWidth||i.offsetWidth,height:i.clientHeight||i.offsetHeight,left:o.left,top:o.top,right:o.right,bottom:o.bottom}}return{width:0,height:0,left:0,top:0,right:0,bottom:0}},getRefreshElementBoundsById:function(e){var t=document.getElementById(e);if(t){for(var i=document.getElementsByClassName("e-chart"),o=i.length,s=0;s<o;s++){if((n=i[s]).id.indexOf("_stockChart_")<0)n.querySelector("[id*=_svg]").style.display="none"}t.style.width="100%",t.style.height="100%";var r=t.getBoundingClientRect();for(s=0;s<o;s++){var n;if((n=i[s]).id.indexOf("_stockChart_")<0)n.querySelector("[id*=_svg]").style.display=""}return{width:r.width||t.clientWidth||t.offsetWidth,height:r.height||t.clientHeight||t.offsetHeight,left:r.left,top:r.top,right:r.right,bottom:r.bottom}}return{width:0,height:0,left:0,top:0,right:0,bottom:0}},getBrowserDeviceInfo:function(){return{browserName:sf.base.Browser.info.name,isPointer:sf.base.Browser.isPointer,isDevice:sf.base.Browser.isDevice,isTouch:sf.base.Browser.isTouch,isIos:sf.base.Browser.isIos||sf.base.Browser.isIos7}},setZoomingCipPath:function(e,t,i){var o=document.getElementById(e),s=document.getElementById(t);o&&o.setAttribute("clip-path",i),s&&o.setAttribute("clip-path",i)},setZoomingElementAttributes:function(e,t,i,o,s,r,n,a){"Indicator"===t&&i&&i.parentElement&&i.parentElement.setAttribute("transform",e),i&&i.setAttribute("transform",e),o&&o.setAttribute("transform",e),s&&s.setAttribute("transform",e),r&&r.setAttribute("visibility","hidden"),n&&n.setAttribute("visibility","hidden"),a&&(a.style.visibility="hidden")},measureBreakText:function(e,t,i,o,s,r,n){var a={color:i,size:t,fontFamily:o,fontWeight:s,fontStyle:r,opacity:parseInt(n,10)};return sf.svgbase.measureText(e,a)},getAllCharacters:function(){for(var e=[],t=33;t<591;t++)e.push(String.fromCharCode(t));return e},measureText:function(e,t,i,o){var s=document.getElementById("sfchartmeasuretext");return null===s&&(s=sf.base.createElement("text",{id:"sfchartmeasuretext"}),document.body.appendChild(s))," "===e&&(e="&nbsp;"),s.innerHTML=e,s.style.position="fixed",s.style.fontSize="100px",s.style.fontWeight=t,s.style.fontStyle=i,s.style.fontFamily=o,s.style.visibility="hidden",s.style.top="-100",s.style.left="0",s.style.whiteSpace="nowrap",s.style.lineHeight="normal",{Width:s.clientWidth,Height:s.clientHeight}},getCharCollectionSize:function(e){for(var t=[],i=this.getAllCharacters(),o=i.length,s=e.length,r=0;r<s;r++)for(var n=e[r].split("_"),a=n[0],l=n[1],h=n[2],d=0;d<o;d++)t.push(this.measureText(i[d],a,l,h).Width+"");return JSON.stringify(t)},getCharSizeByFontKeys:function(e){for(var t,i={},o=e.length,s=[],r=0;r<o;r++)s=e[r].split("_"),t=this.measureText(s[0],s[1],s[2],s[3]),i[e[r]]={X:t.Width,Y:t.Height};return JSON.stringify(i)},getElementRect:function(e){var t=document.getElementById(e),i=t.getBoundingClientRect();return sf.base.remove(t),{Left:i.left,Right:i.right,Top:i.top,Bottom:i.bottom,Width:i.width,Height:i.height}},getElement:function(e){return document.getElementById(e)},drawTrackBall:function(e,t,i,o,s,r){var n=this.getElement(e);if(n){for(var a=document.createElementNS("http://www.w3.org/2000/svg",i),l=Object.keys(t),h="",d=0;d<l.length;d++)h="strokeWidth"===l[d]?"stroke-width":"strokeDashArray"===l[d]?"stroke-dashArray":"direction"===l[d]?"d":l[d],a.setAttribute(h,t[l[d]]);a.setAttribute("class",o),a.setAttribute("clip-path",s),a.setAttribute("transform",r),n.appendChild(a)}},removeHighLightedMarker:function(e){for(var t=document.getElementsByClassName(e.markerExplodeBase.trackBallClass),i=0;i<t.length;)-1!==t[i].id.indexOf(e.id)?sf.base.remove(t[i]):i++},setAttribute:function(e,t,i){var o=this.getElement(e);o&&o.setAttribute(t,i)},createTooltip:function(e,t,i,o,s){var r=document.getElementById(e),n="top:"+((window.scrollY||0)+i).toString()+"px;left:"+o.toString()+"px;color:black !important; background:#FFFFFF !important; position:absolute;border:1px solid #707070;font-size:"+s+";border-radius:2px; z-index:10";r?(r.setAttribute("innerHTML","&nbsp;"+t+"&nbsp;"),r.setAttribute("styles",n)):(r=sf.base.createElement("div",{id:e,innerHTML:"&nbsp;"+t+"&nbsp;",styles:n}),document.body.appendChild(r))},removeElement:function(e){if(!e)return null;var t=this.getElement(e);t&&sf.base.remove(t)},applySelection:function(e,t){var i,o=document.getElementById(e);if(o&&o.childNodes)for(var s=1,r=(i=o.childNodes).length;s<r;s++)i[s]&&"rect"!==i[s].tagName&&i[s].setAttribute&&i[s].setAttribute("fill",t)},getAndSetTextContent:function(e,t,i){var o=document.getElementById(e);if(o){if(t)return o.textContent;o.textContent=i}return null},doProgressiveAnimation:function(e,t,i,o,s){var r,n=this.getElement(t),a=this.getElement(e),l=new sf.base.Animation({}),h=a.getTotalLength();a.setAttribute("visibility","hidden"),l.animate(a,{duration:i+o,delay:o,progress:function(e){n.setAttribute("visibility","visible"),e.timeStamp>=e.delay&&(a.setAttribute("visibility","visible"),r=Math.abs(Math.round((e.timeStamp-e.delay)*h/e.duration)),a.setAttribute("stroke-dasharray",r+","+h))},end:function(){a.setAttribute("stroke-dasharray",s)}})},linear:function(e,t,i,o){return-i*Math.cos(e/o*(Math.PI/2))+i+t},doLinearAnimation:function(e,t,i,o){var s,r=this.getElement(e),n=new sf.base.Animation({}),a=this.linear,l=+r.getAttribute("height"),h=+r.getAttribute("width"),d=+r.getAttribute("x"),c=o?+r.getAttribute("height")+ +r.getAttribute("y"):+r.getAttribute("y");n.animate(r,{duration:t,delay:i,progress:function(e){e.timeStamp>=e.delay&&(r.setAttribute("visibility","visible"),o?(s=a(e.timeStamp-e.delay,0,l,e.duration),r.setAttribute("transform","translate("+d+" "+c+") scale(1,"+s/l+") translate("+-d+" "+-c+")")):(s=a(e.timeStamp-e.delay,0,h,e.duration),r.setAttribute("transform","translate("+d+" "+c+") scale("+s/h+", 1) translate("+-d+" "+-c+")")))},end:function(){r.setAttribute("transform","translate(0,0)")}})},filterCommentElement:function(e){var t=this.getElement(e);if(!t)return null;for(var i=t.childNodes,o=[],s=0;s<i.length;s++)i[s].nodeName.match("#comment")||o.push(i[s]);return o},doInitialAnimation:function(e){for(var t=0;t<e.length;t++){switch(e[t].type){case"Rect":for(var i=this.getElement(e[t].clipPathId),o=this.filterCommentElement(e[t].elementId),s=0;s<e[t].pointIndex.length;s++)this.doRectAnimation(o[e[t].pointIndex[s]],i,e[t].duration,e[t].delay,e[t].pointX[s],e[t].pointY[s],e[t].pointWidth[s],e[t].pointHeight[s],e[t].isInvertedAxis);break;case"Progressive":this.doProgressiveAnimation(e[t].elementId,e[t].clipPathId,e[t].duration,e[t].delay,e[t].strokeDashArray);break;case"Linear":this.doLinearAnimation(e[t].elementId,e[t].duration,e[t].delay,e[t].isInvertedAxis);break;case"Marker":var r=this.getElement(e[t].clipPathId),n=this.filterCommentElement(e[t].elementId);for(s=0;s<e[t].pointIndex.length;s++)this.doMarkerAnimate(n[e[t].pointIndex[s]],r,e[t].duration,e[t].delay,e[t].pointX[s],e[t].pointY[s]);break;case"PolarRadar":var a=this.getElement(e[t].clipPathId),l=this.filterCommentElement(e[t].elementId);for(s=0;s<l.length;s++)this.doPolarRadarAnimation(l[s],a,e[t].duration,e[t].delay,e[t].pointX[0],e[t].pointY[0])}if(!sf.base.isNullOrUndefined(e[t].markerInfo))for(r=this.getElement(e[t].markerInfo.markerClipPathId),n=this.filterCommentElement(e[t].markerInfo.markerElementId),s=0;s<e[t].markerInfo.pointIndex.length;s++)this.doMarkerAnimate(n[e[t].markerInfo.pointIndex[s]],r,200,e[t].duration+e[t].delay,e[t].markerInfo.pointX[s],e[t].markerInfo.pointY[s]),e[t].markerInfo.lowPointIndex.length>0&&this.doMarkerAnimate(n[e[t].markerInfo.lowPointIndex[s]],r,200,e[t].duration+e[t].delay,e[t].markerInfo.lowPointX[s],e[t].markerInfo.lowPointY[s]);if(!sf.base.isNullOrUndefined(e[t].dataLabelInfo))if(0===e[t].dataLabelInfo.templateId.length)this.doDataLabelAnimation(e[t].dataLabelInfo.shapeGroupId,e[t].dataLabelInfo.textGroupId,"",e[t].clipPathId,200,e[t].duration+e[t].delay);else for(s=0;s<e[t].dataLabelInfo.templateId.length;s++)this.doDataLabelAnimation("","",e[t].dataLabelInfo.templateId[s],"",200,e[t].duration+e[t].delay);sf.base.isNullOrUndefined(e[t].errorBarInfo)||this.doErrorBarAnimation(e[t].errorBarInfo.errorBarElementId,e[t].errorBarInfo.errorBarClipPathId,e[t].duration+e[t].delay,e[t].isInvertedAxis)}},doDynamicAnimation:function(e,t,i){for(var o=0;o<e.length;o++)sf.base.isNullOrUndefined(e[o].previousDir)||""===e[o].previousDir||sf.base.isNullOrUndefined(e[o].currentDir)||""===e[o].currentDir||this.pathAnimation(e[o].id,e[o].currentDir,!0,e[o].previousDir,300);for(var s=0;s<t.length;s++)this.animateRectElement(t[s].id,0,300,t[s].currentRect,t[s].previousRect);for(var r=0;r<i.length;r++)this.animateRedrawElement(i[r].id,300,i[r].preLocationX,i[r].preLocationY,i[r].curLocationX,i[r].curLocationY,i[r].x,i[r].y)},doRectAnimation:function(e,t,i,o,s,r,n,a,l){var h,d=this.linear;sf.base.isNullOrUndefined(e)||(e.setAttribute("visibility","hidden"),new sf.base.Animation({}).animate(e,{duration:i,delay:o,progress:function(i){t.setAttribute("visibility","visible"),i.timeStamp>=i.delay&&(e.setAttribute("visibility","visible"),l?(n=n||1,h=d(i.timeStamp-i.delay,0,n,i.duration),e.setAttribute("transform","translate("+s+" "+r+") scale("+h/n+", 1) translate("+-s+" "+-r+")")):(a=a||1,h=d(i.timeStamp-i.delay,0,a,i.duration),e.setAttribute("transform","translate("+s+" "+r+") scale(1,"+h/a+") translate("+-s+" "+-r+")")))},end:function(){e.setAttribute("transform","translate(0,0)")}}))},doMarkerAnimate:function(e,t,i,o,s,r){var n=0;e&&(e.setAttribute("visibility","hidden"),new sf.base.Animation({}).animate(e,{duration:i,delay:o,progress:function(i){t.setAttribute("visibility","visible"),i.timeStamp>i.delay&&(e.setAttribute("visibility","visible"),n=(i.timeStamp-i.delay)/i.duration,e.setAttribute("transform","translate("+s+" "+r+") scale("+n+") translate("+-s+" "+-r+")"))},end:function(){e.setAttribute("visibility","")}}))},doPolarRadarAnimation:function(e,t,i,o,s,r){var n=0;e.setAttribute("visibility","hidden"),new sf.base.Animation({}).animate(e,{duration:i,delay:o,progress:function(i){t.setAttribute("visibility","visible"),i.timeStamp>i.delay&&(e.setAttribute("visibility","visible"),n=(i.timeStamp-i.delay)/i.duration,e.setAttribute("transform","translate("+s+" "+r+") scale("+n+") translate("+-s+" "+-r+")"))},end:function(){e.setAttribute("visibility","visible"),e.removeAttribute("transform")}})},templateAnimate:function(e,t,i,o,s,r){new sf.base.Animation({}).animate(e,{duration:i,delay:t,name:o,progress:function(e){r&&r.setAttribute("visibility","visible"),e.element.style.visibility="visible"},end:function(e){s?sf.base.remove(e.element):e.element.style.visibility="visible"}})},doDataLabelAnimation:function(e,t,i,o,s,r){for(var n,a,l,h=this.filterCommentElement(e),d=this.filterCommentElement(t),c=this.getElement(o),u=this.getElement(i),m=u?1:d.length,p=0;p<m;p++)u?(u.style.visibility="hidden",this.templateAnimate(u,r,s,"ZoomIn")):(n=+(l=d[p]).getAttribute("x")+ +l.getAttribute("width")/2,a=+l.getAttribute("y")+ +l.getAttribute("height")/2,this.doMarkerAnimate(d[p],c,s,r,n,a),h[p]&&(n=+(l=h[p]).getAttribute("x")+ +l.getAttribute("width")/2,a=+l.getAttribute("y")+ +l.getAttribute("height")/2,this.doMarkerAnimate(h[p],c,s,r,n,a)))},pathAnimation:function(e,t,i,o,s){var r=this,n=this.getElement(e);if(!i||null==n)return null;var a=300;s&&(a=s);var l,h,d,c=o||n.getAttribute("d"),u=c.split(/(?=[LMCZAQ])/),m=t.split(/(?=[LMCZAQ])/),p=[],g=[];n.setAttribute("d",c),new sf.base.Animation({}).animate(sf.base.createElement("div"),{duration:a,progress:function(e){l="",u.map((function(t,i){if(p=t.split(" "),g=m[i]?m[i].split(" "):p,"Z"===p[0]?l+="Z ":l+=p[0]+" "+r.linear(e.timeStamp,+p[1],+g[1]-+p[1],e.duration)+" "+r.linear(e.timeStamp,+p[2],+g[2]-+p[2],e.duration)+" ","C"===p[0]||"Q"===p[0])for(h=3,d="Q"===p[0]?4:6;h<d;)l+=r.linear(e.timeStamp,+p[h],+g[h]-+p[h],e.duration)+" "+r.linear(e.timeStamp,+p[++h],+g[h]-+p[h],e.duration)+" ",++h;"A"===p[0]&&(l+="0 0 1 "+r.linear(e.timeStamp,+p[6],+g[6]-+p[6],e.duration)+" "+r.linear(e.timeStamp,+p[7],+g[7]-+p[7],e.duration)+" ")})),n.setAttribute("d",l)},end:function(){n.setAttribute("d",t)}})},getPreviousDirection:function(e){var t=this.getElement(e);return t?t.getAttribute("d"):null},getPreviousLocation:function(e,t){var i=this.getElement(e);return{X:i?+i.getAttribute(t+"x"):0,Y:i?+i.getAttribute(t+"y"):0}},animateRectElement:function(e,t,i,o,s){var r=this,n=function(t){e.setAttribute("x",t.x+""),e.setAttribute("y",t.y+""),e.setAttribute("width",t.width+""),e.setAttribute("height",t.height+"")};new sf.base.Animation({}).animate(sf.base.createElement("div"),{duration:i,delay:t,progress:function(e){n(new sf.svgbase.Rect(r.linear(e.timeStamp,s.x,o.x-s.x,e.duration),r.linear(e.timeStamp,s.y,o.y-s.y,e.duration),r.linear(e.timeStamp,s.width,o.width-s.width,e.duration),r.linear(e.timeStamp,s.height,o.height-s.height,e.duration)))},end:function(){n(o)}})},animateRedrawElement:function(e,t,i,o,s,r,n,a){var l=this;void 0===n&&(n="x"),void 0===a&&(a="y");var h=this.getElement(e);if(!h)return null;var d="DIV"===h.tagName,c=function(e,t){d?(h.style[n]=e+"px",h.style[a]=t+"px"):(h.setAttribute(n,e+""),h.setAttribute(a,t+""))};c(i,o),new sf.base.Animation({}).animate(sf.base.createElement("div"),{duration:t,progress:function(e){c(l.linear(e.timeStamp,i,s-i,e.duration),l.linear(e.timeStamp,o,r-o,e.duration))},end:function(){c(s,r)}})},appendChildElement:function(e,t,i,o,s,r,n,a,l,h,d,c){void 0===o&&(o=!1),void 0===s&&(s="x"),void 0===r&&(r="y"),void 0===l&&(l=!1),void 0===h&&(h=!1),void 0===d&&(d=null);var u=e.querySelector("#"+t.id)||this.getElement(t.id),m=t,p=c||300;if(i&&o&&u)if(n=n||("DIV"===u.tagName?new sf.svgbase.TooltipLocation(+u.style[s].split("px")[0],+u.style[r].split("px")[0]):new sf.svgbase.TooltipLocation(+u.getAttribute(s),+u.getAttribute(r))),""!==a&&null!==a)this.pathAnimation(t,t.getAttribute("d"),i,a,p);else if(h&&d)this.animateRectElement(m,0,p,new sf.svgbase.Rect(+u.getAttribute("x"),+u.getAttribute("y"),+u.getAttribute("width"),+u.getAttribute("height")),d);else{var g="DIV"===m.tagName?new sf.svgbase.TooltipLocation(+m.style[s].split("px")[0],+m.style[r].split("px")[0]):new sf.svgbase.TooltipLocation(+m.getAttribute(s),+m.getAttribute(r));this.animateRedrawElement(m,p,n,g,s,r)}else i&&o&&!u&&l&&this.templateAnimate(m,0,600,"FadeIn")},processAppendChild:function(e,t,i,o,s,r,n,a,l,h,d,c,u,m,p,g){void 0===h&&(h=!1),void 0===d&&(d="x"),void 0===c&&(c="y"),void 0===m&&(m=!1),void 0===p&&(p=!1);var f=this.getElement(e),v=this.getElement(t),b=new sf.svgbase.TooltipLocation(i,o),x=new sf.svgbase.Rect(s,r,n,a);this.appendChildElement(f,v,l,h,d,c,b,u,m,p,x,g)},createStyleElement:function(e,t){document.body.appendChild(sf.base.createElement("style",{id:e,innerHTML:t}))},isLassoId:function(e,t){var i=document.elementFromPoint(e,t);return i?i.id:""},doErrorBarAnimation:function(e,t,i,o){var s=this.filterCommentElement(e),r=this.getElement(t);if(!s)return null;for(var n=1;n<s.length;)s[n].style.visibility="hidden",this.templateAnimate(s[n],i,350,o?"SlideLeftIn":"SlideBottomIn",!1,r),n++},getTemplateSize:function(e){var t=this.getElement(e);return t?{width:t.offsetWidth,height:t.offsetHeight}:null},resizeBound:{},resize:{},chartResize:function(e,t){var i=this;return this.isResized=!0,this.tooltipModule&&(f.removeTooltip(100,this),f.removeCrosshair(this,100)),this.tooltipModule=null,this.crosshair=null,this.markerExploded=!1,this.resize&&clearTimeout(this.resize),this.isDisposed||(this.resize=setTimeout((function(){for(var t,o,s,r=e.length,n={},a=0;a<r;a++)t=e[a].dotnetref,(o=e[a].id).indexOf("_stockChart_")<0&&document.getElementById(o+"_svg")&&((s=document.getElementById(o+"_svg")).style.display="none");for(a=0;a<r;a++)t=e[a].dotnetref,(o=e[a].id).indexOf("_stockChart_")<0&&document.getElementById(o)&&(s=document.getElementById(o),n[o]={Dotnetref:t,Width:s.clientWidth||s.offsetWidth,Height:s.clientHeight||s.offsetHeight});for(var l in n)t=n[l].Dotnetref,(s=document.getElementById(l+"_svg")).style.display="",t.invokeMethodAsync("OnChartResize",JSON.stringify({Width:n[l].Width,Height:n[l].Height}));clearTimeout(i.resize)}),500)),!1},getDatalabelTemplateSize:function(e){for(var t,i=[],o=e.length,s=0;s<o;s++)t=this.getElementBoundsById(e[s],!1),i.push({X:t.width,Y:t.height});return JSON.stringify(i)},setSvgDimensions:function(e,t,i,o,s){if(e.setAttribute("width",t),e.setAttribute("height",i),o&&!s){var r=e.parentElement.getAttribute("class");r&&-1===r.indexOf("e-chart-focused")?r+=" e-chart-focused":r||(r="e-chart-focused"),e.parentElement.setAttribute("class",r+" e-chart-focused")}},setKeyboardFocusStyle:function(e,t){if(!document.getElementById(this.id+"_Keyboard_ChartFocus")){var i=document.createElement("style");i.setAttribute("id",this.id+"_Keyboard_ChartFocus"),i.innerText=".e-chart-focused:focus, path[class*=_ej2_chart_selection_series]:focus,path[id*=_Point_]:focus, text[id*=_ChartTitle]:focus {outline: none } .e-chart-focused:focus-visible, path[class*=_ej2_chart_selection_series]:focus-visible,path[id*=_Point_]:focus-visible, text[id*=_ChartTitle]:focus-visible {outline: "+e+"; outline-offset: "+t+"px;}",document.body.appendChild(i)}},findDOMElement:function(e){return document.getElementById(e)},calculateSelectedElements:function(e,t){if(!sf.base.isNullOrUndefined(e.target)){var i=e.target,o=window.sfBlazor.getCompInstance(t);if(i=this.findDOMElement(this.findDOMElementFromDataLabel(i.id,o)),("None"!==o.selectionMode||"None"!==o.highlightMode&&!o.enableHighlight)&&(sf.base.isNullOrUndefined(i)||-1!==i.id.indexOf(o.element.id+"_"))&&("mousemove"!==e.type&&"pointermove"!==e.type||(null===i&&i.id.indexOf("Trackball")>-1&&(i=this.findDOMElement(i.id.split("_Trackball")[0])),!i.hasAttribute("class")||!(i.getAttribute("class").indexOf("highlight")>-1||i.getAttribute("class").indexOf("selection")>-1)))&&(this.isAlreadySelected(e,o),!sf.base.isNullOrUndefined(i)&&i.id.indexOf("_Series_")>-1)){var s=void 0;if(i.id.indexOf("_Trackball_1")>-1)s=this.findDOMElement(i.id.split("_Trackball_")[0]+"_Symbol"),s=sf.base.isNullOrUndefined(s)?this.findDOMElement(i.id.split("_Trackball_")[0]):s;else if(i.id.indexOf("_Trackball_0")>-1||i.id.indexOf("_ErrorBarGroup_")>-1||i.id.indexOf("_ErrorBarCap_")>-1)return null;this.performSelection(this.indexFinder(i.id),o,s||i)}}},generateStyle:function(e,t){var i,o=sf.base.isNullOrUndefined(this.findDOMElement(e))?null:this.findDOMElement(e).getAttribute("data-point");if(sf.base.isNullOrUndefined(this.findDOMElement(e))||(e.indexOf("SeriesGroup")>-1||e.indexOf("SymbolGroup")>-1)&&(o=(i=document.getElementById(e).querySelectorAll("path,ellipse")).length>0?i[0].getAttribute("data-point"):null),!o)return"undefined";var s=o.split(",");return s[3]?(t.styleId.indexOf("selection")>1&&"None"!==t.selectionMode&&(t.unSelected=s[7]||t.unSelected),t.styleId.indexOf("highlight")>0&&("None"!==t.highlightMode||t.enableHighlight)&&(t.unSelected=s[5]||t.unSelected),s[6]||t.styleId+"_series"+s[4]):"undefined"},findDOMElementFromDataLabel:function(e,t){if(e.indexOf("Text")>-1&&e.indexOf("Series")>-1){var i=e.split("_Text_")[0];return this.isRectSeries(t.seriesTypes[e.split("_Series_")[1].split("_")[0]])||"Bubble"===t.seriesTypes[0]||"Scatter"===t.seriesTypes[0]?i:i.slice(0,i.length)+"_Symbol "}return e},indexFinder:function(e){var t=["NaN","NaN"];return e.indexOf("_Point_")>-1?(t=e.split("_Series_")[1].split("_Point_"))[1].indexOf("Text")>-1&&(t[1]=t[1].split("_Text_")[0]):e.indexOf("_shape_")>-1&&!e.indexOf("_legend_")?(t=e.split("_shape_"))[0]=t[1]:e.indexOf("_text_")>-1&&!e.indexOf("_legend_")?(t=e.split("_text_"),e.indexOf("datalabel")>-1?t[0]=t[0].split("_Series_")[1]:t[0]=t[1]):e.indexOf("Series")>-1&&!(e.indexOf("points")>-1)&&(t[0]=e.split("_Series_")[1].split("_")[0]),new o(parseInt(t[0],10),parseInt(t[1],10))},addOrRemoveIndex:function(e,t,i,o){for(var s=0;s<e.length;s++)this.toEquals(e[s],t,i.isSeriesMode,i)&&(e.splice(s,1),s--);o&&e.push(t)},toEquals:function(e,t,i,o){return(e.series===t.series||"Cluster"===o.currentMode&&!i)&&(i||e.point===t.point)},removeSvgClass:function(e,t){var i=sf.base.isNullOrUndefined(e)||sf.base.isNullOrUndefined(e.getAttribute("class"))?"":e.getAttribute("class");i.indexOf(t)>-1&&e.setAttribute("class",i.replace(t,""))},getSelectionClass:function(e,t){return this.generateStyle(e,t)},removeStyles:function(e,t){for(var i=0,o=e;i<o.length;i++){var s=o[i];if(null!==s){var r=s,n=s.id,a=this.findDOMElement(r.id).getAttribute("data-point"),l=a?a.split(","):[];if(r.id.indexOf("_chart_legend_shape_")>-1&&(n=t.element.id+"SeriesGroup"+t.currentSeriesIndex),this.removeSvgClass(s,this.getSelectionClass(n,t)),"None"===t.highlightPattern&&""!==t.highlightColor&&!sf.base.isNullOrUndefined(t.highlightColor))if(s.id.indexOf("Group")>0)for(var h=0;h<s.children.length;h++)s.children[h].setAttribute("fill",l[2]);else s.setAttribute("fill",l[2])}}},findTrackballElements:function(e,t,i){var o,s;t=t.trim();for(var r=0;r<e.length;r++)if(!sf.base.isNullOrUndefined(e[r])&&(o=sf.base.isNullOrUndefined(e[r].parentNode)?[]:[].slice.call(e[0].parentNode.querySelectorAll("."+t))).length>0){s=[];for(var n=0;n<o.length;n++)o[n].id.indexOf("Trackball")>-1&&s.push(o[n]);this.removeStyles(s,i)}},blurEffect:function(e,t,i){void 0===i&&(i=!1);for(var o=this.checkVisibility(t.highlightDataIndexes,t)||this.checkVisibility(t.selectedDataIndexes,t),s=document.getElementById(t.element.id+"SeriesCollection").querySelectorAll("[id*='SeriesGroup']"),r=0;r<s.length;r++){var n=parseInt(s[r].id.split("SeriesGroup")[1]);this.checkSelectionElements(this.findDOMElement(t.element.id+"SeriesGroup"+n),this.generateStyle(t.element.id+"SeriesGroup"+n,t),o,i,n,t),sf.base.isNullOrUndefined(this.findDOMElement(t.element.id+"SymbolGroup"+n))||this.checkSelectionElements(this.findDOMElement(t.element.id+"SymbolGroup"+n),this.generateStyle(t.element.id+"SymbolGroup"+n,t),o,i,n,t)}},checkVisibility:function(e,t){if(!e)return!1;for(var i=!1,o=[],s=0,r=e;s<r.length;s++){var n=r[s];-1===o.indexOf(n.series)&&o.push(n.series)}for(var a=0,l=o;a<l.length;a++){n=l[a];if(document.getElementById(t.element.id+"SeriesGroup"+n)){i=!0;break}}return i},findElements:function(e,t,i,o,s){return void 0===o&&(o=""),e.isSeriesMode?this.getSeriesElements(i.series.toString(),e):"Cluster"===e.currentMode?this.getClusterElements(e,i):this.findDOMElementByIndex(e,i,o,s)},removeMultiSelectElements:function(e,t,i){this.getSeriesElements(i.series.toString(),e);for(var o,s=0;s<t.length;s++)o=e.seriesTypes[t[s].series],(e.isSeriesMode&&!this.toEquals(t[s],i,e.isSeriesMode,e)||"Cluster"===e.currentMode&&!this.toEquals(t[s],i,!1,e)||!e.isSeriesMode&&this.toEquals(t[s],i,!0,e)&&!this.toEquals(t[s],i,!1,e))&&(this.removeStyles(this.findElements(e,o,t[s],"",!1),e),e.element.id+"SymbolGroup"+t[s].series&&this.removeStyles(this.findElements(e,o,t[s],"",!0),e),t.splice(s,1),s--)},checkSelectionElements:function(e,t,i,o,s,r,n){var a,l,h,d=[];t=t.trim(),e.id.indexOf("Series")>-1&&(this.pointIdRequired(r.seriesTypes[s])?(d=r.isSeriesMode?[e]:e.querySelectorAll("[id*='_Series']"),"Histogram"===r.seriesTypes[s]&&d.push(this.findDOMElement(r.element.id+"_Series_"+s+"_NDLine")),"Waterfall"===r.seriesTypes[s]&&d.push(this.findDOMElement(r.element.id+"_Series_"+s+"_Connector")),"BoxAndWhisker"===r.seriesTypes[s]&&(d=this.findDOMElement(r.element.id+"SymbolGroup"+s).querySelectorAll("path,ellipse"))):d=r.isSeriesMode?[e]:e.querySelectorAll("[id*='_Series']")),("None"!==r.selectionMode||"None"!==r.highlightMode||r.enableHighlight)&&(d=e.querySelectorAll("[id*='_Series']"));for(var c=e,u=this.isRectSeries(r.seriesTypes[s])?null:this.findDOMElement(r.element.id+"_Series_"+s),m=0;m<d.length;m++)a=d[m].getAttribute("class")||"",l=sf.base.isNullOrUndefined(u)?"":u.getAttribute("class")?u.getAttribute("class"):"",("None"!==r.selectionMode||"None"!==r.highlightMode||r.enableHighlight)&&(t=!sf.base.isNullOrUndefined(a)&&(a.indexOf("selection")>0||a.indexOf("highlight")>0)?a:t,t=!sf.base.isNullOrUndefined(a)&&(l.indexOf("selection")>0||l.indexOf("highlight")>0)?l:t),sf.base.isNullOrUndefined(a)||sf.base.isNullOrUndefined(l)||-1!==a.indexOf(t)||-1!==l.indexOf(t)||!i?(c=d[m],this.removeSvgClass(d[m],r.unSelected),this.removeSvgClass(u,r.unSelected)):this.addSvgClass(d[m],r.unSelected),d[m].id.indexOf("Trackball")>0&&c.classList[0]===t&&(this.removeSvgClass(d[m],r.unSelected),this.removeSvgClass(u,r.unSelected),this.addSvgClass(d[m],t));if(e.id.indexOf("Symbol")>-1&&e.querySelectorAll("."+t)[0]&&e.querySelectorAll("."+t)[0].getAttribute("class")===t){var p=this.findDOMElement(e.id+"_Series_"+e.id[e.id.length-1]);(p&&p.hasAttribute("class")?p.getAttribute("class"):"").indexOf(r.unSelected)>-1&&this.removeSvgClass(p,r.unSelected)}var g,f=this.findDOMElement(e.id).getAttribute("data-point");if((e.id.indexOf("SeriesGroup")>-1||e.id.indexOf("SymbolGroup")>-1)&&(f=(g=document.getElementById(e.id).querySelectorAll("path,ellipse")).length>0?g[0].getAttribute("data-point"):null),f){var v=f.split(",");(h=this.findDOMElement(r.id+"_chart_legend_shape_"+s))&&(h.hasAttribute("class")&&(this.removeSvgClass(h,h.getAttribute("class")),sf.base.isNullOrUndefined(r.highlightColor&&""!==r.highlightColor)||(h.setAttribute("stroke",v[2]),"None"===r.highlightPattern&&h.setAttribute("fill",v[2]))),a=c.getAttribute("class")||"",l=c.parentNode.getAttribute("class")||"",-1===a.indexOf(t)&&-1===l.indexOf(t)&&i?(this.addSvgClass(h,r.unSelected),this.removeSvgClass(h,t),""===r.highlightColor||sf.base.isNullOrUndefined(r.highlightColor)||(h.setAttribute("stroke",v[2]),"None"===r.highlightPattern&&h.setAttribute("fill",v[2]))):(this.removeSvgClass(h,r.unSelected),sf.base.isNullOrUndefined(r.highlightColor)||""===r.highlightColor||(h.setAttribute("stroke",v[2]),"None"===r.highlightPattern&&h.setAttribute("fill",v[2])),"e-chart-focused"==a.trim()&&(a=""),""===a&&""===l||"EJ2-Trackball"===a.trim()?this.removeSvgClass(h,t):(this.addSvgClass(h,t),t.indexOf("highlight")>0&&""!==r.highlightColor&&!sf.base.isNullOrUndefined(r.highlightColor)&&(h.setAttribute("stroke",r.highlightColor),r.styleId.indexOf("highlight")>0&&"None"===r.highlightPattern&&h.setAttribute("fill",r.highlightColor)))),o&&l.indexOf(t)>-1&&this.addSvgClass(h,t))}},addSvgClass:function(e,t){var i=e.getAttribute("class")||"";-1===(i+=""!==i?" ":"").indexOf(t)&&e.setAttribute("class",i+t)},isRectSeries:function(e){return"Column"===e||"StackingColumn"===e||"StackingColumn100"===e||"Bar"===e||"StackingBar"===e||"StackingBar100"===e||"RangeColumn"===e||"Histogram"===e||"BoxAndWhisker"===e||"Waterfall"===e||"Pareto"===e||"RangeArea"===e||"SplineRangeArea"===e||"RangeStepArea"===e||"Hilo"===e||"HiloOpenClose"===e||"Candle"===e},pointIdRequired:function(e){return!(!this.isRectSeries(e)&&"Bubble"!==e&&"Scatter"!==e)},getSeriesElements:function(e,t){var i,o=[],s=!sf.base.isNullOrUndefined(document.getElementById(t.element.id+"SymbolGroup"+e))&&"Scatter"!==t.seriesTypes[e]&&"Bubble"!==t.seriesTypes[e]&&!this.isRectSeries(t.seriesTypes[e]);if(s?(i=this.findDOMElement(t.element.id+"SymbolGroup"+e).querySelectorAll("#"+t.element.id+"SymbolGroup"+e+" path"),o=this.addSeriesElements(o,i),sf.base.isNullOrUndefined(this.findDOMElement(t.element.id+"SymbolGroup"+e).querySelectorAll("#"+t.element.id+"SymbolGroup"+e+" ellipse"))||(i=this.findDOMElement(t.element.id+"SymbolGroup"+e).querySelectorAll("#"+t.element.id+"SymbolGroup"+e+" ellipse"),o=this.addSeriesElements(o,i))):sf.base.isNullOrUndefined(this.findDOMElement(t.element.id+"SeriesGroup"+e))||(i=this.findDOMElement(t.element.id+"SeriesGroup"+e).querySelectorAll("#"+t.element.id+"SeriesGroup"+e+" path"),o=this.addSeriesElements(o,i),sf.base.isNullOrUndefined(this.findDOMElement(t.element.id+"SeriesGroup"+e).querySelectorAll("#"+t.element.id+"SeriesGroup"+e+" ellipse"))||(i=this.findDOMElement(t.element.id+"SeriesGroup"+e).querySelectorAll("#"+t.element.id+"SeriesGroup"+e+" ellipse"),o=this.addSeriesElements(o,i))),(t.seriesTypes[e].indexOf("Area")>-1||t.seriesTypes[e].toLowerCase().indexOf("line")>-1)&&(s?(i=this.findDOMElement(t.element.id+"SeriesGroup"+e).querySelectorAll("#"+t.element.id+"SeriesGroup"+e+" path"),o.push.apply(o,this.addSeriesElements(o,i))):sf.base.isNullOrUndefined(this.findDOMElement(t.element.id+"SeriesGroup"+e))||(i=this.findDOMElement(t.element.id+"SeriesGroup"+e).querySelectorAll("#"+t.element.id+"SeriesGroup"+e+" path"),o=this.addSeriesElements(o,i))),"Histogram"===t.seriesTypes[e]){var r=this.findDOMElement(t.element.id+"_Series_"+e+"_NDLine");o.push(r)}return"Polar"!==t.seriesTypes[e]&&"Radar"!==t.seriesTypes[e]||"Scatter"!==t.drawType[e]||sf.base.isNullOrUndefined(this.findDOMElement(t.element.id+"SeriesGroup"+e))||(i=this.findDOMElement(t.element.id+"SeriesGroup"+e).querySelectorAll("#"+t.element.id+"SeriesGroup"+e+" path"),o=this.addSeriesElements(o,i),sf.base.isNullOrUndefined(this.findDOMElement(t.element.id+"SeriesGroup"+e).querySelectorAll("#"+t.element.id+"SeriesGroup"+e+" ellipse"))||(i=this.findDOMElement(t.element.id+"SeriesGroup"+e).querySelectorAll("#"+t.element.id+"SeriesGroup"+e+" ellipse"),o=this.addSeriesElements(o,i))),o},addSeriesElements:function(e,t){for(var i=0;i<t.length;i++){var o=document.getElementById(t[i].id);null!=o&&e.push(o),e=e.filter((function(e,t,i){return t===i.indexOf(e)}))}return e},findDOMElementByIndex:function(e,t,i,o){void 0===i&&(i="");var s=e.element.id+"_Series_"+t.series+"_Point_"+t.point,r=e.seriesTypes[t.series];return s=!this.isRectSeries(r)&&"Scatter"!==r&&"Bubble"!==r&&o?s+"_Symbol"+i:s,"BoxAndWhisker"===r&&(s+="_BoxPath"),[this.findDOMElement(s),"RangeArea"!==r&&"SplineRangeArea"!==r||!o?null:this.findDOMElement(s+"1")]},applyStyles:function(e,t){for(var i=0,o=e;i<o.length;i++){var s=o[i];if(s&&(this.removeSvgClass(s,t.unSelected),this.addSvgClass(s,this.getSelectionClass(s.id,t)),t.styleId.indexOf("highlight")>0&&""!==t.highlightColor&&!sf.base.isNullOrUndefined(t.highlightColor)&&"None"===t.highlightPattern))if(s.id.indexOf("Group")>0)for(var r=0;r<s.children.length;r++)s.children[r].setAttribute("fill",t.highlightColor);else s.setAttribute("fill",t.highlightColor)}},getClusterElements:function(e,t){for(var i,s,r,n,a=[],l=document.getElementById(e.element.id+"SeriesCollection").querySelectorAll("[id*='SeriesGroup']"),h=(document.getElementById(e.element.id+"SeriesCollection").querySelectorAll("[id*='SymbolGroup']"),0);h<l.length;h++){r=l[h].querySelectorAll("#"+l[h].id+" path"),n=Array.prototype.slice.call(r);for(var d=0;d<n.length;d++){var c=n[d].getAttribute("data-point").split(",");t=new o(+c[4],t.point),this.isRectSeries(c[3])&&a.push(this.findDOMElementByIndex(e,t)[0]),a.push(this.findDOMElementByIndex(e,t,"","True"===c[9])[0]),i=this.generateStyle(n[d].id,e),s=document.querySelectorAll("."+i),this.findTrackballElements(s,i);var u="True"===c[9]&&this.isRectSeries(c[3])?2:1;!e.allowMultiSelection&&s.length>0&&s[0].id!==a[a.length-u].id&&this.removeSelection(e,t.series,s,i,!0)}}return a},clusterSelection:function(e,t){this.selection(e,t,this.getClusterElements(e,new o(t.series,t.point)))},removeSelection:function(e,t,i,s,r){var n,a=document.getElementById(e.element.id+"SeriesGroup"+t).querySelectorAll("#"+e.element.id+"SeriesGroup"+t+" path");Array.prototype.slice.call(a);if(i.length>0){for(var l=[],h=0;h<i.length;h++)l.push(i[h]);this.removeStyles(l,e),e.isSeriesMode=!0,this.addOrRemoveIndex(e.selectedDataIndexes,new o(t,NaN),e);for(var d=0;d<i.length;d++)if(n=i[d].id,s=this.generateStyle(n,e),document.querySelectorAll("."+s).length>0){for(var c=0,u=l;c<u.length;c++){var m=u[c];this.checkSelectionElements(m,s,!0,!0,t,e)}r=!1;break}r&&(e.isSeriesMode="Series"===e.selectionMode,this.blurEffect(n,e))}},isAlreadySelected:function(e,t){var i=e.target;if("click"===e.type?(t.currentMode=t.selectionMode,t.styleId=t.element.id+"_ej2_chart_selection"):"mousemove"!==e.type&&"pointermove"!==e.type||(t.currentMode=t.highlightMode,t.highlightDataIndexes=[],t.styleId=t.element.id+"_ej2_chart_highlight"),("None"!==t.highlightMode||t.enableHighlight)&&"None"===t.selectionMode&&"click"===e.type)return!1;if(("None"!==t.highlightMode||t.enableHighlight)&&0!==t.previousSelectedEle.length&&null!==t.previousSelectedEle[0]){t.previousSelectedEle.filter((function(e){return null!==e}));var o=i.parentNode.id,s=void 0;i.parentNode&&(s=o.indexOf("Point")>0||o.indexOf("Symbol")>0);for(var r=0;r<t.previousSelectedEle.length;r++)if(t.previousSelectedEle[r].hasAttribute("class"))if(t.previousSelectedEle[r].getAttribute("class").indexOf("highlight")>-1&&(s||"click"===e.type)){if(t.previousSelectedEle[r].removeAttribute("class"),""!==t.highlightColor&&!sf.base.isNullOrUndefined(t.highlightColor)&&"None"===t.highlightPattern)if(t.previousSelectedEle[r].id.indexOf("Group")>0)for(var n=0;n<t.previousSelectedEle[r].children.length;n++){var a=t.previousSelectedEle[r].children[n];a.setAttribute("fill",a.getAttribute("data-point").split(",")[2])}else t.previousSelectedEle[r].setAttribute("fill",t.previousSelectedEle[r].getAttribute("data-point").split(",")[2]);this.addOrRemoveIndex(t.highlightDataIndexes,this.indexFinder(t.previousSelectedEle[r].id),t)}else!s&&t.previousSelectedEle[r].getAttribute("class").indexOf("highlight")>-1&&this.performSelection(this.indexFinder(t.previousSelectedEle[r].id),t,t.previousSelectedEle[r])}return!0},performSelection:function(e,t,i){if(t.isSeriesMode="Series"===t.currentMode,"BoxAndWhisker"===t.seriesTypes[e.series]&&i&&i.id===t.element.id+"_Series_"+e.series+"_Point_"+e.point+"_BoxPath"&&(i=i.parentNode),"Area"===t.seriesTypes[e.series]&&("Point"===t.currentMode||"Cluster"===t.currentMode)&&i&&i.id===t.element.id+"_Series_"+e.series){var o=this.generateStyle(i.id,t),s=document.querySelectorAll("."+o);sf.base.isNullOrUndefined(s)||(this.findTrackballElements(s,o),this.blurEffect(i.id,t,"True"===i.getAttribute("data-point").split(",")[8]))}switch(t.currentMode){case"Series":this.selection(t,e,this.getSeriesElements(e.series.toString(),t)),this.selectionComplete(t,e,t.selectionMode),this.blurEffect(i.id,t);break;case"Point":if(!isNaN(e.point)&&i){var r=[];r.push(i),this.selection(t,e,r),this.selectionComplete(t,e,t.selectionMode),this.blurEffect(i.id,t)}break;case"Cluster":isNaN(e.point)||(this.clusterSelection(t,e),this.selectionComplete(t,e,t.selectionMode),this.blurEffect(i.id,t))}},selection:function(e,t,i){if(i=i.filter((function(e){return null!==e})),"Lasso"!==e.currentMode&&(e.allowMultiSelection||-1!==e.currentMode.indexOf("Drag")||-1!==e.styleId.indexOf("highlight")||"None"===e.selectionMode||this.removeMultiSelectElements(e,e.selectedDataIndexes,t,i)),!sf.base.isNullOrUndefined(i[0])){this.isRectSeries(e.seriesTypes[t.series])&&"Histogram"!==e.seriesTypes[t.series]&&i[0].id&&(document.getElementById(i[0].id+"_Symbol")?i.push(this.findDOMElement(i[0].id+"_Symbol")):-1!==i[0].id.indexOf("SeriesGroup")&&document.getElementById(i[0].id.replace("SeriesGroup","SymbolGroup"))&&i.push(this.findDOMElement(i[0].id.replace("SeriesGroup","SymbolGroup"))));var o=void 0,s=i[0]&&(i[0].getAttribute("class")||""),r=i[0].parentNode&&(i[0].parentNode.getAttribute("class")||"");""!==s&&"Cluster"!==e.currentMode&&this.findTrackballElements(i,s),i[0]&&s.indexOf(this.getSelectionClass(i[0].id,e))>-1?this.removeStyles(i,e):i[0].parentNode&&r.indexOf(this.getSelectionClass(i[0].id,e))>-1?this.removeStyles([i[0].parentNode],e):(e.previousSelectedEle="None"!==e.highlightMode||e.enableHighlight?i:[],this.applyStyles(i,e),o=!0),e.styleId.indexOf("highlight")>0&&("None"!==e.highlightMode||e.enableHighlight)?this.addOrRemoveIndex(e.highlightDataIndexes,t,e,o):this.addOrRemoveIndex(e.selectedDataIndexes,t,e,o)}},resetPreviousHighlightIndex:function(e){e.previousHighlightedIndex=-1},isTargetChanged:function(e,t){var i=!1,o=!1;return-1==t.previousHighlightedIndex&&(i=!0),t.previousHighlightedIndex==e&&(o=!0),t.previousHighlightedIndex=e,!o||i},legendSelection:function(e,t){var i,s,r,n=e.target.id,a=window.sfBlazor.getCompInstance(t),l=[this.id+"_chart_legend_text_",this.id+"_chart_legend_shape_marker_",this.id+"_chart_legend_shape_",this.id+"_chart_legend_g_"],h="None";if(!this.isDragSelection&&-1!=n.indexOf("legend")&&!a.isTouch){for(var d=0;d<l.length;d++)if(!n.indexOf(l[d])){s=parseInt(n.split(l[d])[1]);break}if(a.currentSeriesIndex=s,r=n.indexOf("text")<=0?f.findDOMElement(n):null,"mousemove"===e.type){if(n.indexOf("text")&&(r=f.findDOMElement(n.replace("text","shape"))),r.hasAttribute("class")&&!a.enableHighlight&&a.styleId.indexOf("highlight")>0&&("None"!==a.highlightMode||a.enableHighlight)&&a.styleId.indexOf("selection")>1&&"None"!==a.selectionMode)return;"None"==(h=a.highlightMode)&&a.enableHighlight&&(h="Series")}if(this.isAlreadySelected(e,a)&&!sf.base.isNullOrUndefined(this.findDOMElement(a.element.id+"SeriesGroup"+s)))if(this.seriesStyle=this.generateStyle(a.element.id+"SeriesGroup"+s,a),i=document.querySelectorAll("."+this.seriesStyle),this.isSeriesMode="Series"==h,this.isBlurEffectNeeded=!0,i.length>0)this.removeSelection(a,s,i,this.seriesStyle,this.isBlurEffectNeeded);else{for(var c=document.getElementById(a.element.id+"SeriesCollection").querySelectorAll("[id*='SeriesGroup']"),u=0;u<c.length;u++){var m=c[u].id.indexOf("TrendLineSeriesGroup")>-1?+c[u].id.split("TrendLineSeriesGroup")[1]:+c[u].id.split("SeriesGroup")[1];a.allowMultiSelection||m==s||(this.seriesStyle=this.generateStyle(c[u].id,a),i=document.querySelectorAll("."+this.seriesStyle),this.removeSelection(a,s,i,this.seriesStyle,this.isBlurEffectNeeded))}var p=this.getSeriesElements(s.toString(),a);for(d=0;d<p.length;d++)this.checkSelectionElements(p[d],this.seriesStyle,!1,!0,s,a,p);a.isSeriesMode=!0;var g=new o(s,0);this.selection(a,g,p),this.isSeriesMode="Series"==this.selectionMode,this.blurEffect(a.element.id,a,!0)}else!sf.base.isNullOrUndefined(r)&&n.indexOf("legend")>-1&&sf.base.isNullOrUndefined(this.findDOMElement(a.element.id+"SeriesGroup"+s))&&this.resetPreviousHighlightIndex(a),!sf.base.isNullOrUndefined(r)&&"None"==a.highlightMode&&n.indexOf("legend")>-1&&this.removeSelectionStyles(a.dataId)}},selectionComplete:function(e,t,i){var o,s,r,n,a,l,h,d,c,u,m=[],p=this.getSeriesElements(t.series.toString(),e);if("Cluster"===i){for(var g=0;g<p.length;g++)if(c=this.findDOMElement(p[g].id).getAttribute("data-point").split(",")[3],u=this.findDOMElement(p[g].id).getAttribute("data-point").split(",")[11],r=+this.findDOMElement(p[g].id).getAttribute("data-point").split(",")[10],"visible"===this.findDOMElement(p[g].id).getAttribute("visibility"))for(var f=0;f<e.selectedDataIndexes.length;f++)n=e.allowMultiSelection?e.selectedDataIndexes[f].point:t.point,a=+this.findDOMElement(p[g].id).getAttribute("data-point").split(",")[4],o=+this.findDOMElement(p[g].id).getAttribute("data-point").split(",")[0],s=+this.findDOMElement(p[g].id).getAttribute("data-point").split(",")[1],isNaN(n)||(h=s,d=o),"Category"===e.primaryXAxis?d=o.toLocaleString():"DateTime"===e.primaryXAxis&&(d=new Date(o)),"Indicator"!==u&&m.push({x:d,y:h,seriesIndex:a,pointIndex:n}),"RangeArea"!==c&&"SplineRangeArea"!==c||m.push({x:d,y:r,seriesIndex:a,pointIndex:n})}else if("Series"===i)if(e.allowMultiSelection)for(g=0;g<e.selectedDataIndexes.length;g++)a=e.selectedDataIndexes[g].series,m.push({seriesIndex:a});else a=e.selectedDataIndexes.length>0?e.selectedDataIndexes[0].series:0,m.push({seriesIndex:a});else if("Point"===i)for(g=0;g<e.selectedDataIndexes.length;g++)n=e.selectedDataIndexes[g].point,a=e.selectedDataIndexes[g].series,l=e.element.id+"_Series_"+a+"_Point_"+n,o=+this.findDOMElement(l).getAttribute("data-point").split(",")[0],s=+this.findDOMElement(l).getAttribute("data-point").split(",")[1],r=+this.findDOMElement(l).getAttribute("data-point").split(",")[10],isNaN(n)||(d=o,h=s,"Category"===e.primaryXAxis?d=o.toLocaleString():"DateTime"===e.primaryXAxis&&(d=new Date(o)),m.push({x:d,y:h,seriesIndex:a,pointIndex:n}));e.dotnetref.invokeMethodAsync("OnSelectionChange",m)},addTouchPointer:function(e,t,i){if(i.length>0)e=i;else if(0==(e=e.length>0?e:[]).length)e.push({pageX:t.mouseX,pageY:t.mouseY,pointerId:t.pointerId});else for(var o=0;o<e.length;o++)e[o].pointerId==t.pointerId?e[o]={pageX:t.mouseX,pageY:t.mouseY,pointerId:t.pointerId}:e.push({pageX:t.mouseX,pageY:t.mouseY,pointerId:t.pointerId});return e},onZoomingMouseMove:function(e,t){var i=[];"touchmove"===e.type&&(i=e.touches),t.zoomBase.isChartDrag&&(t.zoomBase.zoomSettings.isOnZoomStartCalled&&(t.dotnetref.invokeMethodAsync("TriggerZoomingEvents","OnZoomStart",!t.zoomBase.zoomingModule.isZoomStart),t.zoomBase.zoomingModule.isZoomStart=!1),t.isTouch&&(t.zoomBase.zoomingModule.touchMoveList=this.addTouchPointer(t.zoomBase.zoomingModule.touchMoveList,e,i),t.zoomBase.zoomSettings.enablePinchZooming&&t.zoomBase.zoomingModule.touchMoveList.length>1&&t.zoomBase.zoomingModule.touchStartList.length>1&&this.performPinchZooming(t)),this.renderZooming(e,t,t.isTouch))},onZoomingMouseEnd:function(e,t){t.zoomBase.isChartDrag=!1;var i=!(e.target.indexOf(t.element.id+"_ZoomOut")>-1)||e.target.indexOf(t.element.id+"_ZoomIn")>-1;(t.zoomBase.isChartDrag||i)&&(this.clearSelectionRect(t.element.id+"_ZoomArea"),this.performZoomRedraw(t),t.zoomBase.zoomSettings.isOnZoomEndCalled&&!t.zoomToolkitBase.isReset&&t.zoomBase.zoomingModule.zoomAxes.length>0&&t.dotnetref.invokeMethodAsync("TriggerZoomingEvents","OnZoomEnd",!1),t.zoomBase.zoomingModule.isZoomStart=!0),t.isTouch&&(t.zoomBase.isDoubleTap&&sf.svgbase.withInAreaBounds(t.mouseX,t.mouseY,t.userInteractionBase.axisClipRect)&&1==t.zoomBase.zoomingModule.touchStartList.length&&t.zoomBase.zoomingModule.isZoomed&&(this.zoomToolkitReset(t),t.zoomBase.zoomingModule.isZoomStart=!0),t.zoomBase.zoomingModule.touchStartList=[],t.zoomBase.isDoubleTap=!1),t.userInteractionBase.disableTrackTooltip=!1,t.zoomToolkitBase.isReset=!1,t.zoomBase.zoomingModule.zoomAxes=[]},onZoomingMouseDown:function(e,t){var i=sf.base.Browser.isDevice?20:30;t.zoomBase.mouseDownX=t.zoomBase.previousMouseMoveX=e.mouseX,t.zoomBase.mouseDownY=t.zoomBase.previousMouseMoveY=e.mouseY,t.isTouch&&(t.zoomBase.isDoubleTap=(new Date).getTime()<t.zoomBase.threshold&&!(e.target.indexOf(t.element.id+"_Zooming")>-1)&&(t.zoomBase.mouseDownX-i>=t.mouseX||t.zoomBase.mouseDownX+i>=t.mouseX)&&(t.zoomBase.mouseDownY-i>=t.mouseY||t.zoomBase.mouseDownY+i>=t.mouseY)&&(t.mouseX-i>=t.zoomBase.mouseDownX||t.mouseX+i>=t.zoomBase.mouseDownX)&&(t.mouseY-i>=t.zoomBase.mouseDownY||t.mouseY+i>=t.zoomBase.mouseDownY)),this.zoomingMouseDownHandler(e,t)},zoomingMouseDownHandler:function(e,t){e.target.indexOf(t.element.id+"_Zooming")>-1||e.target.indexOf(t.element.id+"_scrollBar")>-1||!sf.svgbase.withInAreaBounds(t.zoomBase.previousMouseMoveX,t.zoomBase.previousMouseMoveY,t.userInteractionBase.axisClipRect)||(t.zoomBase.isChartDrag=!0),t.isTouch&&(t.zoomBase.zoomingModule.touchStartList=this.addTouchPointer(t.zoomBase.zoomingModule.touchStartList,e,e.touches))},zoomingMouseWheelHandler:function(e,t){var i=e.target;t.zoomBase.zoomSettings&&t.zoomBase.zoomSettings.enableMouseWheelZooming&&sf.svgbase.withInAreaBounds(t.mouseX,t.mouseY,t.userInteractionBase.axisClipRect)&&!(i.id.indexOf("scrollbar")>-1)&&this.performMouseWheelZooming(e,t.mouseX,t.mouseY,t,t.userInteractionBase.axes),this.isMouseWheelScroll(i.id)&&(t.scrollbarBase.isScrollWheel=!t.scrollbarBase.isScrollWheel||t.scrollbarBase.isScrollWheel,this.performMouseWheelScrolling(e,t.mouseX,t.mouseY,t,t.userInteractionBase.axes))},renderZooming:function(e,t,i){this.calculateZoomAxesRange(t),!t.zoomBase.zoomSettings.enableSelectionZooming||t.zoomBase.zoomSettings.enablePan||i&&(!t.zoomBase.isDoubleTap||1!=t.zoomBase.zoomingModule.touchStartList.length)||t.zoomBase.zoomingModule.isPanning&&!t.zoomBase.isDoubleTap?t.zoomBase.zoomingModule.isPanning&&t.zoomBase.isChartDrag&&(!i||i&&1==t.zoomBase.zoomingModule.touchStartList.length)&&(t.zoomBase.zoomingModule.pinchTarget=i?e.target:null,this.doPan(t,t.zoomBase.axisCollections)):(t.zoomBase.zoomingModule.isPanning=!!t.zoomBase.zoomingModule.isDevice||t.zoomBase.zoomingModule.isPanning,t.zoomBase.zoomingModule.performedUI=!0,this.drawZoomingRectangle(t))},calculateZoomAxesRange:function(e){for(var t,i,o=0;o<e.zoomBase.axisCollections.length;o++){var s=e.zoomBase.axisCollections[o];i=s.visibleRange,e.zoomBase.zoomingModule.zoomAxes.length>o&&e.zoomBase.zoomingModule.zoomAxes[o]&&!e.zoomBase.delayRedraw?(e.zoomBase.zoomingModule.zoomAxes[o].min=i.start,e.zoomBase.zoomingModule.zoomAxes[o].delta=i.delta):(t={actualMin:s.actualRange.start,actualDelta:s.actualRange.delta,min:i.start,delta:i.delta},e.zoomBase.zoomingModule.zoomAxes[o]=t)}},drawZoomingRectangle:function(e){var t=e.userInteractionBase.axisClipRect,i=new s(e.zoomBase.previousMouseMoveX,e.zoomBase.previousMouseMoveY),o=new s(e.mouseX,e.mouseY),r=this.getElement(e.element.id+"_svg"),n=e.zoomBase.zoomingModule.zoomingRect=this.getRectLocation(i,o,t);if(n.width>0&&n.height>0){e.zoomBase.zoomingModule.isZoomed=!0,e.userInteractionBase.disableTrackTooltip=e.isChartZoom=!0,this.setAttribute(e.userInteractionBase.isStockChart?e.element.id:r.id,"cursor","crosshair"),"X"==e.zoomBase.zoomingModule.zooming.mode?(n.height=t.height,n.y=t.y):"Y"==e.zoomBase.zoomingModule.zooming.mode&&(n.width=t.width,n.x=t.x);var a=this.getElement(e.element.id+e.zoomBase.zoomToolkitId);if(a&&(a.style.visibility="hidden"),r)r.appendChild(e.userInteractionBase.svgRenderer.drawRectangle(new c(e.element.id+"_ZoomArea",e.zoomBase.zoomingModule.zooming.themeStyleSelectionRectFill,{color:e.zoomBase.zoomingModule.zooming.themeStyleSelectionRectStroke,width:1},1,n,0,0,"","3")));else document.getElementById(e.element.id).appendChild(e.userInteractionBase.svgRenderer.drawRectangle(new c(e.element.id+"_ZoomArea",e.zoomBase.zoomingModule.zooming.themeStyleSelectionRectFill,{color:e.zoomBase.zoomingModule.zooming.themeStyleSelectionRectStroke,width:1},1,n,0,0,"","3")))}},getRectLocation:function(e,t,i){var o=t.x<i.x?i.x:t.x>i.x+i.width?i.x+i.width:t.x,s=t.y<i.y?i.y:t.y>i.y+i.height?i.y+i.height:t.y;return new sf.svgbase.Rect(o>e.x?e.x:o,s>e.y?e.y:s,Math.abs(o-e.x),Math.abs(s-e.y))},getVisibleRangeModel:function(e,t){return{min:e.start,max:e.end,interval:t,delta:e.delta}},minMax:function(e,t,i){return isFinite(e)?e>i?i:e<t?t:e:0},doPan:function(e,t,i,o,s){if(void 0===i&&(i=0),void 0===o&&(o=0),void 0===s&&(s=!1),e.userInteractionBase.startMove&&e.crosshairBase.crosshair.enable)e.userInteractionBase.disableTrackTooltip=!1;else{e.isChartPanning=!0,e.zoomBase.zoomingModule.isZoomed=!0,e.zoomBase.zoomingModule.offset=e.zoomBase.delayRedraw?e.zoomBase.zoomingModule.offset:e.userInteractionBase.axisClipRect,e.zoomBase.delayRedraw=e.userInteractionBase.disableTrackTooltip=e.isChartZoom=!0;for(var r,n,a,l=[],h=[],d=[],c=[],u=0,m=t;u<m.length;u++){var p=m[u];r=p.zoomFactor,n=p.zoomPosition,a=Math.max(1/this.minMax(r,0,1),1),n="Horizontal"==p.orientation?this.minMax(p.zoomPosition+(0!=i?i:e.zoomBase.previousMouseMoveX-e.mouseX)/p.rect.w/a,0,1-p.zoomFactor):this.minMax(p.zoomPosition-(0!=o?o:e.zoomBase.previousMouseMoveY-e.mouseY)/p.rect.h/a,0,1-p.zoomFactor),p.isZoomingScrollBar&&(p.isScrollUI=!1),l.push({axisName:p.name,zoomFactor:r,zoomPosition:n,axisRange:this.getVisibleRangeModel(p.visibleRange,p.visibleInterval)})}var g={name:"OnZooming",axisCollection:l};if(e.zoomBase.zoomingEventArgs=g,!g.cancel){for(var f=function(e){t.filter((function(t){t.name==e.axisName&&(h.push(t.name),d.push(e.zoomFactor),c.push(e.zoomPosition),t.zoomFactor=e.zoomFactor,t.zoomPosition=e.zoomPosition)}))},v=0,b=l;v<b.length;v++){f(b[v])}this.performDeferredZoom(e,h,d,c,s)}}},doZoom:function(e,t,i){var o=e.zoomBase.zoomingModule.zoomingRect;e.zoomBase.zoomingModule.isPanning=e.zoomBase.zoomSettings.enablePan||e.zoomBase.zoomingModule.isPanning;for(var s,r,n=[],a=0,l=t;a<l.length;a++){var h=l[a];s=h.zoomFactor,r=h.zoomPosition,"Horizontal"==h.orientation&&"Y"!=e.zoomBase.zoomingModule.zooming.mode?(r+=Math.abs((o.x-i.x)/i.width)*h.zoomFactor,s*=o.width/i.width):"Vertical"==h.orientation&&"X"!=e.zoomBase.zoomingModule.zooming.mode&&(r+=(1-Math.abs((o.height+(o.y-i.y))/i.height))*h.zoomFactor,s*=o.height/i.height),n.push({axisName:h.name,zoomFactor:s<.001?h.zoomFactor:s,zoomPosition:s<.001?h.zoomPosition:r,axisRange:this.getVisibleRangeModel(h.visibleRange,h.visibleInterval)})}var d={name:"OnZooming",axisCollection:n};if(e.zoomBase.zoomingEventArgs=d,!d.cancel){for(var c=function(e){t.filter((function(t){t.name==e.axisName&&(t.zoomFactor=e.zoomFactor,t.zoomPosition=e.zoomPosition)}))},u=0,m=n;u<m.length;u++){c(m[u])}e.zoomBase.zoomingModule.zoomingRect=new sf.svgbase.Rect(0,0,0,0),this.performZoomRedraw(e)}},performZoomRedraw:function(e,t){if(void 0===t&&(t=!1),e.zoomBase.zoomingModule.isZoomed){if(e.zoomBase.zoomingModule.zoomingRect.width>0&&e.zoomBase.zoomingModule.zoomingRect.height>0)e.zoomBase.zoomingModule.performedUI=!0,this.doZoom(e,e.userInteractionBase.axes,e.userInteractionBase.axisClipRect),e.zoomBase.isDoubleTap=!1,this.setAttribute(e.element.id+"_svg","cursor","auto");else if(e.userInteractionBase.disableTrackTooltip){e.userInteractionBase.disableTrackTooltip=e.zoomBase.delayRedraw=!1;var i={isZoomed:e.zoomBase.zoomingModule.isZoomed,isPanning:e.zoomBase.zoomingModule.isPanning,isDoubleTap:e.zoomBase.isDoubleTap,isWheelZoom:e.zoomBase.zoomingModule.isWheelZoom,performedUI:e.zoomBase.zoomingModule.performedUI,delayRedraw:e.zoomBase.delayRedraw,isChartDrag:e.zoomBase.isChartDrag,isChartPanning:e.isChartPanning};e.dotnetref.invokeMethodAsync("ZoomingComplete",e.zoomBase.zoomingEventArgs,i),t&&this.focusTarget(e.element.id)}e.isChartPanning=e.isChartZoom=!1}},clearSelectionRect:function(e){var t=document.getElementById(e);t&&(t.setAttribute("x","0"),t.setAttribute("y","0"),t.setAttribute("width","0"),t.setAttribute("height","0"))},isMouseWheelScroll:function(e){return-1!=e.indexOf("scrollBarThumb_")||-1!=e.indexOf("scrollBarBackRect_")||-1!=e.indexOf("scrollBar_leftCircle_")||-1!=e.indexOf("scrollBar_rightCircle_")||-1!=e.indexOf("scrollBar_gripCircle_")||-1!=e.indexOf("scrollBar_leftArrow_")||-1!=e.indexOf("scrollBar_rightArrow_")||-1!=e.indexOf("scrollBar_gripCircle")},getAxisName:function(e){var t=[];return f.isExist(e,"_scrollBar_svg")?(t=e.split("_scrollBar_svg"))[t.length-1]:(t=e.split("_"))[t.length-1]},calculateDelta:function(e,t,i,o){for(var s=0;s<t.axes.length;s++)e.name==t.axes[s].name&&(t.axis=t.axes[s]);var r=t.scrollbarOptions[e.name];r.isVertical="Vertical"===e.orientation;var n=e.isAxisInverse;if(sf.base.isNullOrUndefined(r)||r&&!document.getElementById(r.svgObject.id))return null;var a=f.getScrollEventArgs(o,[i.id,i.id+"_scrollBar_svg"+e.name]);this.mouseX=a.mouseX,this.mouseY=a.mouseY,t.previousXY=r.isVertical&&n?this.mouseY:r.isVertical?r.width-this.mouseY:n?r.width-this.mouseX:this.mouseX;return this.delta=Math.max(-1,Math.min(1,o.wheelDelta||-o.detail)),this.delta>0?t.previousXY=t.previousXY+5:t.previousXY=t.previousXY-5,r},lazyLoadScrollChanged:function(e,t,i,o){var s,r=e.isAxisInverse,n=i.isVertical&&r?i.width-this.mouseY:i.isVertical?this.mouseY:this.mouseX,a=i.zoomPosition,l=i.zoomFactor;i.isLazyLoad&&t.isScrollWheel&&8!=i.thumbRectX&&i.thumbRectX+i.thumbRectWidth+8<i.width&&0!=this.delta&&(s=f.calculateLazyRange(t,null,this.delta));var h=s?s.currentRange:null;if(t.isScrollWheel){n=i.isVertical||r?i.width-n:n;var d=i.thumbRectX+(n-t.previousXY);i.svgObject.style.cursor="default",(n>=0||n<=d+i.thumbRectWidth)&&(i.thumbRectX=f.isWithIn(d,t)?d:i.thumbRectX,f.positionThumb(i.thumbRectX,i.thumbRectWidth,t),t.previousXY=n,f.setZoomFactorPosition(t,d,i.thumbRectWidth,!1)),s&&o.dotnetref.invokeMethodAsync("TriggerScrollEvents",f.getArgs("OnScrollChanged",e.name,a,l,h))}},lazyLoadScrollEnd:function(e,t,i,o){var s;i.startX=i.thumbRectX;var r=i.startX+i.thumbRectWidth+8+1;i.isLazyLoad||(e.zoomFactor=r>=i.width-1&&i.startX-8.5<=0?1:i.zoomFactor),i.isLazyLoad&&(8!=i.thumbRectX&&i.thumbRectX+i.thumbRectWidth+8<i.width&&0!=this.delta&&(s=f.calculateLazyRange(t,null,this.delta)),s&&o.dotnetref.invokeMethodAsync("TriggerScrollEvents",f.getArgs("OnScrollEnd",e.name,e.zoomPosition,e.zoomFactor,s.currentRange)),t.isScrollWheel=!1)},performMouseWheelScrolling:function(e,t,i,o,s){o.isWheelScrolling=!0;var r=o.scrollbarBase,n=e.target.id,a=this.getAxisName(n),l=s.filter((function(e){return e.name===a}))[0],h=this.calculateDelta(l,r,o,e);this.lazyLoadScrollChanged(l,r,h,o),this.lazyLoadScrollEnd(l,r,h,o)},performMouseWheelZooming:function(e,t,i,o,s){var r="mozilla"!==o.zoomBase.zoomingModule.browser.browserName||o.zoomBase.zoomingModule.browser.isPointer?e.wheelDelta>0?1:-1:-e.detail/3>0?1:-1;o.zoomBase.zoomingModule.isZoomed=!0,this.calculateZoomAxesRange(o),o.userInteractionBase.disableTrackTooltip=!0,o.zoomBase.zoomingModule.performedUI=!0,o.zoomBase.zoomingModule.isPanning=o.zoomBase.zoomSettings.enablePan||o.zoomBase.zoomingModule.isPanning;var n,a,l,h,d=[];o.zoomBase.zoomingModule.isWheelZoom=!0,o.isMouseWheelZoom=!0;for(var c=0,u=s;c<u.length;c++){var m=u[c];l=m.zoomFactor,h=m.zoomPosition,("Vertical"==m.orientation&&"X"!=o.zoomBase.zoomingModule.zooming.mode||"Horizontal"==m.orientation&&"Y"!=o.zoomBase.zoomingModule.zooming.mode)&&(a=Math.max(Math.max(1/this.minMax(m.zoomFactor,0,1),1)+.25*r,1))>=1&&(n=(n="Horizontal"==m.orientation?t/m.rect.w:1-i/m.rect.h)>1?1:n<0?0:n,l=1==a?1:this.minMax(1/a,0,1),h=1==a?0:m.zoomPosition+(m.zoomFactor-l)*n,m.zoomPosition==h&&m.zoomFactor==l||(l=h+l>1?1-h:l)),d.push({axisName:m.name,zoomFactor:l<.001?m.zoomFactor:l,zoomPosition:l<.001?m.zoomPosition:h,axisRange:this.getVisibleRangeModel(m.visibleRange,m.visibleInterval)})}var p={name:"OnZooming",axisCollection:d};if(o.zoomBase.zoomingEventArgs=p,!p.cancel){for(var g=function(e){s.filter((function(t){t.name==e.axisName&&(t.zoomFactor=e.zoomFactor,t.zoomPosition=e.zoomPosition)}))},f=0,v=d;f<v.length;f++){g(v[f])}this.performZoomRedraw(o)}o.zoomBase.zoomingModule.isWheelZoom=!1},performPinchZooming:function(e){if(e.zoomBase.zoomingModule.zoomingRect.width>0&&e.zoomBase.zoomingModule.zoomingRect.height>0||e.userInteractionBase.startMove&&e.crosshairBase.crosshair.enable)return!1;this.calculateZoomAxesRange(e),e.zoomBase.zoomingModule.isZoomed=e.zoomBase.zoomingModule.isPanning=e.zoomBase.zoomingModule.performedUI=!0,e.zoomBase.zoomingModule.offset=!e.zoomBase.delayRedraw||sf.base.isNullOrUndefined(e.zoomBase.zoomingModule.offset)?e.userInteractionBase.axisClipRect:e.zoomBase.zoomingModule.offset,e.zoomBase.delayRedraw=e.userInteractionBase.disableTrackTooltip=!0;var t=e.element.getBoundingClientRect(),i=e.zoomBase.zoomingModule.touchStartList,o=e.zoomBase.zoomingModule.touchMoveList,s=i[0].pageX-t.left,r=i[0].pageY-t.top,n=o[0].pageX-t.left,a=o[0].pageY-t.top,l=i[1].pageX-t.left,h=i[1].pageY-t.top,d=o[1].pageX-t.left,c=o[1].pageY-t.top,u=Math.abs(n-d)/Math.abs(s-l),m=Math.abs(a-c)/Math.abs(r-h),p=(e.zoomBase.zoomingModule.offset.x-n)/u+s,g=(e.zoomBase.zoomingModule.offset.y-a)/m+r,f=n-u*s,v=a-m*r,b=new sf.svgbase.Rect(p,g,e.zoomBase.zoomingModule.offset.width/u,e.zoomBase.zoomingModule.offset.height/m);if(!isNaN(u-u)&&!isNaN(m-m))switch(e.zoomBase.zoomSettings.mode){case"XY":this.setTransform(f,v,u,m,e,!0);break;case"X":this.setTransform(f,0,u,1,e,!0);break;case"Y":this.setTransform(0,v,1,m,e,!0)}return this.calculatePinchZoomFactor(e,b),!0},setTransform:function(e,t,i,o,s,r){var n,a=document.getElementById(s.element.id+"SeriesCollection"),l=document.getElementById(s.element.id+"IndicatorCollection");if(a&&a.setAttribute("clip-path","url(#"+s.element.id+"_ChartAreaClipRect_)"),l&&l.setAttribute("clip-path","url(#"+s.element.id+"_ChartAreaClipRect_)"),null!==e&&null!==t)for(var h=0;h<s.userInteractionBase.visibleSeries.length;h++){var d=s.userInteractionBase.visibleSeries[h],c=s.userInteractionBase.isInverted?d.y_Axis.rect.x:d.x_Axis.rect.x,u=s.userInteractionBase.isInverted?d.x_Axis.rect.y:d.y_Axis.rect.y;if(n="translate("+(e+(r?i*c:c))+","+(t+(r?o*u:u))+")",n=i||o?n+" scale("+i+" "+o+")":n,d.visible){var m=this.getElement(s.element.id+"SeriesGroup"+d.index),p=this.getElement(s.element.id+"IndicatorGroup"+d.index),g=this.getElement(s.element.id+"ErrorBarGroup"+d.index),f=this.getElement(s.element.id+"SymbolGroup"+d.index),v=this.getElement(s.element.id+"TextGroup"+d.index),b=this.getElement(s.element.id+"ShapeGroup"+d.index);"Indicator"===d.category&&p?p.setAttribute("transform",n):m&&m.setAttribute("transform",n);var x=this.getElement(s.element.id+"_Series_"+d.index+"_DataLabelCollections");g&&g.setAttribute("transform",n),f&&f.setAttribute("transform",n),v&&(v.setAttribute("visibility","hidden"),b.setAttribute("visibility","hidden")),x&&(x.style.visibility="hidden")}}},performDeferredZoom:function(e,t,i,o,s){var r,n;if(e.zoomBase.zoomSettings.enableDeferredZooming){switch(r=e.mouseX-e.zoomBase.mouseDownX,n=e.mouseY-e.zoomBase.mouseDownY,e.zoomBase.zoomSettings.mode){case"X":n=0;break;case"Y":r=0}this.setTransform(r,n,0,0,e,!1)}else e.dotnetref.invokeMethodAsync("ChartPan",t,i,o);e.zoomBase.previousMouseMoveX=e.mouseX,e.zoomBase.previousMouseMoveY=e.mouseY},calculatePinchZoomFactor:function(e,t){for(var i,o,s,r,n,a,l=e.zoomBase.zoomingModule.zooming.mode,h=[],d=0;d<e.userInteractionBase.axes.length;d++){var c=e.userInteractionBase.axes[d];if(n=c.zoomFactor,a=c.zoomPosition,"Horizontal"===c.orientation&&"Y"!==l||"Vertical"===c.orientation&&"X"!==l){"Horizontal"===c.orientation?(i=(s=t.x-e.zoomBase.zoomingModule.offset.x)/(r=c.rect.w/e.zoomBase.zoomingModule.zoomAxes[d].delta)+e.zoomBase.zoomingModule.zoomAxes[d].min,o=(s=t.x+t.width-e.zoomBase.zoomingModule.offset.x)/r+e.zoomBase.zoomingModule.zoomAxes[d].min):(s=t.y-e.zoomBase.zoomingModule.offset.y,r=c.rect.h/e.zoomBase.zoomingModule.zoomAxes[d].delta,i=(-1*s+c.rect.h)/r+e.zoomBase.zoomingModule.zoomAxes[d].min,o=(-1*(s=t.y+t.height-e.zoomBase.zoomingModule.offset.y)+c.rect.h)/r+e.zoomBase.zoomingModule.zoomAxes[d].min);var u=Math.min(i,o),m=Math.max(i,o),p=(u-e.zoomBase.zoomingModule.zoomAxes[d].actualMin)/e.zoomBase.zoomingModule.zoomAxes[d].actualDelta,g=(m-u)/e.zoomBase.zoomingModule.zoomAxes[d].actualDelta;a=p<0?0:p,n=g>1?1:g<.03?.03:g}h.push({axisName:c.name,zoomFactor:n,zoomPosition:a,axisRange:this.getVisibleRangeModel(c.visibleRange,c.visibleInterval)})}var f={name:"OnZooming",axisCollection:h};if(e.zoomBase.zoomingEventArgs=f,!f.cancel)for(var v=function(t){e.userInteractionBase.axes.filter((function(e){e.name==t.axisName&&(e.zoomFactor=t.zoomFactor,e.zoomPosition=t.zoomPosition)}))},b=0,x=h;b<x.length;b++){v(x[b])}},mouseCancelHandler:function(e){e.zoomBase.zoomingModule&&e.zoomBase.zoomingModule.isZoomed&&(this.clearSelectionRect(e.element.id+"_ZoomArea"),this.performZoomRedraw(e),e.zoomBase.zoomingModule.isPanning&&e.zoomBase.isChartDrag&&(e.zoomBase.zoomingModule.isPanning=e.zoomBase.isChartDrag=!1)),e.zoomBase.zoomingModule.pinchTarget=null,e.zoomBase.zoomingModule.touchStartList=[],e.zoomBase.zoomingModule.touchMoveList=[]},createClipRect:function(e){var t={id:e.element.id+"_ChartAreaClipRect_",x:e.userInteractionBase.axisClipRect.x,y:e.userInteractionBase.axisClipRect.y,width:e.userInteractionBase.axisClipRect.width,height:e.userInteractionBase.axisClipRect.height,fill:"transparent","stroke-width":1,stroke:"Gray"},i=e.userInteractionBase.svgRenderer.drawClipPath(t),o=this.getElement(e.element.id+"SeriesCollection");o&&o.appendChild(i)},updateClipRect:function(e,t){if(t.userInteractionBase.visibleSeries.length>0)for(var i=0;i<t.userInteractionBase.visibleSeries.length;i++){var o=t.userInteractionBase.visibleSeries[i];if(o.visible&&"Indicator"===o.category){var s=this.getElement(t.element.id+"IndicatorGroup"+o.index),r="translate("+o.clipRect.x.toString()+","+o.clipRect.y.toString()+")";s&&s.setAttribute("transform",r)}}if(e){var n=t.userInteractionBase.axisClipRect;e.setAttribute("x",n.x.toString()),e.setAttribute("y",n.y.toString()),e.setAttribute("width",n.width.toString()),e.setAttribute("height",n.height.toString()),e.parentNode.parentNode.parentNode.appendChild(e.parentNode.parentNode)}},isAxisZoomed:function(e){for(var t=!1,i=0,o=e;i<o.length;i++){var s=o[i];1===s.zoomFactor&&0===s.zoomPosition||(t=!0)}return t},applyZoomingToolkit:function(e){this.getElement(e.element.id+e.zoomBase.zoomToolkitId)&&(this.isAxisZoomed(e.userInteractionBase.axes)?(e.zoomBase.zoomingModule.isZoomed=!0,e.zoomBase.zoomingModule.isPanning&&this.zoomToolkitPan(e)):(e.zoomBase.zoomingModule.isZoomed=e.zoomBase.zoomingModule.isPanning=!1,this.setAttribute(e.element.id+"_svg","cursor","auto")))},zoomToolkitZoom:function(e){e.zoomBase.zoomingModule.isPanning=!1;this.setAttribute(e.element.id+"_svg","cursor","auto"),this.setAttribute(e.element.id+e.zoomBase.zoomToolkitZoomIn,"opacity","1"),this.setAttribute(e.element.id+e.zoomBase.zoomToolkitZoomOut,"opacity","Always"!==e.zoomBase.zoomSettings.toolbarDisplayMode||e.zoomBase.zoomingModule.isZoomed?"1":"0.2"),this.applySelection(e.element.id+e.zoomBase.zoomToolkitZoom,e.zoomToolkitBase.selectionColor),this.applySelection(e.element.id+e.zoomBase.zoomToolkitPan,"#737373"),e.zoomToolkitBase.selectedId&&this.setAttribute(e.zoomToolkitBase.selectedId,"fill","transparent"),e.zoomToolkitBase.selectedId=e.element.id+e.zoomBase.zoomToolkitZoom+"_1",this.setAttribute(e.zoomToolkitBase.selectedId,"fill",e.zoomToolkitBase.iconRectSelectionFill)},zoomToolkitPan:function(e){if(e.zoomBase.zoomingModule.isZoomed){e.zoomBase.zoomingModule.isPanning=!("Always"==e.zoomBase.zoomSettings.toolbarDisplayMode)||e.zoomBase.zoomingModule.isZoomed,this.setAttribute(e.element.id+"_svg","cursor","pointer");this.setAttribute(e.element.id+e.zoomBase.zoomToolkitZoomIn,"opacity","Always"!=e.zoomBase.zoomSettings.toolbarDisplayMode||e.zoomBase.zoomingModule.isZoomed?"0.2":"1"),this.setAttribute(e.element.id+e.zoomBase.zoomToolkitZoomOut,"opacity","0.2"),this.applySelection(e.element.id+e.zoomBase.zoomToolkitZoom,"#737373"),this.applySelection(e.element.id+e.zoomBase.zoomToolkitPan,e.zoomToolkitBase.selectionColor),e.zoomToolkitBase.selectedId&&this.setAttribute(e.zoomToolkitBase.selectedId,"fill","transparent"),e.zoomToolkitBase.selectedId=e.element.id+e.zoomBase.zoomToolkitPan+"_1",this.setAttribute(e.zoomToolkitBase.selectedId,"fill",e.zoomToolkitBase.iconRectSelectionFill)}},zoomToolkitZoomIn:function(e,t){e.zoomBase.zoomingModule.isZoomed=!0,this.zoomInOutCalculation(1,e,t)},zoomToolkitZoomOut:function(e,t){this.zoomInOutCalculation(-1,e,t)},zoomToolkitReset:function(e){if(!e.zoomBase.zoomingModule.isDevice&&"Always"!==e.zoomBase.zoomSettings.toolbarDisplayMode){var t=this.getElement(e.element.id+e.zoomBase.zoomToolkitId);t&&(t.style.visibility="hidden")}this.zoomToolkitRemoveTooltip(e);for(var i=[],o=0,s=e.userInteractionBase.axes;o<s.length;o++){var r=s[o];r.zoomFactor=1,r.zoomPosition=0,i.push({axisName:r.name,zoomFactor:r.zoomFactor,zoomPosition:r.zoomPosition,axisRange:this.getVisibleRangeModel(r.visibleRange,r.visibleInterval)})}var n={name:"OnZoomEnd",axisCollection:i};n.cancel||(e.zoomToolkitBase.isReset=!0,this.zoomToolkitSetDeferredZoom(e,n))},zoomInOutCalculation:function(e,t,i){if(!t.zoomBase.zoomingModule.isPanning&&("0.2"!=i||"Always"===t.zoomBase.zoomSettings.toolbarDisplayMode)){t.zoomBase.zoomSettings.isOnZoomStartCalled&&(t.dotnetref.invokeMethodAsync("TriggerZoomingEvents","OnZoomStart",!1),t.zoomBase.zoomingModule.isZoomStart=!1);var o=t.zoomBase.zoomingModule.zooming.mode,s=void 0,r=void 0,n=void 0;t.userInteractionBase.disableTrackTooltip=t.zoomBase.delayRedraw=!0;for(var a=[],l=0,h=t.userInteractionBase.axes;l<h.length;l++){var d=h[l];"Horizontal"==d.orientation&&"Y"!=o||"Vertical"==d.orientation&&"X"!=o?(r=1==(s=Math.max(Math.max(1/this.minMax(d.zoomFactor,0,1),1)+.25*e,1))?1:this.minMax(1/s,0,1),n=1==s?0:d.zoomPosition+.5*(d.zoomFactor-r),d.zoomPosition==n&&d.zoomFactor==r||(r=n+r>1?1-n:r),a.push({axisName:d.name,zoomFactor:r,zoomPosition:n,axisRange:this.getVisibleRangeModel(d.visibleRange,d.visibleInterval)})):a.push({axisName:d.name,zoomFactor:d.zoomFactor,zoomPosition:d.zoomPosition,axisRange:this.getVisibleRangeModel(d.visibleRange,d.visibleInterval)})}var c={name:"OnZooming",axisCollection:a};if(t.zoomBase.zoomingEventArgs=c,!c.cancel)for(var u=function(e){t.userInteractionBase.axes.filter((function(t){t.name==e.axisName&&(t.zoomFactor=e.zoomFactor,t.zoomPosition=e.zoomPosition)}))},m=0,p=a;m<p.length;m++){u(p[m])}}},zoomToolkitRemoveTooltip:function(e){var t=e.zoomToolkitBase.hoverId;t&&(this.setAttribute(t,"fill",e.zoomBase.zoomingModule.isPanning?t.indexOf("_Pan_")>-1?e.zoomToolkitBase.iconRectSelectionFill:"transparent":t.indexOf("_Zoom_")>-1?e.zoomToolkitBase.iconRectSelectionFill:"transparent"),this.setAttribute(t.replace("_1","_2"),"fill",e.zoomBase.zoomingModule.isPanning?t.indexOf("_Pan_")>-1?e.zoomToolkitBase.selectionColor:e.zoomToolkitBase.fillColor:t.indexOf("_Zoom_")>-1?e.zoomToolkitBase.selectionColor:e.zoomToolkitBase.fillColor),this.setAttribute(t.replace("_1","_3"),"fill",e.zoomBase.zoomingModule.isPanning?e.zoomToolkitBase.fillColor:t.indexOf("_Zoom_")>-1?e.zoomToolkitBase.selectionColor:e.zoomToolkitBase.fillColor),e.dotnetref.invokeMethodAsync("SetSelectedIcon",t,!1)),this.removeElement(e.zoomBase.chartZoomTip)},zoomToolkitShowTooltip:function(e,t,i,o){this.zoomToolkitRemoveTooltip(e);var s=o.clientX-(sf.svgbase.measureText(i,{size:"10px",fontWeight:"Normal",fontStyle:"Normal",fontFamily:"Segoe UI"}).width+5),r=t+"_1";e.zoomToolkitBase.hoverId=r,this.setAttribute(r,"fill",e.zoomToolkitBase.iconRectOverFill),this.setAttribute(t+"_2","fill",e.zoomToolkitBase.selectionColor),this.setAttribute(t+"_3","fill",e.zoomToolkitBase.selectionColor),t&&"1"===document.getElementById(t).getAttribute("opacity")?document.getElementById(t).setAttribute("cursor","pointer"):document.getElementById(t).setAttribute("cursor","auto"),e.isTouch||this.createTooltip(e.zoomBase.chartZoomTip,i,o.clientY+10,s,"10px"),e.dotnetref.invokeMethodAsync("SetSelectedIcon",t,!0)},zoomToolkitSetDeferredZoom:function(e,t){e.userInteractionBase.disableTrackTooltip=!1,e.zoomBase.zoomingModule.isZoomed=e.zoomBase.zoomingModule.isPanning=e.zoomBase.isChartDrag=e.zoomBase.delayRedraw=!1,e.zoomBase.zoomingModule.touchMoveList=e.zoomBase.zoomingModule.touchStartList=[],e.zoomBase.zoomingModule.pinchTarget=null,e.dotnetref.invokeMethodAsync("ZoomToolkitSetDeferredZoom",t)},zoomToolkitTooltip:function(e,t,i){var o,s=e.zoomBase;if(t.indexOf(s.zoomToolkitZoom)>-1&&!(t.indexOf(s.zoomToolkitZoomIn)>-1||t.indexOf(s.zoomToolkitZoomOut)>-1))o=e.element.id+s.zoomToolkitZoom;else if(t.indexOf(s.zoomToolkitZoomIn)>-1)o=e.element.id+s.zoomToolkitZoomIn;else if(t.indexOf(s.zoomToolkitZoomOut)>-1)o=e.element.id+s.zoomToolkitZoomOut;else if(t.indexOf(s.zoomToolkitPan)>-1)o=e.element.id+s.zoomToolkitPan;else{if(!(t.indexOf(s.zoomToolkitReset)>-1))return;o=e.element.id+s.zoomToolkitReset}o&&f.zoomToolkitShowTooltip(e,o,this.getElement(o).getAttribute("aria-label"),i)},zoomToolkitMouseDown:function(e,t){var i=e.zoomBase;if(t.id.indexOf(i.zoomToolkitZoom)>-1&&!(t.id.indexOf(i.zoomToolkitZoomIn)>-1||t.id.indexOf(i.zoomToolkitZoomOut)>-1))this.zoomToolkitZoom(e);else if(t.id.indexOf(i.zoomToolkitZoomIn)>-1)this.zoomToolkitZoomIn(e,t.getAttribute("opacity"));else if(t.id.indexOf(i.zoomToolkitZoomOut)>-1)this.zoomToolkitZoomOut(e,t.getAttribute("opacity"));else if(t.id.indexOf(i.zoomToolkitPan)>-1)this.zoomToolkitPan(e);else{if(!(t.id.indexOf(i.zoomToolkitReset)>-1))return;this.zoomToolkitReset(e)}},setScrollbarOptions:function(e,t){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||(i.scrollbarBase=i.scrollbarBase?i.scrollbarBase:{},i.scrollbarBase.isResize=t.isResize,i.scrollbarBase.axes=t.axes,i.scrollbarBase.scrollbarThemeStyle=t.scrollbarThemeStyle,i.scrollbarBase.chartAreaType=t.chartAreaType,i.scrollbarBase.isScrollExist=t.isScrollExist,i.scrollbarBase.scrollbarOptions=i.scrollbarBase.scrollbarOptions?i.scrollbarBase.scrollbarOptions:{})},renderScrollbar:function(e,t){var i,o,s,r=window.sfBlazor.getCompInstance(e);r.axes=r.scrollbarBase.axes=t,r.calculateSecondaryOffset(r.element.id),sf.base.isNullOrUndefined(r.zoomBase.zoomingModule)?o=!1:s=(o=r.zoomBase.zoomingModule).isZoomed?o.isZoomed:this.isAxisZoomed(r.userInteractionBase.axes),r.scrollbarBase.chartId=r.id;for(var n=0;n<r.axes.length;n++)if(i=r.axes[n],r.scrollbarBase.axis=i,"PolarAxes"!=r.scrollbarBase.chartAreaType&&(s&&(i.zoomFactor<1||i.zoomPosition>0)||i.scrollbarSettings.enable&&(i.zoomFactor<=1||i.zoomPosition>=0)))!r.scrollElement&&"PolarAxes"!=r.scrollbarBase.chartAreaType&&i.visible&&(r.scrollElement=document.getElementById(r.id+"_scrollElement")),(o||r.scrollbarBase.isScrollExist&&i.scrollbarSettings.enable)&&this.getDefaults(r,i),this.getTheme(r),this.removeScrollSvg(r,i),this.createScrollSvg(i,r,r.userInteractionBase.svgRenderer),r.scrollbarBase.scrollbarOptions[i.name].svgObject.appendChild(this.renderElements(r,i)),r.scrollElement.appendChild(r.scrollbarBase.scrollbarOptions[i.name].svgObject);else{if(this.getElement(r.id+"_scrollElement").childNodes.length>0)var a=document.getElementById(r.id+"_scrollBar_svg"+i.name);a&&(a.style.display="none")}},renderElements:function(e,t){var i=t.isAxisInverse,o=e.scrollbarBase,s=o.scrollbarOptions[t.name],r=e.userInteractionBase.svgRenderer,n=r.createGroup({id:e.id+"scrollBar_"+t.name,transform:"translate("+(s.isVertical&&i?s.height:i?s.width:"0")+","+(s.isVertical&&i?"0":i?s.height:s.isVertical?s.width:"0")+") rotate("+(s.isVertical&&i?"90":s.isVertical?"270":i?"180":"0")+")"}),a=r.createGroup({id:e.id+"scrollBar_backRect_"+t.name}),l=r.createGroup({id:e.id+"scrollBar_thumb_"+t.name,transform:"translate(0,0)"});return this.backRect(o,r,a),this.thumb(o,r,l),this.renderCircle(o,r,l),this.arrows(o,r,l),this.thumbGrip(o,r,l,e.userInteractionBase.theme.indexOf("Fluent2")>-1),n.appendChild(a),n.appendChild(l),n},backRect:function(e,t,i){var o=e.scrollbarThemeStyle,s=t.drawRectangle(new c(e.chartId+"scrollBarBackRect_"+e.axis.name,o.backRect,{width:1,color:o.backRect},1,new sf.svgbase.Rect(0,0,e.scrollbarOptions[e.axis.name].width,e.scrollbarOptions[e.axis.name].height),0,0));i.appendChild(s)},thumb:function(e,t,i){e.scrollbarOptions[e.axis.name].startX=e.scrollbarOptions[e.axis.name].thumbRectX;var o=e.scrollbarThemeStyle;e.scrollbarOptions[e.axis.name].slider=t.drawRectangle(new c(e.chartId+"scrollBarThumb_"+e.axis.name,o.thumb,{width:1,color:""},1,new sf.svgbase.Rect(e.scrollbarOptions[e.axis.name].thumbRectX,0,e.scrollbarOptions[e.axis.name].thumbRectWidth,e.scrollbarOptions[e.axis.name].height))),i.appendChild(e.scrollbarOptions[e.axis.name].slider)},renderCircle:function(e,t,i){var o=e.scrollbarThemeStyle,s=new g(e.chartId+"scrollBar_leftCircle_"+e.axis.name,o.circle,{width:1,color:o.circle},1,e.scrollbarOptions[e.axis.name].thumbRectX,e.scrollbarOptions[e.axis.name].height/2,8),r=t.createDefs(),n=t.createGroup({id:e.chartId+e.axis.name+"_thumb_shadow"});r.innerText='<filter x="-25.0%" y="-20.0%" width="150.0%" height="150.0%" filterUnits="objectBoundingBox"id="scrollbar_shadow"><feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset><feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur><feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite><feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.16 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix></filter>',n.innerText='<use fill="black" fill-opacity="1" filter="url(#scrollbar_shadow)" xlink:href="#'+e.chartId+"scrollBar_leftCircle_"+e.axis.name+'"></use><use fill="black" fill-opacity="1" filter="url(#scrollbar_shadow)" xlink:href="#'+e.chartId+"scrollBar_rightCircle_"+e.axis.name+'"></use>',e.scrollbarOptions[e.axis.name].leftCircleEle=t.drawCircle(s),s.id=e.chartId+"scrollBar_rightCircle_"+e.axis.name,s.cx=e.scrollbarOptions[e.axis.name].thumbRectX+e.scrollbarOptions[e.axis.name].thumbRectWidth,e.scrollbarOptions[e.axis.name].rightCircleEle=t.drawCircle(s),i.appendChild(r),i.appendChild(e.scrollbarOptions[e.axis.name].leftCircleEle),i.appendChild(e.scrollbarOptions[e.axis.name].rightCircleEle),i.appendChild(n)},arrows:function(e,t,i){var o=e.scrollbarThemeStyle,s=new sf.svgbase.PathOption(e.chartId+"scrollBar_leftArrow_"+e.axis.name,o.arrow,1,o.arrow,1,"",""),r=e.scrollbarOptions[e.axis.name];r.leftArrowEle=t.drawPath(s),s.id=e.chartId+"scrollBar_rightArrow_"+e.axis.name,r.rightArrowEle=t.drawPath(s),this.setArrowDirection(e);var n="M "+(r.thumbRectX-4+1)+" "+r.height/2+" L "+(r.thumbRectX-4+6)+" 11 L "+(r.thumbRectX-4+6)+" 5 Z",a="M "+(r.thumbRectX+r.thumbRectWidth+4-.5)+" "+r.height/2+" L "+(r.thumbRectX+r.thumbRectWidth+4-6)+" 11.5 L "+(r.thumbRectX+r.thumbRectWidth+4-6)+" 4.5 Z";r.leftArrowEle.setAttribute("d",n),r.rightArrowEle.setAttribute("d",a),i.appendChild(r.leftArrowEle),i.appendChild(r.rightArrowEle)},setArrowDirection:function(e){var t=e.scrollbarOptions[e.axis.name],i=t.thumbRectX,o=t.thumbRectWidth,s=t.height,r="M "+(i-4+1)+" "+s/2+" L "+(i-4+6)+" 11 L "+(i-4+6)+" 5 Z",n="M "+(i+o+4-.5)+" "+s/2+" L "+(i+o+4-6)+" 11.5 L "+(i+o+4-6)+" 4.5 Z";t.leftArrowEle.setAttribute("d",r),t.rightArrowEle.setAttribute("d",n)},thumbGrip:function(e,t,i,o){var s=0,r=0,n=e.scrollbarThemeStyle,a=new g(e.chartId+"scrollBar_gripCircle0_"+e.axis.name,n.grip,{width:1,color:n.grip},1,0,0,1),l=e.scrollbarOptions[e.axis.name];l.gripCircle=t.createGroup({id:e.chartId+"scrollBar_gripCircle_"+e.axis.name,transform:"translate("+(l.thumbRectX+l.thumbRectWidth/2+5*(l.isVertical?1:-1)-(o?l.isVertical?-4:4:0))+","+(l.isVertical?"10":"5")+") rotate("+(l.isVertical?"180":"0")+")"});for(var h=1;h<=(o?10:6);h++)a.id=e.chartId+"scrollBar_gripCircle"+h+"_"+e.axis.name,a.cx=s,a.cy=r,l.gripCircle.appendChild(t.drawCircle(a)),s=h===(o?5:3)?0:s+5,r=h>=(o?5:3)?5:0;i.appendChild(l.gripCircle)},getTheme:function(e){},getDefaults:function(e,t){e.scrollbarBase.scrollbarOptions[t.name]||(e.scrollbarBase.scrollbarOptions[t.name]={});var i=e.scrollbarBase.scrollbarOptions[t.name];t.scrollbarSettings.enable&&(i.isLazyLoad=!0,this.getLazyDefaults(e,t)),i.isVertical="Vertical"===t.orientation,i.zoomFactor=i.isLazyLoad?i.zoomFactor:t.zoomFactor,i.zoomPosition=i.isLazyLoad?i.zoomPosition:t.zoomPosition;var o=i.zoomFactor*(i.isVertical?t.rect.h:t.rect.w);o=o>40?o:40,i.scrollX=t.rect.x,i.scrollY=t.rect.y,i.width=i.isVertical?t.rect.h:t.rect.w,i.height=16;var s=i.zoomPosition*(i.isVertical?t.rect.h:i.width);s=i.isLazyLoad&&0==s&&i.startX?i.startX:s;var r=i.width-40-8;i.thumbRectX=s>r?r:s<8?8:s,i.thumbRectWidth=e.scrollbarBase.isThumbDrag||e.isWheelScrolling?i.thumbRectWidth:o+i.thumbRectX<i.width-16?o:i.width-i.thumbRectX-8},getLazyDefaults:function(e,t){var i,o,s=t.valueType,r=t.scrollbarSettings,n=t.scrollbarSettings.range,a=t.visibleRange,l=t.scrollbarSettings.pointsLength,h=e.scrollbarBase.scrollbarOptions[t.name];switch(h.valueType=s=sf.base.isNullOrUndefined(r.range.minimum)&&sf.base.isNullOrUndefined(r.range.maximum)||!r.pointsLength?s:"Double",s){case"Double":case"Category":case"Logarithmic":i=n.minimum?n.minimum:l?0:a.start,o=n.maximum?n.maximum:l?l-1:a.end;break;case"DateTime":case"DateTimeCategory":i=n.minimum?Date.parse(n.minimum):a.start,o=n.maximum?Date.parse(n.maximum):a.end}"Category"!==t.valueType&&(i=Math.min(i,a.start),o=Math.max(o,a.end));var d=(a.end-a.start)/(o-i),c=(a.start-i)/(o-i);h.zoomFactor=n.minimum||n.maximum?d:h.zoomFactor?h.zoomFactor:t.maxPointLength/t.scrollbarSettings.pointsLength,h.zoomPosition=n.minimum||n.maximum?c:h.zoomPosition?h.zoomPosition:t.zoomPosition,h.scrollRange={start:i,end:o,delta:"Category"!=t.valueType||n.maximum?o-i:l},h.previousStart=a.start,h.previousEnd=a.end},createScrollSvg:function(e,t,i){var o=e.rect,s="Horizontal"===e.orientation,r=t.scrollbarBase.scrollbarOptions[e.name],n=t.scrollbarBase.isThumbDrag&&r.isLazyLoad?"-webkit-grabbing":"auto";r.svgObject=i.createSvg({id:t.element.id+"_scrollBar_svg"+e.name,width:r.isVertical?r.height:r.width,height:r.isVertical?r.width:r.height,style:"position: absolute;top: "+((e.isAxisOppositePosition&&s?-16:0)+o.y+t.userInteractionBase.secondaryElementOffset.top)+"px;left: "+((e.isAxisOppositePosition&&!s?16:0)+o.x-(r.isVertical?r.height:0)+t.userInteractionBase.secondaryElementOffset.left)+"px;cursor:"+n+";"})},removeScrollSvg:function(e,t){document.getElementById(e.element.id+"_scrollBar_svg"+t.name)&&sf.base.remove(document.getElementById(e.element.id+"_scrollBar_svg"+t.name))},removeTooltipCrosshair:function(){for(var e in window.sfBlazor.instances)if(window.sfBlazor.instances.hasOwnProperty(e)){var t=window.sfBlazor.instances[e];!sf.base.isNullOrUndefined(t)&&!sf.base.isNullOrUndefined(document.getElementById(t.id))&&-1!==t.dataId.indexOf("sfChart-")&&t.tooltipBase.tooltipModule&&t.crosshairBase.crosshair&&(this.removeTooltip(10,t),this.removeCrosshair(t,10))}}};return f}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfchart');})})();