(window.webpackJsonp=window.webpackJsonp||[]).push([[3],{"./bundles/navigationsbase.js":function(e,t,s){"use strict";s.r(t);s("./modules/navigationsbase.js")},"./modules/navigationsbase.js":function(e,t){function s(e){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}window.sf=window.sf||{};window.sf.navigations=window.sf.base.extend({},window.sf.navigations,function(e){"use strict";var t,l,i=(t=function(e,s){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var s in t)t.hasOwnProperty(s)&&(e[s]=t[s])})(e,s)},function(e,s){function l(){this.constructor=e}t(e,s),e.prototype=null===s?Object.create(s):(l.prototype=s.prototype,new l)}),r=function(e,t,l,i){var r,o=arguments.length,n=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,l):i;if("object"===("undefined"==typeof Reflect?"undefined":s(Reflect))&&"function"==typeof Reflect.decorate)n=Reflect.decorate(e,t,l,i);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(n=(o<3?r(n):o>3?r(t,l,n):r(t,l))||n);return o>3&&n&&Object.defineProperty(t,l,n),n},o=function(e){function t(t,s){return e.call(this,t,s)||this}return i(t,e),t.prototype.preRender=function(){this.browser=sf.base.Browser.info.name,this.browserCheck="mozilla"===this.browser,this.isDevice=sf.base.Browser.isDevice,this.customStep=!0;var e=this.element;this.ieCheck="edge"===this.browser||"msie"===this.browser,this.initialize(),""===e.id&&(e.id=sf.base.getUniqueID("hscroll"),this.uniqueId=!0),e.style.display="block",this.enableRtl&&e.classList.add("e-rtl")},t.prototype.render=function(){this.touchModule=new sf.base.Touch(this.element,{scroll:this.touchHandler.bind(this),swipe:this.swipeHandler.bind(this)}),sf.base.EventHandler.add(this.scrollEle,"scroll",this.scrollHandler,this),this.isDevice?(this.element.classList.add("e-scroll-device"),this.createOverlay(this.element)):this.createNavIcon(this.element),this.setScrollState()},t.prototype.setScrollState=function(){sf.base.isNullOrUndefined(this.scrollStep)||this.scrollStep<0?(this.scrollStep=this.scrollEle.offsetWidth,this.customStep=!1):this.customStep=!0},t.prototype.initialize=function(){var e=this.createElement("div",{className:"e-hscroll-content"}),t=this.createElement("div",{className:"e-hscroll-bar"});t.setAttribute("tabindex","-1");for(var s=this.element,l=0,i=[].slice.call(s.children);l<i.length;l++){var r=i[l];e.appendChild(r)}t.appendChild(e),s.appendChild(t),t.style.overflowX="hidden",this.scrollEle=t,this.scrollItems=e},t.prototype.getPersistData=function(){return this.addOnPersist(["scrollStep"])},t.prototype.getModuleName=function(){return"hScroll"},t.prototype.destroy=function(){var t=this.element;t.style.display="",t.classList.remove("e-hscroll"),t.classList.remove("e-scroll-device"),t.classList.remove("e-rtl");var s=sf.base.selectAll(".e-"+t.id+"_nav.e-scroll-nav",t),l=sf.base.selectAll(".e-scroll-overlay",t);[].slice.call(l).forEach((function(e){sf.base.detach(e)}));for(var i=0,r=[].slice.call(this.scrollItems.children);i<r.length;i++){var o=r[i];t.appendChild(o)}this.uniqueId&&this.element.removeAttribute("id"),sf.base.detach(this.scrollEle),s.length>0&&(sf.base.detach(s[0]),sf.base.isNullOrUndefined(s[1])||sf.base.detach(s[1])),sf.base.EventHandler.remove(this.scrollEle,"scroll",this.scrollHandler),this.touchModule.destroy(),this.touchModule=null,e.prototype.destroy.call(this)},t.prototype.disable=function(e){var t=sf.base.selectAll(".e-scroll-nav:not(.e-overlay)",this.element);e?this.element.classList.add("e-overlay"):this.element.classList.remove("e-overlay"),[].slice.call(t).forEach((function(t){t.setAttribute("tabindex",e?"-1":"0")}))},t.prototype.createOverlay=function(e){var t=e.id.concat("_nav"),s=this.createElement("div",{className:"e-scroll-overlay e-scroll-right-overlay"}),l="e-"+e.id.concat("_nav e-scroll-nav e-scroll-right-nav"),i=this.createElement("div",{id:t.concat("_right"),className:l}),r=this.createElement("div",{className:"e-nav-right-arrow e-nav-arrow e-icons"});i.appendChild(r);var o=this.createElement("div",{className:"e-scroll-overlay e-scroll-left-overlay"});this.ieCheck&&i.classList.add("e-ie-align"),e.appendChild(s),e.appendChild(i),e.insertBefore(o,e.firstChild),this.eventBinding([i])},t.prototype.createNavIcon=function(e){var t=e.id.concat("_nav"),s="e-"+e.id.concat("_nav e-scroll-nav e-scroll-right-nav"),l={role:"button",id:t.concat("_right"),"aria-label":"Scroll right"},i=this.createElement("div",{className:s,attrs:l});i.setAttribute("aria-disabled","false");var r=this.createElement("div",{className:"e-nav-right-arrow e-nav-arrow e-icons"}),o="e-"+e.id.concat("_nav e-scroll-nav e-scroll-left-nav"),n={role:"button",id:t.concat("_left"),"aria-label":"Scroll left"},a=this.createElement("div",{className:o+" e-overlay",attrs:n});a.setAttribute("aria-disabled","true");var c=this.createElement("div",{className:"e-nav-left-arrow e-nav-arrow e-icons"});a.appendChild(c),i.appendChild(r),e.appendChild(i),e.insertBefore(a,e.firstChild),this.ieCheck&&(i.classList.add("e-ie-align"),a.classList.add("e-ie-align")),this.eventBinding([i,a])},t.prototype.onKeyPress=function(e){var t=this;if("Enter"===e.key){this.keyTimer=window.setTimeout((function(){t.keyTimeout=!0,t.eleScrolling(10,e.target,!0)}),100)}},t.prototype.onKeyUp=function(e){"Enter"===e.key&&(this.keyTimeout?this.keyTimeout=!1:e.target.click(),clearTimeout(this.keyTimer))},t.prototype.eventBinding=function(e){var t=this;[].slice.call(e).forEach((function(e){new sf.base.Touch(e,{tapHold:t.tabHoldHandler.bind(t),tapHoldThreshold:500}),e.addEventListener("keydown",t.onKeyPress.bind(t)),e.addEventListener("keyup",t.onKeyUp.bind(t)),e.addEventListener("mouseup",t.repeatScroll.bind(t)),e.addEventListener("touchend",t.repeatScroll.bind(t)),e.addEventListener("contextmenu",(function(e){e.preventDefault()})),sf.base.EventHandler.add(e,"click",t.clickEventHandler,t)}))},t.prototype.repeatScroll=function(){clearInterval(this.timeout)},t.prototype.tabHoldHandler=function(e){var t=this,s=e.originalEvent.target;s=this.contains(s,"e-scroll-nav")?s.firstElementChild:s;this.timeout=window.setInterval((function(){t.eleScrolling(10,s,!0)}),50)},t.prototype.contains=function(e,t){return e.classList.contains(t)},t.prototype.eleScrolling=function(e,t,s){var l=this.element,i=t.classList;i.contains("e-scroll-nav")&&(i=t.querySelector(".e-nav-arrow").classList),this.contains(l,"e-rtl")&&this.browserCheck&&(e=-e),!this.contains(l,"e-rtl")||this.browserCheck||this.ieCheck?i.contains("e-nav-right-arrow")?this.frameScrollRequest(e,"add",s):this.frameScrollRequest(e,"",s):i.contains("e-nav-left-arrow")?this.frameScrollRequest(e,"add",s):this.frameScrollRequest(e,"",s)},t.prototype.clickEventHandler=function(e){this.eleScrolling(this.scrollStep,e.target,!1)},t.prototype.swipeHandler=function(e){var t,s=this.scrollEle;t=e.velocity<=1?e.distanceX/(10*e.velocity):e.distanceX/e.velocity;var l=.5;!function i(){var r=Math.sin(l);r<=0?window.cancelAnimationFrame(r):("Left"===e.swipeDirection?s.scrollLeft+=t*r:"Right"===e.swipeDirection&&(s.scrollLeft-=t*r),l-=.5,window.requestAnimationFrame(i))}()},t.prototype.scrollUpdating=function(e,t){"add"===t?this.scrollEle.scrollLeft+=e:this.scrollEle.scrollLeft-=e,this.enableRtl&&this.scrollEle.scrollLeft>0&&(this.scrollEle.scrollLeft=0)},t.prototype.frameScrollRequest=function(e,t,s){var l=this;if(s)this.scrollUpdating(e,t);else{this.customStep||[].slice.call(sf.base.selectAll(".e-scroll-overlay",this.element)).forEach((function(t){e-=t.offsetWidth}));!function s(){var i,r;l.contains(l.element,"e-rtl")&&l.browserCheck?(i=-e,r=-10):(i=e,r=10),i<10?window.cancelAnimationFrame(r):(l.scrollUpdating(r,t),e-=r,window.requestAnimationFrame(s))}()}},t.prototype.touchHandler=function(e){var t=this.scrollEle,s=e.distanceX;this.ieCheck&&this.contains(this.element,"e-rtl")&&(s=-s),"Left"===e.scrollDirection?t.scrollLeft=t.scrollLeft+s:"Right"===e.scrollDirection&&(t.scrollLeft=t.scrollLeft-s)},t.prototype.arrowDisabling=function(e,t){if(this.isDevice){var s=(sf.base.isNullOrUndefined(e)?t:e).querySelector(".e-nav-arrow");sf.base.isNullOrUndefined(e)?sf.base.classList(s,["e-nav-right-arrow"],["e-nav-left-arrow"]):sf.base.classList(s,["e-nav-left-arrow"],["e-nav-right-arrow"])}else e&&t&&(e.classList.add("e-overlay"),e.setAttribute("aria-disabled","true"),e.removeAttribute("tabindex"),t.classList.remove("e-overlay"),t.setAttribute("aria-disabled","false"),t.setAttribute("tabindex","0"));this.repeatScroll()},t.prototype.scrollHandler=function(e){var t=e.target,s=t.offsetWidth,l=(this.element,this.element.querySelector(".e-scroll-left-nav")),i=this.element.querySelector(".e-scroll-right-nav"),r=this.element.querySelector(".e-scroll-left-overlay"),o=this.element.querySelector(".e-scroll-right-overlay"),n=t.scrollLeft;if(n<=0&&(n=-n),this.isDevice&&(!this.enableRtl||this.browserCheck||this.ieCheck||(r=this.element.querySelector(".e-scroll-right-overlay"),o=this.element.querySelector(".e-scroll-left-overlay")),r.style.width=n<40?n+"px":"40px",t.scrollWidth-Math.ceil(s+n)<40?o.style.width=t.scrollWidth-Math.ceil(s+n)+"px":o.style.width="40px"),0===n)this.arrowDisabling(l,i);else if(Math.ceil(s+n+.1)>=t.scrollWidth)this.arrowDisabling(i,l);else{var a=this.element.querySelector(".e-scroll-nav.e-overlay");a&&(a.classList.remove("e-overlay"),a.setAttribute("aria-disabled","false"),a.setAttribute("tabindex","0"))}},t.prototype.onPropertyChanged=function(e,t){for(var s=0,l=Object.keys(e);s<l.length;s++){switch(l[s]){case"scrollStep":this.setScrollState();break;case"enableRtl":e.enableRtl?this.element.classList.add("e-rtl"):this.element.classList.remove("e-rtl")}}},r([sf.base.Property(null)],t.prototype,"scrollStep",void 0),t=r([sf.base.NotifyPropertyChanges],t)}(sf.base.Component),n=(l=function(e,t){return(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var s in t)t.hasOwnProperty(s)&&(e[s]=t[s])})(e,t)},function(e,t){function s(){this.constructor=e}l(e,t),e.prototype=null===t?Object.create(t):(s.prototype=t.prototype,new s)}),a=function(e,t,l,i){var r,o=arguments.length,n=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,l):i;if("object"===("undefined"==typeof Reflect?"undefined":s(Reflect))&&"function"==typeof Reflect.decorate)n=Reflect.decorate(e,t,l,i);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(n=(o<3?r(n):o>3?r(t,l,n):r(t,l))||n);return o>3&&n&&Object.defineProperty(t,l,n),n},c=function(e){function t(t,s){return e.call(this,t,s)||this}return n(t,e),t.prototype.preRender=function(){this.browser=sf.base.Browser.info.name,this.browserCheck="mozilla"===this.browser,this.isDevice=sf.base.Browser.isDevice,this.customStep=!0;var e=this.element;this.ieCheck="edge"===this.browser||"msie"===this.browser,this.initialize(),""===e.id&&(e.id=sf.base.getUniqueID("vscroll"),this.uniqueId=!0),e.style.display="block",this.enableRtl&&e.classList.add("e-rtl")},t.prototype.render=function(){this.touchModule=new sf.base.Touch(this.element,{scroll:this.touchHandler.bind(this),swipe:this.swipeHandler.bind(this)}),sf.base.EventHandler.add(this.scrollEle,"scroll",this.scrollEventHandler,this),this.isDevice?(this.element.classList.add("e-scroll-device"),this.createOverlayElement(this.element)):this.createNavIcon(this.element),this.setScrollState(),sf.base.EventHandler.add(this.element,"wheel",this.wheelEventHandler,this)},t.prototype.setScrollState=function(){sf.base.isNullOrUndefined(this.scrollStep)||this.scrollStep<0?(this.scrollStep=this.scrollEle.offsetHeight,this.customStep=!1):this.customStep=!0},t.prototype.initialize=function(){var e=sf.base.createElement("div",{className:"e-vscroll-content"}),t=sf.base.createElement("div",{className:"e-vscroll-bar"});t.setAttribute("tabindex","-1");for(var s=this.element,l=0,i=[].slice.call(s.children);l<i.length;l++){var r=i[l];e.appendChild(r)}t.appendChild(e),s.appendChild(t),t.style.overflow="hidden",this.scrollEle=t,this.scrollItems=e},t.prototype.getPersistData=function(){return this.addOnPersist(["scrollStep"])},t.prototype.getModuleName=function(){return"vScroll"},t.prototype.destroy=function(){var t=this.element;t.style.display="",sf.base.removeClass([this.element],["e-vscroll","e-scroll-device","e-rtl"]);var s=sf.base.selectAll(".e-"+t.id+"_nav.e-scroll-nav",t),l=sf.base.selectAll(".e-scroll-overlay",t);[].slice.call(l).forEach((function(e){sf.base.detach(e)}));for(var i=0,r=[].slice.call(this.scrollItems.children);i<r.length;i++){var o=r[i];t.appendChild(o)}this.uniqueId&&this.element.removeAttribute("id"),sf.base.detach(this.scrollEle),s.length>0&&(sf.base.detach(s[0]),sf.base.isNullOrUndefined(s[1])||sf.base.detach(s[1])),sf.base.EventHandler.remove(this.scrollEle,"scroll",this.scrollEventHandler),this.touchModule.destroy(),this.touchModule=null,e.prototype.destroy.call(this)},t.prototype.disable=function(e){var t=sf.base.selectAll(".e-scroll-nav:not(.e-overlay)",this.element);e?this.element.classList.add("e-overlay"):this.element.classList.remove("e-overlay"),[].slice.call(t).forEach((function(t){t.setAttribute("tabindex",e?"-1":"0")}))},t.prototype.createOverlayElement=function(e){var t=e.id.concat("_nav"),s=sf.base.createElement("div",{className:"e-scroll-overlay e-scroll-down-overlay"}),l="e-"+e.id.concat("_nav e-scroll-nav e-scroll-down-nav"),i=sf.base.createElement("div",{id:t.concat("down"),className:l}),r=sf.base.createElement("div",{className:"e-nav-down-arrow e-nav-arrow e-icons"});i.appendChild(r);var o=sf.base.createElement("div",{className:"e-scroll-overlay e-scroll-up-overlay"});this.ieCheck&&i.classList.add("e-ie-align"),e.appendChild(s),e.appendChild(i),e.insertBefore(o,e.firstChild),this.eventBinding([i])},t.prototype.createNavIcon=function(e){var t=e.id.concat("_nav"),s="e-"+e.id.concat("_nav e-scroll-nav e-scroll-down-nav"),l=sf.base.createElement("div",{id:t.concat("_down"),className:s});l.setAttribute("aria-disabled","false");var i=sf.base.createElement("div",{className:"e-nav-down-arrow e-nav-arrow e-icons"}),r="e-"+e.id.concat("_nav e-scroll-nav e-scroll-up-nav"),o=sf.base.createElement("div",{id:t.concat("_up"),className:r+" e-overlay"});o.setAttribute("aria-disabled","true");var n=sf.base.createElement("div",{className:"e-nav-up-arrow e-nav-arrow e-icons"});o.appendChild(n),l.appendChild(i),l.setAttribute("tabindex","0"),e.appendChild(l),e.insertBefore(o,e.firstChild),this.ieCheck&&(l.classList.add("e-ie-align"),o.classList.add("e-ie-align")),this.eventBinding([l,o])},t.prototype.onKeyPress=function(e){var t=this;if("Enter"===e.key){this.keyTimer=window.setTimeout((function(){t.keyTimeout=!0,t.eleScrolling(10,e.target,!0)}),100)}},t.prototype.onKeyUp=function(e){"Enter"===e.key&&(this.keyTimeout?this.keyTimeout=!1:e.target.click(),clearTimeout(this.keyTimer))},t.prototype.eventBinding=function(e){var t=this;[].slice.call(e).forEach((function(e){new sf.base.Touch(e,{tapHold:t.tabHoldHandler.bind(t),tapHoldThreshold:500}),e.addEventListener("keydown",t.onKeyPress.bind(t)),e.addEventListener("keyup",t.onKeyUp.bind(t)),e.addEventListener("mouseup",t.repeatScroll.bind(t)),e.addEventListener("touchend",t.repeatScroll.bind(t)),e.addEventListener("contextmenu",(function(e){e.preventDefault()})),sf.base.EventHandler.add(e,"click",t.clickEventHandler,t)}))},t.prototype.repeatScroll=function(){clearInterval(this.timeout)},t.prototype.tabHoldHandler=function(e){var t=this,s=e.originalEvent.target;s=this.contains(s,"e-scroll-nav")?s.firstElementChild:s;this.timeout=window.setInterval((function(){t.eleScrolling(10,s,!0)}),50)},t.prototype.contains=function(e,t){return e.classList.contains(t)},t.prototype.eleScrolling=function(e,t,s){var l=t.classList;l.contains("e-scroll-nav")&&(l=t.querySelector(".e-nav-arrow").classList),l.contains("e-nav-down-arrow")?this.frameScrollRequest(e,"add",s):l.contains("e-nav-up-arrow")&&this.frameScrollRequest(e,"",s)},t.prototype.clickEventHandler=function(e){this.eleScrolling(this.scrollStep,e.target,!1)},t.prototype.wheelEventHandler=function(e){e.preventDefault(),this.frameScrollRequest(this.scrollStep,e.deltaY>0?"add":"",!1)},t.prototype.swipeHandler=function(e){var t,s=this.scrollEle;t=e.velocity<=1?e.distanceY/(10*e.velocity):e.distanceY/e.velocity;var l=.5;!function i(){var r=Math.sin(l);r<=0?window.cancelAnimationFrame(r):("Up"===e.swipeDirection?s.scrollTop+=t*r:"Down"===e.swipeDirection&&(s.scrollTop-=t*r),l-=.02,window.requestAnimationFrame(i))}()},t.prototype.scrollUpdating=function(e,t){"add"===t?this.scrollEle.scrollTop+=e:this.scrollEle.scrollTop-=e},t.prototype.frameScrollRequest=function(e,t,s){var l=this;if(s)this.scrollUpdating(e,t);else{this.customStep||[].slice.call(sf.base.selectAll(".e-scroll-overlay",this.element)).forEach((function(t){e-=t.offsetHeight}));!function s(){e<10?window.cancelAnimationFrame(10):(l.scrollUpdating(10,t),e-=10,window.requestAnimationFrame(s))}()}},t.prototype.touchHandler=function(e){var t=this.scrollEle,s=e.distanceY;"Up"===e.scrollDirection?t.scrollTop=t.scrollTop+s:"Down"===e.scrollDirection&&(t.scrollTop=t.scrollTop-s)},t.prototype.arrowDisabling=function(e,t){if(this.isDevice){var s=(sf.base.isNullOrUndefined(e)?t:e).querySelector(".e-nav-arrow");sf.base.isNullOrUndefined(e)?sf.base.classList(s,["e-nav-down-arrow"],["e-nav-up-arrow"]):sf.base.classList(s,["e-nav-up-arrow"],["e-nav-down-arrow"])}else e.classList.add("e-overlay"),e.setAttribute("aria-disabled","true"),e.removeAttribute("tabindex"),t.classList.remove("e-overlay"),t.setAttribute("aria-disabled","false"),t.setAttribute("tabindex","0");this.repeatScroll()},t.prototype.scrollEventHandler=function(e){var t=e.target,s=t.offsetHeight,l=this.element.querySelector(".e-scroll-up-nav"),i=this.element.querySelector(".e-scroll-down-nav"),r=this.element.querySelector(".e-scroll-up-overlay"),o=this.element.querySelector(".e-scroll-down-overlay"),n=t.scrollTop;if(n<=0&&(n=-n),this.isDevice&&(r.style.height=n<40?n+"px":"40px",t.scrollHeight-Math.ceil(s+n)<40?o.style.height=t.scrollHeight-Math.ceil(s+n)+"px":o.style.height="40px"),0===n)this.arrowDisabling(l,i);else if(Math.ceil(s+n+.1)>=t.scrollHeight)this.arrowDisabling(i,l);else{var a=this.element.querySelector(".e-scroll-nav.e-overlay");a&&(a.classList.remove("e-overlay"),a.setAttribute("aria-disabled","false"),a.setAttribute("tabindex","0"))}},t.prototype.onPropertyChanged=function(e,t){for(var s=0,l=Object.keys(e);s<l.length;s++){switch(l[s]){case"scrollStep":this.setScrollState();break;case"enableRtl":e.enableRtl?this.element.classList.add("e-rtl"):this.element.classList.remove("e-rtl")}}},a([sf.base.Property(null)],t.prototype,"scrollStep",void 0),t=a([sf.base.NotifyPropertyChanges],t)}(sf.base.Component);function d(e,t,s,l,i,r){var n=e("div",{className:"e-menu-"+l});return t.appendChild(n),n.appendChild(s),r?(n.style.overflow="hidden",n.style.height=r+"px"):(n.style.maxHeight=t.style.maxHeight,t.style.overflow="hidden"),"vscroll"===l?new c({enableRtl:i},n).scrollStep=sf.base.select(".e-"+l+"-bar",t).offsetHeight/2:new o({enableRtl:i},n).scrollStep=sf.base.select(".e-"+l+"-bar",t).offsetWidth,n}return e.HScroll=o,e.VScroll=c,e.addScrolling=function(e,t,s,l,i,r){var o,n,a=t.parentElement;if("vscroll"===l?(o=r||t.getBoundingClientRect().height,n=s.getBoundingClientRect().height):(o=t.getBoundingClientRect().width,n=s.getBoundingClientRect().width),o<n)return d(e,t,s,l,i,r);if(a){var c=a.getBoundingClientRect().width;return c<o&&"hscroll"===l?(n=c,t.style.maxWidth=c+"px",d(e,t,s,l,i,r)):s}return s},e.destroyScroll=function(e,t,s){if(e){var l=sf.base.select(".e-menu-parent",t);l?s&&s!==l||(e.destroy(),t.parentElement.appendChild(l),sf.base.detach(t)):(e.destroy(),sf.base.detach(t))}},e}({}))}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{})})();