(window.webpackJsonp=window.webpackJsonp||[]).push([[2],{"./bundles/drawings.js":function(t,e,i){"use strict";i.r(e);i("./modules/drawings.js")},"./modules/drawings.js":function(t,e){function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}window.sf=window.sf||{},window.sf.drawings=function(t){"use strict";var e,r,n,o,s,a,h,d=(e=function(t,i){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,i)},function(t,i){function r(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(r.prototype=i.prototype,new r)}),l=function(t,e,r,n){var o,s=arguments.length,a=s<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"===("undefined"==typeof Reflect?"undefined":i(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var h=t.length-1;h>=0;h--)(o=t[h])&&(a=(s<3?o(a):s>3?o(e,r,a):o(e,r))||a);return s>3&&a&&Object.defineProperty(e,r,a),a},c=function(t){switch(t.type){case"Linear":return g;case"Radial":return m;default:return g}},f=function(t,e,i,r){this.left=t,this.right=e,this.top=i,this.bottom=r},p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return d(e,t),l([sf.base.Property(0)],e.prototype,"left",void 0),l([sf.base.Property(0)],e.prototype,"right",void 0),l([sf.base.Property(0)],e.prototype,"top",void 0),l([sf.base.Property(0)],e.prototype,"bottom",void 0),e}(sf.base.ChildProperty),u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return d(e,t),e.prototype.getClassName=function(){return"Stop"},l([sf.base.Property("")],e.prototype,"color",void 0),l([sf.base.Property(0)],e.prototype,"offset",void 0),l([sf.base.Property(1)],e.prototype,"opacity",void 0),e}(sf.base.ChildProperty),y=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return d(e,t),l([sf.base.Collection([],u)],e.prototype,"stops",void 0),l([sf.base.Property("None")],e.prototype,"type",void 0),l([sf.base.Property("")],e.prototype,"id",void 0),e}(sf.base.ChildProperty),g=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return d(e,t),l([sf.base.Property(0)],e.prototype,"x1",void 0),l([sf.base.Property(0)],e.prototype,"x2",void 0),l([sf.base.Property(0)],e.prototype,"y1",void 0),l([sf.base.Property(0)],e.prototype,"y2",void 0),e}(y),m=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return d(e,t),l([sf.base.Property(0)],e.prototype,"cx",void 0),l([sf.base.Property(0)],e.prototype,"cy",void 0),l([sf.base.Property(0)],e.prototype,"fx",void 0),l([sf.base.Property(0)],e.prototype,"fy",void 0),l([sf.base.Property(50)],e.prototype,"r",void 0),e}(y),x=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return d(e,t),l([sf.base.Property("white")],e.prototype,"fill",void 0),l([sf.base.Property("black")],e.prototype,"strokeColor",void 0),l([sf.base.Property("")],e.prototype,"strokeDashArray",void 0),l([sf.base.Property(1)],e.prototype,"strokeWidth",void 0),l([sf.base.Property(1)],e.prototype,"opacity",void 0),l([sf.base.ComplexFactory(c)],e.prototype,"gradient",void 0),e}(sf.base.ChildProperty),v=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return d(e,t),l([sf.base.Property("transparent")],e.prototype,"fill",void 0),e}(x),w=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return d(e,t),l([sf.base.Property("black")],e.prototype,"color",void 0),l([sf.base.Property("Arial")],e.prototype,"fontFamily",void 0),l([sf.base.Property(12)],e.prototype,"fontSize",void 0),l([sf.base.Property(!1)],e.prototype,"italic",void 0),l([sf.base.Property(!1)],e.prototype,"bold",void 0),l([sf.base.Property("CollapseSpace")],e.prototype,"whiteSpace",void 0),l([sf.base.Property("WrapWithOverflow")],e.prototype,"textWrapping",void 0),l([sf.base.Property("Center")],e.prototype,"textAlign",void 0),l([sf.base.Property("None")],e.prototype,"textDecoration",void 0),l([sf.base.Property("Wrap")],e.prototype,"textOverflow",void 0),l([sf.base.Property("transparent")],e.prototype,"fill",void 0),e}(x);(r=t.RotateTransform||(t.RotateTransform={}))[r.Self=1]="Self",r[r.Parent=2]="Parent",(n=t.ElementAction||(t.ElementAction={}))[n.None=0]="None",n[n.ElementIsPort=2]="ElementIsPort",n[n.ElementIsGroup=4]="ElementIsGroup",(o=t.ConnectorConstraints||(t.ConnectorConstraints={}))[o.None=1]="None",o[o.Select=2]="Select",o[o.Delete=4]="Delete",o[o.Drag=8]="Drag",o[o.DragSourceEnd=16]="DragSourceEnd",o[o.DragTargetEnd=32]="DragTargetEnd",o[o.DragSegmentThumb=64]="DragSegmentThumb",o[o.AllowDrop=128]="AllowDrop",o[o.Bridging=256]="Bridging",o[o.BridgeObstacle=512]="BridgeObstacle",o[o.InheritBridging=1024]="InheritBridging",o[o.PointerEvents=2048]="PointerEvents",o[o.Tooltip=4096]="Tooltip",o[o.InheritTooltip=8192]="InheritTooltip",o[o.Interaction=4218]="Interaction",o[o.ReadOnly=16384]="ReadOnly",o[o.Default=11838]="Default",(s=t.ThumbsConstraints||(t.ThumbsConstraints={}))[s.Rotate=2]="Rotate",s[s.ConnectorSource=4]="ConnectorSource",s[s.ConnectorTarget=8]="ConnectorTarget",s[s.ResizeNorthEast=16]="ResizeNorthEast",s[s.ResizeEast=32]="ResizeEast",s[s.ResizeSouthEast=64]="ResizeSouthEast",s[s.ResizeSouth=128]="ResizeSouth",s[s.ResizeSouthWest=256]="ResizeSouthWest",s[s.ResizeWest=512]="ResizeWest",s[s.ResizeNorthWest=1024]="ResizeNorthWest",s[s.ResizeNorth=2048]="ResizeNorth",s[s.Default=4094]="Default",(a=t.SelectorConstraints||(t.SelectorConstraints={}))[a.None=1]="None",a[a.ConnectorSourceThumb=2]="ConnectorSourceThumb",a[a.ConnectorTargetThumb=4]="ConnectorTargetThumb",a[a.ResizeSouthEast=8]="ResizeSouthEast",a[a.ResizeSouthWest=16]="ResizeSouthWest",a[a.ResizeNorthEast=32]="ResizeNorthEast",a[a.ResizeNorthWest=64]="ResizeNorthWest",a[a.ResizeEast=128]="ResizeEast",a[a.ResizeWest=256]="ResizeWest",a[a.ResizeSouth=512]="ResizeSouth",a[a.ResizeNorth=1024]="ResizeNorth",a[a.Rotate=2048]="Rotate",a[a.UserHandle=4096]="UserHandle",a[a.ToolTip=8192]="ToolTip",a[a.ResizeAll=2046]="ResizeAll",a[a.All=16382]="All",(h=t.NoOfSegments||(t.NoOfSegments={}))[h.Zero=0]="Zero",h[h.One=1]="One",h[h.Two=2]="Two",h[h.Three=3]="Three",h[h.Four=4]="Four",h[h.Five=5]="Five";var b,S=function(){function t(t,e){this.width=t,this.height=e}return t.prototype.clone=function(){return new t(this.width,this.height)},t}(),A=function(){function t(t,e,i,r){this.x=Number.MAX_VALUE,this.y=Number.MAX_VALUE,this.width=0,this.height=0,void 0===t||void 0===e?(t=e=Number.MAX_VALUE,i=r=0):(void 0===i&&(i=0),void 0===r&&(r=0)),this.x=t,this.y=e,this.width=i,this.height=r}return Object.defineProperty(t.prototype,"left",{get:function(){return this.x},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"right",{get:function(){return this.x+this.width},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"top",{get:function(){return this.y},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"bottom",{get:function(){return this.y+this.height},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"topLeft",{get:function(){return{x:this.left,y:this.top}},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"topRight",{get:function(){return{x:this.right,y:this.top}},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"bottomLeft",{get:function(){return{x:this.left,y:this.bottom}},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"bottomRight",{get:function(){return{x:this.right,y:this.bottom}},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"middleLeft",{get:function(){return{x:this.left,y:this.y+this.height/2}},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"middleRight",{get:function(){return{x:this.right,y:this.y+this.height/2}},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"topCenter",{get:function(){return{x:this.x+this.width/2,y:this.top}},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"bottomCenter",{get:function(){return{x:this.x+this.width/2,y:this.bottom}},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"center",{get:function(){return{x:this.x+this.width/2,y:this.y+this.height/2}},enumerable:!0,configurable:!0}),t.prototype.equals=function(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height},t.prototype.uniteRect=function(t){var e=Math.max(Number.NaN===this.right||this.x===Number.MAX_VALUE?t.right:this.right,t.right),i=Math.max(Number.NaN===this.bottom||this.y===Number.MAX_VALUE?t.bottom:this.bottom,t.bottom);return this.x=Math.min(this.left,t.left),this.y=Math.min(this.top,t.top),this.width=e-this.x,this.height=i-this.y,this},t.prototype.unitePoint=function(t){if(this.x===Number.MAX_VALUE)return this.x=t.x,void(this.y=t.y);var e=Math.min(this.left,t.x),i=Math.min(this.top,t.y),r=Math.max(this.right,t.x),n=Math.max(this.bottom,t.y);this.x=e,this.y=i,this.width=r-this.x,this.height=n-this.y},t.prototype.intersection=function(e){if(this.intersects(e)){var i=Math.max(this.left,e.left),r=Math.max(this.top,e.top);return new t(i,r,Math.min(this.right,e.right)-i,Math.min(this.bottom,e.bottom)-r)}return t.empty},t.prototype.Inflate=function(t){return this.x-=t,this.y-=t,this.width+=2*t,this.height+=2*t,this},t.prototype.intersects=function(t){return!(this.right<t.left||this.left>t.right||this.top>t.bottom||this.bottom<t.top)},t.prototype.containsRect=function(t){return this.left<=t.left&&this.right>=t.right&&this.top<=t.top&&this.bottom>=t.bottom},t.prototype.containsPoint=function(t,e){return void 0===e&&(e=0),this.left-e<=t.x&&this.right+e>=t.x&&this.top-e<=t.y&&this.bottom+e>=t.y},t.prototype.toPoints=function(){var t=[];return t.push(this.topLeft),t.push(this.topRight),t.push(this.bottomLeft),t.push(this.bottomRight),t},t.toBounds=function(e){for(var i=new t,r=0,n=e;r<n.length;r++){var o=n[r];i.unitePoint(o)}return i},t.prototype.scale=function(t,e){this.width*=t,this.height*=e},t.prototype.offset=function(t,e){this.x+=t,this.y+=e},t.empty=new t(Number.MAX_VALUE,Number.MIN_VALUE,0,0),t}();(b=t.MatrixTypes||(t.MatrixTypes={}))[b.Identity=0]="Identity",b[b.Translation=1]="Translation",b[b.Scaling=2]="Scaling",b[b.Unknown=4]="Unknown";var M=function(t,e,i,r,n,o,s){this.m11=t,this.m12=e,this.m21=i,this.m22=r,this.offsetX=n,this.offsetY=o,this.type=s};function k(){return new M(1,0,0,1,0,0,t.MatrixTypes.Identity)}function z(e,i){var r=function(e,i,r){switch(e.type){case t.MatrixTypes.Identity:break;case t.MatrixTypes.Translation:i+=e.offsetX,r+=e.offsetY;break;case t.MatrixTypes.Scaling:i*=e.m11,r*=e.m22;break;case t.MatrixTypes.Translation|t.MatrixTypes.Scaling:i*=e.m11,i+=e.offsetX,r*=e.m22,r+=e.offsetY;break;default:var n=r*e.m21+e.offsetX,o=i*e.m12+e.offsetY;i*=e.m11,i+=n,r*=e.m22,r+=o}return{x:i,y:r}}(e,i.x,i.y);return{x:Math.round(100*r.x)/100,y:Math.round(100*r.y)/100}}function P(e,i,r,n){C(e,function(e,i,r){var n=k(),o=Math.sin(e),s=Math.cos(e),a=i*(1-s)+r*o,h=r*(1-s)-i*o;return n.type=t.MatrixTypes.Unknown,T(n,s,o,-o,s,a,h),n}(.017453292519943295*(i%=360),r||0,n||0))}function C(e,i){var r=e.type,n=i.type;if(n!==t.MatrixTypes.Identity){if(r===t.MatrixTypes.Identity)return R(e,i),void(e.type=i.type);if(n===t.MatrixTypes.Translation)return e.offsetX+=i.offsetX,e.offsetY+=i.offsetY,void(r!==t.MatrixTypes.Unknown&&(e.type|=t.MatrixTypes.Translation));if(r!==t.MatrixTypes.Translation){var o=r<<4|n;switch(o){case 34:return e.m11*=i.m11,void(e.m22*=i.m22);case 35:return e.m11*=i.m11,e.m22*=i.m22,e.offsetX=i.offsetX,e.offsetY=i.offsetY,void(e.type=t.MatrixTypes.Translation|t.MatrixTypes.Scaling);case 36:break;default:switch(o){case 50:return e.m11*=i.m11,e.m22*=i.m22,e.offsetX*=i.m11,void(e.offsetY*=i.m22);case 51:return e.m11*=i.m11,e.m22*=i.m22,e.offsetX=i.m11*e.offsetX+i.offsetX,void(e.offsetY=i.m22*e.offsetY+i.offsetY);case 52:break;default:switch(o){case 66:case 67:case 68:break;default:return}}}var s=k();return T(s,e.m11*i.m11+e.m12*i.m21,e.m11*i.m12+e.m12*i.m22,e.m21*i.m11+e.m22*i.m21,e.m21*i.m12+e.m22*i.m22,e.offsetX*i.m11+e.offsetY*i.m21+i.offsetX,e.offsetX*i.m12+e.offsetY*i.m22+i.offsetY),s.m21||s.m12?s.type=t.MatrixTypes.Unknown:((s.m11&&1!==s.m11||s.m22&&1!==s.m22)&&(s.type=t.MatrixTypes.Scaling),(s.offsetX||s.offsetY)&&(s.type|=t.MatrixTypes.Translation),(s.type&(t.MatrixTypes.Translation|t.MatrixTypes.Scaling))===t.MatrixTypes.Identity&&(s.type=t.MatrixTypes.Identity),s.type=t.MatrixTypes.Scaling|t.MatrixTypes.Translation),R(e,s),void(e.type=s.type)}var a=e.offsetX,h=e.offsetY;e.offsetX=a*i.m11+h*i.m21+i.offsetX,e.offsetY=a*i.m12+h*i.m22+i.offsetY,n!==t.MatrixTypes.Unknown?e.type=t.MatrixTypes.Translation|t.MatrixTypes.Scaling:e.type=t.MatrixTypes.Unknown}}function T(t,e,i,r,n,o,s){t.m11=e,t.m12=i,t.m21=r,t.m22=n,t.offsetX=o,t.offsetY=s}function R(t,e){t.m11=e.m11,t.m12=e.m12,t.m21=e.m21,t.m22=e.m22,t.offsetX=e.offsetX,t.offsetY=e.offsetY,t.type=e.type}function I(t){var e,i=[];if("msie"===sf.base.Browser.info.name||"edge"===sf.base.Browser.info.name)for(var r=0;r<t.childNodes.length;r++)1===(e=t.childNodes[parseInt(r.toString(),10)]).nodeType&&i.push(e);else i=t.children;return i}function O(t){var e="pathTable";if(window[e]||(window[e]={}),t){window.measureElement.style.visibility="visible";var i=I(window.measureElement.children[2])[0];i.setAttribute("d",t);var r=void 0;window[e][t]?r=window[e][t]:(window[e][t]=r=i.getBBox(),0!==r.x&&0!==r.y||0!==r.width&&0!==r.height||(window[e][t]=r=function(t){var e,i=0,r=0,n=0,o=0,s=t;s=s.replace(/[a-z].*/g," ").replace(/[\sA-Z]+/gi," ").trim().split(" ");for(var a=0;a<s.length;a++)s[parseInt(a.toString(),10)].length>1&&(e=s[parseInt(a.toString(),10)].split(","),i=r=e[0],n=o=e[1]);for(a=0;a<s.length;a++)(e=s[parseInt(a.toString(),10)].split(","))[1]||(e[0]=i,e[1]=n),i=Math.min(i,e[0]),r=Math.max(r,e[0]),n=Math.min(n,e[1]),o=Math.max(o,e[1]);return{x:i,y:n,width:r-i,height:o-n}}(t)));var n=new A(r.x,r.y,r.width,r.height);return window.measureElement.style.visibility="hidden",n}return new A(0,0,0,0)}function B(t,e){var i,r,n=[],o=0,s=e||t.content;if("nowrap"!==t.whiteSpace&&"pre"!==t.whiteSpace)if("breakall"===t.breakWord)for(i="",i+=s[0],o=0;o<s.length;o++)if((r=H(i,t))>=t.width&&i.length>0)n[n.length]={text:i,x:0,dy:0,width:r},i="";else{var a=H(i+=s[o+1]||"",t);(Math.ceil(a)+2>=t.width&&i.length>0||i.indexOf("\n")>-1)&&(n[n.length]={text:i,x:0,dy:0,width:a},i=""),o===s.length-1&&i.length>0&&(n[n.length]={text:i,x:0,dy:0,width:a},i="")}else n=function(t,e){var i,r,n,o,s=[],a="",h=0,d=0,l="nowrap"!==t.whiteSpace,c=(e||t.content).split("\n");for(h=0;h<c.length;h++)for(i="NoWrap"!==t.textWrapping?c[parseInt(h.toString(),10)].split(" "):c,d=0;d<i.length;d++)if(H(i[parseInt(d.toString(),10)],t)>t.width&&i[parseInt(d.toString(),10)].length>0&&"NoWrap"!==t.textWrapping)c.length>1&&(i[parseInt(d.toString(),10)]=i[parseInt(d.toString(),10)]+"\n"),t.content=i[parseInt(d.toString(),10)],s=E(t,a,s);else{a+=((0!==d||1===i.length)&&l&&a.length>0?" ":"")+i[parseInt(d.toString(),10)];var f=H(r=a+(i[d+1]||""),t);c.length>1&&d===i.length-1&&(a+="\n"),Math.floor(f)>t.width-2&&a.length>0?(e=a,s[s.length]={text:-1===a.indexOf("\n")?a+" ":e,x:0,dy:0,width:r===a?f:a===o?n:H(a,t)},a=""):d===i.length-1&&(s[s.length]={text:a,x:0,dy:0,width:f},a=""),o=r,n=f}return s}(t,e);else n[n.length]={text:s,x:0,dy:0,width:H(s,t)};return n}function E(t,e,i){var r,n,o=0,s=e||t.content;for(r="",r+=s[0],o=0;o<s.length;o++)if((n=H(r,t))>=t.width&&r.length>0)i[i.length]={text:r,x:0,dy:0,width:n},r="";else{var a=H(r+=s[o+1]||"",t);(Math.ceil(a)+2>=t.width&&r.length>0||r.indexOf("\n")>-1)&&(r=r.slice(0,-1),i[i.length]={text:r,x:0,dy:0,width:a},r=s[o+1]||""),o===s.length-1&&r.length>0&&(i[i.length]={text:r,x:0,dy:0,width:a},r="")}return i}function N(t,e,i,r,n){var o,s,a=new S(0,0),h=function(t,e){var i={fill:t.style.fill,stroke:t.style.strokeColor,angle:t.rotateAngle+t.parentTransform,pivotX:t.pivot.x,pivotY:t.pivot.y,strokeWidth:t.style.strokeWidth,dashArray:t.style.strokeDashArray,opacity:t.style.opacity,visible:t.visible,id:t.id,width:e||t.actualSize.width,height:t.actualSize.height,x:t.offsetX-t.actualSize.width*t.pivot.x+.5,y:t.offsetY-t.actualSize.height*t.pivot.y+.5};return i.fontSize=t.style.fontSize,i.fontFamily=t.style.fontFamily,i.textOverflow=t.style.textOverflow,i.textDecoration=t.style.textDecoration,i.doWrap=t.doWrap,i.whiteSpace=Z(t.style.whiteSpace,t.style.textWrapping),i.content=t.content,i.textWrapping=t.style.textWrapping,i.breakWord=V(t.style.textWrapping),i.textAlign=U(t.style.textAlign),i.color=t.style.color,i.italic=t.style.italic,i.bold=t.style.bold,i.dashArray="",i.strokeWidth=0,i.fill="",i}(t,r);return t.childNodes=o=B(h,n),t.wrapBounds=s=function(t,e){var i,r,n={x:0,width:0},o=0;for(o=0;o<e.length;o++)r=i=e[parseInt(o.toString(),10)].width,i="left"===t.textAlign?0:"center"===t.textAlign?i>t.width&&("Ellipsis"===t.textOverflow||"Clip"===t.textOverflow)?0:-i/2:"right"===t.textAlign?-i:e.length>1?0:-i/2,e[parseInt(o.toString(),10)].dy=1.2*t.fontSize,e[parseInt(o.toString(),10)].x=i,n?(n.x=Math.min(n.x,i),n.width=Math.max(n.width,r)):n={x:i,width:r};return n}(h,o),a.width=s.width,t.wrapBounds.width>=r&&"Wrap"!==h.textOverflow&&(a.width=r),a.height=o.length*t.style.fontSize*1.2,a}function D(t,e){var i;return e&&"undefined"!=typeof document&&(i=document.getElementById(e)),i?i.querySelector("#"+t):"undefined"!=typeof document?document.getElementById(t):null}function W(t,e){var i=sf.base.createElement(t);return X(i,e),i}function X(t,e){for(var i=Object.keys(e),r=0;r<i.length;r++)t.setAttribute(i[parseInt(r.toString(),10)],e[i[parseInt(r.toString(),10)]])}function _(t,e){var i=null,r=D(t+e+"_diagramAdornerLayer");return r&&(i=r.getElementsByClassName("e-adorner-layer"+e)[0]),i}function L(t,e){window.measureElement.style.visibility="visible";var i=window.measureElement.children[1];i.setAttribute("src",t);var r=i.getBoundingClientRect(),n=r.width,o=r.height;return e=new S(n,o),window.measureElement.style.visibility="hidden",e}function Y(t){new A;var e=t.offsetY-t.actualSize.height*t.pivot.y,i=t.offsetY+t.actualSize.height*(1-t.pivot.y),r=t.offsetX-t.actualSize.width*t.pivot.x,n=t.offsetX+t.actualSize.width*(1-t.pivot.x),o={x:r,y:e},s={x:n,y:e},a={x:r,y:i},h={x:n,y:i};return A.toBounds([o,s,a,h])}function j(t,e){var i=k();P(i,e,0,0);var r=z(i,{x:0,y:0}),n=z(i,{x:t.width,y:0}),o=z(i,{x:0,y:t.height}),s=z(i,{x:t.width,y:t.height}),a=Math.min(r.x,n.x,o.x,s.x),h=Math.min(r.y,n.y,o.y,s.y),d=Math.max(r.x,n.x,o.x,s.x),l=Math.max(r.y,n.y,o.y,s.y);return new S(d-a,l-h)}function F(t){var e,i=new A,r=(e=Y(t)).middleLeft,n=e.topCenter,o=e.bottomCenter,s=e.middleRight,a=e.topLeft,h=e.topRight,d=e.bottomLeft,l=e.bottomRight;if(t.corners={topLeft:a,topCenter:n,topRight:h,middleLeft:r,middleRight:s,bottomLeft:d,bottomCenter:o,bottomRight:l},0!==t.rotateAngle||0!==t.parentTransform){var c=k();P(c,t.rotateAngle+t.parentTransform,t.offsetX,t.offsetY),t.corners.topLeft=a=z(c,a),t.corners.topCenter=n=z(c,n),t.corners.topRight=h=z(c,h),t.corners.middleLeft=r=z(c,r),t.corners.middleRight=s=z(c,s),t.corners.bottomLeft=d=z(c,d),t.corners.bottomCenter=o=z(c,o),t.corners.bottomRight=l=z(c,l)}return i=A.toBounds([a,h,d,l]),t.corners.left=i.left,t.corners.right=i.right,t.corners.top=i.top,t.corners.bottom=i.bottom,t.corners.center=i.center,t.corners.width=i.width,t.corners.height=i.height,i}function U(t){var e="";switch(t){case"Center":e="center";break;case"Left":e="left";break;case"Right":e="right"}return e}function V(t){var e="";switch(t){case"Wrap":e="breakall";break;case"NoWrap":e="keepall";break;case"WrapWithOverflow":e="normal";break;case"LineThrough":e="line-through"}return e}function H(t,e){window.measureElement.style.visibility="visible";var i=I(window.measureElement.children[2])[1];i.textContent=t,i.setAttribute("style","font-size:"+e.fontSize+"px; font-family:"+e.fontFamily+";font-weight:"+(e.bold?"bold":"normal"));var r=i.getBBox().width;return window.measureElement.style.visibility="hidden",r}function Z(t,e){if("NoWrap"===e&&"PreserveAll"===t)return"pre";var i="";switch(t){case"CollapseAll":i="nowrap";break;case"CollapseSpace":i="pre-line";break;case"PreserveAll":i="pre-wrap"}return i}function q(t,e,i,r){if(0!==t){var n=k();return P(n,t,e,i),z(n,r)}return r}function Q(t,e){return{x:t.x+e.desiredSize.width*e.pivot.x,y:t.y+e.desiredSize.height*e.pivot.y}}var G,J,K,$,tt=function(){function e(){this.pivot={x:.5,y:.5},this.rotateValue={x:0,y:0,angle:0},this.isDirt=!0,this.offsetX=0,this.offsetY=0,this.cornerRadius=0,this.minHeight=void 0,this.minWidth=void 0,this.maxWidth=void 0,this.maxHeight=void 0,this.width=void 0,this.height=void 0,this.horizontalAlignment="Auto",this.verticalAlignment="Auto",this.visible=!0,this.rotateAngle=0,this.margin={left:0,right:0,top:0,bottom:0},this.relativeMode="Point",this.transform=t.RotateTransform.Self|t.RotateTransform.Parent,this.style={fill:"white",strokeColor:"black",opacity:1,strokeWidth:1},this.desiredSize=new S,this.actualSize=new S,this.parentTransform=0,this.preventContainer=!1,this.bounds=new A(0,0,0,0),this.staticSize=!1,this.isRectElement=!1,this.isCalculateDesiredSize=!0,this.elementActions=t.ElementAction.None,this.position=void 0,this.unitMode=void 0,this.float=!1,this.floatingBounds=void 0}return e.prototype.setOffsetWithRespectToBounds=function(t,e,i){this.unitMode=i,this.position={x:t,y:e}},e.prototype.getAbsolutePosition=function(t){if(void 0!==this.position)return"Absolute"===this.unitMode?this.position:{x:this.position.x*t.width,y:this.position.y*t.height}},Object.defineProperty(e.prototype,"outerBounds",{get:function(){return this.floatingBounds||this.bounds},set:function(t){this.floatingBounds=t},enumerable:!0,configurable:!0}),e.prototype.measure=function(t){var e=void 0!==this.width?this.width:(t.width||0)-this.margin.left-this.margin.right,i=void 0!==this.height?this.height:(t.height||0)-this.margin.top-this.margin.bottom;return this.desiredSize=new S(e,i),this.isCalculateDesiredSize&&(this.desiredSize=this.validateDesiredSize(this.desiredSize,t)),this.desiredSize},e.prototype.arrange=function(t){return this.actualSize=t,this.updateBounds(),this.actualSize},e.prototype.updateBounds=function(){this.bounds=F(this)},e.prototype.validateDesiredSize=function(t,e){return!this.isRectElement||this.width||this.minWidth||this.maxWidth||(t.width=50),!this.isRectElement||this.height||this.minHeight||this.maxHeight||(t.height=50),(void 0===t||void 0!==this.width&&void 0!==this.height)&&((t=t||new S).width=void 0===this.width?(e.width||0)-this.margin.left-this.margin.right:this.width,t.height=void 0===this.height?(e.height||0)-this.margin.top-this.margin.bottom:this.height),void 0!==this.minWidth&&(t.width=Math.max(t.width,this.minWidth)),void 0!==this.minHeight&&(t.height=Math.max(t.height,this.minHeight)),void 0!==this.maxWidth&&(t.width=Math.min(t.width,this.maxWidth)),void 0!==this.maxHeight&&(t.height=Math.min(t.height,this.maxHeight)),t},e}(),et=(G=function(t,e){return(G=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}G(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),it=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.desiredBounds=void 0,e.measureChildren=!0,e.prevRotateAngle=0,e}return et(e,t),e.prototype.hasChildren=function(){return void 0!==this.children&&this.children.length>0},e.prototype.measure=function(t){this.desiredBounds=void 0;var i,r,n=void 0;if(this.hasChildren()){for(var o=0;o<this.children.length;o++){"Stretch"!==(i=this.children[parseInt(o.toString(),10)]).horizontalAlignment||t.width||(t.width=i.bounds.width),"Stretch"!==i.verticalAlignment||t.height||(t.height=i.bounds.height);var s="Stretch"===i.horizontalAlignment||"Stretch"===i.verticalAlignment;(this.measureChildren||s||i instanceof e&&void 0!==i.measureChildren)&&i.measure(t),r=this.GetChildrenBounds(i),"Stretch"!==i.horizontalAlignment&&"Stretch"!==i.verticalAlignment?void 0===this.desiredBounds?this.desiredBounds=r:this.desiredBounds.uniteRect(r):!this.actualSize||this.actualSize.width||this.actualSize.height||i.preventContainer||"Stretch"!==i.horizontalAlignment||"Stretch"!==i.verticalAlignment||(void 0===this.desiredBounds?this.desiredBounds=i.bounds:this.desiredBounds.uniteRect(i.bounds))}if(void 0!==this.desiredBounds&&0!==this.rotateAngle){var a={x:this.desiredBounds.x+this.desiredBounds.width*this.pivot.x,y:this.desiredBounds.y+this.desiredBounds.height*this.pivot.y},h=q(this.rotateAngle,void 0,void 0,a);this.desiredBounds.x=h.x-this.desiredBounds.width*this.pivot.x,this.desiredBounds.y=h.y-this.desiredBounds.height*this.pivot.y}this.desiredBounds&&(n=new S(this.desiredBounds.width,this.desiredBounds.height))}return n=this.validateDesiredSize(n,t),this.stretchChildren(n),this.desiredSize=n,n},e.prototype.arrange=function(t){var i,r=this.desiredBounds;if(r){this.offsetX,this.offsetY;if(this.offsetX=r.x+r.width*this.pivot.x,this.offsetY=r.y+r.height*this.pivot.y,this.hasChildren())for(var n=0;n<this.children.length;n++){var o=!1;"Stretch"===(i=this.children[parseInt(n.toString(),10)]).horizontalAlignment&&(i.offsetX=this.offsetX,i.parentTransform=this.parentTransform+this.rotateAngle,o=!0),"Stretch"===i.verticalAlignment&&(i.offsetY=this.offsetY,i.parentTransform=this.parentTransform+this.rotateAngle,o=!0),(o||this.measureChildren||i instanceof e&&void 0!==i.measureChildren)&&i.arrange(i.desiredSize)}}return this.actualSize=t,this.updateBounds(),this.prevRotateAngle=this.rotateAngle,t},e.prototype.stretchChildren=function(t){if(this.hasChildren())for(var i=0,r=this.children;i<r.length;i++){var n=r[i];"Stretch"!==n.horizontalAlignment&&void 0!==n.desiredSize.width||(n.desiredSize.width=t.width-n.margin.left-n.margin.right),"Stretch"!==n.verticalAlignment&&void 0!==n.desiredSize.height||(n.desiredSize.height=t.height-n.margin.top-n.margin.bottom),n instanceof e&&n.stretchChildren(n.desiredSize)}},e.prototype.findChildOffsetFromCenter=function(t,e){var i=Q({x:e.x-t.desiredSize.width/2,y:e.y-t.desiredSize.height/2},t);i=q(t.rotateAngle,e.x,e.y,i),i=q(this.rotateAngle+this.parentTransform,this.offsetX,this.offsetY,i),t.offsetX=i.x,t.offsetY=i.y},e.prototype.GetChildrenBounds=function(t){var e;e=t.desiredSize.clone();t.rotateAngle,this.rotateAngle;var i=t.offsetX,r=t.offsetY,n=i-e.width*t.pivot.x,o=r-e.height*t.pivot.y,s=n+e.width,a=o+e.height,h={x:n,y:o},d={x:s,y:o},l={x:n,y:a},c={x:s,y:a};return h=q(t.rotateAngle,t.offsetX,t.offsetY,h),d=q(t.rotateAngle,t.offsetX,t.offsetY,d),l=q(t.rotateAngle,t.offsetX,t.offsetY,l),c=q(t.rotateAngle,t.offsetX,t.offsetY,c),0!==this.rotateAngle&&(h=q(-this.rotateAngle,void 0,void 0,h),d=q(-this.rotateAngle,void 0,void 0,d),l=q(-this.rotateAngle,void 0,void 0,l),c=q(-this.rotateAngle,void 0,void 0,c)),A.toBounds([h,d,l,c])},e}(tt),rt=(J=function(t,e){return(J=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}J(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),nt=function(t){function e(){var e=t.call(this)||this;return e.textContent="",e.canMeasure=!0,e.canConsiderBounds=!0,e.doWrap=!0,e.textNodes=[],e.style={color:"black",fill:"transparent",strokeColor:"black",strokeWidth:1,fontFamily:"Arial",fontSize:12,whiteSpace:"CollapseSpace",textWrapping:"WrapWithOverflow",textAlign:"Center",italic:!1,bold:!1,textDecoration:"None",strokeDashArray:"",opacity:1,textOverflow:"Wrap"},e.style.fill="transparent",e.style.strokeColor="transparent",e}return rt(e,t),Object.defineProperty(e.prototype,"content",{get:function(){return this.textContent},set:function(t){this.textContent!==t&&(this.textContent=t,this.isDirt=!0,this.doWrap=!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"childNodes",{get:function(){return this.textNodes},set:function(t){this.textNodes=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"wrapBounds",{get:function(){return this.textWrapBounds},set:function(t){this.textWrapBounds=t},enumerable:!0,configurable:!0}),e.prototype.refreshTextElement=function(){this.isDirt=!0},e.prototype.measure=function(t){var e;return e=this.isDirt&&this.canMeasure?N(this,this.style,this.content,this.width||t.width):this.desiredSize,void 0===this.width||void 0===this.height?this.desiredSize=new S(e.width,e.height):this.desiredSize=new S(this.width,this.height),this.desiredSize=this.validateDesiredSize(this.desiredSize,t),this.desiredSize},e.prototype.arrange=function(t){return(t.width!==this.actualSize.width||t.height!==this.actualSize.height||this.isDirt)&&(this.doWrap=!0),this.actualSize=t,this.updateBounds(),this.isDirt=!1,this.actualSize},e}(tt),ot=(K=function(t,e){return(K=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}K(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),st=function(e){function i(){var t=null!==e&&e.apply(this,arguments)||this;return t.measureChildren=void 0,t}return ot(i,e),i.prototype.measure=function(t){var i=void 0,r=void 0;if(this.hasChildren()){for(var n=0,o=this.children;n<o.length;n++){var s=o[n];if(s instanceof nt){if(!s.canMeasure)break;t.width=t.width||this.maxWidth||this.minWidth,s.measure(t)}else s instanceof nt||s.measure(t);var a=s.desiredSize.clone();0!==s.rotateAngle&&(a=j(a,s.rotateAngle));var h=a.width+s.margin.right,d=a.height+s.margin.bottom,l=new A(s.margin.left,s.margin.top,h,d);if(s.float)if(void 0!==s.getAbsolutePosition(a))continue;s instanceof nt&&!(s instanceof nt&&s.canConsiderBounds)||(void 0===r?r=l:r.uniteRect(l))}if(r){var c,f;c=Math.max(r.left,0),f=Math.max(r.top,0),i=new S(r.width+c,r.height+f)}}return i=e.prototype.validateDesiredSize.call(this,i,t),e.prototype.stretchChildren.call(this,i),this.desiredSize=i,i},i.prototype.arrange=function(i){if(this.outerBounds=new A,this.hasChildren()){var r,n;r=this.offsetY-i.height*this.pivot.y,n=this.offsetX-i.width*this.pivot.x;for(var o=0,s=this.children;o<s.length;o++){var a=s[o];if(0!=(a.transform&t.RotateTransform.Parent)){a.parentTransform=this.parentTransform+this.rotateAngle;var h=a.desiredSize.clone(),d=void 0,l={x:0,y:0},c=n,f=r;if("Point"===a.relativeMode){var p=a.getAbsolutePosition(i);void 0!==p&&(c+=p.x,f+=p.y)}if(l={x:(d="Object"===a.relativeMode?this.alignChildBasedOnParent(a,h,i,c,f):this.alignChildBasedOnaPoint(a,c,f)).x+h.width/2,y:d.y+h.height/2},a.rotateValue){var u={x:this.offsetX+(a.rotateValue.x||0),y:this.offsetY+(a.rotateValue.y||0)},y={x:this.offsetX,y:this.offsetY},g=0|a.rotateValue.angle,m=k();P(m,g,y.x,y.y),l=z(m,u)}e.prototype.findChildOffsetFromCenter.call(this,a,l)}"Stretch"===a.horizontalAlignment||"Stretch"===a.verticalAlignment?a.arrange(i):a instanceof nt&&a.canMeasure?(a.arrange(a.desiredSize),this.outerBounds.uniteRect(a.outerBounds)):a instanceof nt||(a.arrange(a.desiredSize),this.outerBounds.uniteRect(a.outerBounds))}}return this.actualSize=i,this.updateBounds(),this.outerBounds.uniteRect(this.bounds),i},i.prototype.alignChildBasedOnParent=function(t,e,i,r,n){switch(t.horizontalAlignment){case"Auto":case"Left":r+=t.margin.left;break;case"Right":r+=i.width-e.width-t.margin.right;break;case"Stretch":case"Center":r+=i.width/2-e.width/2}switch(t.verticalAlignment){case"Auto":case"Top":n+=t.margin.top;break;case"Bottom":n+=i.height-e.height-t.margin.bottom;break;case"Stretch":case"Center":n+=i.height/2-e.height/2}return{x:r,y:n}},i.prototype.alignChildBasedOnaPoint=function(t,e,i){switch(e+=t.margin.left-t.margin.right,i+=t.margin.top-t.margin.bottom,t.horizontalAlignment){case"Auto":case"Left":e=e;break;case"Stretch":case"Center":e-=t.desiredSize.width*t.pivot.x;break;case"Right":e-=t.desiredSize.width}switch(t.verticalAlignment){case"Auto":case"Top":i=i;break;case"Stretch":case"Center":i-=t.desiredSize.height*t.pivot.y;break;case"Bottom":i-=t.desiredSize.height}return{x:e,y:i}},i}(it),at=($=function(t,e){return($=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}$(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),ht=function(t){function e(){var e=t.call(this)||this;return e.imageSource="",e.printID="",e.imageScale="None",e.imageAlign="None",e.stretch="Stretch",e}return at(e,t),Object.defineProperty(e.prototype,"source",{get:function(){return this.imageSource},set:function(t){this.imageSource=t,this.isDirt=!0},enumerable:!0,configurable:!0}),e.prototype.measure=function(t){return this.isDirt&&("Stretch"!==this.stretch||void 0===this.width&&void 0===this.height)&&(this.contentSize=L(this.source,this.contentSize),this.isDirt=!1),void 0!==this.width&&void 0!==this.height?(this.desiredSize=new S(this.width,this.height),this.contentSize=this.desiredSize):this.desiredSize=this.contentSize,this.desiredSize=this.validateDesiredSize(this.desiredSize,t),this.desiredSize},e.prototype.arrange=function(t){return this.actualSize=new S(this.desiredSize.width,this.desiredSize.height),this.updateBounds(),this.actualSize},e}(tt);function dt(t){var e,i=[],r=lt(t);if(r.length>0)for(var n=0;n<r.length;n++){var o=r[parseInt(n.toString(),10)],s="";switch((s=o[0]).toLowerCase()){case"m":for(e=1;e<o.length;e++)i.push({command:s,x:o[parseInt(e.toString(),10)],y:o[e+1]}),e+=1,"m"===s?s="l":"M"===s&&(s="L");break;case"l":case"t":for(e=1;e<o.length;e++)i.push({command:s,x:o[parseInt(e.toString(),10)],y:o[e+1]}),e+=1;break;case"h":for(e=1;e<o.length;e++)i.push({command:s,x:o[parseInt(e.toString(),10)]});break;case"v":for(e=1;e<o.length;e++)i.push({command:s,y:o[parseInt(e.toString(),10)]});break;case"z":i.push({command:s});break;case"c":for(e=1;e<o.length;e++)i.push({command:s,x1:o[parseInt(e.toString(),10)],y1:o[e+1],x2:o[e+2],y2:o[e+3],x:o[e+4],y:o[e+5]}),e+=5;break;case"s":for(e=1;e<o.length;e++)i.push({command:s,x2:o[parseInt(e.toString(),10)],y2:o[e+1],x:o[e+2],y:o[e+3]}),e+=3;break;case"q":for(e=1;e<o.length;e++)i.push({command:s,x1:o[parseInt(e.toString(),10)],y1:o[e+1],x:o[e+2],y:o[e+3]}),e+=3;break;case"a":for(e=1;e<o.length;e++)i.push({command:s,r1:o[parseInt(e.toString(),10)],r2:o[e+1],angle:o[e+2],largeArc:o[e+3],sweep:o[e+4],x:o[e+5],y:o[e+6]}),e+=6}}return i}function lt(t){var e=/([a-z]+)|([+-]?(?:\d+\.?\d*|\.\d+))/gi,i=[],r=[],n={};e.lastIndex=0;var o=!1;for(n=e.exec(t);n;)"e"===n[1]?o=!0:n[1]?("zm"===n[1].toLowerCase()?(i.length&&r.push(i),r.push(["Z"]),i=[n[1].substring(1,2)]):(i.length&&r.push(i),i=[n[1]]),o=!1):(i.length||(i=[]),o||i.push(Number(n[2])),o=!1),n=e.exec(t);return i.length&&r.push(i),r}function ct(t,e,i){t>e/2&&(t=e/2),t>i/2&&(t=i/2);var r,n,o,s="",a=[{x:0+t,y:0},{x:0+i-t,y:0},{x:0+i,y:0+t},{x:0+i,y:0+e-t},{x:0+i-t,y:0+e},{x:0+t,y:0+e},{x:0,y:0+e-t},{x:0,y:0+t}],h=[{x:0+i,y:0},{x:0+i,y:0+e},{x:0,y:0+e},{x:0,y:0}],d=0;for(s="M"+a[0].x+" "+a[0].y,o=0;o<a.length;o+=2)s+="L"+(r=a[o+1]).x+" "+r.y,n=a[o+2]||a[0],s+="Q"+h[d].x+" "+h[d].y+" "+n.x+" "+n.y,d++;return s}function ft(t){var e,i,r,n,o,s,a,h,d,l,c=[];for(a=0,h=0,l=0,d=t.length;l<d;++l){var f,p=t[parseInt(l.toString(),10)];f=p.command,"y1"in p&&(n=p.y1),"y2"in p&&(s=p.y2),"x1"in p&&(r=p.x1),"x2"in p&&(o=p.x2),"x"in p&&(a=p.x),"y"in p&&(h=p.y);var u=c[c.length-1];switch(f){case"M":c.push({command:"M",x:a,y:h});break;case"L":c.push({command:"L",x0:e,y0:i,x:a,y:h});break;case"H":c.push({command:"L",x0:e,y0:i,x:a,y:i});break;case"V":c.push({command:"L",x0:e,y0:i,x:e,y:h});break;case"C":c.push({command:"C",x0:e,y0:i,x1:r,y1:n,x2:o,y2:s,x:a,y:h});break;case"S":if(u){var y=void 0,g={x:2*e-(y="C"===u.command||"S"===u.command?{x:u.x2,y:u.y2}:{x:e,y:i}).x,y:2*i-y.y};c.push({command:"C",x0:e,y0:i,x1:g.x,y1:g.y,x2:o,y2:s,x:a,y:h})}break;case"Q":c.push({command:"Q",x0:e,y0:i,x1:r,y1:n,x:a,y:h});break;case"T":if(u){y=void 0,g={x:2*e-(y="Q"===u.command?{x:u.x1,y:u.y1}:{x:e,y:i}).x,y:2*i-y.y};c.push({command:"Q",x0:e,y0:i,x1:g.x,y1:g.y,x:a,y:h})}break;case"A":var m=p;m.command="A",c.push(m);break;case"Z":case"z":c.push({command:"Z"}),a=e,h=i}e=a,i=h}return c}function pt(t,e,i,r,n,o,s,a){var h,d,l,c,f,p,u,y,g;for(f=0,p=0,y=0,u=t.length;y<u;++y){var m=t[parseInt(y.toString(),10)],x=m.command;"x"in m&&(f=m.x),"y"in m&&(p=m.y),"y1"in m&&(d=m.y1),"y2"in m&&(c=m.y2),"x1"in m&&(h=m.x1),"x2"in m&&(l=m.x2),r?(void 0!==f&&(f=yt(f,e,n,s)),void 0!==p&&(p=yt(p,i,o,a)),void 0!==h&&(h=yt(h,e,n,s)),void 0!==d&&(d=yt(d,i,o,a)),void 0!==l&&(l=yt(l,e,n,s)),void 0!==c&&(c=yt(c,i,o,a))):(void 0!==f&&(f=Number((f+e).toFixed(2))),void 0!==p&&(p=Number((p+i).toFixed(2))),void 0!==h&&(h=Number((h+e).toFixed(2))),void 0!==d&&(d=Number((d+i).toFixed(2))),void 0!==l&&(l=Number((l+e).toFixed(2))),void 0!==c&&(c=Number((c+i).toFixed(2)))),(g=ut(m,x,{x:f,y:p,x1:h,y1:d,x2:l,y2:c,r1:m.r1,r2:m.r2},r,e,i))&&(t[parseInt(y.toString(),10)]=g)}return mt(t)}function ut(t,e,i,r,n,o){switch(e){case"M":case"L":t.x=i.x,t.y=i.y;break;case"H":t.x=i.x;break;case"V":t.y=i.y;break;case"C":t.x=i.x,t.y=i.y,t.x1=i.x1,t.y1=i.y1,t.x2=i.x2,t.y2=i.y2;break;case"S":t.x=i.x,t.y=i.y,t.x2=i.x2,t.y2=i.y2;break;case"Q":t.x=i.x,t.y=i.y,t.x1=i.x1,t.y1=i.y1;break;case"T":t.x=i.x,t.y=i.y;break;case"A":var s=i.r1,a=i.r2;r&&(i.r1=s*=n,i.r2=a*=o),t.x=i.x,t.y=i.y,t.r1=i.r1,t.r2=i.r2;break;case"z":case"Z":t={command:"Z"}}return t}function yt(t,e,i,r){return t!==i?t=r!==i?t*e-(Number(i)*e-Number(i))+(r-Number(i)):Number(t)*e-(Number(i)*e-Number(i)):r!==i&&(t=r),Number(t.toFixed(2))}function gt(t){var e,i,r,n,o,s,a,h,d,l;for(a=0,h=0,l=0,d=t.length;l<d;++l){var c=t[parseInt(l.toString(),10)],f=c.command;if(/[MLHVCSQTA]/.test(f))"x"in c&&(c.x=a=c.x),"y"in c&&(c.y=h=c.y);else{"x1"in c&&(c.x1=r=a+c.x1),"x2"in c&&(c.x2=o=a+c.x2),"y1"in c&&(c.y1=n=h+c.y1),"y2"in c&&(c.y2=s=h+c.y2),"x"in c&&(c.x=a+=c.x),"y"in c&&(c.y=h+=c.y);var p=void 0;switch(f){case"m":case"M":p={command:"M",x:a,y:h};break;case"l":case"L":p={command:"L",x:a,y:h};break;case"h":case"H":p={command:"H",x:a};break;case"v":case"V":p={command:"V",y:h};break;case"c":case"C":p={command:"C",x:a,y:h,x1:r,y1:n,x2:o,y2:s};break;case"s":case"S":p={command:"S",x:a,y:h,x2:o,y2:s};break;case"q":case"Q":p={command:"Q",x:a,y:h,x1:r,y1:n};break;case"t":case"T":p={command:"T",x:a,y:h};break;case"a":case"A":(p={command:"A",x:a,y:h}).r1=c.r1,p.r2=c.r2,p.angle=c.angle,p.largeArc=c.largeArc,p.sweep=c.sweep;break;case"z":case"Z":p={command:"Z"},a=e,h=i,p=t[parseInt(l.toString(),10)]}p&&(t[parseInt(l.toString(),10)]=p)}"M"!==f&&"m"!==f||(e=a,i=h)}return t}function mt(t){var e,i="";for(e=0;e<t.length;e++)i+=0===e?xt(t[parseInt(e.toString(),10)]):" "+xt(t[parseInt(e.toString(),10)]);return i}function xt(t){var e="";switch(t.command){case"Z":case"z":e=t.command;break;case"M":case"m":case"L":case"l":e=t.command+" "+t.x+" "+t.y;break;case"C":case"c":e=t.command+" "+t.x1+" "+t.y1+" "+t.x2+" "+t.y2+" "+t.x+" "+t.y;break;case"Q":case"q":e=t.command+" "+t.x1+" "+t.y1+" "+t.x+" "+t.y;break;case"A":case"a":var i=t.command,r=t.angle,n=t.largeArc?"1":"0",o=t.sweep?"1":"0";e=i+" "+t.r1+" "+t.r2+" "+r+" "+n+" "+o+" "+t.x+" "+t.y;break;case"H":case"h":e=t.command+" "+t.x;break;case"V":case"v":e=t.command+" "+t.y;break;case"S":case"s":e=t.command+" "+t.x2+" "+t.y2+" "+t.x+" "+t.y;break;case"T":case"t":e=t.command+" "+t.x+" "+t.y}return e}var vt,wt,bt=(vt=function(t,e){return(vt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}vt(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),St=function(t){function e(){var e=t.call(this)||this;return e.pathData="",e.transformPath=!0,e.absolutePath="",e.canMeasurePath=!1,e.absoluteBounds=new A,e}return bt(e,t),Object.defineProperty(e.prototype,"data",{get:function(){return this.pathData},set:function(t){this.pathData!==t&&(this.pathData=t,this.isDirt=!0)},enumerable:!0,configurable:!0}),e.prototype.measure=function(t){return this.staticSize&&void 0!==this.width&&void 0!==this.height?this.absoluteBounds=new A(this.offsetX-this.width*this.pivot.x,this.offsetY-this.height*this.pivot.y,this.width,this.height):(!this.isDirt||!this.transformPath&&void 0!==this.width&&void 0!==this.height||this.absoluteBounds&&0!==this.absoluteBounds.height)&&!this.canMeasurePath||(this.absoluteBounds=O(this.data?this.data:"")),void 0===this.width?this.desiredSize=new S(this.absoluteBounds.width,this.height||this.absoluteBounds.height):void 0===this.height?this.desiredSize=new S(this.width||this.absoluteBounds.width,this.absoluteBounds.height):this.desiredSize=new S(this.width,this.height),this.desiredSize=this.validateDesiredSize(this.desiredSize,t),this.canMeasurePath=!1,this.desiredSize},e.prototype.arrange=function(t){return(this.isDirt||this.actualSize.width!==t.width||this.actualSize.height!==t.height)&&(this.isDirt=!0,this.absolutePath=this.updatePath(this.data,this.absoluteBounds,t),this.staticSize||(this.points=null)),this.actualSize=this.desiredSize,this.updateBounds(),this.isDirt=!1,this.actualSize},e.prototype.updatePath=function(t,e,i){var r=!1,n="",o=-e.x,s=-e.y,a=[];return i.width===e.width&&i.height===e.height||(o=i.width/Number(e.width?e.width:1),s=i.height/Number(e.height?e.height:1),r=!0),a=gt(a=dt(t)),n=(r||this.isDirt)&&this.transformPath?pt(a,o,s,r,e.x,e.y,0,0):mt(a),r=!1,n},e}(tt),At=(wt=function(t,e){return(wt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}wt(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),Mt=function(t,e,r,n){var o,s=arguments.length,a=s<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"===("undefined"==typeof Reflect?"undefined":i(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var h=t.length-1;h>=0;h--)(o=t[h])&&(a=(s<3?o(a):s>3?o(e,r,a):o(e,r))||a);return s>3&&a&&Object.defineProperty(e,r,a),a},kt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return At(e,t),e.equals=function(t,e){return t===e||!(!t||!e)&&(!t||!e||t.x===e.x&&t.y===e.y)},e.isEmptyPoint=function(t){return!t.x||!t.y},e.transform=function(t,e,i){var r={x:0,y:0};return r.x=Math.round(100*(t.x+i*Math.cos(e*Math.PI/180)))/100,r.y=Math.round(100*(t.y+i*Math.sin(e*Math.PI/180)))/100,r},e.findLength=function(t,e){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))},e.findAngle=function(t,e){var i=Math.atan2(e.y-t.y,e.x-t.x);return i=180*i/Math.PI,(i%=360)<0&&(i+=360),i},e.distancePoints=function(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))},e.getLengthFromListOfPoints=function(t){for(var e=0,i=0;i<t.length-1;i++)e+=this.distancePoints(t[parseInt(i.toString(),10)],t[i+1]);return e},e.adjustPoint=function(t,e,i,r){var n,o=i?{x:t.x,y:t.y}:{x:e.x,y:e.y};return t.x===e.x?t.y<e.y&&i||t.y>e.y&&!i?o.y+=r:o.y-=r:t.y===e.y?t.x<e.x&&i||t.x>e.x&&!i?o.x+=r:o.x-=r:i?(n=this.findAngle(t,e),o=this.transform(t,n,r)):(n=this.findAngle(e,t),o=this.transform(e,n,r)),o},e.direction=function(t,e){return Math.abs(e.x-t.x)>Math.abs(e.y-t.y)?t.x<e.x?"Right":"Left":t.y<e.y?"Bottom":"Top"},e.prototype.getClassName=function(){return"Point"},Mt([sf.base.Property(0)],e.prototype,"x",void 0),Mt([sf.base.Property(0)],e.prototype,"y",void 0),e}(sf.base.ChildProperty),zt=function(){function t(){}return t.getContext=function(t){return t.getContext("2d")},t.prototype.setStyle=function(e,i){var r=t.getContext(e);"none"===i.fill&&(i.fill="transparent"),"none"===i.stroke&&(i.stroke="transparent"),r.strokeStyle=i.stroke,r.lineWidth=i.strokeWidth,0===i.strokeWidth&&(r.strokeStyle="transparent"),r.globalAlpha=i.opacity;var n=[];i.dashArray&&(n=this.parseDashArray(i.dashArray)),r.setLineDash(n),r.fillStyle=i.fill},t.prototype.rotateContext=function(e,i,r,n){var o=t.getContext(e);o.translate(r,n),o.rotate(i*Math.PI/180),o.translate(-r,-n)},t.prototype.setFontStyle=function(e,i){var r=t.getContext(e),n="";i.italic&&(n+="italic "),i.bold&&(n+="bold "),n+=i.fontSize+"px ",n+=i.fontFamily,r.font=n},t.prototype.parseDashArray=function(t){for(var e=[],i=-1!==t.indexOf(" ")?" ":",",r=0,n=t.split(i);r<n.length;r++){var o=n[r];e.push(Number(o))}return e},t.prototype.drawRectangle=function(e,i){if(!0===i.visible)if(i.cornerRadius)i.data=ct(i.cornerRadius,i.height,i.width),this.drawPath(e,i);else{var r=t.getContext(e);r.save(),r.beginPath();i.cornerRadius;var n=i.x+i.width*i.pivotX,o=i.y+i.height*i.pivotY;this.rotateContext(e,i.angle,n,o),this.setStyle(e,i),r.rect(i.x,i.y,i.width,i.height),r.fillRect(i.x,i.y,i.width,i.height),r.fill(),r.stroke(),r.closePath(),r.restore()}},t.prototype.drawPath=function(e,i){var r=[];r=ft(r=dt(i.data));var n=t.getContext(e);n.save(),n.beginPath();var o=i.y+i.height*i.pivotY,s=i.x+i.width*i.pivotX;this.rotateContext(e,i.angle,s,o),this.setStyle(e,i),n.translate(i.x,i.y),this.renderPath(e,i,r),n.fill(),n.translate(-i.x,-i.y),n.stroke(),n.restore()},t.prototype.renderPath=function(e,i,r){if(!0===i.visible){var n,o=t.getContext(e),s=void 0,a=void 0,h=void 0,d=void 0,l=void 0,c=void 0,f=void 0,p=void 0,u=void 0,y=r;for(f=0,p=0,u=0,n=y.length;u<n;++u){var g=y[parseInt(u.toString(),10)],m=g.command;switch("x1"in g&&(h=g.x1),"x2"in g&&(l=g.x2),"y1"in g&&(d=g.y1),"y2"in g&&(c=g.y2),"x"in g&&(f=g.x),"y"in g&&(p=g.y),m){case"M":o.moveTo(f,p),g.x=f,g.y=p;break;case"L":o.lineTo(f,p),g.x=f,g.y=p;break;case"C":o.bezierCurveTo(h,d,l,c,f,p),g.x=f,g.y=p,g.x1=h,g.y1=d,g.x2=l,g.y2=c;break;case"Q":o.quadraticCurveTo(h,d,f,p),g.x=f,g.y=p,g.x1=h,g.y1=d;break;case"A":var x={x:s,y:a},v=g.r1,w=g.r2,b=g.angle*(Math.PI/180),S=g.largeArc,A=g.sweep,M={x:f,y:p},k={x:Math.cos(b)*(x.x-M.x)/2+Math.sin(b)*(x.y-M.y)/2,y:-Math.sin(b)*(x.x-M.x)/2+Math.cos(b)*(x.y-M.y)/2},z=Math.pow(k.x,2)/Math.pow(v,2)+Math.pow(k.y,2)/Math.pow(w,2);z>1&&(v*=Math.sqrt(z),w*=Math.sqrt(z));var P=Math.pow(w,2)*Math.pow(k.x,2),C=(S===A?-1:1)*Math.sqrt((Math.pow(v,2)*Math.pow(w,2)-Math.pow(v,2)*Math.pow(k.y,2)-P)/(Math.pow(v,2)*Math.pow(k.y,2)+Math.pow(w,2)*Math.pow(k.x,2)));isNaN(C)&&(C=0);var T={x:C*v*k.y/w,y:C*-w*k.x/v},R={x:(x.x+M.x)/2+Math.cos(b)*T.x-Math.sin(b)*T.y,y:(x.y+M.y)/2+Math.sin(b)*T.x+Math.cos(b)*T.y},I=this.a([1,0],[(k.x-T.x)/v,(k.y-T.y)/w]),O=[(k.x-T.x)/v,(k.y-T.y)/w],B=[(-k.x-T.x)/v,(-k.y-T.y)/w],E=this.a(O,B);this.r(O,B)<=-1&&(E=Math.PI),this.r(O,B)>=1&&(E=0);var N=I+(A?1:-1)*(E/2);Math.cos(N),Math.sin(N);if(g.centp=R,g.xAxisRotation=b,g.rx=v,g.ry=w,g.a1=I,g.ad=E,g.sweep=A,null!=o){var D=v>w?v:w,W=v>w?1:v/w,X=v>w?w/v:1;o.save(),o.translate(R.x,R.y),o.rotate(b),o.scale(W,X),o.arc(0,0,D,I,I+E,!A),o.scale(1/W,1/X),o.rotate(-b),o.translate(-R.x,-R.y),o.restore()}break;case"Z":case"z":o.closePath(),f=s,p=a}s=f,a=p}}},t.prototype.drawText=function(e,i){if(i.content&&!0===i.visible){var r=t.getContext(e);r.save(),this.setStyle(e,i);var n=i.x+i.width*i.pivotX,o=i.y+i.height*i.pivotY;this.rotateContext(e,i.angle,n,o),this.setFontStyle(e,i);var s,a=0;s=i.childNodes;var h=i.wrapBounds;if(r.fillStyle=i.color,h){var d=this.labelAlign(i,h,s);for(a=0;a<s.length;a++){var l=s[parseInt(a.toString(),10)];if("\n"!==l.text){var c=d.x+l.x-h.x,f=d.y+l.dy*a+.8*i.fontSize;if(r.fillText(l.text,c,f),"Underline"===i.textDecoration||"Overline"===i.textDecoration||"LineThrough"===i.textDecoration){var p=c,u=void 0,y=c+r.measureText(l.text).width,g=void 0;switch(i.textDecoration){case"Underline":u=f+2,g=f+2;break;case"Overline":u=d.y+l.dy*a,g=d.y+l.dy*a;break;case"LineThrough":u=(f+d.y+l.dy*a)/2+2,g=(f+d.y+l.dy*a)/2+2}r.beginPath(),r.moveTo(p,u),r.lineTo(y,g),r.strokeStyle=i.color,r.lineWidth=.08*i.fontSize,r.globalAlpha=i.opacity,r.stroke()}}}}r.restore()}},t.prototype.m=function(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))},t.prototype.r=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(this.m(t)*this.m(e))},t.prototype.a=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(this.r(t,e))},t.prototype.getMeetOffset=function(t,e,i){var r=Math.max(e,i),n=Math.min(e,i);switch(t){case"min":return 0;case"mid":return(r-n)/2;case"max":return r-n;default:return 0}},t.prototype.getSliceOffset=function(t,e,i,r){switch(t){case"min":return 0;case"mid":return(e-i)/2*r/e;case"max":return(e-i)*r/e;default:return 0}},t.prototype.image=function(t,e,i,r,n,o,s){t.beginPath();var a=e.width,h=e.height,d=n,l=o,c=0,f=0;if(t.globalAlpha=s.opacity,s&&"None"!==s.alignment){var p=s.alignment.toLowerCase().substr(1,3),u=s.alignment.toLowerCase().substr(5,3);if("Slice"===s.scale){var y=function(){c=d,f=h*d/a},g=function(){c=a*l/h,f=l};d>l?(y(),l>f&&g()):d===l?a>h?g():y():(g(),d>c&&y());var m=this.getSliceOffset(p,c,d,a),x=this.getSliceOffset(u,f,l,h),v=a-m,w=h-x,b=c-m*(c/a),S=f-x*(f/h),A=W("canvas",{width:n.toString(),height:o.toString()}),M=A.getContext("2d");t.clearRect(i,r,b,S),M.drawImage(e,m,x,v,w,0,0,b,S),t.clearRect(i,r,n,o),t.drawImage(A,i,r,n,o)}else if("Meet"===s.scale){var k=h/a,z=l/d;c=z>k?d:l/k,f=z>k?d*k:l,i+=this.getMeetOffset(p,c,d),r+=this.getMeetOffset(u,f,l),t.clearRect(i,r,c,f),t.drawImage(e,0,0,a,h,i,r,c,f)}else t.clearRect(i,r,n,o),t.drawImage(e,i,r,n,o)}else if(e.complete)t.clearRect(i,r,n,o),t.drawImage(e,i,r,n,o);else{var P=t.getTransform();e.onload=null,e.onload=function(){t.setTransform(P.a,P.b,P.c,P.d,P.e,P.f),t.clearRect(i,r,n,o),t.drawImage(e,i,r,n,o)}}t.closePath()},t.prototype.loadImage=function(t,e,i,r,n){var o;this.rotateContext(i,e.angle,r,n),window.customStampCollection&&window.customStampCollection.get(e.printID)?o=window.customStampCollection.get(e.printID):(o=new Image).src=e.source,this.image(t,o,e.x,e.y,e.width,e.height,e)},t.prototype.drawImage=function(e,i,r,n){var o=this;if(i.visible){var s=t.getContext(e);s.save();var a=i.x+i.width*i.pivotX,h=i.y+i.height*i.pivotY,d=new Image;d.src=i.source;var l=s.canvas.id.split("_");l[l.length-1];n?d.onload=function(){o.loadImage(s,i,e,a,h)}:this.loadImage(s,i,e,a,h),s.restore()}},t.prototype.labelAlign=function(t,e,i){var r=new S(e.width,i.length*(1.2*t.fontSize)),n={x:0,y:0},o=t.x,s=t.y,a=.5*t.width,h=.5*t.height;return"left"===t.textAlign?a=0:"center"===t.textAlign?a=e.width>t.width&&("Ellipsis"===t.textOverflow||"Clip"===t.textOverflow)?0:.5*t.width:"right"===t.textAlign&&(a=1*t.width),n.x=o+a+(e?e.x:0),n.y=s+h-r.height/2,n},t}();var Pt=function(){function t(t,e){this.renderer=null,this.isSvgMode=!0,this.diagramId=t,this.element=D(this.diagramId),this.isSvgMode=e,this.renderer=new zt}return t.prototype.renderElement=function(t,e,i,r,n,o,s,a){t instanceof it?this.renderContainer(t,e,i,r,n,o,s,a):t instanceof ht?this.renderImageElement(t,e,r,n,s):t instanceof St?this.renderPathElement(t,e,r,n,s):t instanceof nt?this.renderTextElement(t,e,r,n,s):this.renderRect(t,e,r,n)},t.prototype.renderImageElement=function(t,e,i,r,n){var o,s,a,h,d=this.getBaseAttributes(t,i);if(d.cornerRadius=0,this.renderer.drawRectangle(e,d),"Stretch"===t.stretch)o=t.actualSize.width,s=t.actualSize.height;else{var l=t.contentSize.width,c=t.contentSize.height,f=d.width/l,p=d.height/c,u=void 0;switch(t.stretch){case"Meet":o=l*(u=Math.min(f,p)),s=c*u,d.x+=Math.abs(d.width-o)/2,d.y+=Math.abs(d.height-s)/2;break;case"Slice":f=d.width/l,p=d.height/c,o=l*(u=Math.max(f,p)),s=c*u,a=d.width/o*l,h=d.height/s*c;break;case"None":o=l,s=c}}d.width=o,d.height=s,d.sourceWidth=a,d.sourceHeight=h,d.source=t.source,d.alignment=t.imageAlign,d.scale=t.imageScale,d.printID=t.printID,this.renderer.drawImage(e,d,r,n)},t.prototype.renderPathElement=function(t,e,i,r,n){var o=this.getBaseAttributes(t,i);o.data=t.absolutePath,o.data=t.absolutePath;t.id;this.isSvgMode||(o.x=o.x,o.y=o.y),this.renderer.drawPath(e,o)},t.prototype.renderTextElement=function(t,e,i,r,n){var o=this.getBaseAttributes(t,i);o.cornerRadius=0,o.whiteSpace=Z(t.style.whiteSpace,t.style.textWrapping),o.content=t.content,o.breakWord=V(t.style.textWrapping),o.textAlign=U(t.style.textAlign),o.color=t.style.color,o.italic=t.style.italic,o.bold=t.style.bold,o.fontSize=t.style.fontSize,o.fontFamily=t.style.fontFamily,o.textOverflow=t.style.textOverflow,o.textDecoration=t.style.textDecoration,o.doWrap=t.doWrap,o.wrapBounds=t.wrapBounds,o.childNodes=t.childNodes,o.dashArray="",o.strokeWidth=0,o.fill=t.style.fill;t.content?t.content:t.id;this.renderer.drawRectangle(e,o),this.renderer.drawText(e,o)},t.prototype.renderContainer=function(t,e,i,r,n,o,s,a){if(r={tx:0,ty:0,scale:1},this.diagramId&&(n=n),this.renderRect(t,e,r,n),t.hasChildren())for(var h=0,d=t.children;h<d.length;h++){var l=d[h];this.renderElement(l,e,i,r,n,!0,s,a)}},t.prototype.renderRect=function(t,e,i,r){var n=this.getBaseAttributes(t,i);n.cornerRadius=t.cornerRadius||0;t.id;this.renderer.drawRectangle(e,n)},t.prototype.getBaseAttributes=function(t,e){var i={width:t.actualSize.width,height:t.actualSize.height,x:t.offsetX-t.actualSize.width*t.pivot.x+.5,y:t.offsetY-t.actualSize.height*t.pivot.y+.5,fill:t.style.fill,stroke:t.style.strokeColor,angle:t.rotateAngle+t.parentTransform,pivotX:t.pivot.x,pivotY:t.pivot.y,strokeWidth:t.style.strokeWidth,dashArray:t.style.strokeDashArray||"",opacity:t.style.opacity,visible:t.visible,id:t.id,gradient:t.style.gradient};return e&&(i.x+=e.tx,i.y+=e.ty),i},t}(),Ct=function(){function t(){}return t.prototype.parseDashArray=function(t){return[]},t.prototype.drawRectangle=function(t,e,i,r,n,o,s){var a,h;a=e.id===t.id?e.id+"_container":e.id,h&&!n||(h=document.createElementNS("http://www.w3.org/2000/svg","rect"),t.appendChild(h));var d={id:a,x:e.x.toString(),y:e.y.toString(),width:e.width.toString(),height:e.height.toString(),visibility:e.visible?"visible":"hidden",transform:"rotate("+e.angle+","+(e.x+e.width*e.pivotX)+","+(e.y+e.height*e.pivotY)+")",rx:e.cornerRadius||0,ry:e.cornerRadius||0,opacity:e.opacity,"aria-label":s||""};e.class&&(d.class=e.class);s||(d["pointer-events"]="none"),Tt(h,d),this.setSvgStyle(h,e,i)},t.prototype.updateSelectionRegion=function(t,e){var i,r;i=t.parentNode.getElementById(e.id),r={id:e.id,x:e.x.toString(),y:e.y.toString(),width:e.width.toString(),height:e.height.toString(),transform:"rotate("+e.angle+","+(e.x+e.width*e.pivotX)+","+(e.y+e.height*e.pivotY)+")",class:"e-diagram-selected-region"},i||(i=document.createElementNS("http://www.w3.org/2000/svg","rect"),t.appendChild(i)),this.setSvgStyle(i,e),Tt(i,r)},t.prototype.createGElement=function(t,e){return Rt(t,e)},t.prototype.drawCircle=function(t,e,i,r){var n=document.createElementNS("http://www.w3.org/2000/svg","circle");this.setSvgStyle(n,e);var o=e.class||"";i||(o+=" e-disabled");var s={id:e.id,cx:e.centerX,cy:e.centerY,r:e.radius,visibility:e.visible?"visible":"hidden",class:o,"aria-label":r?r["aria-label"]:""};""===s["aria-label"]&&(s["pointer-events"]="none"),n.style.display=e.visible?"block":"none",Tt(n,s),t.appendChild(n)},t.prototype.setSvgStyle=function(t,e,i){if(e.canApplyStyle||void 0===e.canApplyStyle){"none"===e.fill&&(e.fill="transparent"),"none"===e.stroke&&(e.stroke="transparent");var r,n=[];if(void 0!==e.dashArray)n=(new zt).parseDashArray(e.dashArray);r=e.fill,e.stroke&&t.setAttribute("stroke",e.stroke),void 0!==e.strokeWidth&&null!==e.strokeWidth&&t.setAttribute("stroke-width",e.strokeWidth.toString()),n&&t.setAttribute("stroke-dasharray",n.toString()),r&&t.setAttribute("fill",r)}},t.prototype.svgLabelAlign=function(t,e,i){var r=new S(e.width,i.length*(1.2*t.fontSize)),n={x:0,y:0},o=.5*t.width,s=.5*t.height;return"left"===t.textAlign?o=0:"center"===t.textAlign?o=e.width>t.width&&("Ellipsis"===t.textOverflow||"Clip"===t.textOverflow)?0:.5*t.width:"right"===t.textAlign&&(o=1*t.width),n.x=0+o+(e?e.x:0),n.y=1.2+s-r.height/2,n},t.prototype.drawLine=function(t,e){var i=document.createElementNS("http://www.w3.org/2000/svg","line");this.setSvgStyle(i,e);var r=e.x+e.width*e.pivotX,n=e.y+e.height*e.pivotY,o={id:e.id,x1:e.startPoint.x+e.x,y1:e.startPoint.y+e.y,x2:e.endPoint.x+e.x,y2:e.endPoint.y+e.y,stroke:e.stroke,"stroke-width":e.strokeWidth.toString(),opacity:e.opacity.toString(),transform:"rotate("+e.angle+" "+r+" "+n+")",visibility:e.visible?"visible":"hidden"};e.class&&(o.class=e.class),Tt(i,o),t.appendChild(i)},t.prototype.drawPath=function(t,e,i,r,n,o){var s=Math.floor(10*Math.random()+1);t.id,s.toString();var a,h,d=[];d=ft(d=dt(e.data)),n&&(a=n.getElementById(e.id+"_groupElement_shadow"))&&a.parentNode.removeChild(a),n&&(h=n.getElementById(e.id)),h&&!r||(h=document.createElementNS("http://www.w3.org/2000/svg","path"),t.appendChild(h)),this.renderPath(h,e,d);var l={id:e.id,transform:"rotate("+e.angle+","+(e.x+e.width*e.pivotX)+","+(e.y+e.height*e.pivotY)+")translate("+e.x+","+e.y+")",visibility:e.visible?"visible":"hidden",opacity:e.opacity,"aria-label":o||""};e.class&&(l.class=e.class),Tt(h,l),this.setSvgStyle(h,e,i)},t.prototype.renderPath=function(t,e,i){var r,n,o,s,a,h,d,l,c=i,f="";for(a=0,h=0,l=0,d=c.length;l<d;++l){var p=c[parseInt(l.toString(),10)],u=p.command;switch("x1"in p&&(r=p.x1),"x2"in p&&(o=p.x2),"y1"in p&&(n=p.y1),"y2"in p&&(s=p.y2),"x"in p&&(a=p.x),"y"in p&&(h=p.y),u){case"M":f=f+"M"+a.toString()+","+h.toString()+" ";break;case"L":f=f+"L"+a.toString()+","+h.toString()+" ";break;case"C":f=f+"C"+r.toString()+","+n.toString()+","+o.toString()+","+s.toString()+",",f+=a.toString()+","+h.toString()+" ";break;case"Q":f=f+"Q"+r.toString()+","+n.toString()+","+a.toString()+","+h.toString()+" ";break;case"A":f=f+"A"+p.r1.toString()+","+p.r2.toString()+","+p.angle.toString()+",",f+=p.largeArc.toString()+","+p.sweep+","+a.toString()+","+h.toString()+" ";break;case"Z":case"z":f+="Z "}}t.setAttribute("d",f)},t}();function Tt(t,e){for(var i=Object.keys(e),r=0;r<i.length;r++)t.setAttribute(i[parseInt(r.toString(),10)],e[i[parseInt(r.toString(),10)]])}function Rt(t,e){var i=document.createElementNS("http://www.w3.org/2000/svg",t);return Tt(i,e),i}function It(t,e,i){for(var r=t.children.length-1;r>=0;r--){var n=t.children[parseInt(r.toString(),10)];if(n&&n.bounds.containsPoint(e,0)){if(n instanceof it){var o=this.findTargetElement(n,e);if(o)return o}if(n.bounds.containsPoint(e,0))return n}}return t.bounds.containsPoint(e,i)&&"none"!==t.style.fill?t:null}function Ot(t,e){var i={x:0,y:0},r=t,n=e,o=(n.y2-n.y1)*(r.x2-r.x1)-(n.x2-n.x1)*(r.y2-r.y1),s=(n.x2-n.x1)*(r.y1-n.y1)-(n.y2-n.y1)*(r.x1-n.x1),a=(r.x2-r.x1)*(r.y1-n.y1)-(r.y2-r.y1)*(r.x1-n.x1);if(0===o)return{enabled:!1,intersectPt:i};var h=s/o,d=a/o;return h>=0&&h<=1&&d>=0&&d<=1?(i.x=r.x1+h*(r.x2-r.x1),i.y=r.y1+h*(r.y2-r.y1),{enabled:!0,intersectPt:i}):{enabled:!1,intersectPt:i}}function Bt(t,e,i,r){return{x1:Number(t)||0,y1:Number(e)||0,x2:Number(i)||0,y2:Number(r)||0}}return t.Canvas=st,t.CanvasRenderer=zt,t.Container=it,t.DrawingElement=tt,t.DrawingRenderer=Pt,t.Gradient=y,t.ImageElement=ht,t.LinearGradient=g,t.Margin=p,t.Matrix=M,t.PathElement=St,t.Point=kt,t.RadialGradient=m,t.Rect=A,t.ShapeStyle=x,t.Size=S,t.Stop=u,t.StrokeStyle=v,t.SvgRenderer=Ct,t.TextElement=nt,t.TextStyle=w,t.Thickness=f,t.bBoxText=H,t.contains=function(t,e,i){return t.x>=e.x-i&&t.x<=e.x+i&&t.y>=e.y-i&&t.y<=e.y+i},t.cornersPointsBeforeRotation=Y,t.createHtmlElement=W,t.createMeasureElements=function(){if(window.measureElement)window.measureElement.usageCount+=1;else{var t=W("div",{id:"measureElement",style:"visibility:hidden ; height: 0px ; width: 0px; overflow: hidden;"}),e=W("span",{style:"display:inline-block ; line-height: normal"});t.appendChild(e);var i;if(i=W("img",{}),t.appendChild(i),"undefined"!=typeof document){var r=document.createElementNS("http://www.w3.org/2000/svg","svg");r.setAttribute("xlink","http://www.w3.org/1999/xlink"),t.appendChild(r);var n=document.createElementNS("http://www.w3.org/2000/svg","path");r.appendChild(n);document.createTextNode("");var o=document.createElementNS("http://www.w3.org/2000/svg","text");o.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),r.appendChild(o),window.measureElement=t,window.measureElement.usageCount=1,document.body.appendChild(t)}}},t.createSvg=function(t,e,i){var r=document.createElementNS("http://www.w3.org/2000/svg","svg");return Tt(r,{id:t,width:e,height:i}),r},t.createSvgElement=Rt,t.findElementUnderMouse=function(t,e,i){return It(t.wrapper,e,i)},t.findNearestPoint=function(t,e,i){var r;r=kt.findLength(e,t)>kt.findLength(i,t)?i:e;var n=kt.findAngle(e,i),o=kt.findAngle(r,t),s=kt.findLength(r,t),a=o+2*(n-o);return{x:r.x+s*Math.cos(a*Math.PI/180),y:r.y+s*Math.sin(a*Math.PI/180)}},t.findTargetElement=It,t.getAdornerLayerSvg=_,t.getBezierDirection=function(t,e){return Math.abs(e.x-t.x)>Math.abs(e.y-t.y)?t.x<e.x?"right":"left":t.y<e.y?"bottom":"top"},t.getBounds=F,t.getChildNode=I,t.getDiagramElement=D,t.getLineSegment=Bt,t.getOffset=Q,t.getParentSvg=function(t,e,i){return t&&t.id&&e&&"selector"===e?this.pdfViewer.adornerSvgLayer:i},t.getPathString=mt,t.getPoint=function(t,e,i,r,n,o,s,a){var h={x:0,y:0},d=k();switch(P(d,n,o,s),a.x){case 0:switch(a.y){case 0:h=z(d,{x:t,y:e});break;case.5:h=z(d,{x:t,y:e+r/2});break;case 1:h=z(d,{x:t,y:e+r})}break;case.5:switch(a.y){case 0:h=z(d,{x:t+i/2,y:e});break;case.5:h=z(d,{x:t+i/2,y:e+r/2});break;case 1:h=z(d,{x:t+i/2,y:e+r})}break;case 1:switch(a.y){case 0:h=z(d,{x:t+i,y:e});break;case.5:h=z(d,{x:t+i,y:e+r/2});break;case 1:h=z(d,{x:t+i,y:e+r})}}return{x:h.x,y:h.y}},t.getPoints=function(t,e,i){var r=[];i=i||0;var n={x:e.topLeft.x-i,y:e.topLeft.y},o={x:e.topRight.x+i,y:e.topRight.y},s={x:e.bottomRight.x,y:e.bottomRight.y-i},a={x:e.bottomLeft.x,y:e.bottomLeft.y+i};return r.push(n),r.push(o),r.push(s),r.push(a),r},t.getRectanglePath=ct,t.getSelectorElement=function(t,e){var i=null,r=_(t,e);return r&&(i=r.getElementById(t+"_SelectorElement")),i},t.getString=xt,t.identityMatrix=k,t.intersect2=function(t,e,i,r){var n=Ot(Bt(t.x,t.y,e.x,e.y),Bt(i.x,i.y,r.x,r.y));return n.enabled?n.intersectPt:{x:0,y:0}},t.intersect3=Ot,t.measureImage=L,t.measurePath=O,t.measureText=N,t.middleElement=function(t,e){return(t+e)/2},t.multiplyMatrix=C,t.parsePathData=lt,t.pathSegmentCollection=ft,t.processPathData=dt,t.randomId=function(){for(var t,e="0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz",i="",r=0;r<5;r++){if("crypto"in window&&"getRandomValues"in crypto){var n=new Uint16Array(1);t=(window.msCrypto||window.crypto).getRandomValues(n)[0]%(e.length-1)}else t=Math.floor(Math.random()*e.length);0===r&&t<10?r--:i+=e.substring(t,t+1)}return i},t.refreshDiagramElements=function(t,e,i){for(var r=0;r<e.length;r++)i.renderElement(e[parseInt(r.toString(),10)],t,void 0)},t.rotateMatrix=P,t.rotatePoint=q,t.rotateSize=j,t.scaleElement=function t(e,i,r,n){if(void 0!==e.width&&void 0!==e.height&&(e.width*=i,e.height*=r),e instanceof it){var o=k(),s=n.width||n.actualSize.width,a=n.height||n.actualSize.height;if(void 0!==s&&void 0!==a){var h=n.offsetX-s*n.pivot.x,d=n.offsetY-a*n.pivot.y,l={x:h+s*n.pivot.x,y:d+a*n.pivot.y};l=q(n.rotateAngle,n.offsetX,n.offsetY,l),P(o,-n.rotateAngle,l.x,l.y),P(o,n.rotateAngle,l.x,l.y);for(var c=0,f=e.children;c<f.length;c++){var p=f[c];if(void 0!==p.width&&void 0!==p.height){var u=z(o,{x:p.offsetX,y:p.offsetY});p.offsetX=u.x,p.offsetY=u.y,t(p,i,r,n)}}}}},t.scaleMatrix=function(e,i,r,n,o){void 0===n&&(n=0),void 0===o&&(o=0),C(e,function(e,i,r,n){var o=k();return o.type=r||n?t.MatrixTypes.Scaling|t.MatrixTypes.Translation:t.MatrixTypes.Scaling,T(o,e,0,0,i,r-e*r,n-i*n),o}(i,r,n,o))},t.scalePathData=yt,t.setAttributeHtml=X,t.setAttributeSvg=Tt,t.splitArrayCollection=gt,t.textAlignToString=U,t.transformPath=pt,t.transformPointByMatrix=z,t.transformPointsByMatrix=function(t,e){for(var i=[],r=0,n=e;r<n.length;r++){var o=n[r];i.push(z(t,o))}return i},t.translateMatrix=function(e,i,r){return e.type&t.MatrixTypes.Identity?(e.type=t.MatrixTypes.Translation,void T(e,1,0,0,1,i,r)):e.type&t.MatrixTypes.Unknown?(e.offsetX+=i,void(e.offsetY+=r)):(e.offsetX+=i,e.offsetY+=r,void(e.type|=t.MatrixTypes.Translation))},t.translatePoints=function(t,e){for(var i=[],r=0,n=e;r<n.length;r++){var o=n[r],s={x:t.offsetX-t.actualSize.width*t.pivot.x+o.x,y:t.offsetY-t.actualSize.height*t.pivot.y+o.y},a=void 0,h=t.rotateAngle+t.parentTransform;h&&P(a=k(),h,t.offsetX,t.offsetY),a&&(s=z(a,s)),i.push(s)}return i},t.updateStyle=function(t,e){for(var i=e.style,r=e,n=0,o=Object.keys(t);n<o.length;n++){switch(o[n]){case"fill":i.fill=t.fill,i instanceof v&&(i.fill="transparent");break;case"textOverflow":i.textOverflow=t.textOverflow;break;case"opacity":i.opacity=t.opacity;break;case"strokeColor":i.strokeColor=t.strokeColor;break;case"strokeDashArray":i.strokeDashArray=t.strokeDashArray;break;case"strokeWidth":i.strokeWidth=t.strokeWidth;break;case"bold":i.bold=t.bold;break;case"color":i.color=t.color;break;case"textWrapping":i.textWrapping=t.textWrapping;break;case"fontFamily":i.fontFamily=t.fontFamily;break;case"fontSize":i.fontSize=t.fontSize;break;case"italic":i.italic=t.italic;break;case"textAlign":i.textAlign=t.textAlign;break;case"whiteSpace":i.whiteSpace=t.whiteSpace;break;case"textDecoration":i.textDecoration=t.textDecoration}}e instanceof nt&&r.refreshTextElement()},t.updatedSegment=ut,t.whiteSpaceToString=Z,t.wordBreakToString=V,t},sf.drawings=sf.drawings({})}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{})})();