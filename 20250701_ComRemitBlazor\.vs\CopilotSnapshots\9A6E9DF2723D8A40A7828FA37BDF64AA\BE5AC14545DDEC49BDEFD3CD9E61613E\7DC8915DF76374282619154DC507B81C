﻿using ComRemitBlazor.Models;
using Microsoft.EntityFrameworkCore;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using QuestPDF.Previewer;
using QuestPDF.Drawing;
using QuestPDF.Elements;

namespace ComRemitBlazor.Services
{
    public class RemitedListService
    {
        private readonly ComRemitDbContext _context;
        private readonly BarcodeService _barcodeService;

        public RemitedListService(ComRemitDbContext context, BarcodeService barcodeService)
        {
            _context = context;
            _barcodeService = barcodeService;
        }

        public async Task<(List<RemitedListViewModel> Items, int TotalPages)> GetPagedListAsync(string keyword, int pageIndex, int pageSize)
        {
            // 只顯示已完成彙整的資料（CashDate 不為 null）
            var query = _context.RemitedList.Where(r => r.CashDate != null).AsQueryable();
            if (!string.IsNullOrWhiteSpace(keyword))
            {
                query = query.Where(r => (r.CollecName ?? "").Contains(keyword) || (r.RemitMemo ?? "").Contains(keyword));
            }
            var group = query.GroupBy(r => r.ConSno)
                .Select(g => new RemitedListViewModel
                {
                    ConSno = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(x => (decimal)(x.RemitPrice ?? 0)),
                    RemitMemo = g.Select(x => x.RemitMemo).FirstOrDefault(),
                    ConDate = g.Select(x => x.ConDate).FirstOrDefault(),
                    ConMemo = g.Select(x => x.ConMemo).FirstOrDefault(), // 使用正確的 ConMemo 欄位
                    CashDate = g.Select(x => x.CashDate).FirstOrDefault() // 添加 CashDate 用於排序
                });
            var totalCount = await group.CountAsync();
            var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
            var items = await group.OrderByDescending(x => x.CashDate ?? x.ConDate) // 優先按 CashDate 排序，如果沒有則用 ConDate
                .ThenByDescending(x => x.ConSno) // 次要排序條件
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // 為每個項目載入前5筆明細
            foreach (var item in items)
            {
                if (item.ConSno.HasValue)
                {
                    item.Details = await GetDetailListAsync(item.ConSno.Value);
                }
            }

            return (items, totalPages);
        }

        public async Task<List<RemitedListDetailViewModel>> GetDetailListAsync(int conSno)
        {
            return await _context.RemitedList
                .Where(r => r.ConSno == conSno)
                .OrderBy(r => r.Sno)
                .Take(5)
                .Select(r => new RemitedListDetailViewModel
                {
                    CollecName = r.CollecName,
                    CollecAcc = r.CollecAcc,
                    RemitPrice = r.RemitPrice,
                    CollectNo = r.CollectNo
                })
                .ToListAsync();
        }

        public async Task DeleteByConSnoAsync(int conSno)
        {
            var items = await _context.RemitedList.Where(r => r.ConSno == conSno).ToListAsync();
            if (items.Any())
            {
                _context.RemitedList.RemoveRange(items);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<string> ExportTxtAsync(int conSno)
        {
            var items = await _context.RemitedList.Where(r => r.ConSno == conSno).OrderBy(r => r.Sno).ToListAsync();
            if (!items.Any()) return string.Empty;
            // 參考 Fun.cs 格式，這裡僅示範：每行「戶名,帳號,金額」
            var lines = items.Select(r => $"{r.CollecName},{r.CollecAcc},{r.RemitPrice}");
            return string.Join("\r\n", lines);
        }

        public async Task<(byte[] PdfBytes, string Filename)> PrintPdfAsync(int conSno)
        {
            // 設定 QuestPDF 授權，確保關閉除錯模式
            QuestPDF.Settings.License = LicenseType.Community;
            QuestPDF.Settings.EnableDebugging = false;

            var items = await _context.RemitedList.Where(r => r.ConSno == conSno).OrderBy(r => r.Sno).ToListAsync();
            if (!items.Any()) return (Array.Empty<byte>(), "");

            // 判斷是否為郵局格式 (CollectNo 全部為 "7000021")
            var ifGenPostFile = items.All(r => r.CollectNo == "7000021");
            var searchId = conSno.ToString();

            byte[] pdfBytes;
            string filename;

            if (ifGenPostFile)
            {
                pdfBytes = GeneratePostalReportAdvanced(items, conSno);
                filename = $"CR{searchId.PadLeft(6, '0')}_POST.pdf";
            }
            else
            {
                pdfBytes = GenerateTWBankReportAdvanced(items, conSno);
                filename = $"CR{searchId.PadLeft(6, '0')}_TWBANK.pdf";
            }

            return (pdfBytes, filename);
        }



        private byte[] GeneratePostalReportAdvanced(List<RemitedList> list, int conSno)
        {
            try
            {
                var totalAmount = list.Count;
                var totalCost = list.Sum(x => (decimal)(x.RemitPrice ?? 0));
                var searchId = conSno.ToString();
                var filename = $"CR{searchId.PadLeft(6, '0')}_POST";
                var memo = list.FirstOrDefault()?.RemitMemo ?? "";
                var cashDate = list.FirstOrDefault()?.CashDate;

                return Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(1.5f, Unit.Centimetre);
                        page.DefaultTextStyle(x => x.FontFamily("Microsoft YaHei").FontSize(11));

                        page.Content().Element(container =>
                        {
                            container.Column(column =>
                            {
                                for (int copyIndex = 0; copyIndex < 3; copyIndex++)
                                {
                                    if (copyIndex > 0)
                                    {
                                        column.Item().PageBreak();
                                    }

                                    // 總表部分
                                    GenerateSummarySection(column, filename, totalCost, totalAmount, memo);

                                    // 明細表部分
                                    if (copyIndex == 0) // 只在第一份加入明細表
                                    {
                                        column.Item().PageBreak();
                                        GenerateDetailSection(column, filename, list);
                                    }
                                }
                            });
                        });

                        // 簡化的頁腳條碼
                        page.Footer().AlignCenter().Text(text =>
                        {
                            text.Span($"{filename} - ");
                            text.CurrentPageNumber();
                            text.Span(" / ");
                            text.TotalPages();
                        });
                    });
                }).GeneratePdf();
            }
            catch (Exception)
            {
                // 如果生成失敗，返回簡化版本
                return GenerateSimplePostalReport(list, conSno);
            }
        }

        private void GenerateSummarySection(ColumnDescriptor column, string filename, decimal totalCost, int totalAmount, string memo)
        {
            column.Item().PaddingBottom(10);
            column.Item().Text($"條碼：{filename}").AlignCenter().FontSize(8);

            column.Item().PaddingBottom(20);
            column.Item().Text("委託郵局代存員工薪資總表(408)").FontSize(18).Bold().AlignCenter();

            // 第一行表格 - 按照Fun.cs的genRow1Cell3標準
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn(1);
                    columns.RelativeColumn(1);
                    columns.RelativeColumn(1);
                });

                table.Cell().Element(CellStyle).Text($"\n委託機構名稱:宜蘭縣政府\n\n委託機構地址:260宜蘭市縣政北路1號\n\n連絡電話:\n\n檔名: {filename}");
                table.Cell().Element(CellStyle).Text($"\n劃撥儲金帳號:11206482\n\n\n押碼值:");

                // 第三個cell包含詳細的資料別表格
                table.Cell().Element(container => container).Table(innerTable =>
                {
                    innerTable.ColumnsDefinition(columns =>
                    {
                        columns.ConstantColumn(30);
                        columns.RelativeColumn(1);
                        columns.ConstantColumn(30);
                    });

                    innerTable.Cell().Element(BorderCellStyle).Text("勾註");
                    innerTable.Cell().Element(BorderCellStyle).Text("資料別及作業方式");
                    innerTable.Cell().Element(BorderCellStyle).Text("批次");

                    innerTable.Cell().Element(BorderCellStyle).Text(" ");
                    innerTable.Cell().Element(BorderCellStyle).Text("媒體薪資類(Y)");
                    innerTable.Cell().Element(BorderCellStyle).Text(" ");

                    innerTable.Cell().Element(BorderCellStyle).Text(" ");
                    innerTable.Cell().Element(BorderCellStyle).Text("媒體非薪資類(N)");
                    innerTable.Cell().Element(BorderCellStyle).Text(" ");

                    innerTable.Cell().Element(BorderCellStyle).Text(" ");
                    innerTable.Cell().Element(BorderCellStyle).Text("非媒體類(C)");
                    innerTable.Cell().Element(BorderCellStyle).Text(" ");

                    innerTable.Cell().Element(BorderCellStyle).Text(" ");
                    innerTable.Cell().Element(BorderCellStyle).Text("薪資存款直接傳輸作業");
                    innerTable.Cell().Element(BorderCellStyle).Text(" ");

                    innerTable.Cell().Element(BorderCellStyle).Text(" ");
                    innerTable.Cell().Element(BorderCellStyle).Text("本局磁片傳輸");
                    innerTable.Cell().Element(BorderCellStyle).Text(" ");

                    innerTable.Cell().Element(BorderCellStyle).Text(" ");
                    innerTable.Cell().Element(BorderCellStyle).Text("本局傳輸(非媒體)");
                    innerTable.Cell().Element(BorderCellStyle).Text(" ");
                });
            });

            column.Item().PaddingVertical(10);

            // 第二行表格 - 按照Fun.cs的genRow2標準
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.ConstantColumn(80);
                    columns.ConstantColumn(80);
                    columns.ConstantColumn(80);
                    columns.ConstantColumn(80);
                    columns.ConstantColumn(80);
                    columns.RelativeColumn(1);
                });

                // 表頭
                table.Cell().Element(BorderCellStyle).AlignCenter().Text("指定轉存日期");
                table.Cell().Element(BorderCellStyle).AlignCenter().Text("委存總件數");
                table.Cell().Element(BorderCellStyle).AlignCenter().Text("委存總金額");
                table.Cell().Element(BorderCellStyle).AlignCenter().Text("款項細目代碼");
                table.Cell().Element(BorderCellStyle).AlignCenter().Text("款項來源代號\n(本欄由郵局填寫)");
                table.Cell().Element(BorderCellStyle).AlignCenter().Text("備註");

                // 資料行
                table.Cell().Element(BorderCellStyle).Text(" ");
                table.Cell().Element(BorderCellStyle).AlignCenter().Text($"{totalAmount:N0}");
                table.Cell().Element(BorderCellStyle).AlignCenter().Text($"{totalCost:N0}");
                table.Cell().Element(BorderCellStyle).Text(" ");
                table.Cell().Element(BorderCellStyle).Text(" ");
                table.Cell().Element(BorderCellStyle).Text(memo);
            });

            column.Item().PaddingVertical(20);

            column.Item().Text("\n主管____________\n\n經辦____________");

            column.Item().PaddingVertical(20);

            column.Item().Text("說明:\n(1)本單由委託機關填寫一式三份，檢附在委託郵局代存員工薪資總表交受託局，如因資料錯誤致郵局誤入帳戶，應由委託單位自行負責。\n(2)本單局號，帳號請加註檢查號碼，切勿遺漏。\n(3)受託機關名稱及其所設立之劃撥帳戶帳號，請正確填寫，以利受託局還有帳目無法轉存時理撥退手續。\n(4)委託機構使用媒體或非媒體處理，請在右上角作業方式欄勾註。\n(5)媒體磯片在受託局傳輸者，請在右上角「本局磁片傳輸」欄勾註。")
                .FontSize(9);

            column.Item().PaddingVertical(20);

            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.ConstantColumn(50);
                    columns.ConstantColumn(80);
                    columns.ConstantColumn(50);
                    columns.ConstantColumn(80);
                    columns.ConstantColumn(50);
                    columns.ConstantColumn(80);
                    columns.RelativeColumn(1);
                });

                table.Cell().Element(BorderCellStyle).Text("審核");
                table.Cell().Element(BorderCellStyle).Text("");
                table.Cell().Element(BorderCellStyle).Text("經辦員");
                table.Cell().Element(BorderCellStyle).Text("");
                table.Cell().Element(BorderCellStyle).Text("主管");
                table.Cell().Element(BorderCellStyle).Text("");
                table.Cell().Element(BorderCellStyle).Text("郵戳");
            });
        }

        private void GenerateDetailSection(ColumnDescriptor column, string filename, List<RemitedList> list)
        {
            // 明細表頁面標題和條碼
            column.Item().PaddingBottom(10);
            // 在右上角添加條碼
            column.Item().Row(row =>
            {
                row.RelativeItem(3).Text(""); // 左側空白
                row.RelativeItem(1).AlignRight().Text($"{filename}-1").FontFamily("Courier New").FontSize(8); // 右側條碼文字
            });

            column.Item().PaddingBottom(10);
            column.Item().Text("郵政(存簿儲金)薪資存款團體戶存款單").FontSize(16).Bold().AlignCenter();

            // 表頭信息區塊
            column.Item().PaddingVertical(10);
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn(1);
                    columns.RelativeColumn(1);
                    columns.RelativeColumn(1);
                });

                // 第一行
                table.Cell().Element(CellStyle).Text("受託局名\n及局號戳");
                table.Cell().Element(CellStyle).Column(col =>
                {
                    col.Item().Text("劃撥儲金帳號: ");
                    col.Item().Text("存款日期:  年  月  日");
                });
                table.Cell().Element(CellStyle).Column(col =>
                {
                    col.Item().Text("030 連線");
                    col.Item().Text("033 非連線");
                    col.Item().PaddingTop(5);
                    col.Item().Text("立帳資料數").FontSize(9);
                    col.Item().Text($"{list.Count}人次").FontSize(9);
                    col.Item().Text("遞送局請蓋").FontSize(9);
                });
            });

            column.Item().PaddingVertical(5);

            const int detailRecordsPerPage = 20; // 按照Fun.cs的標準，每頁20筆
            var detailTotalPages = Math.Max(1, (int)Math.Ceiling((double)list.Count / detailRecordsPerPage));

            for (int detailPageIndex = 0; detailPageIndex < detailTotalPages; detailPageIndex++)
            {
                if (detailPageIndex > 0)
                {
                    column.Item().PageBreak();
                    column.Item().PaddingBottom(10);
                    // 在右上角添加條碼（後續頁面）
                    column.Item().Row(row =>
                    {
                        row.RelativeItem(3).Text(""); // 左側空白
                        row.RelativeItem(1).AlignRight().Text($"{filename}-{detailPageIndex + 1}").FontFamily("Courier New").FontSize(8); // 右側條碼文字
                    });
                    column.Item().PaddingBottom(10);
                    column.Item().Text("郵政(存簿儲金)薪資存款團體戶存款單").FontSize(16).Bold().AlignCenter();
                }

                var detailStartIndex = detailPageIndex * detailRecordsPerPage;
                var detailEndIndex = Math.Min(detailStartIndex + detailRecordsPerPage, list.Count);
                var detailPageData = list.Skip(detailStartIndex).Take(detailEndIndex - detailStartIndex).ToList();

                // 按照Fun.cs的35欄位結構建立表格
                GenerateDetailTable(column, detailPageData, list.Sum(x => (int)(x.RemitPrice ?? 0)), list.Count, detailPageIndex == detailTotalPages - 1);
            }
        }

        private void GenerateDetailTable(ColumnDescriptor column, List<RemitedList> pageData, int totalCost, int totalAmount, bool isLastPage)
        {
            // 按照郵局存款單格式建立表格，完全符合Fun.cs的35欄位標準
            column.Item().Table(table =>
            {
                // 35個欄位的定義，完全對應Fun.cs中的float[] { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 }
                table.ColumnsDefinition(columns =>
                {
                    // 員工立帳局號 (前6個數字欄位，每個寬度為1)
                    for (int i = 0; i < 6; i++) columns.ConstantColumn(10);
                    // 檢號 (寬度為1)
                    columns.ConstantColumn(10);
                    // 存簿帳號 (7個數字欄位，每個寬度為1) - 按照Fun.cs標準
                    for (int i = 0; i < 7; i++) columns.ConstantColumn(10);
                    // 檢號 (寬度為1)
                    columns.ConstantColumn(10);
                    // 戶名 (寬度為4，較寬欄位)
                    columns.ConstantColumn(40);
                    // 身分證統一編號 (10個字元欄位，每個寬度為1)
                    for (int i = 0; i < 10; i++) columns.ConstantColumn(10);
                    // 存款金額 (8個數字欄位，每個寬度為1)
                    for (int i = 0; i < 8; i++) columns.ConstantColumn(10);
                });

                // 第一行表頭 - 完全按照Fun.cs的標準
                table.Cell().ColumnSpan(6).RowSpan(2).Element(HeaderCellStyle).AlignCenter().AlignMiddle().Text("員工立帳局號");
                table.Cell().RowSpan(2).Element(HeaderCellStyle).AlignCenter().AlignMiddle().Text("檢號");
                table.Cell().ColumnSpan(7).RowSpan(2).Element(HeaderCellStyle).AlignCenter().AlignMiddle().Text("存簿帳號");
                table.Cell().RowSpan(2).Element(HeaderCellStyle).AlignCenter().AlignMiddle().Text("檢號");
                table.Cell().RowSpan(2).Element(HeaderCellStyle).AlignCenter().AlignMiddle().Text("戶名");
                table.Cell().ColumnSpan(10).RowSpan(2).Element(HeaderCellStyle).AlignCenter().AlignMiddle().Text("身分證統一編號");
                table.Cell().ColumnSpan(8).Element(HeaderCellStyle).AlignCenter().Text("存款金額");

                // 第二行表頭 - 金額位數標題（只有金額部分需要第二行）
                table.Cell().Element(HeaderCellStyle).AlignCenter().Text("千萬");
                table.Cell().Element(HeaderCellStyle).AlignCenter().Text("百萬");
                table.Cell().Element(HeaderCellStyle).AlignCenter().Text("十萬");
                table.Cell().Element(HeaderCellStyle).AlignCenter().Text("萬");
                table.Cell().Element(HeaderCellStyle).AlignCenter().Text("千");
                table.Cell().Element(HeaderCellStyle).AlignCenter().Text("百");
                table.Cell().Element(HeaderCellStyle).AlignCenter().Text("十");
                table.Cell().Element(HeaderCellStyle).AlignCenter().Text("元");

                // 資料行 - 完全按照Fun.cs的標準格式處理
                foreach (var item in pageData)
                {
                    var account = (item.CollecAcc ?? "").Replace("-", "").Trim();
                    var collectId = (item.CollectId ?? "").Trim().ToUpper();
                    var price = (int)(item.RemitPrice ?? 0);

                    // 驗證帳號長度（郵局帳號應為14碼）
                    if (account.Length != 14)
                    {
                        // 如果長度不足14碼，補0到14碼
                        account = account.PadLeft(14, '0');
                        if (account.Length > 14)
                        {
                            account = account.Substring(account.Length - 14); // 取後14碼
                        }
                    }

                    // 員工立帳局號 (前6碼，每個數字獨立欄位) - 對應Fun.cs的cr[0]~cr[5]
                    for (int i = 0; i < 6; i++)
                    {
                        table.Cell().Element(BorderCellStyle).AlignCenter().Text(account[i].ToString());
                    }
                    // 檢號 - 對應Fun.cs的"-"
                    table.Cell().Element(BorderCellStyle).AlignCenter().Text("-");

                    // 存簿帳號 (第7-13碼，每個數字獨立欄位) - 對應Fun.cs的cr[6]~cr[12]
                    for (int i = 6; i < 13; i++)
                    {
                        table.Cell().Element(BorderCellStyle).AlignCenter().Text(account[i].ToString());
                    }
                    // 檢號 - 對應Fun.cs的"-"
                    table.Cell().Element(BorderCellStyle).AlignCenter().Text("-");

                    // 第14碼 - 對應Fun.cs的cr[13]
                    table.Cell().Element(BorderCellStyle).AlignCenter().Text(account[13].ToString());

                    // 戶名 - 對應Fun.cs的CollecName，置中對齊
                    table.Cell().Element(BorderCellStyle).AlignCenter().Text(item.CollecName ?? "");

                    // 身分證統一編號處理 - 完全按照Fun.cs的邏輯
                    char[] idChars;
                    if (collectId.Length == 8)
                    {
                        idChars = collectId.PadLeft(10, ' ').ToCharArray();
                    }
                    else
                    {
                        idChars = collectId.ToCharArray();
                        if (idChars.Length < 10)
                        {
                            var temp = new char[10];
                            for (int i = 0; i < 10; i++)
                            {
                                temp[i] = i < idChars.Length ? idChars[i] : ' ';
                            }
                            idChars = temp;
                        }
                        else if (idChars.Length > 10)
                        {
                            var temp = new char[10];
                            Array.Copy(idChars, 0, temp, 0, 10);
                            idChars = temp;
                        }
                    }

                    // 身分證號每個字元獨立欄位 - 對應Fun.cs的cr[0]~cr[9]
                    for (int i = 0; i < 10; i++)
                    {
                        table.Cell().Element(BorderCellStyle).AlignCenter().Text(idChars[i].ToString());
                    }

                    // 存款金額 (8位數，每個數字獨立欄位) - 對應Fun.cs的cr[0]~cr[7]
                    var priceChars = price.ToString().PadLeft(8, '0').ToCharArray();
                    for (int i = 0; i < 8; i++)
                    {
                        table.Cell().Element(BorderCellStyle).AlignCenter().Text(priceChars[i].ToString());
                    }
                }

                // 補足空行到20行 - 按照Fun.cs的標準，每頁固定20行資料
                var emptyRows = Math.Max(0, 20 - pageData.Count);
                for (int i = 0; i < emptyRows; i++)
                {
                    for (int j = 0; j < 35; j++)
                    {
                        table.Cell().Element(BorderCellStyle).Text(" ");
                    }
                }

                // 如果是最後一頁，添加總計行 - 完全按照Fun.cs的genRow4n標準
                if (isLastPage)
                {
                    // 小計行 - 對應Fun.cs的"小計X次"
                    table.Cell().ColumnSpan(27).Element(BorderCellStyle).AlignRight().Text($"小計{totalAmount}次");

                    // 總金額的每位數字 - 對應Fun.cs的totalCost.ToString().PadLeft(8, '0')
                    var totalCostChars = totalCost.ToString().PadLeft(8, '0').ToCharArray();
                    for (int i = 0; i < 8; i++)
                    {
                        table.Cell().Element(BorderCellStyle).AlignCenter().Text(totalCostChars[i].ToString());
                    }
                }
            });
        }

        private byte[] GenerateSimplePostalReport(List<RemitedList> list, int conSno)
        {
            var totalAmount = list.Count;
            var totalCost = list.Sum(x => (decimal)(x.RemitPrice ?? 0));
            var searchId = conSno.ToString();
            var filename = $"CR{searchId.PadLeft(6, '0')}_POST";
            var memo = list.FirstOrDefault()?.RemitMemo ?? "";

            return Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);
                    page.DefaultTextStyle(x => x.FontFamily("Microsoft YaHei").FontSize(12));

                    page.Content().Column(column =>
                    {
                        column.Item().Text("委託郵局代存員工薪資總表(408)").FontSize(18).Bold().AlignCenter();
                        column.Item().PaddingVertical(20);

                        column.Item().Text($"檔名: {filename}");
                        column.Item().Text($"撥存總額: {totalCost:N0}");
                        column.Item().Text($"總筆數: {totalAmount}");
                        column.Item().Text($"備註: {memo}");

                        column.Item().PaddingVertical(20);

                        // 簡化的明細列表
                        column.Item().Table(table =>
                        {
                            table.ColumnsDefinition(columns =>
                            {
                                columns.ConstantColumn(50);   // 序號
                                columns.RelativeColumn(2);   // 戶名
                                columns.ConstantColumn(100); // 帳號
                                columns.ConstantColumn(80);  // 金額
                            });

                            table.Cell().Element(HeaderCellStyle).Text("序號");
                            table.Cell().Element(HeaderCellStyle).Text("戶名");
                            table.Cell().Element(HeaderCellStyle).Text("帳號");
                            table.Cell().Element(HeaderCellStyle).Text("金額");

                            for (int i = 0; i < list.Count; i++)
                            {
                                var item = list[i];
                                table.Cell().Element(BorderCellStyle).Text((i + 1).ToString());
                                table.Cell().Element(BorderCellStyle).Text(item.CollecName ?? "");
                                table.Cell().Element(BorderCellStyle).Text(item.CollecAcc ?? "");
                                table.Cell().Element(BorderCellStyle).AlignRight().Text($"{(item.RemitPrice ?? 0):N0}");
                            }
                        });
                    });

                    page.Footer().AlignCenter().Text(text =>
                    {
                        text.Span($"{filename} - ");
                        text.CurrentPageNumber();
                        text.Span(" / ");
                        text.TotalPages();
                    });
                });
            }).GeneratePdf();
        }

        private static IContainer BorderCellStyle(IContainer container)
        {
            return container
                .Border(0.3f)
                .BorderColor(Colors.Black)
                .PaddingVertical(1)
                .PaddingHorizontal(1)
                .AlignCenter()
                .AlignMiddle();
        }

        private static IContainer HeaderCellStyle(IContainer container)
        {
            return container
                .Border(0.3f)
                .BorderColor(Colors.Black)
                .Background(Colors.White)
                .PaddingVertical(3)
                .PaddingHorizontal(2)
                .AlignCenter()
                .AlignMiddle();
        }

        private static IContainer CellStyle(IContainer container)
        {
            return container
                .PaddingVertical(3)
                .PaddingHorizontal(5)
                .AlignLeft()
                .AlignMiddle();
        }

        private byte[] GenerateTWBankReportAdvanced(List<RemitedList> data, int conSno)
        {
            var totalAmount = data.Count;
            var totalCost = data.Sum(x => (decimal)(x.RemitPrice ?? 0));
            var totalFee = data.Count(x => x.IfFee == "是" || x.IfFee == "1") * 30;
            var conOrga = "相關單位";
            var searchId = conSno.ToString();
            var filename = $"CR{searchId.PadLeft(6, '0')}_TWBANK";
            var batchNum = GenerateBatchNumber();
            var conMemo = data.FirstOrDefault()?.RemitMemo ?? "113全國學生音樂比賽獎金";

            const int recordsPerPage = 18;
            var totalPages = Math.Max(1, (int)Math.Ceiling((double)data.Count / recordsPerPage));

            return Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(50, Unit.Point);
                    page.MarginBottom(80, Unit.Point);
                    page.DefaultTextStyle(x => x.FontFamily("Microsoft YaHei").FontSize(10));

                    page.Content().Element(container =>
                    {
                        container.Column(column =>
                        {
                            for (int copyIndex = 0; copyIndex < 3; copyIndex++)
                            {
                                for (int pageIndex = 0; pageIndex < totalPages; pageIndex++)
                                {
                                    if (copyIndex > 0 || pageIndex > 0)
                                    {
                                        column.Item().PageBreak();
                                    }

                                    var currentPageNumber = pageIndex + 1;
                                    var startIndex = pageIndex * recordsPerPage;
                                    var endIndex = Math.Min(startIndex + recordsPerPage, data.Count);
                                    var pageData = data.Skip(startIndex).Take(endIndex - startIndex).ToList();

                                    column.Item().Column(col =>
                                    {
                                        col.Item().Row(row =>
                                        {
                                            row.RelativeItem(2).Text("整批匯款資料清單");
                                            row.RelativeItem(1).Text("");
                                            row.RelativeItem(2).Text($"頁次：{currentPageNumber}/{totalPages}");
                                        });

                                        col.Item().Row(row =>
                                        {
                                            row.RelativeItem(2).Text($"匯款人:宜府{conOrga}");
                                            row.RelativeItem(1).Text("");
                                            row.RelativeItem(2).Text($"製表日：{DateTime.Now.Year - 1911}年{DateTime.Now.Month}月{DateTime.Now.Day}日");
                                        });

                                        col.Item().Row(row =>
                                        {
                                            row.RelativeItem(2).Text("匯款行:0040222[台灣銀行宜蘭分行]");
                                            row.RelativeItem(1).Text("");
                                            row.RelativeItem(2).Text($"用途：{conMemo}");
                                        });

                                        col.Item().Row(row =>
                                        {
                                            row.RelativeItem(2).Text("匯款日: 年 月 日");
                                            row.RelativeItem(1).Text("");
                                            row.RelativeItem(2).Text($"批號：{batchNum}");
                                        });

                                        col.Item().Row(row =>
                                        {
                                            row.RelativeItem(2).Text("");
                                            row.RelativeItem(1).Text("");
                                            row.RelativeItem(2).Text($"檔名：{filename}");
                                        });

                                        col.Item().PaddingVertical(10);

                                        col.Item().Row(row =>
                                        {
                                            row.ConstantItem(35).Text("序號").AlignCenter();
                                            row.ConstantItem(55).Text("解款行").AlignCenter();
                                            row.ConstantItem(100).Text("帳號").AlignCenter();
                                            row.ConstantItem(55).Text("匯款金額").AlignCenter();
                                            row.ConstantItem(35).Text("手續費").AlignCenter();
                                            row.ConstantItem(60).Text("合計\n(帳款金額)").AlignCenter();
                                            row.RelativeItem(3).Text("收款人姓名\n備註").AlignCenter();
                                        });

                                        col.Item().Text("-------------------------------------------------------------------------------------");

                                        for (int i = 0; i < pageData.Count; i++)
                                        {
                                            var item = pageData[i];
                                            var currentPrice = (decimal)(item.RemitPrice ?? 0);
                                            var fee = (item.IfFee == "是" || item.IfFee == "1") ? 30 : 0;
                                            var netAmount = currentPrice - fee;
                                            var memo = item.RemitMemo ?? "";
                                            if (memo.Length > 35)
                                                memo = memo.Substring(0, 35) + "..";

                                            col.Item().Row(row =>
                                            {
                                                row.ConstantItem(35).Text((startIndex + i + 1).ToString()).AlignCenter();
                                                row.ConstantItem(55).Text(item.CollectNo ?? "").AlignCenter();
                                                row.ConstantItem(100).Text((item.CollecAcc ?? "").Replace("-", ""));
                                                row.ConstantItem(55).Text($"{netAmount:N0}").AlignRight();
                                                row.ConstantItem(35).Text(fee.ToString()).AlignRight();
                                                row.ConstantItem(60).Text($"{currentPrice:N0}").AlignRight();
                                                row.RelativeItem(3).Text($"({item.CollecName}){memo}");
                                            });
                                        }

                                        if (pageIndex == totalPages - 1)
                                        {
                                            col.Item().PaddingVertical(5);

                                            col.Item().Row(row =>
                                            {
                                                row.RelativeItem(3).Text($"資料總筆數:{totalAmount}       資料總金額:").AlignRight();
                                                row.ConstantItem(55).Text($"{totalCost - totalFee:N0}").AlignRight();
                                                row.ConstantItem(35).Text($"{totalFee}").AlignRight();
                                                row.ConstantItem(60).Text($"{totalCost:N0}").AlignRight();
                                                row.RelativeItem(1).Text("");
                                            });

                                            col.Item().PaddingVertical(5);

                                            col.Item().Row(row =>
                                            {
                                                row.RelativeItem(1).Text("經辦:");
                                                row.RelativeItem(1).Text("單位主管:");
                                                row.RelativeItem(1).Text("製表人:");
                                                row.RelativeItem(1).Text("電話:");
                                            });

                                            col.Item().PaddingVertical(3);

                                            col.Item().Row(row =>
                                            {
                                                row.RelativeItem(2).Text("身分證字號:");
                                                row.RelativeItem(2).Text("電子檔傳送日期:");
                                            });
                                        }
                                    });
                                }
                            }
                        });
                    });

                    page.Foreground().PaddingBottom(20).PaddingRight(20).AlignBottom().AlignRight().Column(footerCol =>
                    {
                        var qrText = filename;
                        var barcodeBytes = _barcodeService.GenerateCode128Barcode(qrText);
                        if (barcodeBytes.Length > 0)
                        {
                            footerCol.Item().Width(200).Image(barcodeBytes).FitWidth();
                        }
                        else
                        {
                            var barcodeAscii = _barcodeService.GenerateBarcodeAscii(qrText);
                            footerCol.Item().Width(200).Text(barcodeAscii).FontFamily("Courier New").FontSize(6);
                            footerCol.Item().Width(200).PaddingTop(2).Text(qrText).FontSize(8).AlignCenter();
                        }
                    });
                });
            }).GeneratePdf();
        }

        private string GenerateBatchNumber()
        {
            var today = DateTime.Now;
            var dateStr = $"{today.Year - 1911:D2}{today.Month:D2}{today.Day:D2}";
            return $"{dateStr}6101";
        }

        public async Task<int?> CopyByConSnoAsync(int conSno)
        {
            var items = await _context.RemitedList.Where(r => r.ConSno == conSno).ToListAsync();
            if (!items.Any()) return null;
            var newConSno = await _context.RemitedList.MaxAsync(r => r.ConSno) ?? 0;
            newConSno++;
            var newItems = items.Select(r => new RemitedList
            {
                CollectNo = r.CollectNo,
                CollecAcc = r.CollecAcc,
                CollecName = r.CollecName,
                CollectId = r.CollectId,
                RemitPrice = r.RemitPrice,
                RemitMemo = r.RemitMemo,
                ConSno = newConSno,
                ConPer = r.ConPer,
                ConUnit = r.ConUnit,
                ConDate = DateTime.Now,
                Kindno = r.Kindno,
                CashDate = null,
                BatchNum = r.BatchNum,
                IfFee = r.IfFee
            }).ToList();
            await _context.RemitedList.AddRangeAsync(newItems);
            await _context.SaveChangesAsync();
            return newConSno;
        }
    }
}
