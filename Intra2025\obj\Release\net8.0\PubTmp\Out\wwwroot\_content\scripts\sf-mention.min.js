/*!*  filename: sf-mention.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[39],{"./bundles/sf-mention.js":function(t,e,i){"use strict";i.r(e);i("./modules/sf-mention.js")},"./modules/sf-mention.js":function(t,e){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Mention=function(){"use strict";var t="HidePopupAsync",e="ShowPopupAsync",i="OnSearchAsync",n=function(){function n(t,e,i,n,s){this.activeIndex=null,this.collision=[],window.sfBlazor=window.sfBlazor,this.dataId=t;var o=document.querySelector(e);this.inputElement=i?o.firstElementChild:this.checkAndUpdateInternalComponent(o),i&&(o.insertAdjacentElement("beforebegin",o.firstElementChild),sf.base.detach(o)),this.isContentEditable(this.inputElement)&&(this.inputElement.setAttribute("contenteditable","true"),sf.base.addClass([this.inputElement],["e-mention"])),this.inputElement.setAttribute("role","textbox"),this.options=s,window.sfBlazor.setCompInstance(this),this.dotNetRef=n}return n.prototype.initialize=function(t){this.keyConfigure={tab:"tab",enter:"13",escape:"27",end:"35",home:"36",down:"40",up:"38",pageUp:"33",pageDown:"34",open:"alt+40",close:"shift+tab",hide:"alt+38",space:"32"},sf.base.EventHandler.add(t,"keyup",this.onKeyUp,this),sf.base.Browser.isDevice||new sf.base.KeyboardEvents(t,{keyAction:this.keyActionHandler.bind(this),keyConfigs:this.keyConfigure,eventName:"keydown"})},n.prototype.checkAndUpdateInternalComponent=function(t){return t&&t.classList.contains("e-richtexteditor")?t.querySelector(".e-content"):t},n.prototype.keyActionHandler=function(t){var e={Action:t.action,Key:t.key,Events:t,Type:t.type};this.isDisposed||this.dotNetRef.invokeMethodAsync("KeyActionHandlerAsync",e),("tab"!==t.action&&"up"!==t.action&&"down"!==t.action&&"close"!==t.action&&"space"!==t.action&&"enter"!==t.action&&"open"!==t.action&&("SfMention"===this.options.moduleName||"home"!==t.action&&"end"!==t.action)||"enter"===t.action&&!sf.base.isNullOrUndefined(this.isPopupOpen())||"tab"===t.action&&!sf.base.isNullOrUndefined(this.isPopupOpen())||("up"===t.action||"down"===t.action)&&this.isPopupOpen())&&t.preventDefault()},n.prototype.onPropertyChanged=function(t){for(var e=0,i=Object.keys(t);e<i.length;e++){var n=i[e];switch(n){case"MinLength":this.options.minLength=t[n];break;case"SuffixText":this.options.suffixText=t[n];break;case"AllowSpaces":this.options.allowSpaces=t[n];break;case"MentionChar":this.options.mentionChar=t[n];break;case"ShowMentionChar":this.options.showMentionChar=t[n]}}},n.prototype.getItems=function(){return this.list?this.list.querySelectorAll(".e-list-item"):[]},n.prototype.onKeyUp=function(n){var s,o={Key:n.key,Code:n.code,Location:n.location,CtrlKey:n.ctrlKey,ShiftKey:n.shiftKey,AltKey:n.altKey,MetaKey:n.metaKey,Repeat:n.repeat,Type:n.type};this.isContentEditable(this.inputElement)&&(this.range=this.getCurrentRange(),s=this.range.startContainer.textContent.split(""));var r=this.getTextRange(),a=this.getLastLetter(r),l=new RegExp("\\".concat(this.options.mentionChar),"g"),p=new RegExp("[a-zA-Z]","g");"Shift"!==n.key&&37!==n.keyCode&&39!==n.keyCode&&(!r||!a||"Enter"===n.code||27===n.keyCode||a.match(l)&&a.match(l).length>1||this.isContentEditable(this.inputElement)&&this.range.startContainer&&this.range.startContainer.previousElementSibling&&"BR"!==this.range.startContainer.previousElementSibling.nodeName&&this.range.startContainer.textContent.split("").length>0&&(1===s.length||-1===s[s.length-2].indexOf("")||1===this.range.startContainer.nodeType)?this.options.allowSpaces&&this.isPopupOpen()&&r&&""!==r.trim()&&p.test(r)&&-1!==r.indexOf(this.options.mentionChar)&&!this.isMatchedText()&&r.length>1&&" "!==r.replace(/\u00A0/g," ").charAt(r.length-2)&&this.list&&this.list.querySelectorAll("ul").length>0?(this.queryString=r.substring(r.lastIndexOf(this.options.mentionChar)+1).replace(" "," "),this.dotNetRef.invokeMethodAsync(i,o,this.queryString)):!this.isPopupOpen()||this.options.allowSpaces&&a?"Backspace"!==n.key&&"Delete"!==n.key||this.range.startOffset===this.range.endOffset||this.range.deleteContents():(this.dotNetRef.invokeMethodAsync(t),this.lineBreak=!0):(this.queryString=a.replace(this.options.mentionChar,""),this.options.mentionChar.charCodeAt(0)===a.charCodeAt(0)&&""!==this.queryString&&!this.lineBreak||""===this.queryString&&this.isPopupOpen()&&38!==n.keyCode&&40!==n.keyCode?this.dotNetRef.invokeMethodAsync(i,o,this.queryString):0!==a.indexOf(this.options.mentionChar)||this.isPopupOpen()||8===n.keyCode?this.options.allowSpaces&&this.isPopupOpen()&&""!==this.queryString&&r&&""!==r.trim()&&r.replace(" "," ").lastIndexOf(" ")<r.length-1&&38!==n.keyCode&&40!==n.keyCode&&8!==n.keyCode&&(this.queryString=r.substring(r.lastIndexOf(this.options.mentionChar)+1).replace(" "," "),this.dotNetRef.invokeMethodAsync(i,o,this.queryString)):(this.dotNetRef.invokeMethodAsync(i,o,this.queryString),this.isPopupOpen()||38===n.keyCode||40===n.keyCode||(this.dotNetRef.invokeMethodAsync(e),this.lineBreak=!1))))},n.prototype.isMatchedText=function(){for(var t=!1,e=0;e<(this.liCollections&&this.liCollections.length);e++)this.getTextRange()&&this.getTextRange().substring(this.getTextRange().lastIndexOf(this.options.mentionChar)+1).replace(" "," ").trim()===this.liCollections[e].getAttribute("data-value").toLowerCase()&&(t=!0);return t},n.prototype.getCurrentRange=function(){return this.range=this.inputElement.ownerDocument.getSelection().getRangeAt(0),this.range},n.prototype.getTextRange=function(){var t;if(this.isContentEditable(this.inputElement)){var e=this.range.startContainer;if(!sf.base.isNullOrUndefined(e)){var i=e.textContent,n=this.range.startOffset;i&&n>=0&&(t=i.substring(0,n))}}else{var s=this.inputElement;if(!sf.base.isNullOrUndefined(s)){var o=s.selectionStart;s.value&&o>=0&&(t=s.value.substring(0,o))}}return t},n.prototype.getLastLetter=function(t){if(sf.base.isNullOrUndefined(t))return"";var e=t.replace(/\u00A0/g," ").split(/\s+/);return e[e.length-1].trim()},n.prototype.renderPopup=function(t,e,i){if(!this.isPopupOpen()){var n;this.isContentEditable(this.inputElement)&&(this.range=this.getCurrentRange()),this.options=e,this.list=t.querySelector(".e-content"),this.list.querySelector(".e-active")&&sf.base.attributes(this.inputElement,{"aria-activedescendant":this.list.querySelector(".e-active").id}),sf.base.attributes(this.inputElement,{"aria-owns":this.dataId+"_options"}),this.liCollections=this.getItems();var s=sf.base.formatUnit(this.options.popupHeight);if(this.wireListEvents(),t){var o=document.getElementById(t.id);o&&sf.base.remove(o)}document.body.appendChild(t),t.style.visibility="hidden",t.style.display="block","auto"!==this.options.popupHeight?(this.list.style.maxHeight=(parseInt(s,10)-2).toString()+"px",t.style.maxHeight=sf.base.formatUnit(this.options.popupHeight)):t.style.height="auto",this.initializePopup(t,0,0),this.checkCollision(t),t.style.visibility="visible";var r=t.parentElement.offsetWidth-t.offsetWidth,a=t.offsetHeight;sf.base.addClass([t],"e-popup-close");if(this.popupObj.show(new sf.base.Animation({name:"FadeIn",duration:100}),this.inputElement),n=this.getCoordinates(this.inputElement,this.getTriggerCharPosition()),this.isCollided?(this.collision.length>0&&this.collision.indexOf("right")>-1&&-1===this.collision.indexOf("bottom")?t.style.cssText="top: ".concat(n.top.toString(),"px;\n left: ").concat(r.toString(),"px;\nposition: absolute;\n display: block;"):this.collision&&this.collision.length>0&&this.collision.indexOf("bottom")>-1&&-1===this.collision.indexOf("right")?(t.style.left=sf.base.formatUnit(n.left),t.style.top=sf.base.formatUnit(n.top-parseInt(a.toString()))):this.collision&&this.collision.length>0&&this.collision.indexOf("bottom")>-1&&this.collision.indexOf("right")>-1?(t.style.left=sf.base.formatUnit(r),t.style.top=sf.base.formatUnit(n.top-parseInt(a.toString()))):t.style.left=sf.base.formatUnit(n.left),this.collision=[],this.isCollided=!1):t.style.cssText="top: ".concat(n.top.toString(),"px;\n left: ").concat(n.left.toString(),"px;\nposition: absolute;\n display: block;"),t.style.width="100%"===this.options.popupWidth||sf.base.isNullOrUndefined(this.options.popupWidth)?"auto":sf.base.formatUnit(this.options.popupWidth),"auto"!==this.options.popupHeight?(this.list.style.maxHeight=(parseInt(s,10)-2).toString()+"px",t.style.maxHeight=sf.base.formatUnit(this.options.popupHeight)):t.style.height="auto",t.style.zIndex=1e3===this.options.zIndex?sf.popups.getZindexPartial(t).toString():this.options.zIndex.toString(),this.isPopupOpen()||this.inputElement.removeAttribute("aria-activedescendant"),this.inputElement.parentElement){var l=this.inputElement.parentElement.closest(".e-richtexteditor");l&&t.firstElementChild&&t.firstElementChild.childElementCount>0&&(t.firstElementChild.setAttribute("aria-owns",l.id),sf.base.addClass([t],"e-rte-elements"))}}},n.prototype.search=function(i,n,s){var o=this,r=this.getTextRange(),a=this.getLastLetter(r);this.options.ignoreCase&&(i===a||i===a.toLowerCase())||!this.options.ignoreCase&&i===a?this.dotNetRef.invokeMethodAsync(e).then((function(){sf.base.isNullOrUndefined(o.popupObj)||(o.popupObj.element.style.left=sf.base.formatUnit(n),o.popupObj.element.style.top=sf.base.formatUnit(s))})):this.isPopupOpen()&&this.dotNetRef.invokeMethodAsync(t)},n.prototype.getCoordinates=function(t,e){var i,n,s,o,r,a,l;if(this.isContentEditable(this.inputElement)){var p=this.getTriggerCharPosition();o=this.range,s=document.createRange(),this.getTextRange()&&-1!==this.getTextRange().lastIndexOf(this.options.mentionChar)?(s.setStart(o.startContainer,p),s.setEnd(o.startContainer,p)):(s.setStart(o.startContainer,o.startOffset),s.setEnd(o.startContainer,o.endOffset)),s.collapse(!1),l=0===s.getBoundingClientRect().top?s.startContainer.getClientRects()[0]:s.getBoundingClientRect()}else i=sf.base.createElement("div",{className:"e-form-mirror-div"}),document.body.appendChild(i),a=getComputedStyle(t),i.style.position="absolute",i.style.visibility="hidden",["direction","boxSizing","width","height","overflowX","overflowY","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","paddingTop","paddingRight","paddingBottom","paddingLeft","fontStyle","fontVariant","fontWeight","fontStretch","fontSize","fontSizeAdjust","lineHeight","fontFamily","textAlign","textTransform","textIndent","textDecoration","letterSpacing","wordSpacing"].forEach((function(t){i.style[t]=a[t]})),i.textContent=t.value.substring(0,e),"INPUT"===this.inputElement.nodeName&&(i.textContent=i.textContent.replace(/\s/g," ")),(n=sf.base.createElement("span")).textContent=t.value.substring(e)||".",i.appendChild(n),l=t.getBoundingClientRect();var h=document.documentElement,d=(window.pageXOffset||h.scrollLeft)-(h.clientLeft||0),c=(window.pageYOffset||h.scrollTop)-(h.clientTop||0),f=0;if(!sf.base.isNullOrUndefined(s)&&0===s.getBoundingClientRect().top)for(var u=0;u<this.range.startContainer.childNodes.length;u++)if(this.range.startContainer.childNodes[u].nodeType!==Node.TEXT_NODE&&""!==this.range.startContainer.childNodes[u].textContent.trim())f+=this.range.startContainer.childNodes[u].getClientRects()[0].width;else if(""!==this.range.startContainer.childNodes[u].textContent){var g=document.createElement("span");g.innerHTML=this.range.startContainer.childNodes[u].nodeValue,document.body.appendChild(g);var m=g.offsetWidth;document.body.removeChild(g),f+=m}return this.isContentEditable(this.inputElement)?r=this.collision&&this.collision.length>0&&this.collision.indexOf("right")>-1&&-1===this.collision.indexOf("bottom")?{top:l.top+c+parseInt(getComputedStyle(this.inputElement).fontSize,10),left:l.left+d+f}:{top:l.top+c+parseInt(getComputedStyle(this.inputElement).fontSize,10)-(this.isCollided?10:0),left:l.left+d+f}:(r={top:l.top+c+n.offsetTop+parseInt(a.borderTopWidth,10)+parseInt(a.fontSize,10)+3-t.scrollTop,left:l.left+d+n.offsetLeft+parseInt(a.borderLeftWidth,10)},document.body.removeChild(i)),r},n.prototype.getTriggerCharPosition=function(){var t,e=this.getTextRange();if(null!=e){t=0;var i=e.lastIndexOf(this.options.mentionChar);i>=t&&(t=i)}return t},n.prototype.isContentEditable=function(t){return t&&"INPUT"!==t.nodeName&&"TEXTAREA"!==t.nodeName},n.prototype.wireListEvents=function(){sf.base.EventHandler.add(this.list,"mouseover",this.onMouseOver,this),sf.base.EventHandler.add(this.list,"mouseout",this.onMouseLeave,this)},n.prototype.unWireListEvents=function(){sf.base.EventHandler.remove(this.list,"mouseover",this.onMouseOver),sf.base.EventHandler.remove(this.list,"mouseout",this.onMouseLeave)},n.prototype.onMouseOver=function(t){var e=sf.base.closest(t.target,".e-list-item");this.setHover(e)},n.prototype.onMouseLeave=function(t){this.removeHover()},n.prototype.checkCollision=function(t){if(!sf.base.Browser.isDevice||sf.base.Browser.isDevice&&"SfMention"!==this.options.moduleName){var e=this.getCoordinates(this.inputElement,this.getTriggerCharPosition());this.collision=sf.popups.isCollide(t,null,e.left,e.top),this.collision.length>0&&(t.style.marginTop=-parseInt(getComputedStyle(t).marginTop,10)+"px",this.isCollided=!0)}},n.prototype.destroy=function(){sf.base.EventHandler.remove(document,"mousedown",this.onDocumentClick)},n.prototype.initializePopup=function(t,e,i){var n=this;this.popupContainer=t,this.popupObj=new sf.popups.Popup(this.popupContainer,{width:this.setWidth(),targetType:"relative",relateTo:this.inputElement,collision:{X:"flip",Y:"flip"},offsetY:e,enableRtl:this.options.enableRtl,offsetX:i,position:{X:"top",Y:"left"},actionOnScroll:"hide",zIndex:this.options.zIndex,close:function(){n.unWireListEvents(),n.popupObj&&n.popupObj.destroy(),!n.isDisposed&&document.body.contains(n.inputElement)&&n.dotNetRef.invokeMethodAsync("ClosePopupAsync").catch((function(){})),n.popupObj=null},open:function(){sf.base.EventHandler.add(document,"mousedown",n.onDocumentClick,n)}})},n.prototype.setValue=function(t,e){var i,n,s,o,r,a=this.inputElement.ownerDocument.getSelection(),l=this.getTriggerCharPosition();if(i="string"==typeof this.options.suffixText?this.options.suffixText:"",null!==t.text&&(n=this.mentionVal(t.text)),this.isContentEditable(this.inputElement)){s=this.getTriggerCharPosition()+this.options.mentionChar.length,this.range&&this.range.startContainer.textContent.trim()!==this.options.mentionChar&&(s=this.range.endOffset),r=this.range,o=document.createRange(),this.getTextRange()&&-1!==this.getTextRange().lastIndexOf(this.options.mentionChar)||this.getTextRange()&&this.getTextRange().trim()===this.options.mentionChar?(o.setStart(r.startContainer,l),o.setEnd(r.startContainer,s)):""!==r.commonAncestorContainer.textContent.trim()&&!sf.base.isNullOrUndefined(r.commonAncestorContainer.textContent.trim())&&this.getTextRange()&&-1!==this.getTextRange().lastIndexOf(this.options.mentionChar)?(o.setStart(r.startContainer,r.startOffset-1),o.setEnd(r.startContainer,r.endOffset-1)):(o.setStart(r.startContainer,r.startOffset),o.setEnd(r.startContainer,r.endOffset)),o.deleteContents();var p=sf.base.createElement("div");p.innerHTML=n;for(var h=document.createDocumentFragment(),d=void 0,c=void 0;d=p.firstChild;)c=h.appendChild(d);o.insertNode(h),c&&((o=o.cloneRange()).setStartAfter(c),o.collapse(!0),a.removeAllRanges(),a.addRange(o)),this.isPopupOpen()&&this.inputElement.focus();var f=new CustomEvent("UpdateEditorValue",{detail:{click:e}});return this.inputElement.dispatchEvent(f),!0}var u=this.inputElement,g=this.getTextRange().substring(l+this.options.mentionChar.length,this.getTextRange().length);return n+=i,s=l+this.options.mentionChar.length,s+=g.length,u.value=u.value.substring(0,l)+n+u.value.substring(s,u.value.length),u.selectionStart=l+n.length,u.selectionEnd=l+n.length,this.isPopupOpen()&&this.inputElement.focus(),!0},n.prototype.mentionVal=function(t){var e=this.options.showMentionChar?this.options.mentionChar:"";return this.options.hasDisplayTemplate&&(t=this.removeComments(document.querySelector("#"+this.dataId+"_displayTemplate"))),this.isContentEditable(this.inputElement)?'<span contenteditable="false" class="e-mention-chip">'+e+t+"</span>".concat("string"==typeof this.options.suffixText?this.options.suffixText:" "):e+t},n.prototype.removeComments=function(t){var e=t.innerHTML;return(e=e.replace(/<!--[\s\S]*?-->/g,"")).trim()},n.prototype.setWidth=function(){var t=sf.base.formatUnit(this.options.popupWidth);t.indexOf("%")>-1&&(t=(this.inputElement.offsetWidth*parseFloat(t)/100).toString()+"px");return t},n.prototype.onDocumentClick=function(e){var i=e.target;!sf.base.isNullOrUndefined(this.popupObj)&&sf.base.closest(i,"#"+this.popupObj.element.id)||!this.isPopupOpen()||this.dotNetRef.invokeMethodAsync(t)},n.prototype.closePopup=function(t,e){if(this.options=e,this.isPopupOpen()&&!t.cancel&&this.popupObj){this.popupObj.hide(new sf.base.Animation({name:"FadeOut",duration:20,delay:0})),this.inputElement.removeAttribute("aria-owns"),this.inputElement.removeAttribute("aria-activedescendant")}},n.prototype.setScrollPosition=function(t){if(sf.base.isNullOrUndefined(t))this.scrollBottom(!0);else switch(t.action){case"pageDown":case"down":case"end":this.scrollBottom();break;default:this.scrollTop()}},n.prototype.scrollBottom=function(t){if(this.list&&this.list.querySelector(".e-active")&&!t){var e=this.list.querySelector(".e-active"),i=this.list.offsetHeight,n=e.offsetTop+e.offsetHeight-this.list.scrollTop,s=this.list.scrollTop+n-i;s=t?s+2*parseInt(getComputedStyle(this.list).paddingTop,10):s+parseInt(getComputedStyle(this.list).paddingTop,10);var o=e.offsetTop+e.offsetHeight-this.list.scrollTop;0===this.activeIndex?this.list.scrollTop=0:(n>i||!(o>0&&this.list.offsetHeight>o))&&(this.list.scrollTop=s)}},n.prototype.scrollTop=function(){if(this.list&&this.list.querySelector(".e-active")){var t=this.list.querySelector(".e-active"),e=t.offsetTop-this.list.scrollTop,i=t.offsetTop+t.offsetHeight-this.list.scrollTop;0===this.activeIndex?this.list.scrollTop=0:e<0?this.list.scrollTop=this.list.scrollTop+e:i>0&&this.list.offsetHeight>i||(this.list.scrollTop=t.offsetTop)}},n.prototype.setHover=function(t){t&&!t.classList.contains("e-hover")&&(this.removeHover(),sf.base.addClass([t],"e-hover"))},n.prototype.removeHover=function(){if(this.list){var t=this.list.querySelectorAll(".e-hover");t&&t.length&&sf.base.removeClass(t,"e-hover")}},n.prototype.isPopupOpen=function(){return this.popupObj&&document.body.contains(this.popupObj.element)},n}();return{initialize:function(t,e,i,s,o){if(document.querySelector(e)){var r=i?document.querySelector(e).firstElementChild:document.querySelector(e);new n(t,e,i,s,o).initialize(r)}},renderPopup:function(t,e,i){var n=window.sfBlazor.getCompInstance(t),s=document.querySelector("#"+t+"_popup");!sf.base.isNullOrUndefined(n)&&s&&n.renderPopup(s,e,i)},search:function(t,e,i,n){var s=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(s)||s.search(e,i,n)},closePopup:function(t,e,i){var n=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(n)||n.closePopup(e,i)},setValue:function(t,e,i){var n=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(n)||n.setValue(e,i)},updateScrollPosition:function(t,e){var i=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(i)||i.setScrollPosition(e)},onPropertyChanged:function(t){var e=window.sfBlazor.getCompInstance(t.dataId);sf.base.isNullOrUndefined(e)||e.onPropertyChanged(t)},destroy:function(t,e,i){var n=window.sfBlazor.getCompInstance(t),s=document.querySelector(i.target),o=document.querySelector("#"+t+"_popup");sf.base.EventHandler.remove(s,"keyup",this.onKeyUp),sf.base.isNullOrUndefined(n)||(o&&o instanceof HTMLElement&&(n.isDisposed=!0,n.closePopup(e,i)),n.destroy())}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfmention');})})();