/*!*  filename: sf-listbox.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[35],{"./bundles/sf-listbox.js":function(e,t,n){"use strict";n.r(t);n("./modules/sf-listbox.js")},"./modules/sf-listbox.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.ListBox=function(){"use strict";var e,t="e-list-item",n=function(){function n(t,n,s,a,i){this.element=t,this.dotnetRef=a,this.allowDragAndDrop=s,e=i,this.element.blazor__instance=this,this.scope=n,sf.base.EventHandler.add(this.element,"keydown",this.keyDownHandler,this),s&&this.initializeDraggable()}return n.prototype.initializeDraggable=function(){var e=this;if(this.allowDragAndDrop){var n=sf.base.select(".e-ul",this.element),s=new sf.lists.Sortable(n,{scope:this.scope,itemClass:t,beforeDragStart:this.triggerBeforeDragStart.bind(this),drag:this.triggerDrag.bind(this),dragStart:this.triggerDragStart.bind(this),beforeDrop:this.dragEnd.bind(this),placeHolder:function(){return sf.base.createElement("span",{className:"e-placeholder"})},helper:function(n){var s=e.element.cloneNode(),a=n.sender.cloneNode(!0);s.appendChild(a);var i=sf.base.select("."+t,e.element);s.style.width=i.offsetWidth+"px",s.style.height=i.offsetHeight+"px";var r=[].slice.call(sf.base.selectAll("."+t+".e-selected",e.element));return r.length&&r.length>1&&r.indexOf(n.sender)>-1&&a.appendChild(sf.base.createElement("span",{className:"e-list-badge",innerHTML:r.length.toString()})),s.style.zIndex=sf.popups.getZindexPartial(e.element).toString(),s}});this.updateDraggable(s,this.allowDragAndDrop)}},n.prototype.triggerBeforeDragStart=function(e){e.cancel=e.target.classList.contains("e-drag-prevent")},n.prototype.triggerDragStart=function(e){e.bindEvents(e.dragElement)},n.prototype.triggerDrag=function(t){var n,s,a,i=0,r=10,l=t.event;e&&(r=t.target.scrollHeight),t.target&&(t.target.classList.contains("e-listbox-container")||t.target.classList.contains("e-list-item")||t.target.classList.contains("e-input-group")||t.target.classList.contains("e-filter-parent"))&&(t.target.classList.contains("e-list-item")||t.target.classList.contains("e-input-group")||t.target.classList.contains("e-filter-parent")?(a=t.target.closest(".e-listbox-container"),sf.base.isNullOrUndefined(a)&&(a=this.element)):a=t.target,s=(n=a.querySelector(".e-list-parent")).getBoundingClientRect(),a.classList.contains("e-filter-list")&&(i=40),s.y+n.offsetHeight-(l.pageY+36)<1?n.scrollTop=n.scrollTop+r:l.pageY-36-(s.y+i)<1&&(n.scrollTop=n.scrollTop-r)),t.target},n.prototype.dragEnd=function(e){var t,n,s,a=!1,i=!1,r=!1;s=e.target.parentElement.parentElement,"UL"==e.target.tagName&&e.target.className.indexOf("e-list-parent")>-1&&(s=e.target.parentElement),s.blazor__instance||(((s=s.parentElement).className.indexOf("e-list-item")>-1||s.className.indexOf("e-filter-list")>-1&&s.closest(".e-listboxtool-wrapper"))&&(s=s.parentElement),s.className.indexOf("e-list-parent")>-1?s=s.parentElement.parentElement:s.className.indexOf("e-list-wrap")>-1&&(s=s.parentElement)),e.cancel=!0,s.blazor__instance?(t=s.blazor__instance.scope,n=s.blazor__instance.dotnetRef,i=this.scope===t,this.element===s?(r=!0,i=!1):t||(a=!0)):a=!0;var l=e.helper.getBoundingClientRect();this.dotnetRef.invokeMethodAsync("DragEndAsync",a,e.droppedElement.getAttribute("data-value"),r,i,e.previousIndex,e.currentIndex,n,l.x,l.y)},n.prototype.keyDownHandler=function(e){var n=e.target;if(38===e.keyCode||40===e.keyCode)if(e.preventDefault(),n.classList.contains("e-listbox-container")){var s=sf.base.select("."+t,this.element);s&&s.focus()}else{var a=[].slice.call(sf.base.selectAll("."+t,this.element)),i=a.indexOf(n);if(i<0)return;if((i=38===e.keyCode?i-1:i+1)<0||i>a.length-1)return;a[i].focus()}else 65===e.keyCode&&e.ctrlKey&&!e.target.classList.contains("e-input-filter")&&e.preventDefault()},n.prototype.updateDraggable=function(e,n){if(e){var s=sf.base.getComponent(e.element,sf.base.Draggable);s.abort=n?"":"."+t}},n.prototype.destroyDraggable=function(){var e=sf.base.getComponent(sf.base.select(".e-ul",this.element),"sortable");sf.base.isNullOrUndefined(e)||(this.updateDraggable(e,this.allowDragAndDrop),e.destroy())},n.prototype.destroy=function(){this.destroyDraggable(),sf.base.EventHandler.remove(this.element,"keydown",this.keyDownHandler)},n.prototype.onPropertyChanged=function(e){this.allowDragAndDrop=e,e?this.initializeDraggable():this.destroyDraggable()},n.prototype.getScopedElement=function(e){return sf.base.select("#"+e).blazor__instance.dotnetRef},n}();return{initialize:function(e,t,s,a,i){if(!sf.base.isNullOrUndefined(e)){new n(e,t,s,a,i);var r,l,o=!1;e.addEventListener("touchstart",(function(){o=!1,l=null,r=setTimeout((function(){o=!0}),500)}),{passive:!0}),e.addEventListener("touchmove",(function(e){l=e.target}),{passive:!0}),e.addEventListener("touchend",(function(e){clearTimeout(r),sf.base.isNullOrUndefined(l)&&(o&&e.preventDefault(),a.invokeMethodAsync("TouchHoldAsync",o,e.target.getAttribute("data-value")))}))}},destroy:function(e){sf.base.isNullOrUndefined(e)||sf.base.isNullOrUndefined(e.blazor__instance)||e.blazor__instance.destroy()},onPropertyChanged:function(e,t){sf.base.isNullOrUndefined(e)||sf.base.isNullOrUndefined(e.blazor__instance)||e.blazor__instance.onPropertyChanged(t)},getScopedListBox:function(e,t){return e.blazor__instance.getScopedElement(t)}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sflistbox');})})();