/*!*  filename: sf-carousel.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[12],{"./bundles/sf-carousel.js":function(e,t,i){"use strict";i.r(t);i("./modules/sf-carousel.js")},"./modules/sf-carousel.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Carousel=function(){"use strict";var e="e-carousel-item",t=function(){function t(e,t,i,s){if(window.sfBlazor=window.sfBlazor,this.element=t,this.dotnetRef=s,this.swipeMode=i,this.dataId=e,window.sfBlazor.setCompInstance(this),this.itemsContainer=this.element.querySelector(".e-carousel-items"),"-4"!=this.swipeMode){new sf.base.Touch(this.itemsContainer,{scroll:this.scrollHandler.bind(this),swipe:this.swipeHandler.bind(this),swipeSettings:{swipeThresholdDistance:1}});sf.base.EventHandler.add(this.element,"transitionend",this.onTransitionEnd,this)}}return t.prototype.scrollHandler=function(e){if(!this.element.classList.contains("e-carousel-custom-animation")){if(this.timeStampStart||(this.timeStampStart=Date.now()),this.element.classList.contains("e-translate")&&this.onTransitionEnd(),"Touch"===this.swipeMode){if("mousemove"===e.originalEvent.type)return}else if("Mouse"===this.swipeMode&&"touchmove"===e.originalEvent.type)return;if("Left"===e.scrollDirection||"Right"===e.scrollDirection){var t=this.element.querySelector(".e-carousel-items");if(t&&t.scrollWidth>t.clientWidth)return;this.isScrollTriggered=!0,e.originalEvent.preventDefault(),e.originalEvent.stopPropagation()}"Left"===e.scrollDirection?this.itemsContainer.style.transform="translatex("+(this.getTranslateX(this.itemsContainer)+-e.distanceX)+"px)":"Right"===e.scrollDirection&&(this.itemsContainer.style.transform="translatex("+(this.getTranslateX(this.itemsContainer)+e.distanceX)+"px)")}},t.prototype.swipeHandler=function(e){var t=this.itemsContainer.children.length;if("Left"===e.swipeDirection||"Right"===e.swipeDirection){var i=Date.now()-this.timeStampStart,s=e.distanceX*(sf.base.Browser.isDevice?6:1.66);if(s>i||e.distanceX>this.element.offsetWidth/2){e.distanceX>this.element.offsetWidth/2&&this.applySwipeAnimation(e,s,i);var n=getComputedStyle(this.itemsContainer).getPropertyValue("--carousel-items-current");(this.element.classList.contains("e-loop")||"Right"!==e.swipeDirection||"0"!==n)&&(this.element.classList.contains("e-loop")||"Left"!==e.swipeDirection||n!==(t-1).toString())?(this.element.classList.contains("e-rtl")&&("Right"===e.swipeDirection?e.swipeDirection="Left":e.swipeDirection="Right"),this.confirmSwipe(e.swipeDirection)):this.cancelSwipe()}else this.cancelSwipe()}else this.cancelSwipe();this.timeStampStart=null},t.prototype.inverseDirection=function(e){switch(e){case"Left":e="Next";break;case"Right":e="Previous";break;case"Next":e="Left";break;case"Previous":e="Right"}return e},t.prototype.applySwipeAnimation=function(e,t,i){this.element.classList.contains("e-carousel-slide-animation")?sf.base.isNullOrUndefined(e)||sf.base.isNullOrUndefined(t)||sf.base.isNullOrUndefined(i)?this.itemsContainer.classList.add("e-slide"):this.itemsContainer.style.transitionDuration=(sf.base.Browser.isDevice?e.distanceX:t)/i/10+"s":this.element.classList.contains("e-carousel-fade-animation")&&this.itemsContainer.classList.add("e-fade-in-out")},t.prototype.confirmSwipe=function(e,t){sf.base.addClass([this.element],"e-translate"),sf.base.isNullOrUndefined(t)?(this.changeActiveClass(e),this.changeSlide(e)):this.changeActiveClass(e,t)},t.prototype.cancelSwipe=function(){this.element.classList.add("e-translate"),this.onTransitionEnd()},t.prototype.changeActiveClass=function(t,i){var s=this.element.querySelector("."+e+".e-active:not(.e-clone)").previousElementSibling,n=this.element.querySelector("."+e+".e-active:not(.e-clone)").nextElementSibling;if(this.element.querySelectorAll("."+e+".e-active").forEach((function(e){e.classList.remove("e-active")})),!sf.base.isNullOrUndefined(i)){var o=this.element.querySelectorAll("."+e+":not(.e-clone)")[i];return o.classList.add("e-active"),void("Left"===t?o.classList.add("e-next"):(t="Right")&&o.classList.add("e-prev"))}"Left"===t?n.classList.contains("e-clone")?this.element.querySelector("."+e+":not(.e-clone)").classList.add("e-active"):n.classList.add("e-active","e-next"):s.classList.contains("e-clone")?this.element.querySelectorAll("."+e)[this.itemsContainer.children.length-2].classList.add("e-active"):s.classList.add("e-active")},t.prototype.getTranslateX=function(e){var t=window.getComputedStyle(e);return new WebKitCSSMatrix(t.webkitTransform).m41},t.prototype.onTransitionEnd=function(){this.itemsContainer.style.transitionDuration="",this.itemsContainer.style.transitionTimingFunction="",this.itemsContainer.style.transform="",this.element.classList.remove("e-translate"),sf.base.removeClass([this.itemsContainer],["e-fade-in-out","e-slide"]),this.timeStampStart=null,this.isScrollTriggered=!1;var t=this.element.querySelector("."+e+".e-prev, ."+e+".e-next");sf.base.isNullOrUndefined(t)||(t.classList.remove("e-prev"),t.classList.remove("e-next"))},t.prototype.changeSlide=function(e,t){e=this.inverseDirection(e);try{this.dotnetRef.invokeMethodAsync("ChangeSlide",e)}catch(e){console.log(e)}},t}();return{initialize:function(e,i,s,n){new t(e,i,s,n)},swipeHandler:function(e,t,i){var s=window.sfBlazor.getCompInstance(e);s&&s.element&&(s.applySwipeAnimation(),t=s.inverseDirection(t),s.confirmSwipe(t,i))}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfcarousel');})})();