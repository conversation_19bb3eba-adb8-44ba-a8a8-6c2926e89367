/*!*  filename: sf-progressbar.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[47],{"./bundles/sf-progressbar.js":function(t,e,a){"use strict";a.r(e);a("./modules/sf-progressbar.js")},"./modules/sf-progressbar.js":function(t,e){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Progressbar=function(){"use strict";var t=function(){function t(t,e,a){window.sfBlazor=window.sfBlazor,this.resizeTo=0,this.element=e,this.dotNetRef=a,this.dataId=t,window.sfBlazor.setCompInstance(this)}return t.prototype.wireEvents=function(){window.addEventListener(sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize",this.resize.bind(this))},t.prototype.unWireEvents=function(){window.removeEventListener(sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize",this.resize.bind(this)),this.element=null,this.dotNetRef=null},t.prototype.resize=function(){var t=this;(this.dotNetRef&&!this.cancelResize||this.lineardata&&this.lineardata.isActive||this.circularData&&this.circularData.isActive)&&(this.resizeTo&&clearTimeout(this.resizeTo),sf.base.isNullOrUndefined(this.dotNetRef)||(this.resizeTo=window.setTimeout((function(){t.dotNetRef.invokeMethodAsync("TriggerResize")}),500)))},t.prototype.getPathArc=function(t,e,a,i,n,r,l){var s=this.degreeToLocation(t,e,a,i),o=this.degreeToLocation(t,e,a,n),d="0",c=r?"0":"1";return d=r?(i>=n?i:i+360)-n<=180?"0":"1":(n>=i?n:n+360)-i<=180?"0":"1",l?"M "+t+" "+e+" L "+s.x+" "+s.y+" A "+a+" "+a+"  0  "+d+" "+c+" "+o.x+" "+o.y+" Z":"M"+s.x+" "+s.y+"A"+a+" "+a+" 0 "+d+" "+c+" "+o.x+" "+o.y},t.prototype.degreeToLocation=function(t,e,a,i){var n=(i-90)*(Math.PI/180);return{x:t+a*Math.cos(n),y:e+a*Math.sin(n)}},t.prototype.effect=function(t,e,a,i,n){return(n?a:-a)*Math.cos(t/i*(Math.PI/2))+(e+(n?-a:a))},t.prototype.activeAnimate=function(t,e,a,i){var n=1-Math.pow(1-t,3);return e+(i?-n*a:n*a)},t.prototype.getPathLine=function(t,e){var a=e.enableRtl?"Round"===e.cornerRadius?t+e.rectWidth-.45*e.thickness:t+e.rectWidth:"Round"===e.cornerRadius?t+.45*e.thickness:t,i=e.enableRtl?"Round"===e.cornerRadius&&e.progressWidth?a-e.progressWidth+.9*e.thickness:a-e.progressWidth:"Round"===e.cornerRadius&&e.progressWidth?a+e.progressWidth-.9*e.thickness:a+e.progressWidth;return"M"+a+" "+(e.rectX+e.rectHeight/2)+"L"+i+" "+(e.rectY+e.rectHeight/2)},t.prototype.circularAnimation=function(){var t=this,e=new sf.base.Animation({}),a=document.getElementById(this.element.id+"_clippathcircle"),i=0;a&&this.circularData&&(a.style.visibility="hidden",e.animate(a,{duration:this.circularData.duration,delay:this.circularData.delay,progress:function(e){if(t.cancelResize=!0,e.timeStamp>=e.delay){if(a.style.visibility="visible",t.circularData.isActive){i=t.activeAnimate(e.timeStamp/e.duration,t.circularData.startPos,t.circularData.endPos,t.circularData.enableRtl);var n=document.getElementById(t.element.id+"_CircularActiveProgress");n&&n.setAttribute("opacity",t.effect(e.timeStamp,.5,.5,e.duration,!0).toString())}else i=t.effect(e.timeStamp,t.circularData.startPos,t.circularData.endPos,e.duration,t.circularData.enableRtl);a.setAttribute("d",t.getPathArc(t.circularData.x,t.circularData.y,t.circularData.pathRadius,t.circularData.start,i%360,t.circularData.enableRtl,!0))}},end:function(){t.cancelResize=!1,a.setAttribute("d",t.getPathArc(t.circularData.x,t.circularData.y,t.circularData.pathRadius,t.circularData.start,t.circularData.progressEnd,t.circularData.enableRtl,!0)),t.circularData.isActive&&t.circularAnimation(),t.dotNetRef.invokeMethodAsync("TriggerAnimationComplete")}}))},t.prototype.circularBufferAnimation=function(){var t=this,e=new sf.base.Animation({}),a=document.getElementById(this.element.id+"_clippathBuffercircle"),i=0;a&&this.circularBufferData&&(a.style.visibility="hidden",e.animate(a,{duration:this.circularBufferData.duration,delay:this.circularBufferData.delay,progress:function(e){t.cancelResize=!0,e.timeStamp>=e.delay&&(a.style.visibility="visible",i=t.effect(e.timeStamp,t.circularBufferData.startPos,t.circularBufferData.endPos,e.duration,t.circularBufferData.enableRtl),a.setAttribute("d",t.getPathArc(t.circularBufferData.x,t.circularBufferData.y,t.circularBufferData.pathRadius,t.circularBufferData.start,i%360,t.circularBufferData.enableRtl,!0)))},end:function(){t.cancelResize=!1,a.setAttribute("d",t.getPathArc(t.circularBufferData.x,t.circularBufferData.y,t.circularBufferData.pathRadius,t.circularBufferData.start,t.circularBufferData.progressEnd,t.circularBufferData.enableRtl,!0)),t.dotNetRef.invokeMethodAsync("TriggerAnimationComplete")}}))},t.prototype.circularIndeterminateAnimation=function(t,e){var a=this;void 0===t&&(t=0),void 0===e&&(e=0);var i=document.getElementById(this.element.id+"_clippathcircle"),n=new sf.base.Animation({});i&&this.circularData&&(n.destroy(),n.animate(i,{duration:this.circularData.duration,delay:0,progress:function(){a.circularData&&a.circularData.enable&&a.circularData.isIndeterminate&&(i.style.visibility="visible",t+=a.circularData.enableRtl?-a.circularData.segmentValue:a.circularData.segmentValue,e+=a.circularData.enableRtl?-a.circularData.segmentValue:a.circularData.segmentValue,i.setAttribute("d",a.getPathArc(a.circularData.x,a.circularData.y,a.circularData.pathRadius,t%360,e%360,a.circularData.enableRtl,!a.circularData.enableProgressSegments)))},end:function(){a.circularData&&a.circularData.enable&&a.circularData.isIndeterminate&&a.circularIndeterminateAnimation(t,e)}}))},t.prototype.annotationAnimation=function(){var t,e,a=this,i=new sf.base.Animation({}),n=document.getElementById(this.element.id+"Annotation0").children[0];n&&n.children[0]&&"SPAN"===n.children[0].tagName&&(e=n.children[0]),e&&this.annotationData&&(t="Linear"===this.annotationData.type?document.getElementById(this.element.id+"_clippathrect"):document.getElementById(this.element.id+"_clippathcircle"),this.annotationData.isContent?e.innerHTML=this.annotationData.annotateValue+"%":t&&i.animate(t,{duration:this.annotationData.duration,delay:this.annotationData.delay,progress:function(t){a.cancelResize=!0;var i=a.effect(t.timeStamp,a.annotationData.startPos,a.annotationData.endPos,t.duration,!1),n=parseInt(((Math.round(i)-a.annotationData.start)/a.annotationData.totalAngle*100).toString(),10);e.innerHTML=n?n.toString()+"%":"0%"},end:function(){a.cancelResize=!1,e.innerHTML=a.annotationData.annotateValue+"%"}}))},t.prototype.labelAnimation=function(){var t,e=this;this.labelData&&(t="Linear"===this.labelData.type?document.getElementById(this.element.id+"_linearLabel"):document.getElementById(this.element.id+"_circularLabel"));var a=new sf.base.Animation({}),i=new sf.base.Animation({});t&&this.labelData&&!this.labelData.isStriped&&(t.style.visibility="hidden",a.animate(t,{duration:this.labelData.duration,delay:this.labelData.delay,progress:function(a){if(e.cancelResize=!0,"Linear"===e.labelData.type&&a.timeStamp>=a.delay&&""===e.labelData.labelText){t.style.visibility="visible";var i=e.effect(a.timeStamp,e.labelData.progressPos,Math.round(e.labelData.end-e.labelData.progressPos),a.duration,!1),n=parseInt((i/e.labelData.width*100).toString(),10);if(t.innerHTML=n.toString()+"%","Far"===e.labelData.labelPos||"Center"===e.labelData.labelPos){var r=e.effect(a.timeStamp,e.labelData.startPos,e.labelData.endPos-(e.labelData.enableRtl?0:e.labelData.startPos),a.duration,e.labelData.enableRtl);t.setAttribute("x",r.toString())}}else if("Circular"===e.labelData.type&&""===e.labelData.labelText){t.style.visibility="visible";i=e.effect(a.timeStamp,e.labelData.start,e.labelData.end-e.labelData.start,a.duration,!1),n=parseInt((i/e.labelData.totalAngle*100).toString(),10);t.innerHTML=n.toString()+"%"}},end:function(){e.cancelResize=!1,t&&""===e.labelData.labelText?(t.style.visibility="visible",t.innerHTML=e.labelData.text,t.setAttribute("x",e.labelData.x.toString())):i.animate(t,{progress:function(a){t.style.visibility="visible";var i=e.effect(a.timeStamp,0,1,a.duration,!1);t.setAttribute("opacity",i.toString())},end:function(){t.setAttribute("opacity","1")}})}}))},t.prototype.stripeAnimation=function(t){var e=this;void 0===t&&(t=0);var a=new sf.base.Animation({}),i=document.getElementById(this.element.id+"_LinearStriped");i&&this.stripeData&&a.animate(i,{duration:this.stripeData.duration,delay:this.stripeData.delay,progress:function(){e.stripeData.enable&&(t+=e.stripeData.enableRtl?-e.stripeData.durationValue:e.stripeData.durationValue,i.setAttribute("gradientTransform","translate("+t+") rotate(-45)"))},end:function(){e.stripeData.enable&&e.stripeAnimation(t)}})},t.prototype.linearIndeterminateAnimation=function(){var t=this,e=new sf.base.Animation({}),a=document.getElementById(this.element.id+"_clippathrect");a&&this.lineardata&&(a.style.visibility="hidden",e.animate(a,{duration:this.lineardata.duration,delay:0,progress:function(e){a.style.visibility="visible",t.lineardata.enableRtl&&t.lineardata.enableRtl&&t.lineardata.enable&&t.lineardata.isIndeterminate?t.animationProgress(a,e.timeStamp,t.lineardata.x||t.lineardata.rectX+t.lineardata.progressWidth,t.lineardata.end,e.duration,t.lineardata.enableRtl):t.lineardata.enable&&t.lineardata.isIndeterminate&&t.animationProgress(a,e.timeStamp,t.lineardata.start,t.lineardata.end,e.duration,t.lineardata.enableRtl)},end:function(){t.lineardata&&t.lineardata.enable&&t.lineardata.isIndeterminate&&(t.lineardata.enableRtl&&!t.lineardata.enableProgressSegments&&"Round4px"!==t.lineardata.cornerRadius?a.setAttribute("x",t.lineardata.x.toString()):t.lineardata.enableProgressSegments||a.setAttribute("x",t.lineardata.start.toString()),t.linearIndeterminateAnimation(),t.dotNetRef.invokeMethodAsync("TriggerAnimationComplete"))}}))},t.prototype.animationProgress=function(t,e,a,i,n,r){var l=this.effect(e,a,i,n,r);this.lineardata.enableProgressSegments?t.setAttribute("d",this.getPathLine(l,this.lineardata)):t.setAttribute("x",l.toString())},t.prototype.linearAnimation=function(){var t=this,e=new sf.base.Animation({}),a=document.getElementById(this.element.id+"_clippathrect"),i=0;a&&this.lineardata&&(a.style.visibility="hidden",e.animate(a,{duration:this.lineardata.duration,delay:this.lineardata.delay,progress:function(e){if(t.cancelResize=!0,t.lineardata.enableRtl&&"Round4px"!==t.lineardata.cornerRadius&&e.timeStamp>=e.delay)if(a.style.visibility="visible",t.lineardata.isActive){var n=document.getElementById(t.element.id+"_LinearActiveProgress");i=t.activeAnimate(e.timeStamp/e.duration,t.lineardata.x,t.lineardata.width,!0),n&&(n.setAttribute("opacity",t.effect(e.timeStamp,.5,.5,e.duration,!0).toString()),a.setAttribute("x",i.toString()))}else a.setAttribute("x",t.effect(e.timeStamp,t.lineardata.start,t.lineardata.end,e.duration,!0).toString());else if(e.timeStamp>=e.delay)if(a.style.visibility="visible",t.lineardata.isActive){n=document.getElementById(t.element.id+"_LinearActiveProgress");i=t.activeAnimate(e.timeStamp/e.duration,0,t.lineardata.width,t.lineardata.enableRtl),n&&(n.setAttribute("opacity",t.effect(e.timeStamp,.5,.5,e.duration,!0).toString()),a.setAttribute("width",i.toString()))}else a.setAttribute("width",t.effect(e.timeStamp,t.lineardata.start,t.lineardata.end,e.duration,!1).toString())},end:function(){t.cancelResize=!1,t.lineardata&&t.lineardata.enable&&t.lineardata.enableRtl&&"Round4px"!==t.lineardata.cornerRadius?t.lineardata.isActive?(a.setAttribute("x",t.lineardata.x.toString()),t.linearAnimation()):a.setAttribute("x",t.lineardata.rtlX.toString()):t.lineardata&&t.lineardata.enable&&(a.setAttribute("width",t.lineardata.width.toString()),t.lineardata.isActive&&t.linearAnimation()),t.dotNetRef.invokeMethodAsync("TriggerAnimationComplete")}}))},t.prototype.linearBufferAnimation=function(){var t=this,e=new sf.base.Animation({}),a=document.getElementById(this.element.id+"_clippathBufferrect");a&&this.linearBufferdata&&(a.style.visibility="hidden",e.animate(a,{duration:this.linearBufferdata.duration,delay:this.linearBufferdata.delay,progress:function(e){t.cancelResize=!0,t.linearBufferdata.enableRtl&&"Round4px"!==t.linearBufferdata.cornerRadius&&e.timeStamp>=e.delay?e.timeStamp>=e.delay&&(a.style.visibility="visible",a.setAttribute("x",t.effect(e.timeStamp,t.linearBufferdata.start,t.linearBufferdata.end,e.duration,!0).toString())):e.timeStamp>=e.delay&&(a.style.visibility="visible",a.setAttribute("width",t.effect(e.timeStamp,t.linearBufferdata.start,t.linearBufferdata.end,e.duration,!1).toString()))},end:function(){t.cancelResize=!1,a.style.visibility="",t.lineardata&&t.linearBufferdata.enable&&t.linearBufferdata.enableRtl&&"Round4px"!==t.linearBufferdata.cornerRadius?a.setAttribute("x",t.linearBufferdata.rtlX.toString()):t.linearBufferdata&&t.linearBufferdata.enable&&a.setAttribute("width",t.linearBufferdata.width.toString()),t.dotNetRef.invokeMethodAsync("TriggerAnimationComplete")}}))},t}();return{initialize:function(e,a,i,n,r){return new t(e,a,r).wireEvents(),this.getElementSize(a,i,n)},setSecondaryElementStyle:function(t){if(t){var e=document.getElementById(t.id+"SVG").getBoundingClientRect(),a=document.getElementById(t.id+"Secondary_Element"),i=t.getBoundingClientRect();a&&e&&(a.style.visibility="visible",a.style.left=Math.max(e.left-i.left,0)+"px",a.style.top=Math.max(e.top-i.top,0)+"px")}},getElementSize:function(t,e,a){var i,n;if(t){var r=t.style.display;t.style.display="block",t.style.height=e,t.style.width=a;var l=t.getBoundingClientRect();i=l.width,n=l.height,t.style.width="",t.style.height="",t.style.display=r}return{width:i,height:n}},doLinearBufferAnimation:function(t,e){var a=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(a)||(a.linearBufferdata=e,a.linearBufferAnimation())},doLinearAnimation:function(t,e){var a=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(a)||(a.lineardata=e,a.linearAnimation())},doLinearIndeterminate:function(t,e){var a=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(a)||(a.lineardata=e,a.linearIndeterminateAnimation())},doStripedAnimation:function(t,e){var a=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(a)||(a.stripeData=e,a.stripeAnimation())},doCircularAnimation:function(t,e){var a=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(a)||(a.circularData=e,a.circularAnimation())},doCircularBufferAnimation:function(t,e){var a=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(a)||(a.circularBufferData=e,a.circularBufferAnimation())},doCircularIndeterminate:function(t,e){var a=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(a)||(a.circularData=e,a.circularIndeterminateAnimation(e.start,e.end))},doAnnotationAnimation:function(t,e){var a=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(a)||(a.annotationData=e,a.annotationAnimation())},doLabelAnimation:function(t,e){var a=window.sfBlazor.getCompInstance(t);sf.base.isNullOrUndefined(a)||(a.labelData=e,a.labelAnimation())},update:function(t,e,a,i,n){var r=window.sfBlazor.getCompInstance(t);if(!sf.base.isNullOrUndefined(r)){if("Linear"===a){var l=e;l.isStriped?r.stripeData=l:r.lineardata=l}else r.circularData=e;i&&(r.labelData=e),n&&(r.annotationData=e)}},destroy:function(t){var e=window.sfBlazor.getCompInstance(t);t&&e&&e.unWireEvents()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfprogressbar');})})();