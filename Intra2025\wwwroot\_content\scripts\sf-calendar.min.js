/*!*  filename: sf-calendar.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[11],{"./bundles/sf-calendar.js":function(e,t,o){"use strict";o.r(t);o("./modules/sf-calendar.js")},"./modules/sf-calendar.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.CalendarBase=function(){"use strict";return{initialize:function(e,t,o,a,n){var r={controlUp:"ctrl+38",controlDown:"ctrl+40",moveDown:"downarrow",moveUp:"uparrow",moveLeft:"leftarrow",moveRight:"rightarrow",select:"enter",home:"home",end:"end",pageUp:"pageup",pageDown:"pagedown",shiftPageUp:"shift+pageup",shiftPageDown:"shift+pagedown",controlHome:"ctrl+home",controlEnd:"ctrl+end",altUpArrow:"alt+uparrow",spacebar:"space",altRightArrow:"alt+rightarrow",altLeftArrow:"alt+leftarrow"};r=sf.base.extend(r,o),new sf.base.KeyboardEvents(e,{eventName:"keydown",keyAction:this.keyActionHandle.bind(this,a,n,t),keyConfigs:r})},keyActionHandle:function(e,t,o,a){a.preventDefault();var n=sf.base.closest(a.target,".e-calendar").querySelector("tbody");if(n){!1;var r,s=n.querySelector("tr td.e-focused-date");r=n.querySelector("tr td.e-selected"),n.focus();var l=a.target,i={Action:a.action,Key:a.key,Events:a,SelectDate:r?r.id:null,FocusedDate:s?s.id:null,classList:r?r.classList.toString():s?s.classList.toString():"e-cell",Id:s?s.id:r?r.id:null,TargetClassList:l.classList.toString()};o.invokeMethodAsync("OnCalendarKeyboardEvent",i),l.classList.contains("e-today")&&(l.blur(),n.focus())}}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfcalendar');})})();