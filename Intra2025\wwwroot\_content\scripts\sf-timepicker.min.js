/*!*  filename: sf-timepicker.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[66],{"./bundles/sf-timepicker.js":function(e,t,s){"use strict";s.r(t);s("./modules/sf-timepicker.js")},"./modules/sf-timepicker.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.TimePicker=function(){"use strict";var e="Tab",t=function(){function t(){this.mask="",this.hiddenMask="",this.validCharacters="dMyhmHts",this.isDayPart=!1,this.isMonthPart=!1,this.isYearPart=!1,this.isHourPart=!1,this.isMinutePart=!1,this.isSecondsPart=!1,this.isMilliSecondsPart=!1,this.monthCharacter="",this.periodCharacter="",this.isHiddenMask=!1,this.isComplete=!1,this.isNavigate=!1,this.navigated=!1,this.formatRegex=/E{3,4}|d{1,4}|M{1,4}|y{1,4}|H{1,2}|h{1,2}|m{1,2}|f{1,7}|FF|t{1,2}|s{1,2}|z{1,4}|'[^']*'|'[^']*'/g,this.isDeletion=!1,this.isShortYear=!1,this.isDeleteKey=!1,this.isDateZero=!1,this.isMonthZero=!1,this.isYearZero=!1,this.isLeadingZero=!1,this.dayTypeCount=0,this.monthTypeCount=0,this.hourTypeCount=0,this.minuteTypeCount=0,this.secondTypeCount=0}return t.prototype.keydownHandler=function(t){if(!this.options.readonly)switch(t.code){case"ArrowLeft":case"ArrowRight":case"ArrowUp":case"ArrowDown":case"Home":case"End":case"Delete":"Delete"!==t.code&&t.preventDefault(),this.isPopupOpen||this.maskKeydownHandler(t);break;case e:case"shiftTab":this.maskKeydownHandler(t)}},t.prototype.clearHandler=function(){this.isDayPart=this.isMonthPart=this.isYearPart=this.isHourPart=this.isMinutePart=this.isSecondsPart=!1,this.updateValue()},t.prototype.updateValue=function(){this.monthCharacter=this.periodCharacter="";var e=this.dateformat.replace(this.formatRegex,this.formatCheck());this.isHiddenMask=!0,this.hiddenMask=this.dateformat.replace(this.formatRegex,this.formatCheck()),this.isHiddenMask=!1,this.previousHiddenMask=this.hiddenMask,this.previousValue=e},t.prototype.zeroCheck=function(e,t,s){var i=s;return e&&!t&&(i="0"),i},t.prototype.roundOff=function(e,t){for(var s=e.toString(),i=t-s.length,a="",o=0;o<i;o++)a+="0";return a+s},t.prototype.formatCheck=function(){var e=this;return function(t){var s,i,a=e.dayAbbreviatedName,o=Object.keys(a),n=e.dayName,r=Object.keys(n),h=e.monthAbbreviatedName,l=e.monthName,u=e.dayPeriod,d=Object.keys(u);switch(t){case"d":s=e.isDayPart?e.maskDateValue.getDate().toString():e.maskPlaceholderDictionary.Day.toString(),s=e.zeroCheck(e.isDateZero,e.isDayPart,s),2==e.dayTypeCount&&(e.isNavigate=!0,e.dayTypeCount=0);break;case"dd":s=e.isDayPart?e.roundOff(e.maskDateValue.getDate(),2):e.maskPlaceholderDictionary.Day.toString(),s=e.zeroCheck(e.isDateZero,e.isDayPart,s),2==e.dayTypeCount&&(e.isNavigate=!0,e.dayTypeCount=0);break;case"ddd":s=e.isDayPart&&e.isMonthPart&&e.isYearPart?a[o[e.maskDateValue.getDay()]].toString():e.maskPlaceholderDictionary.DayOfWeek.toString();break;case"dddd":s=e.isDayPart&&e.isMonthPart&&e.isYearPart?n[r[e.maskDateValue.getDay()]].toString():e.maskPlaceholderDictionary.DayOfWeek.toString();break;case"M":s=e.isMonthPart?(e.maskDateValue.getMonth()+1).toString():e.maskPlaceholderDictionary.Month.toString(),s=e.zeroCheck(e.isMonthZero,e.isMonthPart,s),2==e.monthTypeCount&&(e.isNavigate=!0,e.monthTypeCount=0);break;case"MM":s=e.isMonthPart?e.roundOff(e.maskDateValue.getMonth()+1,2):e.maskPlaceholderDictionary.Month.toString(),s=e.zeroCheck(e.isMonthZero,e.isMonthPart,s),2==e.monthTypeCount&&(e.isNavigate=!0,e.monthTypeCount=0);break;case"MMM":s=e.isMonthPart?h[e.maskDateValue.getMonth()]:e.maskPlaceholderDictionary.Month.toString();break;case"MMMM":s=e.isMonthPart?l[e.maskDateValue.getMonth()]:e.maskPlaceholderDictionary.Month.toString();break;case"yy":s=e.isYearPart?e.roundOff(e.maskDateValue.getFullYear()%100,2):e.maskPlaceholderDictionary.Year.toString(),s=e.zeroCheck(e.isYearZero,e.isYearPart,s);break;case"y":case"yyy":case"yyyy":s=e.isYearPart?e.roundOff(e.maskDateValue.getFullYear(),4):e.maskPlaceholderDictionary.Year.toString(),s=e.zeroCheck(e.isYearZero,e.isYearPart,s);break;case"h":s=e.isHourPart?(e.maskDateValue.getHours()%12||12).toString():e.maskPlaceholderDictionary.Hour.toString(),2==e.hourTypeCount&&(e.isNavigate=!0,e.hourTypeCount=0);break;case"hh":s=e.isHourPart?e.roundOff(e.maskDateValue.getHours()%12||12,2):e.maskPlaceholderDictionary.Hour.toString(),2==e.hourTypeCount&&(e.isNavigate=!0,e.hourTypeCount=0);break;case"H":s=e.isHourPart?e.maskDateValue.getHours().toString():e.maskPlaceholderDictionary.Hour.toString(),2==e.hourTypeCount&&(e.isNavigate=!0,e.hourTypeCount=0);break;case"HH":s=e.isHourPart?e.roundOff(e.maskDateValue.getHours(),2):e.maskPlaceholderDictionary.Hour.toString(),2==e.hourTypeCount&&(e.isNavigate=!0,e.hourTypeCount=0);break;case"m":s=e.isMinutePart?e.maskDateValue.getMinutes().toString():e.maskPlaceholderDictionary.Minute.toString(),2==e.minuteTypeCount&&(e.isNavigate=!0,e.minuteTypeCount=0);break;case"mm":s=e.isMinutePart?e.roundOff(e.maskDateValue.getMinutes(),2):e.maskPlaceholderDictionary.Minute.toString(),2==e.minuteTypeCount&&(e.isNavigate=!0,e.minuteTypeCount=0);break;case"s":s=e.isSecondsPart?e.maskDateValue.getSeconds().toString():e.maskPlaceholderDictionary.Second.toString(),2==e.secondTypeCount&&(e.isNavigate=!0,e.secondTypeCount=0);break;case"ss":s=e.isSecondsPart?e.roundOff(e.maskDateValue.getSeconds(),2):e.maskPlaceholderDictionary.Second.toString(),2==e.secondTypeCount&&(e.isNavigate=!0,e.secondTypeCount=0);break;case"f":s=Math.floor(e.maskDateValue.getMilliseconds()/100).toString();break;case"FF":case"ff":i=e.maskDateValue.getMilliseconds(),e.maskDateValue.getMilliseconds()>99&&(i=Math.floor(e.maskDateValue.getMilliseconds()/10)),s=e.roundOff(i,2);break;case"fff":s=e.roundOff(e.maskDateValue.getMilliseconds(),3);break;case"ffff":s=e.roundOff(e.maskDateValue.getMilliseconds(),4);break;case"fffff":s=e.roundOff(e.maskDateValue.getMilliseconds(),5);break;case"ffffff":s=e.roundOff(e.maskDateValue.getMilliseconds(),6);break;case"t":case"tt":s=e.maskDateValue.getHours()<12?u[d[0]]:u[d[1]];break;case"z":case"zz":s=e.offset.substring(0,3);break;case"zzz":case"zzzz":s=e.offset}if("ddd"===t&&(t="EEE"),"dddd"===t&&(t="EEEE"),s=void 0!==s?s:t.slice(1,t.length-1),e.isHiddenMask){for(var p="",c=0;c<s.length;c++)p+=t[0];return p}return s}},t.prototype.differenceCheck=function(){var e=this.element,t=e.selectionStart,s=e.value,i=this.previousValue.substring(0,t+this.previousValue.length-s.length),a=s.substring(0,t),o=!sf.base.isNullOrUndefined(a)&&a.length>0?new Date(+this.maskDateValue):new Date(this.options.valueString),n=new Date(o.getFullYear(),o.getMonth()+1,0).getDate();if(0===i.indexOf(a)&&(0===a.length||this.previousHiddenMask[a.length-1]!==this.previousHiddenMask[a.length])){for(var r=a.length;r<i.length;r++)""!==this.previousHiddenMask[r]&&this.validCharacters.indexOf(this.previousHiddenMask[r])>=0&&(this.isDeletion=this.handleDeletion(this.previousHiddenMask[r],!1));if(this.isDeletion)return}switch(this.previousHiddenMask[t-1]){case"d":var h=(this.isDayPart&&o.getDate().toString().length<2&&!this.isPersist()?10*o.getDate():0)+parseInt(a[t-1],10);if(this.isDateZero="0"==a[t-1],this.isFocused=!this.isFocused&&this.isFocused,this.navigated=!this.navigated&&this.navigated,isNaN(h))return;for(r=0;h>n;r++)h=parseInt(h.toString().slice(1),10);if(h>=1){if(o.setDate(h),this.isNavigate=2===h.toString().length,this.previousDate=new Date(o.getFullYear(),o.getMonth(),o.getDate()),o.getMonth()!==this.maskDateValue.getMonth())return;this.isDayPart=!0,this.dayTypeCount=this.dayTypeCount+1}else this.isDayPart=!1,this.dayTypeCount=this.isDateZero?this.dayTypeCount+1:this.dayTypeCount;break;case"M":var l=void 0;if(l=o.getMonth().toString().length<2&&!this.isPersist()?(this.isMonthPart?10*(o.getMonth()+1):0)+parseInt(a[t-1],10):parseInt(a[t-1],10),this.isFocused=!this.isFocused&&this.isFocused,this.navigated=!this.navigated&&this.navigated,this.isMonthZero="0"==a[t-1],isNaN(l)){var u=this.monthName,d=Object.keys(u);for(this.monthCharacter+=a[t-1].toLowerCase();this.monthCharacter.length>0;){r=0;for(var p=0,c=d;p<c.length;p++){c[p];if(0===u[r].toLowerCase().indexOf(this.monthCharacter))return o.setMonth(r),this.isMonthPart=!0,void(this.maskDateValue=o);r++}this.monthCharacter=this.monthCharacter.substring(1,this.monthCharacter.length)}}else{for(;l>12;){var f=l;0===(l=parseInt(l.toString().slice(1),10))&&(l=parseInt(f.toString().slice(0,1),10))}if(l>=1){if(o.setMonth(l-1),l>=10||1==l?this.isLeadingZero&&1==l?(this.isNavigate=1===l.toString().length,this.isLeadingZero=!1):this.isNavigate=2===l.toString().length:this.isNavigate=1===l.toString().length,o.getMonth()!==l-1&&(o.setDate(1),o.setMonth(l-1)),this.isDayPart){var m=new Date(this.previousDate.getFullYear(),this.previousDate.getMonth()+1,0).getDate(),k=new Date(o.getFullYear(),o.getMonth()+1,0).getDate();this.previousDate.getDate()==m&&k<=m&&o.setDate(k)}this.previousDate=new Date(o.getFullYear(),o.getMonth(),o.getDate()),this.isMonthPart=!0,this.monthTypeCount=this.monthTypeCount+1}else o.setMonth(0),this.isLeadingZero=!0,this.isMonthPart=!1,this.monthTypeCount=this.isMonthZero?this.monthTypeCount+1:this.monthTypeCount}break;case"y":var g=(this.isYearPart&&o.getFullYear().toString().length<4&&!this.isShortYear?10*o.getFullYear():0)+parseInt(a[t-1],10),y=(this.dateformat.match(/y/g)||[]).length;if(this.isShortYear=!1,this.isYearZero="0"==a[t-1],isNaN(g))return;for(;g>9999;)g=parseInt(g.toString().slice(1),10);g<1?this.isYearPart=!1:(o.setFullYear(g),g.toString().length===y&&(this.isNavigate=!0),this.previousDate=new Date(o.getFullYear(),o.getMonth(),o.getDate()),this.isYearPart=!0);break;case"h":if(this.hour=(this.isHourPart&&(o.getHours()%12||12).toString().length<2&&!this.isPersist()?10*(o.getHours()%12||12):0)+parseInt(a[t-1],10),this.isFocused=!this.isFocused&&this.isFocused,this.navigated=!this.navigated&&this.navigated,isNaN(this.hour))return;for(;this.hour>12;)this.hour=parseInt(this.hour.toString().slice(1),10);o.setHours(12*Math.floor(o.getHours()/12)+this.hour%12),this.isNavigate=2===this.hour.toString().length,this.isHourPart=!0,this.hourTypeCount=this.hourTypeCount+1;break;case"H":if(this.hour=(this.isHourPart&&o.getHours().toString().length<2&&!this.isPersist()?10*o.getHours():0)+parseInt(a[t-1],10),this.isFocused=!this.isFocused&&this.isFocused,this.navigated=!this.navigated&&this.navigated,isNaN(this.hour))return;for(r=0;this.hour>23;r++)this.hour=parseInt(this.hour.toString().slice(1),10);o.setHours(this.hour),this.isNavigate=2===this.hour.toString().length,this.isHourPart=!0,this.hourTypeCount=this.hourTypeCount+1;break;case"m":var v=(this.isMinutePart&&o.getMinutes().toString().length<2&&!this.isPersist()?10*o.getMinutes():0)+parseInt(a[t-1],10);if(this.isFocused=!this.isFocused&&this.isFocused,this.navigated=!this.navigated&&this.navigated,isNaN(v))return;for(r=0;v>59;r++)v=parseInt(v.toString().slice(1),10);o.setMinutes(v),this.isNavigate=2===v.toString().length,this.isMinutePart=!0,this.minuteTypeCount=this.minuteTypeCount+1;break;case"s":var b=(this.isSecondsPart&&o.getSeconds().toString().length<2&&!this.isPersist()?10*o.getSeconds():0)+parseInt(a[t-1],10);if(this.isFocused=!this.isFocused&&this.isFocused,this.navigated=!this.navigated&&this.navigated,isNaN(b))return;for(r=0;b>59;r++)b=parseInt(b.toString().slice(1),10);o.setSeconds(b),this.isNavigate=2===b.toString().length,this.isSecondsPart=!0,this.secondTypeCount=this.secondTypeCount+1;break;case"t":this.periodCharacter+=a[t-1].toLowerCase();var D=this.dayPeriod,M=Object.keys(D);for(r=0;this.periodCharacter.length>0;r++)(0===D[M[0]].toLowerCase().indexOf(this.periodCharacter)&&o.getHours()>=12||0===D[M[1]].toLowerCase().indexOf(this.periodCharacter)&&o.getHours()<12)&&(o.setHours((o.getHours()+12)%24),this.maskDateValue=o),this.periodCharacter=this.periodCharacter.substring(1,this.periodCharacter.length)}this.maskDateValue=o},t.prototype.maskKeydownHandler=function(t){var s=this.element;if(this.dayTypeCount=this.monthTypeCount=this.hourTypeCount=this.minuteTypeCount=this.secondTypeCount=0,"Delete"!==t.key){if(!(t.altKey||t.ctrlKey||"ArrowLeft"!==t.key&&"ArrowRight"!==t.key&&"shiftTab"!==t.key&&t.key!==e&&"shiftTab"!==t.action&&"End"!==t.key&&"Home"!==t.key)){var i=s.selectionStart,a=s.selectionEnd,o=s.value.length;if(0==i&&a==o&&(t.key===e&&!t.shiftKey||t.key===e&&t.shiftKey)){var n=t.key===e&&t.shiftKey?a:0;s.selectionStart=s.selectionEnd=n}if("End"===t.key||"Home"===t.key){var r="End"===t.key?o:0;s.selectionStart=s.selectionEnd=r}if(("ArrowLeft"===t.key||"ArrowRight"===t.key)&&0===i&&a===o){if("ArrowLeft"===t.key)for(var h=s.selectionEnd,l=h,u=h-1;l<this.hiddenMask.length||u>=0;l++,u--){if(l<this.hiddenMask.length&&-1!==this.validCharacters.indexOf(this.hiddenMask[l]))return void this.setSelection(this.hiddenMask[l]);if(u>=0&&-1!==this.validCharacters.indexOf(this.hiddenMask[u]))return void this.setSelection(this.hiddenMask[u])}else this.validCharacterCheck();return}this.navigateSelection(!!("ArrowLeft"===t.key||t.shiftKey&&t.key===e||"End"===t.key))}if(!(t.altKey||t.ctrlKey||"ArrowUp"!==t.key&&"ArrowDown"!==t.key)){i=s.selectionStart;var d="";-1!==this.validCharacters.indexOf(this.hiddenMask[i])&&(d=this.hiddenMask[i]),this.dateAlteration("ArrowDown"===t.key);var p=this.dateformat.replace(this.formatRegex,this.formatCheck());this.isHiddenMask=!0,this.hiddenMask=this.dateformat.replace(this.formatRegex,this.formatCheck()),this.isHiddenMask=!1,this.previousHiddenMask=this.hiddenMask,this.previousValue=p,s.value=p;for(l=0;l<this.hiddenMask.length;l++)if(d===this.hiddenMask[l]){i=l;break}s.selectionStart=i,this.validCharacterCheck()}}else this.isDeleteKey=!0},t.prototype.dateAlteration=function(e){var t=this.element.selectionStart,s="";if(-1!==this.validCharacters.indexOf(this.hiddenMask[t])){s=this.hiddenMask[t];var i=new Date(this.maskDateValue.getFullYear(),this.maskDateValue.getMonth(),this.maskDateValue.getDate(),this.maskDateValue.getHours(),this.maskDateValue.getMinutes(),this.maskDateValue.getSeconds());this.previousDate=new Date(this.maskDateValue.getFullYear(),this.maskDateValue.getMonth(),this.maskDateValue.getDate(),this.maskDateValue.getHours(),this.maskDateValue.getMinutes(),this.maskDateValue.getSeconds());var a=e?-1:1;switch(s){case"d":i.setDate(i.getDate()+a);break;case"M":var o=i.getMonth()+a;if(i.setDate(1),i.setMonth(o),this.isDayPart){var n=new Date(this.previousDate.getFullYear(),this.previousDate.getMonth()+1,0).getDate(),r=new Date(i.getFullYear(),i.getMonth()+1,0).getDate();this.previousDate.getDate()==n&&r<=n?i.setDate(r):i.setDate(this.previousDate.getDate())}else i.setDate(this.previousDate.getDate());this.previousDate=new Date(i.getFullYear(),i.getMonth(),i.getDate());break;case"y":i.setFullYear(i.getFullYear()+a);break;case"H":case"h":i.setHours(i.getHours()+a);break;case"m":i.setMinutes(i.getMinutes()+a);break;case"s":i.setSeconds(i.getSeconds()+a);break;case"t":i.setHours((i.getHours()+12)%24)}this.maskDateValue=i.getFullYear()>0?i:this.maskDateValue,-1!==this.validCharacters.indexOf(this.hiddenMask[t])&&this.handleDeletion(this.hiddenMask[t],!0)}},t.prototype.handleDeletion=function(e,t){switch(e){case"d":this.isDayPart=t;break;case"M":this.isMonthPart=t,t||(this.maskDateValue.setMonth(0),this.monthCharacter="");break;case"y":this.isYearPart=t;break;case"H":case"h":this.isHourPart=t,t||(this.periodCharacter="");break;case"m":this.isMinutePart=t;break;case"s":this.isSecondsPart=t;break;default:return!1}return!0},t.prototype.isPersist=function(){return this.isFocused||this.navigated},t.prototype.setDynamicValue=function(){this.maskDateValue=new Date(this.options.valueString),this.isDayPart=this.isMonthPart=this.isYearPart=this.isHourPart=this.isMinutePart=this.isSecondsPart=!0,this.updateValue(),this.options.isBlurred||this.validCharacterCheck()},t.prototype.validCharacterCheck=function(){var e=this.element,t=e.selectionStart;if("TimePicker"!==this.componentName&&t===this.hiddenMask.length&&this.mask===e.value&&(t=0),"TimePicker"===this.componentName){if(t===this.hiddenMask.length)return void e.setSelectionRange(0,t);if(this.isPopupOpen)return void e.setSelectionRange(0,this.hiddenMask.length)}for(var s=t,i=t-1;s<this.hiddenMask.length||i>=0;s++,i--){if(s<this.hiddenMask.length&&-1!==this.validCharacters.indexOf(this.hiddenMask[s]))return void this.setSelection(this.hiddenMask[s]);if(i>=0&&-1!==this.validCharacters.indexOf(this.hiddenMask[i]))return void this.setSelection(this.hiddenMask[i])}},t.prototype.setSelection=function(e){for(var t=this.element,s=-1,i=0,a=0;a<this.hiddenMask.length;a++)this.hiddenMask[a]===e&&(i=a+1,-1===s&&(s=a));s<0&&(s=0),t.setSelectionRange(s,i)},t.prototype.maskInputHandler=function(){var e,t=this.element,s=t.selectionStart,i="";-1!==this.validCharacters.indexOf(this.hiddenMask[s])&&(i=this.hiddenMask[s]),this.differenceCheck(),e=this.dateformat.replace(this.formatRegex,this.formatCheck()),this.isHiddenMask=!0,this.hiddenMask=this.dateformat.replace(this.formatRegex,this.formatCheck()),this.isDateZero=this.isMonthZero=this.isYearZero=!1,this.isHiddenMask=!1,this.previousHiddenMask=this.hiddenMask,this.previousValue=e,t.value=e;for(var a=0;a<this.hiddenMask.length;a++)if(i===this.hiddenMask[a]){s=a;break}if(t.selectionStart=s,this.validCharacterCheck(),(this.isNavigate||this.isDeletion)&&!this.isDeleteKey){var o=!this.isNavigate;this.isNavigate=this.isDeletion=!1,this.navigateSelection(o)}this.isDeleteKey&&(this.isDeletion=!1),this.isDeleteKey=!1},t.prototype.navigateSelection=function(e){var t=this.element,s=t.selectionStart,i=t.selectionEnd,a=e?s-1:i;for(this.navigated=!0;a<this.hiddenMask.length&&a>=0;){if(this.validCharacters.indexOf(this.hiddenMask[a])>=0){this.setSelection(this.hiddenMask[a]);break}a+=e?-1:1}},t.prototype.createMask=function(e){this.options=e;this.element;this.isDayPart=this.isMonthPart=this.isYearPart=this.isHourPart=this.isMinutePart=this.isSecondsPart=!1,this.dateformat=this.options.format;var t=this.dateformat.replace(this.formatRegex,this.formatCheck());return this.isHiddenMask=!0,this.hiddenMask=this.dateformat.replace(this.formatRegex,this.formatCheck()),this.isHiddenMask=!1,this.previousHiddenMask=this.hiddenMask,this.mask=this.previousValue=t,this.options.value&&(this.value=this.options.value,this.navigated=this.options.navigated,this.setDynamicValue()),t=this.dateformat.replace(this.formatRegex,this.formatCheck()),this.options.minMax&&this.validCharacterCheck(),{currentMaskFormat:this.mask,inputElementValue:t}},t.prototype.updateCurrentValue=function(e){this.element.value=e},t}(),s=(new Date).getDate(),i=(new Date).getMonth(),a=(new Date).getFullYear(),o=function(){function e(e,s,i,a,o){window.sfBlazor=window.sfBlazor,this.maskObj=new t,this.componentName="TimePicker",this.dataId=e,this.containerElement=s,this.element=i,this.options=o,window.sfBlazor.setCompInstance(this),this.dotNetRef=a,this.maskObj.element=this.element,this.enableMask=this.options.enableMask,this.enableMask&&(this.maskObj.dateformat=this.options.format,this.maskObj.isRendered=this.options.isRendered,this.maskObj.componentName=this.componentName,this.maskObj.offset=this.options.offset,this.maskObj.floatLabelType=this.options.floatLabelType,this.maskObj.maskPlaceholderDictionary=this.options.maskPlaceholderDictionary,this.maskObj.dayAbbreviatedName=this.options.dayAbbreviatedName,this.maskObj.dayName=this.options.dayName,this.maskObj.dayPeriod=this.options.dayPeriod,this.maskObj.monthAbbreviatedName=this.options.monthAbbreviatedName,this.maskObj.monthName=this.options.monthName,this.maskObj.maskDateValue=sf.base.isNullOrUndefined(this.options.value)?new Date:new Date(this.options.value.toString()),this.maskObj.maskDateValue.setMonth(0),this.maskObj.maskDateValue.setHours(0),this.maskObj.maskDateValue.setMinutes(0),this.maskObj.maskDateValue.setSeconds(0),this.maskObj.previousDate=new Date(this.maskObj.maskDateValue.getFullYear(),this.maskObj.maskDateValue.getMonth(),this.maskObj.maskDateValue.getDate(),this.maskObj.maskDateValue.getHours(),this.maskObj.maskDateValue.getMinutes(),this.maskObj.maskDateValue.getSeconds()),this.removeEventListener(),this.addEventListener())}return e.prototype.initialize=function(){this.keyConfigure={enter:"enter",escape:"escape",end:"end",tab:"tab",home:"home",down:"downarrow",up:"uparrow",left:"leftarrow",right:"rightarrow",open:"alt+downarrow",close:"alt+uparrow"},sf.base.Browser.isDevice||(this.keyConfigure=sf.base.extend(this.keyConfigure,this.options.keyConfigs),new sf.base.KeyboardEvents(this.containerElement,{keyAction:this.inputHandler.bind(this),keyConfigs:this.keyConfigure,eventName:"keydown"}))},e.prototype.bindInputEvent=function(){this.enableMask?sf.base.EventHandler.add(this.element,"input",this.inputkeyHandler,this):sf.base.EventHandler.remove(this.element,"input",this.inputkeyHandler)},e.prototype.addEventListener=function(){sf.base.EventHandler.add(this.element,"mouseup",this.mouseUpHandler,this),sf.base.EventHandler.add(this.element,"keydown",this.keydownHandler,this),this.bindInputEvent()},e.prototype.removeEventListener=function(){sf.base.EventHandler.remove(this.element,"mouseup",this.mouseUpHandler),sf.base.EventHandler.remove(this.element,"keydown",this.keydownHandler)},e.prototype.mouseUpHandler=function(e){this.enableMask&&(this.maskObj.element=this.element,e.preventDefault(),this.maskObj.validCharacterCheck())},e.prototype.inputkeyHandler=function(){this.enableMask&&(this.maskObj.element=this.element,this.maskObj.maskInputHandler())},e.prototype.keydownHandler=function(e){this.enableMask&&(this.maskObj.element=this.element,this.maskObj.keydownHandler(e))},e.prototype.renderPopup=function(e,t,s,i){this.options=i,this.maskObj.isPopupOpen=!0,this.popupHolder=t,this.timeCollections=[],this.listWrapper=t.querySelector(".e-content")||sf.base.select(".e-content"),this.getTimeCollection(),sf.base.isNullOrUndefined(this.element.value)||(this.removeSelection(),this.selectedElement=this.listWrapper.querySelector('li[data-value = "'+this.element.value+'"]'),this.updateSelection(this.selectedElement)),this.popupCreation(e,i),sf.base.Browser.isDevice&&(this.mobilePopupWrapper=sf.base.createElement("div",{className:"e-timepicker-mob-popup-wrap"}),document.body.appendChild(this.mobilePopupWrapper)),("model"===s.appendTo?this.mobilePopupWrapper:document.body).appendChild(this.popupWrapper),this.setScrollPosition(),this.popupObj.refreshPosition(this.element);var a={name:"FadeIn",duration:sf.base.Browser.isDevice?0:300};1e3===this.options.zIndex?this.popupObj.show(new sf.base.Animation(a),this.element):this.popupObj.show(new sf.base.Animation(a),null),this.setOverlayIndex(this.mobilePopupWrapper,this.popupObj.element,this.modal,sf.base.Browser.isDevice),sf.base.EventHandler.add(document,"mousedown touchstart",this.documentClickHandler,this)},e.prototype.getTimeCollection=function(){for(var e=this.listWrapper.querySelectorAll(".e-list-item"),t=0;t<e.length;t++)this.timeCollections.push(e[t].getAttribute("data-value"))},e.prototype.updateSelection=function(e){e&&(sf.base.addClass([e],"e-active"),e.setAttribute("aria-selected","true"))},e.prototype.setScrollPosition=function(){sf.base.isNullOrUndefined(this.selectedElement)?this.popupWrapper&&this.options.scrollTo&&this.checkDateValue(new Date(this.options.scrollTo))&&this.setScrollTo():this.findScrollTop(this.selectedElement)},e.prototype.checkDateValue=function(e){return!sf.base.isNullOrUndefined(e)&&e instanceof Date&&!isNaN(+e)?e:null},e.prototype.findScrollTop=function(e){var t=this.getPopupHeight(),s=e.nextElementSibling,i=s?s.offsetTop:e.offsetTop,a=e.getBoundingClientRect().height;i+e.offsetTop>t?this.popupWrapper.scrollTop=s?i-(t/2+a/2):i:this.popupWrapper.scrollTop=0},e.prototype.setScrollTo=function(){var e;if(sf.base.isNullOrUndefined(this.popupWrapper))this.popupWrapper.scrollTop=0;else{var t=this.popupWrapper.querySelectorAll(".e-list-item");if(t.length){var s=new Date((new Date).toDateString()+" "+this.timeCollections[0]).setMilliseconds(0),i=this.getDateObject(new Date(this.options.scrollTo)).getTime();e=t[Math.round((i-s)/(6e4*this.options.step))]}}sf.base.isNullOrUndefined(e)?this.popupWrapper.scrollTop=0:this.findScrollTop(e)},e.prototype.getDateObject=function(e){if(!sf.base.isNullOrUndefined(e)){var t=e,o=!sf.base.isNullOrUndefined(this.options.value);if(this.checkDateValue(t)){var n=o?new Date(this.options.value).getDate():s,r=o?new Date(this.options.value).getMonth():i,h=o?new Date(this.options.value).getFullYear():a;return new Date(h,r,n,t.getHours(),t.getMinutes(),t.getSeconds())}}return null},e.prototype.getPopupHeight=function(){var e=parseInt("240px",10),t=this.popupWrapper.getBoundingClientRect().height;return t>e?e:t},e.prototype.popupCreation=function(e,t){var s=this;this.popupWrapper=e,this.containerStyle=this.containerElement.getBoundingClientRect(),sf.base.Browser.isDevice&&(this.modal=sf.base.createElement("div"),this.modal.className="e-timepicker e-time-modal",document.body.className+=" e-time-overflow",this.modal.style.display="block",document.body.appendChild(this.modal)),this.popupObj=new sf.popups.Popup(this.popupWrapper,{width:this.setPopupWidth(this.options.width),relateTo:sf.base.Browser.isDevice?document.body:this.containerElement,position:sf.base.Browser.isDevice?{X:"center",Y:"center"}:{X:"left",Y:"bottom"},collision:sf.base.Browser.isDevice?{X:"fit",Y:"fit"}:{X:"flip",Y:"flip"},offsetY:4,targetType:"relative",enableRtl:t.enableRtl,zIndex:t.zIndex,open:function(){s.popupWrapper.style.visibility="visible"},close:function(){s.popupHolder.appendChild(s.popupWrapper),s.popupObj&&s.popupObj.destroy(),s.isDisposed||s.dotNetRef.invokeMethodAsync("ClosePopup").catch((function(){})),s.popupObj=null},targetExitViewport:function(){sf.base.Browser.isDevice||s.isDisposed||s.dotNetRef.invokeMethodAsync("HidePopup",null)}}),sf.base.Browser.isDevice||(this.popupObj.collision={X:"none",Y:"flip"}),this.popupWrapper.classList.contains("e-popup-expand")?(this.popupObj.element.style.maxHeight="100%",this.popupObj.element.style.width="100%"):this.popupObj.element.style.maxHeight="240px"},e.prototype.closePopup=function(e,t){this.options=t,this.maskObj.isPopupOpen=!1,sf.base.removeClass([document.body],"e-time-overflow"),this.closeEventCallback(e)},e.prototype.removeSelection=function(){if(this.removeHover("e-hover"),!sf.base.isNullOrUndefined(this.popupWrapper)){var e=this.popupWrapper.querySelectorAll(".e-active");e.length&&(sf.base.removeClass(e,"e-active"),e[0].removeAttribute("aria-selected"))}},e.prototype.removeHover=function(e){var t=this.popupWrapper?this.popupWrapper.querySelectorAll("."+e):[];t&&t.length&&(sf.base.removeClass(t,e),"e-navigation"===e&&t[0].removeAttribute("aria-selected"))},e.prototype.setWidth=function(e){return e="number"==typeof e||"string"==typeof e?sf.base.formatUnit(e):"100%"},e.prototype.setPopupWidth=function(e){(e=this.setWidth(e)).indexOf("%")>-1&&(e=(this.containerStyle.width*parseFloat(e)/100).toString()+"px");return e},e.prototype.closeEventCallback=function(e){var t=e;if(this.isPopupOpen()&&!t.cancel&&this.popupObj){var s={name:"FadeOut",duration:50,delay:0};this.popupObj.hide(new sf.base.Animation(s))}sf.base.Browser.isDevice&&(this.modal&&(this.modal.style.display="none",this.modal.outerHTML="",this.modal=null),sf.base.isNullOrUndefined(this.mobilePopupWrapper)||(this.mobilePopupWrapper.remove(),this.mobilePopupWrapper=null)),sf.base.EventHandler.remove(document,"mousedown touchstart",this.documentClickHandler)},e.prototype.isPopupOpen=function(){return!(sf.base.isNullOrUndefined(this.popupObj)||!this.popupObj.element.classList.contains("e-popup"))},e.prototype.documentClickHandler=function(e){var t=e.target;!sf.base.isNullOrUndefined(this.popupObj)&&(this.containerElement.contains(t)||this.popupObj.element&&this.popupObj.element.contains(t))&&"touchstart"!==e.type&&e.preventDefault();var s=this.containerElement.querySelector(".e-clear-icon"),i=this.containerElement.querySelector(".e-time-icon.e-icons");sf.base.closest(t,".e-popup-wrapper")||t===this.element||t===i||t===s||t===this.containerElement||this.isPopupOpen()&&!this.isDisposed&&this.dotNetRef.invokeMethodAsync("HidePopup",null)},e.prototype.setOverlayIndex=function(e,t,s,i){if(i&&!sf.base.isNullOrUndefined(t)&&!sf.base.isNullOrUndefined(s)&&!sf.base.isNullOrUndefined(e)){var a=parseInt(t.style.zIndex,10)?parseInt(t.style.zIndex,10):1e3;s.style.zIndex=(a-1).toString(),e.style.zIndex=a.toString()}},e.prototype.selectInputText=function(e,t,s){sf.base.Browser.isDevice||this.enableMask&&!this.maskObj.isPopupOpen||t&&this.listWrapper&&(this.selectedElement=this.listWrapper.querySelectorAll(".e-list-item")[s],this.setScrollPosition())},e.prototype.isDevice=function(){return sf.base.Browser.isDevice},e.prototype.inputHandler=function(e){var t=this.element;"right"!=e.action&&"left"!=e.action&&"tab"!=e.action&&("home"!=e.action&&"end"!=e.action&&"up"!=e.action&&"down"!=e.action||this.isPopupOpen()||this.enableMask)&&e.preventDefault(),"enter"===e.action&&this.isPopupOpen()&&e.stopPropagation();var s={Action:e.action,Key:e.key,KeyCode:e.keyCode,Events:e,SelectDate:null,FocusedDate:null,classList:"",Id:null,TargetClassList:null};this.enableMask&&this.maskObj.isPopupOpen&&(t.value=null),this.isDisposed||this.dotNetRef.invokeMethodAsync("KeyboardHandler",s,t.value),"enter"===e.action&&!sf.base.isNullOrUndefined(this.popupObj)&&document.body.contains(this.popupObj.element)&&e.preventDefault()},e}();return{initialize:function(e,t,s,i,a){if(s){var n=new o(e,t,s,i,a);if(n.initialize(),!sf.base.isNullOrUndefined(sf.base.closest(s,"fieldset"))&&sf.base.closest(s,"fieldset").disabled){var r=a.enabled=!1;n.dotNetRef.invokeMethodAsync("UpdateFieldSetStatus",r)}if(a.enableMask)return n.maskObj.createMask(a)}return{currentMaskFormat:null,inputElementValue:a.value}},createMask:function(e,t){return e&&t.enableMask?window.sfBlazor.getCompInstance(e).maskObj.createMask(t):{currentMaskFormat:null,inputElementValue:t.value}},updateCurrentValue:function(e,t){window.sfBlazor.getCompInstance(e).maskObj.updateCurrentValue(t)},renderPopup:function(e,t,s,i,a){var o=window.sfBlazor.getCompInstance(e);!sf.base.isNullOrUndefined(o)&&t&&s&&o.renderPopup(t,s,i,a)},closePopup:function(e,t,s){var i=window.sfBlazor.getCompInstance(e);!sf.base.isNullOrUndefined(i)&&t&&i.closePopup(t,s)},selectInputText:function(e,t,s){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.selectInputText(i.element,t,s)},focusIn:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||(t&&s.element.setAttribute("readonly","true"),s.element.focus())},focusOut:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.element.blur()},destroy:function(e,t,s,i,a){var o=window.sfBlazor.getCompInstance(e);!sf.base.isNullOrUndefined(o)&&t&&t instanceof HTMLElement&&s&&(o.isDisposed=!0,o.closePopup(i,a))}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sftimepicker');})})();