@charset "UTF-8";
/* General layout */
body {
    font-family: 'Arial', sans-serif;
    margin: 20px;
    margin-left: 50px;
    background-color: #F3F4F6;
    color: #1F2937;    
}

/* Container styling */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #ffffff;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* 自訂錯誤訊息 */
.validation-inline {
    display: inline;
    margin-left: 10px; /* 與輸入框之間的間距 */
    color: red; /* 自訂錯誤訊息顏色 */
    font-size: smaller; /* 縮小字體 */
}

/* Header styling */
h1 {
    font-size: 24px;
    color: #1E3A8A;
    text-align: center;
    margin-bottom: 20px;
}

/* Category buttons section */
.category-section {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 5px;
    margin-bottom: 2px;
}

.category-button {
    background-color: #56828f;
    color: white;
    border: none;
    padding: 1px 1px;
    margin: 1px;
    border-radius: 10px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    text-align: center;
}

    .category-button:hover {
        background-color: #afc7cf;
    }

#btnAll {
    background-color: #1f4b5b;
}

    #btnAll:hover {
        background-color: #afc7cf;
    }

/* Search and filter section */
.search-section {
    display: flex;
    margin-left:50px;
    justify-content: left;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}

/* Search bar */
.search-bar {
    display: flex;
    justify-content: center;
    padding: 10px;
}

    .search-bar input {
        width: 300px;
        border: 1px solid #D1D5DB;
        border-radius: 5px 0 0 5px;
        margin-right: 0;
    }

    .search-bar button {
        background-color: #10B981;
        color: white;
        border: none;
        padding-top: 10px;
        padding-bottom: 5px;
        border-radius: 0 5px 5px 0;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .search-bar button:hover {
        background-color: #059669;
    }

.clear {
    color: white;
    border: none;
    padding-top: 10px;
    padding-bottom: 5px;
    border-radius: 0 5px 5px 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
    background-color: #31d2f2;
}


hr {
    margin: 1;
    width: 100%;
    color:red;
    
}




/* Table styling */
table {
    width: 90%;
    border-collapse: collapse;
    margin-bottom: 10px;
}

    table th, table td {
        border: 1px solid #E5E7EB;
        padding: 5px;
        text-align: left;
    }

    table th {
        background-color: #677ab1;
        color: white;
        align-content: center;
    }

    table tr:nth-child(even) {
        background-color: #F9FAFB;
    }

    table tr:hover {
        background-color: #cdefef;
    }
/* Pagination buttons */
.pagination {
    display: flex;
    justify-content: space-between; /* 分開分頁控制區和新連結 */
    align-items: center;
    width: 90%; /* 確保 pagination 填滿整個寬度 */
}

.pagination-controls {
    display: inline-flex;
    justify-content: center;
    flex-grow: 1;
}

.new-link-button {
    border: 2px dotted #79c2d0;
    background-color: transparent;
    color: #79c2d0;
    padding: 5px 10px;
    cursor: pointer;
}

/* Table styling */
.form-table {
    border-collapse: collapse;
    margin-bottom: 10px;
    color: black;
    /*background-color: #E5E7EB;*/
}

.form-table, h1, h3 {
    width: 60%;
    margin-top: 0.5rem;
    margin-left: 2rem;
}

    .form-table th, form-table td {
        border: 1px solid #E5E7EB;
        padding: 5px;
        text-align: left;
        vertical-align: middle; /* 內容垂直置中 */
    }

    .form-table tr:nth-child(even) {
        background-color: #FFFCF0;
    }

    .form-table tr:hover {
        background-color: #E8E8E8;
    }

/*################# Loading effects ###############*/

/* HTML: <div class="loader"></div> */
.loader {
    --w: 25ch;
    font-weight: bold;
    font-family: monospace;
    font-size: 32px;
    letter-spacing: var(--w);
    width: var(--w);
    overflow: hidden;
    white-space: nowrap;
    text-shadow: calc(-1*var(--w)) 0, calc(-2*var(--w)) 0, calc(-3*var(--w)) 0, calc(-4*var(--w)) 0, calc(-5*var(--w)) 0, calc(-6*var(--w)) 0, calc(-7*var(--w)) 0, calc(-8*var(--w)) 0, calc(-9*var(--w)) 0;
    animation: l16 8s infinite; /* default: 2s */
}

    .loader:before {
        content: "規劃報告書資料庫";
    }

.loader2 {
    --w: 25ch;
    font-weight: bold;
    font-family: monospace;
    font-size: 32px;
    letter-spacing: var(--w);
    width: var(--w);
    overflow: hidden;
    white-space: nowrap;
    text-shadow: calc(-1*var(--w)) 0, calc(-2*var(--w)) 0, calc(-3*var(--w)) 0, calc(-4*var(--w)) 0, calc(-5*var(--w)) 0, calc(-6*var(--w)) 0, calc(-7*var(--w)) 0, calc(-8*var(--w)) 0, calc(-9*var(--w)) 0;
    animation: l16 8s infinite; /* default: 2s */
}

    .loader2:before {
        content: "守護寶貝即時通";
    }

.loader3 {
    --w: 25ch;
    font-weight: bold;
    font-family: monospace;
    font-size: 32px;
    letter-spacing: var(--w);
    width: var(--w);
    overflow: hidden;
    white-space: nowrap;
    text-shadow: calc(-1*var(--w)) 0, calc(-2*var(--w)) 0, calc(-3*var(--w)) 0, calc(-4*var(--w)) 0, calc(-5*var(--w)) 0, calc(-6*var(--w)) 0, calc(-7*var(--w)) 0, calc(-8*var(--w)) 0, calc(-9*var(--w)) 0;
    animation: l16 8s infinite; /* default: 2s */
}

    .loader3:before {
        content: "溫馨關懷表";
    }
@keyframes l16 {
    20% {
        text-shadow: calc(-1*var(--w)) 0, calc(-2*var(--w)) 0 #a8bece, calc(-3*var(--w)) 0, calc(-4*var(--w)) 0 #484163, calc(-5*var(--w)) 0 #63fff4, calc(-6*var(--w)) 0, calc(-7*var(--w)) 0, calc(-8*var(--w)) 0 green, calc(-9*var(--w)) 0;
    }

    40% {
        text-shadow: calc(-1*var(--w)) 0, calc(-2*var(--w)) 0 #a8bece, calc(-3*var(--w)) 0 #bbc7ce, calc(-4*var(--w)) 0, calc(-5*var(--w)) 0 green, calc(-6*var(--w)) 0 #cdc5f2, calc(-7*var(--w)) 0, calc(-8*var(--w)) 0 green, calc(-9*var(--w)) 0;
    }

    60% {
        text-shadow: calc(-1*var(--w)) 0 lightblue, calc(-2*var(--w)) 0, calc(-3*var(--w)) 0 #bbc7ce, calc(-4*var(--w)) 0, calc(-5*var(--w)) 0 green, calc(-6*var(--w)) 0, calc(-7*var(--w)) 0 #bbc7ce, calc(-8*var(--w)) 0 #484163, calc(-9*var(--w)) 0 #a8bece;
    }

    80% {
        text-shadow: calc(-1*var(--w)) 0 lightblue, calc(-2*var(--w)) 0 #bbc7ce, calc(-3*var(--w)) 0 #63fff4, calc(-4*var(--w)) 0 #484163, calc(-5*var(--w)) 0 #a8bece, calc(-6*var(--w)) 0, calc(-7*var(--w)) 0 grey, calc(-8*var(--w)) 0 #63fff4, calc(-9*var(--w)) 0;
    }
}

.highlight {
    border: 2px dotted blue; /* 加粗紅色邊框 */
    padding: 15px; /* 調整內邊距，避免內容擠壓 */
    border-radius: 30px; /* 四角圓角 */
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2); /* 增加陰影效果 */
}

.highlight2 {
    border: 2px dotted gray; /* 加粗紅色邊框 */
    padding: 15px; /* 調整內邊距，避免內容擠壓 */
    border-radius: 10px; /* 四角圓角 */
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2); /* 增加陰影效果 */
}
