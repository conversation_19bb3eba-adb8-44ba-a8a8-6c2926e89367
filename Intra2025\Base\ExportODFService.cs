﻿using Intra2025.Models;
using LibreOfficeLibrary;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Globalization;
using System.Text;
using System.Text.Json;
using static Intra2025.Components.Base.JsonDataLoader;

namespace Intra2025.Components.Base
{
    public class ExportODFService<T>
    {
        private readonly IWebHostEnvironment _env;
        private readonly ILogger<ExportODFService<T>> _logger;

        public ExportODFService(IWebHostEnvironment env, ILogger<ExportODFService<T>> logger)
        {
            _env = env;
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public void ExportToChildRecord(IEnumerable<T> records, string csvFilePath)
        {
            var content = new StringBuilder();
            var properties = typeof(T).GetProperties();
            List<Category> categories;
            List<CaseBelong> caseBelongs;

            // JSON 資料載入
            try
            {
                var jsonDataLoader = new JsonDataLoader(_env);
                categories = jsonDataLoader.LoadCategoriesFromJson("A"); // 使用同步方式載入類別資料
                caseBelongs = jsonDataLoader.LoadCaseBelongFromJson(); // 使用同步方式載入所屬機關資料

                if (categories == null || !categories.Any())
                {
                    throw new Exception("Categories data is empty or null.");
                }

                if (caseBelongs == null || !caseBelongs.Any())
                {
                    throw new Exception("Case belongs data is empty or null.");
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error during JSON data loading: " + ex.Message);
            }

            // 建立表頭映射字典
            var headerMapping = new Dictionary<string, string>
    {
        { "ChildName", "子女姓名" },
        { "ChildIdNumber", "子女身分證字號" },
        { "BirthDate", "出生日期" },
        { "Parent1Name", "父親姓名" },
        { "Parent1Id", "父親身分證字號" },
        { "Pt1Imprison", "是否入監" },
        { "Parent2Name", "母親姓名" },
        { "Parent2Id", "母親身分證字號" },
        { "Pt2Imprison", "是否入監" },
        { "HouseholdAddress", "戶籍地址" },
        { "CurrentAddress", "現居地址" },
        { "Contactor", "案家聯絡人" },
        { "ContactCommunication", "案家聯絡電話" },
        { "AttributesId", "個案類型" },
        { "CaseBelongId", "案件所屬戶所" },
        { "CreateDate", "案件建立日期" },
        { "BelongDate", "案件所屬日期" },
        { "CreateName", "案件承辦人" },
        { "Remarks", "備註" }
    };

            var excludedProperties = new HashSet<string> { "Id", "Parent1Relation", "Parent2Relation", "IsChecked", "IsMainled", "CreateDate", "IsMailed" };

            try
            {
                // 標題列生成
                content.Append("序號,");
                foreach (var prop in properties)
                {
                    if (excludedProperties.Contains(prop.Name))
                        continue;

                    string headerName = headerMapping.ContainsKey(prop.Name) ? headerMapping[prop.Name] : prop.Name;
                    content.Append(headerName + ",");
                }
                content.Length--;
                content.AppendLine();

                int serialNumber = 1;
                foreach (var record in records)
                {
                    content.Append(serialNumber++ + ",");

                    foreach (var prop in properties)
                    {
                        if (excludedProperties.Contains(prop.Name))
                            continue;

                        var value = prop.GetValue(record);

                        if (prop.Name == "BirthDate" && value is DateTime birthDateTime)
                        {
                            value = DateConverter.ToTaiwanCalendar(birthDateTime);
                        }
                        if (prop.Name == "AttributesId")
                        {
                            var attributesId = value?.ToString();
                            var category = categories.FirstOrDefault(c => c.Id == attributesId);
                            if (category == null)
                            {
                                throw new Exception($"Category with Id '{attributesId}' not found.");
                            }
                            value = category.Name;
                        }
                        if (prop.Name == "CaseBelongId")
                        {
                            var caseBelongId = value?.ToString();
                            var caseBelong = caseBelongs.FirstOrDefault(c => c.Id == caseBelongId);
                            if (caseBelong == null)
                            {
                                throw new Exception($"Case belong with Id '{caseBelongId}' not found.");
                            }
                            value = caseBelong.Name;
                        }
                        if (prop.Name == "Pt1Imprison")
                        {
                            if (value is bool booleanValue)
                            {
                                value = booleanValue ? "V" : "";
                            }
                            else
                            {
                                throw new Exception($"Invalid value for property '{prop.Name}'. Expected a boolean.");
                            }
                        }
                        if (prop.Name == "Pt2Imprison")
                        {
                            if (value is bool booleanValue)
                            {
                                value = booleanValue ? "V" : "";
                            }
                            else
                            {
                                throw new Exception($"Invalid value for property '{prop.Name}'. Expected a boolean.");
                            }
                        }
                        if (prop.Name == "BelongDate")
                        {
                            value = Convert.ToDateTime(value).ToString("yyy.MM", CultureInfo.InvariantCulture);
                        }

                        content.Append(EscapeCsvField(value?.ToString() ?? string.Empty) + ",");
                    }

                    content.Length--;
                    content.AppendLine();
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error during record processing: " + ex.Message);
            }

            try
            {
                // 寫入檔案
                using (var fileStream = new FileStream(csvFilePath, FileMode.Create, FileAccess.Write, FileShare.None))
                using (var writer = new StreamWriter(fileStream))
                {
                    writer.Write(content.ToString());
                }

                if (!File.Exists(csvFilePath))
                {
                    throw new FileNotFoundException("CSV file creation failed.");
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error during CSV file creation: " + ex.Message);
            }
        }

        public void ConvertCsvToOds(string csvFilePath, string odsFilePath)
        {
            string libreOfficePath = @"C:\Program Files\MODA ODF Application Tools\program\soffice.exe"; // 請確認路徑正確
            var process = new System.Diagnostics.Process();
            process.StartInfo.FileName = libreOfficePath;
            process.StartInfo.Arguments = $"--headless --convert-to ods \"{csvFilePath}\" --outdir \"{Path.GetDirectoryName(odsFilePath)}\"";
            process.StartInfo.UseShellExecute = false;
            process.StartInfo.CreateNoWindow = true;
            process.StartInfo.RedirectStandardOutput = true;
            process.StartInfo.RedirectStandardError = true;
            process.Start();

            // 安全地讀取輸出，處理可能的 null 值
            string output = process.StandardOutput.ReadToEnd() ?? string.Empty;
            string error = process.StandardError.ReadToEnd() ?? string.Empty;

            process.WaitForExit();

            // 記錄前先檢查是否為空，避免記錄過多空白內容
            if (!string.IsNullOrEmpty(output))
            {
                _logger.LogInformation("轉換命令標準輸出：{Output}", output);
            }
            else
            {
                _logger.LogInformation("轉換命令標準輸出：(空白)");
            }

            if (!string.IsNullOrEmpty(error))
            {
                _logger.LogInformation("轉換命令錯誤輸出：{Error}", error);
            }
            else
            {
                _logger.LogInformation("轉換命令錯誤輸出：(無錯誤)");
            }

            string dirPath = Path.GetDirectoryName(odsFilePath) ?? throw new ArgumentNullException(nameof(odsFilePath), "Directory path is invalid.");
            string fileName = Path.GetFileNameWithoutExtension(csvFilePath) ?? throw new ArgumentNullException(nameof(csvFilePath), "File name is invalid.");
            string generatedOdsPath = Path.Combine(dirPath, fileName + ".ods");
            if (File.Exists(generatedOdsPath))
            {
                // 使用 Move 操作將 ODS 檔案移動到指定位置
                File.Move(generatedOdsPath, odsFilePath, true);
                _logger.LogInformation("ODS 檔案已成功移動至指定位置。");
            }
            else
            {
                _logger.LogInformation("轉換後的 ODS 檔案不存在，請檢查轉換參數和檔案權限。");
            }
        }

        public void ExportToCareRecord(IEnumerable<YCRS_CareRecord> records, string csvFilePath)
        {
            var content = new StringBuilder();
            var properties = typeof(YCRS_CareRecord).GetProperties();

            var jsonDataLoader = new JsonDataLoader(_env);
            var categories = jsonDataLoader.LoadCategoriesFromJson("B"); // 使用同步方式載入類別資料
            var caseBelongs = jsonDataLoader.LoadCaseBelongFromJson(); // 使用同步方式載入所屬機關資料

            // 建立表頭映射字典
            var headerMapping = new Dictionary<string, string>
    {
        { "Id", "系統代碼" },
        { "BelongDate", "案件所屬月份" },
        { "Caregiver", "照顧者" },
        { "AttributesId", "個案類型" },
        { "NumberOfCare", "照顧人數" },
        { "CaseBelongId", "個案所屬戶所" },
        { "Visitcare", "是否訪視關懷" },
        { "FamilyMediation", "是否進行家事協調" },
        { "IsChecked", "是否確認" },
        { "IsMailed", "是否已通知" },
        { "Remarks", "備註" },
        { "CreateDate", "案件新增日期" },
        { "CreateName", "案件承辦人" }
    };

            // 定義要排除的屬性名稱
            var excludedProperties = new HashSet<string>();

            // 生成標題列，先添加序號欄位，並根據字典映射修改名稱
            content.Append("序號,");
            foreach (var prop in properties)
            {
                if (excludedProperties.Contains(prop.Name))
                    continue;

                string headerName = headerMapping.ContainsKey(prop.Name) ? headerMapping[prop.Name] : prop.Name;
                content.Append(headerName + ",");
            }
            content.Length--; // 去掉最後一個逗號
            content.AppendLine();

            // 生成資料列，並從 1 開始編號
            int serialNumber = 1;
            foreach (var record in records)
            {
                content.Append(serialNumber++ + ","); // 添加序號

                foreach (var prop in properties)
                {
                    if (excludedProperties.Contains(prop.Name))
                        continue;

                    var value = prop.GetValue(record);

                    // 特殊欄位處理
                    if (prop.Name == "BelongDate" && value is DateTime belongDate)
                    {
                        value = $"{belongDate.Year - 1911}.{belongDate.Month:D2}"; // 民國年格式
                    }
                    if (prop.Name == "AttributesId")
                    {
                        var attributesId = value?.ToString();
                        var category = categories.FirstOrDefault(c => c.Id == attributesId);
                        if (category != null)
                        {
                            value = category.Name; // 屬性編號轉為名稱
                        }
                    }
                    if (prop.Name == "CaseBelongId")
                    {
                        var caseBelongId = value?.ToString();
                        var caseBelong = caseBelongs.FirstOrDefault(c => c.Id == caseBelongId);
                        if (caseBelong != null)
                        {
                            value = caseBelong.Name; // 單位代號轉為名稱
                        }
                    }
                    if (prop.Name == "Visitcare" && value is bool visitcare)
                    {
                        value = visitcare ? "是" : "否"; // 布林值轉為 是/否
                    }
                    if (prop.Name == "FamilyMediation" && value is bool familyMediation)
                    {
                        value = familyMediation ? "是" : "否";
                    }
                    if (prop.Name == "IsChecked" && value is bool isChecked)
                    {
                        value = isChecked ? "是" : "否";
                    }
                    if (prop.Name == "IsMailed" && value is bool isMailed)
                    {
                        value = isMailed ? "是" : "否";
                    }
                    content.Append(EscapeCsvField(value?.ToString() ?? string.Empty) + ",");
                }

                content.Length--; // 去掉最後一個逗號
                content.AppendLine();
            }

            // 寫入最終的 CSV 檔案
            using (var writer = new StreamWriter(csvFilePath, false, Encoding.UTF8))
            {
                writer.Write(content.ToString());
            }

            Console.WriteLine($"CSV 檔案成功生成於: {csvFilePath}");
        }
        //產生出【溫馨關懷表】ODS檔案

        private string EscapeCsvField(string field)
        {
            // 去掉欄位中的換行符號，並將欄位放在雙引號內以避免 CSV 格式問題
            field = field.Replace("\r", " ").Replace("\n", " ");
            if (field.Contains(",") || field.Contains("\"") || field.Contains(" "))
            {
                field = "\"" + field.Replace("\"", "\"\"") + "\"";
            }
            return field;
        }

    }
}