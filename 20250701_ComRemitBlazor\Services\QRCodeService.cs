using System.IO;
using System.Text;
using System.Runtime.Versioning;
#if WINDOWS
using System.Drawing;
using System.Drawing.Imaging;
#endif

namespace ComRemitBlazor.Services
{
    public class BarcodeService
    {
        /// <summary>
        /// 確�?條碼?��??��??�Code128?�援?�ASCII字符
        /// </summary>
        /// <param name="text">?��??��?</param>
        /// <returns>?��??�ASCII字符?��?�?/returns>
        private string SanitizeBarcodeText(string text)
        {
            // 確�??��??�Code128?�援?��?符�?ASCII 0-127�?
            var result = new StringBuilder();
            foreach (char c in text)
            {
                if (c >= 0 && c <= 127)
                {
                    result.Append(c);
                }
                else
                {
                    // 將�??�援?��?符�??�為底�?
                    result.Append('_');
                }
            }
            return result.ToString();
        }

        /// <summary>
        /// 生成Code128條碼
        /// </summary>
        /// <param name="text">要編碼的文字</param>
        /// <returns>PNG格式條碼圖片的byte陣列</returns>
        [SupportedOSPlatform("windows")]
        public byte[] GenerateCode128Barcode(string text)
        {
            try
            {
                // 清理文字，確保Code128相容性
                var sanitizedText = SanitizeBarcodeText(text);

                // 直接生成條碼圖片
                return CreateCode128Barcode(sanitizedText);
            }
            catch (Exception)
            {
                return GenerateFallbackBarcode(text);
            }
        }

        /// <summary>
        /// 建立標準Code128條碼圖片
        /// </summary>
        [SupportedOSPlatform("windows")]
        private byte[] CreateCode128Barcode(string text)
        {
#if WINDOWS
            try
            {
                using (var bitmap = new Bitmap(600, 150)) // 增加尺寸便於辨識
                {
                    using (var graphics = Graphics.FromImage(bitmap))
                    {
                        graphics.Clear(System.Drawing.Color.White);

                        // 設定高品質渲染
                        graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.None;
                        graphics.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.Half;

                        // 繪製標準Code128條碼（含靜音區）
                        DrawCode128BarcodeWithQuietZone(graphics, text, 100, 25, 2.5f, 80); // 放大參數便於辨識

                        // 繪製文字
                        using (var font = new System.Drawing.Font("Arial", 14, FontStyle.Regular))
                        {
                            var textSize = graphics.MeasureString(text, font);
                            var textX = (bitmap.Width - textSize.Width) / 2;
                            graphics.DrawString(text, font, Brushes.Black, textX, 110); // 調整文字位置
                        }
                    }

                    // 轉換為PNG
                    using (var ms = new MemoryStream())
                    {
                        bitmap.Save(ms, ImageFormat.Png);
                        return ms.ToArray();
                    }
                }
            }
            catch (Exception)
            {
                return GenerateFallbackBarcode(text);
            }
#else
            // 非 Windows 平台返回空陣列
            return new byte[0];
#endif
        }

        /// <summary>
        /// 繪製帶靜音區的標準Code128條碼
        /// </summary>
#if WINDOWS
        [SupportedOSPlatform("windows")]
        private void DrawCode128BarcodeWithQuietZone(Graphics graphics, string text, float startX, float startY, float baseWidth, float barHeight)
        {
            // ?�音?�寬度（Code128標�?要�??��?10?�模組寬度�?
            var quietZoneWidth = baseWidth * 10;

            // ?��?位置（�??�左?��??��?�?
            var actualStartX = startX - quietZoneWidth;

            // 確�??�音?�?�白?��?已�??�Clear中�??��?
            var x = startX; // 條碼實�??��?位置

            // ?��??�?��?碼模�?
            var allPatterns = GetAllCode128Patterns(text);

            // 繪製每個模�?
            foreach (var pattern in allPatterns)
            {
                x = DrawBarsPatternPrecise(graphics, pattern, x, startY, baseWidth, barHeight);
            }

            // ?�側?�音?�（自?�由?�色?�景?��?�?
        }

        /// <summary>
        /// ?��?完整?�Code128模�?序�?
        /// </summary>
        private List<string> GetAllCode128Patterns(string text)
        {
            var patterns = new List<string>();

            // Start Code B (??104)
            patterns.Add("11010010000");

            // 計�??��???
            var checksum = 104; // Start Code B?��?

            // ?��??��?符添?�模�?
            for (int i = 0; i < text.Length; i++)
            {
                var charValue = text[i] - 32; // Code128 B?��??�ASCII?�移
                if (charValue >= 0 && charValue <= 94)
                {
                    patterns.Add(GetCode128Pattern(charValue));
                    checksum += charValue * (i + 1); // 位置權�?�??��?
                }
                else
                {
                    // ?��?不支?��?字符，使?�空?�替�?
                    patterns.Add(GetCode128Pattern(0)); // 空格?�模�?
                    checksum += 0 * (i + 1);
                }
            }

            // 添�??��?�?
            var checksumValue = checksum % 103;
            patterns.Add(GetCode128Pattern(checksumValue));

            // Stop Code（�??��?終�?紋�?
            patterns.Add("1100011101011");

            return patterns;
        }

        /// <summary>
        /// 精確繪製條碼模式
        /// </summary>
        [SupportedOSPlatform("windows")]
        private float DrawBarsPatternPrecise(Graphics graphics, string pattern, float x, float y, float moduleWidth, float height)
        {
            // Code128每個字符都是11個模組寬
            for (int i = 0; i < pattern.Length; i++)
            {
                if (pattern[i] == '1')
                {
                    // 繪製黑條，確保精確的寬度
                    var barX = x;
                    var barWidth = moduleWidth;

                    // 使用精確的矩形繪製
                    graphics.FillRectangle(Brushes.Black, barX, y, barWidth, height);
                }
                x += moduleWidth;
            }
            return x;
        }
#endif

        /// <summary>
        /// ?��?Code128模�?（�??��?準�?符表�?
        /// </summary>
        private string GetCode128Pattern(int value)
        {
            // 完整?�Code128字符表�?0-102�?
            var patterns = new string[]
            {
                "11011001100", "11001101100", "11001100110", "10010011000", "10010001100", // 0-4
                "10001001100", "10011001000", "10011000100", "10001100100", "11001001000", // 5-9
                "11001000100", "11000100100", "10110011100", "10011011100", "10011001110", // 10-14
                "10111001100", "10011101100", "10011100110", "11001110010", "11001011100", // 15-19
                "11001001110", "11011100100", "11001110100", "11101101110", "11101001100", // 20-24
                "11100101100", "11100100110", "11101100100", "11100110100", "11100110010", // 25-29
                "11011011000", "11011000110", "11000110110", "10100011000", "10001011000", // 30-34
                "10001000110", "10110001000", "10001101000", "10001100010", "11010001000", // 35-39
                "11000101000", "11000100010", "10110111000", "10110001110", "10001101110", // 40-44
                "10111011000", "10111000110", "10001110110", "11101110110", "11010001110", // 45-49
                "11000101110", "11011101000", "11011100010", "11011101110", "11101011000", // 50-54
                "11101000110", "11100010110", "11101101000", "11101100010", "11100011010", // 55-59
                "11101111010", "11001000010", "11110001010", "10100110000", "10100001100", // 60-64
                "10010110000", "10010000110", "10000101100", "10000100110", "10110010000", // 65-69
                "10110000100", "10011010000", "10011000010", "10000110100", "10000110010", // 70-74
                "11000010010", "11001010000", "11110111010", "11000010100", "10001111010", // 75-79
                "10100111100", "10010111100", "10010011110", "10111100100", "10011110100", // 80-84
                "10011110010", "11110100100", "11110010100", "11110010010", "11011011110", // 85-89
                "11011110110", "11110110110", "10101111000", "10100011110", "10001011110", // 90-94
                "10111101000", "10111100010", "11110101000", "11110100010", "10111011110", // 95-99
                "10111101110", "11101011110", "11110101110"  // 100-102
            };

            return (value >= 0 && value < patterns.Length) ? patterns[value] : "11011001100";
        }



        /// <summary>
        /// 備用後備條碼（當主要生成失敗時使用）
        /// </summary>
        [SupportedOSPlatform("windows")]
        private byte[] GenerateFallbackBarcode(string text)
        {
#if WINDOWS
            try
            {
                using (var bitmap = new Bitmap(600, 150))
                {
                    using (var graphics = Graphics.FromImage(bitmap))
                    {
                        graphics.Clear(System.Drawing.Color.White);

                        // 繪製簡單的文字條碼
                        using (var font = new System.Drawing.Font("Courier New", 14, FontStyle.Bold))
                        {
                            var textSize = graphics.MeasureString($"||||| {text} |||||", font);
                            var x = (bitmap.Width - textSize.Width) / 2;
                            graphics.DrawString($"||||| {text} |||||", font, Brushes.Black, x, 50);
                        }

                        // 繪製下方文字
                        using (var font = new System.Drawing.Font("Arial", 12))
                        {
                            var textSize = graphics.MeasureString(text, font);
                            var x = (bitmap.Width - textSize.Width) / 2;
                            graphics.DrawString(text, font, Brushes.Black, x, 110);
                        }
                    }

                    using (var ms = new MemoryStream())
                    {
                        bitmap.Save(ms, ImageFormat.Png);
                        return ms.ToArray();
                    }
                }
            }
            catch
            {
                // 最終備用：返回空白色圖片
                using (var bitmap = new Bitmap(600, 150))
                {
                    using (var graphics = Graphics.FromImage(bitmap))
                    {
                        graphics.Clear(System.Drawing.Color.White);
                    }

                    using (var ms = new MemoryStream())
                    {
                        bitmap.Save(ms, ImageFormat.Png);
                        return ms.ToArray();
                    }
                }
            }
#else
            // 非 Windows 平台返回空陣列
            return new byte[0];
#endif
        }

        public string GenerateBarcodeAscii(string text)
        {
            return $"||||| {text} |||||";
        }

        [SupportedOSPlatform("windows")]
        public byte[] GenerateBarcode(string text, int width = 200, int height = 50)
        {
            return GenerateCode128Barcode(text);
        }

        [SupportedOSPlatform("windows")]
        public byte[] GenerateBarcodeForQuestPDF(string text, int width = 200, int height = 50)
        {
            return GenerateCode128Barcode(text);
        }


    }
}
