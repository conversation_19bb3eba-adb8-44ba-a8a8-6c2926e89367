@page "/remitmt"
@rendermode @(new InteractiveServerRenderMode(prerender: false))
@implements IDisposable
@inject RemitService RemitService
@inject PayeeService PayeeService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@using ComRemitBlazor.Services
@using ComRemitBlazor.Models
@using Microsoft.AspNetCore.Components.Web

<PageTitle>匯款彙整作業</PageTitle>

<style>
    .remit-page {
        width: 100%;
        margin: 0;
        padding: 20px;
        box-sizing: border-box;
        font-family: 'Microsoft JhengHei', sans-serif;
    }
    
    .form-section {
        background-color: #ccffff;
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 5px;
    }
    
    .batch-upload-section {
        background-color: #ccffcc;
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 5px;
    }
    
    .required {
        color: red;
        font-size: 18px;
        font-weight: bold;
    }
    
    .readonly-input {
        background-color: #cccccc;
    }
    
    .autocomplete-container {
        position: relative;
        display: inline-block;
        width: 300px;
    }
    
    .autocomplete-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ccc;
        border-top: none;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
    }
    
    .autocomplete-suggestion {
        padding: 8px 12px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
    }
    
    .autocomplete-suggestion:hover {
        background-color: #f0f0f0;
    }
</style>

<div class="remit-page">
    <div class="card">
        <div class="card-header">
            <h4 style="color: #0033CC; font-weight: bold;">【匯款彙整作業(step2)】</h4>
        </div>
        <div class="card-body">
            <!-- 資料新增模式選擇 -->
            <div style="margin-bottom: 20px;">
                <label>資料新增模式:</label>
                <div style="margin-left: 20px;">
                    <input type="radio" id="single" name="dataMode" value="single" @onchange="OnDataModeChanged" checked="@(dataMode == "single")" />
                    <label for="single" style="margin-right: 20px;">單筆</label>
                    
                    <input type="radio" id="batch" name="dataMode" value="batch" @onchange="OnDataModeChanged" checked="@(dataMode == "batch")" />
                    <label for="batch">整批匯入</label>
                </div>
            </div>

            <!-- 單筆新增區塊 -->
            @if (dataMode == "single")
            {
                <div class="form-section">
                    <table style="width: 80%;">
                        <tr>
                            <td style="width: 180px;"><span class="required">*</span>收款人戶名</td>
                            <td>
                                <div class="autocomplete-container">
                                    <input type="text" class="form-control" @bind="collectName" @oninput="OnCollectNameInput" 
                                           @onfocusout="OnCollectNameBlur" placeholder="請輸入收款人戶名" style="width: 300px;" />
                                    @if (showSuggestions && suggestions.Any())
                                    {
                                        <div class="autocomplete-suggestions">
                                            @foreach (var suggestion in suggestions)
                                            {
                                                <div class="autocomplete-suggestion" @onclick="() => SelectSuggestion(suggestion)">
                                                    @suggestion
                                                </div>
                                            }
                                        </div>
                                    }
                                </div>
                            </td>
                            <td>身分證字號(統編)</td>
                            <td>
                                <input type="text" class="form-control readonly-input" @bind="collectId" readonly />
                            </td>
                        </tr>
                        <tr>
                            <td><span class="required">*</span>收款人帳號</td>
                            <td>
                                <input type="text" class="form-control readonly-input" @bind="collectAcc" readonly />
                            </td>
                            <td style="width: 180px;">解款行代號</td>
                            <td>
                                <input type="text" class="form-control readonly-input" @bind="collectNo" readonly style="width: 150px; display: inline-block;" />
                                <input type="text" class="form-control readonly-input" @bind="financialName" readonly style="width: 200px; display: inline-block; margin-left: 5px;" />
                            </td>
                        </tr>
                        <tr>
                            <td>電話</td>
                            <td>
                                <input type="text" class="form-control readonly-input" @bind="tel" readonly />
                            </td>
                            <td style="width: 180px;">郵遞區號</td>
                            <td>
                                <input type="text" class="form-control readonly-input" @bind="zip" readonly />
                            </td>
                        </tr>
                        <tr>
                            <td>住址</td>
                            <td colspan="3">
                                <input type="text" class="form-control readonly-input" @bind="addr" readonly style="width: 300px;" />
                            </td>
                        </tr>
                        <tr>
                            <td><span class="required">*</span>帳款金額</td>
                            <td>
                                <input type="number" class="form-control" @bind="remitPrice" step="0.01" min="0" />
                            </td>
                            <td><span class="required">*</span>帳款是否有手續費<br />(詳備註三)</td>
                            <td>
                                <input type="radio" id="feeYes" name="ifFee" value="是" @onchange="OnIfFeeChanged" checked="@(ifFee == "是")" />
                                <label for="feeYes" style="margin-right: 20px;">是</label>
                                
                                <input type="radio" id="feeNo" name="ifFee" value="否" @onchange="OnIfFeeChanged" checked="@(ifFee == "否")" />
                                <label for="feeNo">否</label>
                            </td>
                        </tr>
                        <tr>
                            <td>備註</td>
                            <td>
                                <input type="text" class="form-control" @bind="remitMemo" maxlength="40" style="width: 300px;" />
                            </td>
                            <td> </td>
                            <td>
                                彙整編號: <strong>@conSno</strong>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2">
                                <button class="btn btn-primary" @onclick="AddRemitItem">確認新增此筆彙款資料</button>
                            </td>
                            <td></td>
                        </tr>
                    </table>
                </div>
            }

            <!-- 整批匯入區塊 -->
            @if (dataMode == "batch")
            {
                <div class="batch-upload-section">
                    <div style="margin-bottom: 15px;">
                        <InputFile OnChange="HandleFileSelected" accept=".csv" />
                        <button class="btn btn-success" @onclick="ProcessBatchUpload" disabled="@(selectedFile == null)">整批匯入</button>
                        <a href="ExportTxt/匯入範例檔.csv" class="btn btn-link">下載範例檔</a>
                    </div>
                    <div style="color: #FF0066; font-weight: bold;">
                        (PS:備註欄位字數請控制在40字內，避免使用全形或特殊符號)
                    </div>
                </div>
            }

            <!-- 資料列表 -->
            <div style="margin-top: 30px;">
                @if (isLoading)
                {
                    <div style="text-align: center; padding: 30px;">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">載入中...</span>
                        </div>
                        <p>正在載入資料...</p>
                    </div>
                }
                else
                {
                    <table class="table table-bordered table-striped">
                        <thead style="background-color: #507CD1; color: white;">
                            <tr>
                                <th>功能</th>
                                <th>收款人金融局號</th>
                                <th>收款人帳號</th>
                                <th>收款人戶名</th>
                                <th>身分證字號(統編)</th>
                                <th>帳款金額</th>
                                <th>備註</th>
                                <th>帳款是否有手續費</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (remitList.Any())
                            {
                                @foreach (var item in remitList)
                                {
                                    <tr>
                                        <td>
                                            @if (editingSno == item.Sno)
                                            {
                                                <button class="btn btn-sm btn-success" @onclick="() => SaveEdit(item.Sno)">更新</button>
                                                <button class="btn btn-sm btn-secondary" @onclick="CancelEdit">取消</button>
                                            }
                                            else
                                            {
                                                <button class="btn btn-sm btn-primary" @onclick="() => StartEdit(item.Sno)">編輯</button>
                                                <button class="btn btn-sm btn-danger" @onclick="() => DeleteItem(item.Sno)">刪除</button>
                                            }
                                        </td>
                                        <td>@item.CollectNo</td>
                                        <td>@item.CollecAcc</td>
                                        <td>@item.CollecName</td>
                                        <td>@item.CollectId</td>
                                        <td>
                                            @if (editingSno == item.Sno)
                                            {
                                                <input type="number" class="form-control" @bind="editingPrice" step="0.01" min="0" />
                                            }
                                            else
                                            {
                                                @if (item.RemitPrice != null) // 或者用 item.RemitPrice.HasValue
                                                {
                                                    <span>金額: @item.RemitPrice.Value</span>
                                                }
                                                else
                                                {
                                                    <span>金額: 0</span>
                                                }
                                            }
                                        </td>
                                        <td>
                                            @if (editingSno == item.Sno)
                                            {
                                                <input type="text" class="form-control" @bind="editingMemo" maxlength="40" />
                                            }
                                            else
                                            {
                                                @item.RemitMemo
                                            }
                                        </td>
                                        <td>
                                            @if (editingSno == item.Sno)
                                            {
                                                <select class="form-control" @bind="editingIfFee">
                                                    <option value="是">是</option>
                                                    <option value="否">否</option>
                                                </select>
                                            }
                                            else
                                            {
                                                @item.IfFee
                                            }
                                        </td>
                                    </tr>
                                }
                                <tr>
                                    <td colspan="5" style="text-align: right; font-weight: bold;">總計</td>
                                    <td style="font-weight: bold;">@TotalAmount</td>
                                    <td colspan="2"></td>
                                </tr>
                            }
                            else
                            {
                                <tr>
                                    <td colspan="8" style="text-align: center; padding: 20px;">
                                        尚無資料，請由上方新增
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                }
            </div>

            <!-- 錯誤/成功訊息 -->
            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger mt-3">@errorMessage</div>
            }
            @if (!string.IsNullOrEmpty(successMessage))
            {
                <div class="alert alert-success mt-3">@successMessage</div>
            }
            
            <!-- 彙整單用途說明 -->
            @if (remitList.Any())
            {
                <div style="margin-top: 20px; padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
                    <div class="row">
                        <div class="col-md-3">
                            <label style="font-weight: bold; color: #856404;">
                                <span style="color: red;">*</span>彙整單用途說明：
                            </label>
                        </div>
                        <div class="col-md-6">
                            <input type="text" class="form-control" @bind="remitPurpose"
                                   placeholder="請輸入彙整單用途說明" maxlength="100"
                                   style="border: 2px solid #ffc107;" />
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">彙整編號: @conSno</small>
                        </div>
                    </div>
                </div>
            }

            <!-- 頁腳按鈕 -->
            <div style="margin-top: 20px;">
                <button class="btn btn-lg btn-primary" @onclick="CompleteAllItems" disabled="@(!remitList.Any())">
                    完成彙整並送至已彙整清單 (@remitList.Count 筆)
                </button>
                <button class="btn btn-lg btn-info" @onclick="GoToRemitedList">查看已彙整清單</button>
            </div>
            
            <!-- 備註 -->
            <div style="margin-top: 30px; border-top: 1px solid #ccc; padding-top: 15px;">
                <h5 style="color: blue;">備註:</h5>
                <ol>
                    <li>1.若[收款人戶名]與[收款人帳號]不符，則無法成功匯款，請重新確認</li>
                    <li>2.若[收款人帳號]為郵局，[解款行代號]必須為7000021</li>
                    <li>3.依據「臺灣銀行宜蘭分行扣收匯款手續費30元審核原則」，若解款行非「臺灣銀行」、「郵局」、「土地銀行」者，每筆須加收30元手續費</li>
                </ol>
            </div>
        </div>
    </div>
</div>

@code {
    private string dataMode = "single"; // "single" or "batch"

    // Form fields
    private string collectName = "";
    private string collectId = "";
    private string collectAcc = "";
    private string collectNo = "";
    private string financialName = "";
    private string tel = "";
    private string zip = "";
    private string addr = "";
    private decimal? remitPrice;
    private string ifFee = "否"; // "是" or "否"
    private string remitMemo = "";
    private string remitPurpose = ""; // 彙整單用途說明

    // Editing variables
    private int? editingSno = null;
    private decimal? editingPrice;
    private string editingIfFee = "否";
    private string editingMemo = "";
    
    // Autocomplete
    private List<string> suggestions = new();
    private bool showSuggestions = false;

    // Data list and state
    private List<ComRemitBlazor.Models.RemitedList> remitList = new();
    private bool isLoading = true;
    private int conSno;

    // Messages
    private string? errorMessage;
    private string? successMessage;
    
    // File upload
    private IBrowserFile? selectedFile;

    private System.Threading.Timer? debounceTimer;
    
    // public decimal TotalAmount => remitList.Sum(item => decimal.TryParse(item.RemitPrice, out var price) ? price : 0);
    public decimal TotalAmount => remitList.Sum(item => (decimal)(item.RemitPrice ?? 0));

    protected override async Task OnInitializedAsync()
    {
        conSno = await RemitService.GetNextConSnoAsync();
        await LoadRemitList();
        isLoading = false;
    }

    private async Task LoadRemitList()
    {
        isLoading = true;
        remitList = await RemitService.GetRemitListByConSnoAsync(conSno);
        isLoading = false;
        StateHasChanged();
    }

    private void OnDataModeChanged(ChangeEventArgs e)
    {
        dataMode = e.Value?.ToString() ?? "single";
    }

    private void OnCollectNameInput(ChangeEventArgs e)
    {
        collectName = e.Value?.ToString() ?? "";
        if (debounceTimer != null)
        {
            debounceTimer.Dispose();
        }
        debounceTimer = new System.Threading.Timer(async (_) =>
        {
            if (!string.IsNullOrWhiteSpace(collectName) && collectName.Length >= 2)
            {
                suggestions = await PayeeService.GetPayeeNameSuggestionsAsync(collectName);
                showSuggestions = suggestions.Any();
                await InvokeAsync(StateHasChanged);
            }
            else
            {
                showSuggestions = false;
                await InvokeAsync(StateHasChanged);
            }
        }, null, 300, System.Threading.Timeout.Infinite);
    }
    
    private void OnCollectNameBlur()
    {
        // Delay hiding suggestions to allow click event on suggestion to fire
        debounceTimer = new System.Threading.Timer(async (_) =>
        {
            showSuggestions = false;
            await InvokeAsync(StateHasChanged);
        }, null, 200, System.Threading.Timeout.Infinite);
    }

    private async Task SelectSuggestion(string name)
    {
        collectName = name;
        showSuggestions = false;
        var payee = await PayeeService.GetPayeeByNameAsync(name);
        if (payee != null)
        {
            collectId = payee.CollectId ?? string.Empty;
            collectAcc = payee.CollecAcc ?? string.Empty;
            collectNo = payee.CollectNo ?? string.Empty;
            if (!string.IsNullOrEmpty(payee.CollectNo))
            {
                financialName = await PayeeService.GetFinancialNameByNoAsync(payee.CollectNo);
            }
            else
            {
                financialName = "";
            }
            tel = payee.Tel ?? string.Empty;
            zip = payee.Zip ?? string.Empty;
            addr = payee.Addr ?? string.Empty;
        }
    }

    private void OnIfFeeChanged(ChangeEventArgs e)
    {
        ifFee = e.Value?.ToString() ?? "否";
    }

    private async Task AddRemitItem()
    {
        if (string.IsNullOrWhiteSpace(collectName) || string.IsNullOrWhiteSpace(collectAcc))
        {
            await JSRuntime.InvokeVoidAsync("alert", "請務必填寫【收款人戶名】與【收款人帳號】");
            return;
        }

        if (!remitPrice.HasValue || remitPrice.Value <= 0)
        {
            await JSRuntime.InvokeVoidAsync("alert", "帳款金額必須是大於 0 的有效數字。");
            return;
        }

        var newRemitItem = new ComRemitBlazor.Models.RemitedList()
        {
            ConSno = conSno,
            CollecName = collectName,
            CollectId = collectId,
            CollecAcc = collectAcc,
            CollectNo = collectNo,
            RemitPrice =(int)remitPrice.Value,
            IfFee = ifFee,
            RemitMemo = remitMemo,
            ConPer = "User", // Placeholder for actual user
            ConDate = DateTime.Now,
            Kindno = 1
        };

        try
        {
            await RemitService.AddRemitItemAsync(newRemitItem);
            ClearForm();
            await LoadRemitList();
        }
        catch (Exception ex)
        {
            errorMessage = $"新增失敗: {ex.Message}";
        }
    }

    private void ClearForm()
    {
        collectName = "";
        collectId = "";
        collectAcc = "";
        collectNo = "";
        financialName = "";
        tel = "";
        zip = "";
        addr = "";
        remitPrice = null;
        ifFee = "否";
        remitMemo = "";
    }

    private async Task DeleteItem(int sno)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "確定要刪除此筆資料嗎?");
        if (confirmed)
        {
            await RemitService.DeleteRemitItemAsync(sno);
            await LoadRemitList();
        }
    }

    private void StartEdit(int sno)
    {
        var itemToEdit = remitList.FirstOrDefault(r => r.Sno == sno);
        if (itemToEdit != null)
        {
            editingSno = sno;
           if (itemToEdit.RemitPrice.HasValue) // 或者 itemToEdit.RemitPrice != null
            {
                // RemitPrice 有值，將其 int 值轉換為 decimal
                editingPrice = itemToEdit.RemitPrice.Value;
            }
            else
            {
                // RemitPrice 為 null，直接賦予 null
                editingPrice = null;
            }
            editingIfFee = itemToEdit.IfFee ?? "否";
            editingMemo = itemToEdit.RemitMemo ?? "";
        }
    }

    private async Task SaveEdit(int sno)
    {
        var itemToUpdate = await RemitService.GetRemitItemBySnoAsync(sno);
        if (itemToUpdate != null)
        {
            if (!editingPrice.HasValue || editingPrice.Value <= 0)
            {
                 await JSRuntime.InvokeVoidAsync("alert", "請輸入有效的【帳款金額】");
                return;
            }
            
            itemToUpdate.RemitPrice = (int)editingPrice.Value;
            itemToUpdate.IfFee = editingIfFee;
            itemToUpdate.RemitMemo = editingMemo;

            await RemitService.UpdateRemitItemAsync(itemToUpdate);
            CancelEdit();
            await LoadRemitList();
        }
    }

    private void CancelEdit()
    {
        editingSno = null;
        editingPrice = null;
        editingIfFee = "否";
        editingMemo = "";
    }
    
    private void HandleFileSelected(InputFileChangeEventArgs e)
    {
        selectedFile = e.File;
        errorMessage = null;
        successMessage = null;
    }

    private async Task ProcessBatchUpload()
    {
        if (selectedFile == null)
        {
            errorMessage = "請先選擇要上傳的檔案。";
            return;
        }

        isLoading = true;
        StateHasChanged();

        try
        {
            var batchItems = new List<ComRemitBlazor.Models.RemitedList>();
            var stream = selectedFile.OpenReadStream(maxAllowedSize: 5 * 1024 * 1024); // 5MB limit
            using var reader = new System.IO.StreamReader(stream, System.Text.Encoding.Default);
            string? line;
            await reader.ReadLineAsync(); // Skip header row

            while ((line = await reader.ReadLineAsync()) != null)
            {
                try
                {
                    var values = line.Split(',');
                    if (values.Length < 3) continue;

                    var newRemitItem = new ComRemitBlazor.Models.RemitedList()
                    {
                        ConSno = conSno,
                        RemitPrice = decimal.TryParse(values[2].Trim(), out var price) ? (int)price : (int?)null,
                        IfFee = (values.Length > 3 && (values[3].Trim() == "是" || values[3].Trim() == "1")) ? "是" : "否",
                        RemitMemo = values.Length > 4 ? values[4].Trim() : "",
                        ConPer = "BatchUser",
                        ConDate = DateTime.Now,
                        Kindno = 1
                    };

                    ComRemitBlazor.Models.Payee? payee = await PayeeService.GetPayeeByNameAndAccountAsync(
                        values[0].Trim(),
                        values[1].Trim().Replace("-", "")
                    );

                    if (payee != null)
                    {
                        newRemitItem.CollecName = payee.CollectName; // <-- FIX: Changed from CollecName to CollectName
                        newRemitItem.CollecAcc = payee.CollecAcc;
                        newRemitItem.CollectId = payee.CollectId;
                        newRemitItem.CollectNo = payee.CollectNo;
                    }
                    else
                    {
                        newRemitItem.CollecName = values[0].Trim();
                        newRemitItem.CollecAcc = values[1].Trim();
                    }

                    batchItems.Add(newRemitItem);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error processing line: {line}. Error: {ex.Message}");
                }
            }

            if (batchItems.Any())
            {
                await RemitService.AddRemitItemsAsync(batchItems);
                await LoadRemitList();
                successMessage = $"成功匯入 {batchItems.Count} 筆資料。";
            }
            else
            {
                errorMessage = "CSV 檔案中沒有可匯入的有效資料。";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"處理檔案時發生錯誤: {ex.Message}";
        }
        finally
        {
            isLoading = false;
            selectedFile = null;
        }
    }
    
    private void GoToAdminPage()
    {
        if (remitList.Any())
        {
            Navigation.NavigateTo($"/adminlist?searchId={conSno}");
        }
        else
        {
            Navigation.NavigateTo("/adminlist");
        }
    }
    
    private async Task StartNewBatch()
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "確定要放棄目前所有資料，並開啟一個新的彙整編號嗎?");
        if (confirmed)
        {
            conSno = await RemitService.GetNextConSnoAsync();
            await LoadRemitList();
            successMessage = $"已開啟新的彙整編號: {conSno}";
        }
    }



    /// <summary>
    /// 完成彙整 - 將當前資料標記為已彙整並導向已彙整清單
    /// </summary>
    private async Task CompleteRemit()
    {
        if (!remitList.Any())
        {
            await JSRuntime.InvokeVoidAsync("alert", "沒有資料可以彙整！");
            return;
        }

        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
            $"確定要完成彙整編號 {conSno} 的所有資料嗎？\n" +
            $"共 {remitList.Count} 筆資料，總金額 {TotalAmount:N2} 元\n" +
            "完成後資料將移至已彙整清單，無法再修改！");

        if (confirmed)
        {
            try
            {
                var success = await RemitService.CompleteRemitAsync(conSno);
                if (success)
                {
                    successMessage = $"彙整編號 {conSno} 已完成彙整！資料已送至已彙整清單。";
                    await JSRuntime.InvokeVoidAsync("alert", "彙整完成！您可以點擊「查看已彙整清單」查看結果。");
                    StateHasChanged();
                }
                else
                {
                    errorMessage = "彙整失敗，請稍後再試。";
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"彙整時發生錯誤：{ex.Message}";
            }
        }
    }

    /// <summary>
    /// 導向已彙整清單頁面
    /// </summary>
    private void GoToRemitedList()
    {
        Navigation.NavigateTo("/remitedlist");
    }

    /// <summary>
    /// 彙整所有資料
    /// </summary>
    private async Task CompleteAllItems()
    {
        if (!remitList.Any())
        {
            await JSRuntime.InvokeVoidAsync("alert", "沒有資料可以彙整！");
            return;
        }

        // 驗證彙整單用途說明
        if (string.IsNullOrWhiteSpace(remitPurpose))
        {
            await JSRuntime.InvokeVoidAsync("alert", "請填寫【彙整單用途說明】！");
            return;
        }

        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
            $"確定要彙整所有 {remitList.Count} 筆資料嗎？\n" +
            $"總金額 {TotalAmount:N2} 元\n" +
            $"用途說明：{remitPurpose}\n" +
            "彙整後資料將移至已彙整清單！");

        if (confirmed)
        {
            try
            {
                var allSnos = remitList.Select(x => x.Sno).ToList();
                var success = await RemitService.CompleteSelectedRemitAsync(allSnos, remitPurpose);
                if (success)
                {
                    successMessage = $"已成功彙整 {remitList.Count} 筆資料！";
                    await LoadRemitList(); // 重新載入資料
                    remitPurpose = ""; // 清空用途說明
                    await JSRuntime.InvokeVoidAsync("alert", "彙整完成！");
                    StateHasChanged();
                }
                else
                {
                    errorMessage = "彙整失敗，請稍後再試。";
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"彙整時發生錯誤：{ex.Message}";
            }
        }
    }

    public void Dispose()
    {
        debounceTimer?.Dispose();
    }
}