﻿namespace Intra2025.Components.Base
{
    public class ClientIpMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ClientIpService _clientIpService;

        public ClientIpMiddleware(RequestDelegate next, ClientIpService clientIpService)
        {
            _next = next;
            _clientIpService = clientIpService;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // 提取 IP 地址
            var xForwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            var remoteIp = context.Connection?.RemoteIpAddress?.ToString();

            //將 IP 存至 Middleware的變數 ClientIp 中
            _clientIpService.ClientIp = xForwardedFor ?? remoteIp ?? "未知IP"; 

            // 繼續處理請求管道
            await _next(context);
        }

    }
}
