﻿@page "/CareCaseList"
@inject YCRSDbContext _context
@using Intra2025.Components.Base
@using Intra2025.Data
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Forms
@using Intra2025.Models
@using Microsoft.EntityFrameworkCore
@using static Intra2025.Components.Base.JsonDataLoader
@using static Intra2025.Components.Base.BaseFunc
@using Microsoft.AspNetCore.WebUtilities
@using System.Web
@using Components.Shared
@using Microsoft.Extensions.Logging
@inherits BasePageComponent
<title>溫馨關懷表(資料清單)</title>

<style>
    table thead tr th {
        text-align: center;
        vertical-align: middle;
        background-color: #d5e1df !important;
        color: #142d4c
    }
</style>

<!-- 守護寶貝即時通 Title -->
<div style="display: flex;justify-content: center;align-items: center;">
    <div class="loader3"></div>
</div>


<div class="mb-3 d-flex justify-content-between align-items-center" style="margin-left: 2rem;">
    <!-- 使用者名稱區塊 -->
    <div>
        <span class="badge text-white" style="background-color:#127681; border-radius: 10px;font-size:14px">
            【@_userState.UnitName-@_userState.UserName】
        </span>
        <a href="@(Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/'))/ChildCaseList"
           class="text-decoration-none text-primary fw-bold">
            守護寶貝即時通
        </a> 
        @if (_userState.IsAdmin)
        {
            <a href="@(Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/'))/YCRS_AccessLogList"
               class="text-decoration-none text-primary fw-bold">
                歷程資料查詢
            </a>
            <a href="@(Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/'))/YCRS_ReportList"
               class="text-decoration-none text-primary fw-bold">
                案件統計表
            </a>
        }
    </div>
</div>

<!-- 日期選擇與搜尋區塊 -->
<div class="d-flex flex-wrap align-items-center border p-3 rounded bg-light" style="width:1150px; margin-left: 2rem; margin-bottom:0.5rem">
    <!-- 功能按鈕區塊 -->
    <div class="d-flex align-items-center">
        <button class="btn btn-primary me-3" style="background-color:cornflowerblue; border-radius: 10px; width:60px; height:30px;" @onclick="CreateNewRecord">
            新增
        </button>
    </div>
    <!-- 日期選擇 -->
    <div class="me-3" style="width:150px">
        <label for="startDate" class="form-label mb-0 fw-bold">案件起始日期：</label>
        <InputDate TValue="DateTime?" @bind-Value="dt1" id="startDate" class="form-control" style="width: 140px;" />
    </div>

    <div class="me-3" style="width:150px">
        <label for="endDate" class="form-label mb-0 fw-bold">案件結束日期：</label>
        <InputDate TValue="DateTime?" @bind-Value="dt2" id="endDate" class="form-control" style="width: 140px;" />
    </div>


    @if (_userState.IsAdmin)
    {
        <!-- 單位選擇（多選下拉） -->
        <div class="me-3" style="width:210px">
            <label for="unitSelect" class="form-label mb-0 fw-bold">選擇戶所：</label><span style="font-size:11px;color:cornflowerblue">多選請用「ctrl」鍵 + 單位</span>
            <InputSelectMultiple @bind-Value="SelectedUnits" AllValues="@Belongs.Select(u => u.Id).ToList()" class="form-select" multiple style="width: 180px; height: 80px">
                @* 全選選項已由元件自動產生 *@
                @foreach (var unit in Belongs)
                {
                    <option value="@unit.Id">@unit.Name</option>
                }
            </InputSelectMultiple>
        </div>
    }


    <!-- 搜尋輸入框 -->
    <div class="flex-grow-1 me-3">
        <input type="text" @bind="searchTerm" placeholder="搜尋「系統代碼」、或「主要照顧者」"
               class="form-control" style="width: 310px;font-size: 12px;" />
    </div>

    <!-- 搜尋按鈕 -->
    <div class="d-flex">
        <button class="btn btn-outline-primary me-2" @onclick="Search">
            搜尋
        </button>
        <button class="btn btn-outline-secondary" @onclick="Clear">
            清除搜尋
        </button>
    </div>
</div>


<table class="table table-striped">
    <thead style="line-height: 22px;background-color:darkgrey">
        <tr>
            <th style="width: 20px;">序號</th>
            <th style="width: 30px;">系統代碼</th>
            <th style="width: 70px;">兒童主要照顧者</th>
            <th style="width: 220px;">個案類型</th>
            <th style="width: 80px;">是否訪視關懷</th>
            <th style="width: 80px;">是否須家事商談</th>
            <th style="width: 60px;">案件所屬分份</th>
            <th style="width: 60px;">附件</th>
            <th style="width: 80px;">案件所屬戶所</th>
            <th style="width: 90px;">
                確認狀態
                <div style="margin-top: 5px; margin-bottom: 5px; border-bottom: 2px solid #ccc;"></div> <!-- 添加分隔線 -->
                維護
            </th>
        </tr>
    </thead>
    <tbody>
        @if (FilteredRecords != null && FilteredRecords.Any())
        {
            int index = 1;
            @foreach (var record in pagedRecords)
            {
                <tr>
                    <td style="text-align: center">@index</td>
                    <td style="text-align: center">@record.Id</td>
                    <td style="text-align: center">@record.Caregiver</td>
                    <td style="text-align: center">@GetCategoryName(@record.AttributesId)</td>
                    <td style="text-align: center">
                        @(record.Visitcare ? "V" : " ")
                    </td>
                    <td style="text-align: center">
                        @(record.FamilyMediation ? "V" : " ")
                    </td>
                    <td style="text-align: center">
                        @{
                            DateTime belongDateTime = @record.BelongDate.HasValue
                            ? @record.BelongDate.Value.Date
                            : DateTime.MinValue;

                            string displayDate = (belongDateTime == DateTime.MinValue)
                            ? "無資料"
                            : $"{belongDateTime.Year - 1911:D3}-{belongDateTime.Month:D2}"; // 民國年格式
                            @displayDate
                        }
                    </td>
                    <td style="text-align: center">
                        @if (YCRSFiles.TryGetValue(record.Id ?? "0", out var files) && files.Any())
                        {
                            // 取得最新一筆資料（假設 FileDate 表示檔案的日期）
                            var latestFile = files.OrderByDescending(f => f.Createdate).FirstOrDefault();

                            if (latestFile != null)
                            {
                                <span style='font-size: 14px;font-family: DFKai-SB, BiauKai, sans-serif;'>
                                    <a href="@($"{(Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/') ?? "")}/{(latestFile.Filepath?.TrimStart('/') ?? "")}")">
                                        下截關懷表
                                    </a>
                                </span>
                            }
                        }
                    </td>
                    <td>
                        @GetBelongName(@record.CaseBelongId)
                    </td>
                    <td style="text-align: center">
                        <div>@GetStatusMessage(@record.IsChecked)</div> <!--回傳回報狀態-->
                        @if (_userState.DepCode == "110015" || _userState.Account == "stan1217")
                        {
                            <div>
                                <button style="font-size: 8pt;" @onclick="() => HandleRadioChange(record)">變更【確認狀態】</button>
                            </div>
                        }
                        <div style="margin: 0; padding: 0;line-height: 0;"><hr></div>
                        <div style="margin: 0; padding: 0;line-height: 0;">
                            <button type="button" class="btn btn-warning btn-sm" @onclick="() => EditRecord(record.Id)">編輯</button>
                            <button class="btn btn-danger btn-sm" @onclick="() => ConfirmDelete(record.Id)" disabled="@(record.IsChecked == true)">刪除</button>
                        </div>
                    </td>
                </tr>
                index++;
            }
        }
        else
        {
            <tr>
                <td colspan="9" style="text-align: center">無資料</td>
            </tr>
        }
    </tbody>

</table>

<div class="pagination">
    <div class="pagination-controls">
        <button @onclick="PreviousPage" disabled="@(currentPage == 1)">上一頁</button>
        <span>第 @currentPage 頁</span><span>【共 @totalPages 頁】(案件總數量: @totalRecordCount)</span>
        <button @onclick="NextPage" disabled="@(currentPage == totalPages)">下一頁</button>
    </div>
    <button class="new-link-button" @onclick="ExportDataAsync">匯出ODS檔</button>
</div>

<!-- 刪除確認 Modal -->
<div class="modal" tabindex="-1" role="dialog" style="display: @(ShowDeleteModal ? "block" : "none")">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <p>您確定要刪除此筆資料嗎？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" @onclick="DeleteRecord">確認</button>
                <button type="button" class="btn btn-secondary" @onclick="CloseDeleteModal">取消</button>
            </div>
        </div>
    </div>
</div>
<!-- 錯誤訊息 Modal -->
<div class="modal" tabindex="-1" role="dialog" style="display: @(ShowErrorModal ? "block" : "none")">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <p>@ErrorMessage</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" @onclick="CloseErrorModal">關閉</button>
            </div>
        </div>
    </div>
</div>

<script>
    function showToastAndDownload(message, filePath) {
    alert(message);

    if (filePath) {
    const link = document.createElement('a');
    link.href = filePath + "?v=" + new Date().getTime(); // 添加查詢參數避免快取
    console.log("下載路徑：", filePath);

    link.download = filePath.split('/').pop();
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    } else {
    console.error("檔案路徑無效，無法下載");
    }
    }
</script>

@code {

    private int currentPage = 1;
    private int pageSize = 10;
    private int totalRecordCount = 0;
    private int totalPages => (int)Math.Ceiling((double)FilteredRecords.Count / pageSize);
    private string searchTerm = "";
    private string clientIp = "";
    private bool ShowDeleteModal { get; set; } = false;
    private string DeleteRecordId { get; set; } = "";
    private List<Category> Categories = new(); // JSON 中的「分類資料」
    private List<CaseBelong> Belongs = new(); // JSON 中的「12間戶所資料」
    private List<string> Admins = new List<string>();  // JSON 中的「管理者帳號」
    private DateTime? dt1, dt2;
    private bool ShowErrorModal = false; // 控制錯誤 Modal 是否顯示
    private string ErrorMessage = string.Empty; // 錯誤訊息內容
    private bool hasSearched = false;

    private List<YCRS_CareRecord> CareRecords = new();
    private List<YCRS_CareRecord> pagedRecords = new List<YCRS_CareRecord>();
    private List<YCRS_CareRecord> FilteredRecords = new List<YCRS_CareRecord>();
    private Dictionary<string, List<YCRS_Files>> YCRSFiles = new Dictionary<string, List<YCRS_Files>>();
    private List<string> SelectedUnits { get; set; } = new List<string>(); // 反序列化 JSON 資料到物件
    private List<string> AvailableUnits = new List<string>(); // 反序列化 JSON 資料到物件

    [Parameter] public EventCallback OnCreateNewRecord { get; set; }

    [Inject]
    protected IWebHostEnvironment env { get; set; } = null!;
    [Inject]
    protected ILoggerFactory _loggerFactory { get; set; } = null!;


    protected override async Task OnInitializedAsync()
    {
        // 解析 QueryString
        var uri = NavigationManager.ToAbsoluteUri(NavigationManager.Uri);
        var query = QueryHelpers.ParseQuery(uri.Query);
        if (query.TryGetValue("searchTerm", out var qSearchTerm))
            searchTerm = qSearchTerm;
        if (query.TryGetValue("dt1", out var qDt1) && DateTime.TryParse(qDt1, out var parsedDt1))
            dt1 = parsedDt1;
        if (query.TryGetValue("dt2", out var qDt2) && DateTime.TryParse(qDt2, out var parsedDt2))
            dt2 = parsedDt2;
        if (query.TryGetValue("currentPage", out var qPage) && int.TryParse(qPage, out var parsedPage))
            currentPage = parsedPage;

        //抓取Client IP
        clientIp = ClientIpService.ClientIp;

        // 從資料庫中抓取 YCRS_CareRecord 依角色篩選資料
        if (_userState.IsAdmin)
        {
            // Admin 取得所有資料
            CareRecords = await _context.YCRS_CareRecord
                .OrderByDescending(record => record.Id) // 按 Id 降冪排序
                .ToListAsync();
        }
        else
        {
            // User 只取得與所屬單位匹配的資料
            CareRecords = await _context.YCRS_CareRecord
                .Where(r => r.CaseBelongId == _userState.UnitCode)
                .OrderByDescending(record => record.Id) // 按 Id 降冪排序
                .ToListAsync();
        }


        // 初始化時將所有資料顯示
        FilteredRecords = CareRecords;
        totalRecordCount = CareRecords.Count; // 計算資料總筆數

        // 初始化分頁資料
        UpdatePaged();

        // 載入 JSON 資料，使用 JsonDataLoader 類別
        var jsonDataLoader = new JsonDataLoader(env);
        Categories = await jsonDataLoader.LoadCategoriesFromJsonAsync("B");
        Belongs = await jsonDataLoader.LoadCaseBelongFromJsonAsync();
        Admins = await jsonDataLoader.GetAdminAccountsAsync();

        AvailableUnits = Belongs?.Select(u => u.Name).ToList() ?? new List<string>();
        // 替換為您的單位列表

        // 預設全選，將所有單位的 Id 加入到 SelectedUnits
        SelectedUnits = Belongs.Select(unit => unit.Id).ToList();

        //抓取報告檔
        var files = await _context.YCRS_Files.ToListAsync();
        foreach (var file in files)
        {
            if (!YCRSFiles.ContainsKey(file.Id.ToString()))
            {
                YCRSFiles[file.Id.ToString()] = new List<YCRS_Files>();
            }
            YCRSFiles[file.Id.ToString()].Add(file);
        }

        FilterRecords(); // 解析完 QueryString 後立即篩選，確保還原查詢條件
    }

    private void HandleRadioChange(YCRS_CareRecord record)
    {
        try
        {
            record.IsChecked = !record.IsChecked;
            // 將變更寫回資料庫
            _context.YCRS_CareRecord.Update(record);
            _context.SaveChangesAsync(); // 非同步保存
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in HandleRadioChange: {ex.Message}");
        }
    }

    private async Task CreateNewRecord()
    {
        // 導航到新增記錄的頁面，帶上查詢參數
        var query = $"?searchTerm={HttpUtility.UrlEncode(searchTerm)}&dt1={(dt1.HasValue ? dt1.Value.ToString("yyyy-MM-dd") : "")}&dt2={(dt2.HasValue ? dt2.Value.ToString("yyyy-MM-dd") : "")}&currentPage={currentPage}";
        NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/CareCaseNewEdit/{query}", true);
    }

    private void EditRecord(string id)
    {
        // 編輯時帶上查詢參數
        var query = $"?searchTerm={HttpUtility.UrlEncode(searchTerm)}&dt1={(dt1.HasValue ? dt1.Value.ToString("yyyy-MM-dd") : "")}&dt2={(dt2.HasValue ? dt2.Value.ToString("yyyy-MM-dd") : "")}&currentPage={currentPage}";
        NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/CareCaseNewEdit/{id}{query}", true);
    }

    private void ConfirmDelete(string id)
    {
        DeleteRecordId = id;
        ShowDeleteModal = true;
        StateHasChanged();
    }

    private void CloseDeleteModal()
    {
        ShowDeleteModal = false;
    }

    private async Task DeleteRecord()
    {
        var record = await _context.YCRS_CareRecord.FindAsync(DeleteRecordId);
        if (record != null)
        {
            // 確認是否為同單位
            if (_userState.IsAdmin || record.CaseBelongId == _userState.UnitCode)
            {
                await AccessLogHelper.SaveAccessLogAsync(_context, DeleteRecordId, "刪除", _userState.Account, clientIp); // 寫入Log
                _context.YCRS_CareRecord.Remove(record);
                await _context.SaveChangesAsync();
                NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/CareCaseList", true);
            }
            else
            {
                // 無權限刪除，顯示錯誤訊息
                ErrorMessage = "您所刪除的資料非屬貴單位，故無法刪除。";
                ShowErrorModal = true;
            }
        }
        else
        {
            // 記錄不存在，顯示錯誤訊息
            ErrorMessage = $"記錄 {DeleteRecordId} 不存在，無法刪除。";
            ShowErrorModal = true;
        }
        NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/CareCaseList", true);
    }

    private MarkupString GetStatusMessage(bool isChecked)
    {
        // 根據 isChecked 傳入的值回傳對應的 HTML 標籤與字體顏色
        string color = isChecked ? "gray" : "red";
        string message = isChecked ? "已確認" : "未確認";

        // 包裝成 HTML 格式並回傳 MarkupString 讓 Blazor 正確渲染
        return (MarkupString)$"<span style='color: {color};'><font size='1px'>{message}</font></span>";
    }

    // 根據 AttributesId 查找對應的中文名稱
    private string GetCategoryName(string? attributesId)
    {
        var category = Categories.FirstOrDefault(c => c.Id == attributesId);
        return category?.Name ?? "無此分類";
    }

    // 根據 CaseBelongId 查找對應的中文名稱
    private string GetBelongName(string? belongsId)
    {
        var belongs = Belongs.FirstOrDefault(c => c.Id == belongsId);
        return belongs?.Name ?? "無此分類";
    }

    private void Search()
    {
        currentPage = 1;
        hasSearched = true;
        FilterRecords();
    }

    private void Clear()
    {
        searchTerm = "";
        dt1 = null;
        dt2 = null;
        currentPage = 1;
        SelectedUnits = Belongs.Select(unit => unit.Id).ToList(); // 清除搜尋時戶所全選
        hasSearched = true;
        FilterRecords();
    }

    private void FilterRecords()
    {
        if (_userState.IsAdmin)
        {
            // Admin 可以看到所有資料
            FilteredRecords = CareRecords
                .Where(r =>
                    // 搜尋條件的判斷(r =>
                    SelectedUnits.Contains(r.CaseBelongId) && // 單位比對條件，支援多選單位
                    (string.IsNullOrWhiteSpace(searchTerm) ||
                     (r.Caregiver?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false) ||
                     r.Id.ToString().Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                    &&
                    // 日期篩選條件
                    (!dt1.HasValue && !dt2.HasValue ||
                     (r.BelongDate.HasValue &&
                      (!dt1.HasValue || r.BelongDate >= dt1) &&
                      (!dt2.HasValue || r.BelongDate <= dt2)))
                )
                .ToList();
        }
        else
        {
            // User 只能看到與所屬單位匹配的資料
            FilteredRecords = CareRecords
                .Where(r =>
                    SelectedUnits.Contains(r.CaseBelongId) && // 單位比對條件，支援多選單位
                    (string.IsNullOrWhiteSpace(searchTerm) ||
                     (r.Caregiver?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false) ||
                     r.Id.ToString().Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                    &&
                    // 日期篩選條件
                    (!dt1.HasValue && !dt2.HasValue ||
                     (r.BelongDate.HasValue &&
                      (!dt1.HasValue || r.BelongDate >= dt1) &&
                      (!dt2.HasValue || r.BelongDate <= dt2)))
                )
                .ToList();
        }

        totalRecordCount = FilteredRecords.Count; // 計算資料總筆數
        UpdatePaged(); // 更新分頁資料

        if (totalRecordCount == 0 && hasSearched)
        {
            ErrorMessage = "目前無任何符合條件的資料可供篩選。";
            ShowErrorModal = true;
        }
    }

    private void UpdatePaged()
    {
        pagedRecords = FilteredRecords
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize)
            .ToList();
    }

    private void PreviousPage()
    {
        if (currentPage > 1)
        {
            currentPage--;
            UpdatePaged();
        }
    }

    private void NextPage()
    {
        if (currentPage < totalPages)
        {
            currentPage++;
            UpdatePaged();
        }
    }

    public async Task ExportDataAsync()
    {
        // 確保有 ILogger 實例
        var logger = _loggerFactory.CreateLogger<ExportODFService<CareCaseList>>();
        // 先產生 CSV 檔案
        var exporter = new ExportODFService<CareCaseList>(env, logger);


        var fileBaseName = DateTime.Now.ToString("MMdd-hhmmss") + "_溫馨關懷表彙整名冊";
        var csvFileName = fileBaseName + ".csv";
        var odsFileName = fileBaseName + ".ods";
        string csvFilePath = Path.Combine("wwwroot", "exports", csvFileName);
        string odsFilePath = Path.Combine("wwwroot", "exports", odsFileName);

        exporter.ExportToCareRecord(FilteredRecords, csvFilePath);
        exporter.ConvertCsvToOds(csvFilePath, odsFilePath);

        // 顯示通知訊息並下載 ODS
        if (File.Exists(odsFilePath))
        {
            await JS.InvokeVoidAsync("showToastAndDownload", "ODS檔已產生成功", "/exports/" + odsFileName);
            await Task.Delay(3000); //3s 後刪除檔案
            try { File.Delete(csvFilePath); File.Delete(odsFilePath); } catch { }
        }
        else
        {
            await JS.InvokeVoidAsync("showToastAndDownload", "ODS檔案生成失敗，請重試", "");
        }
    }

    // 關閉錯誤訊息 Modal
    private void CloseErrorModal()
    {
        ShowErrorModal = false;
        ErrorMessage = string.Empty;
    }
}