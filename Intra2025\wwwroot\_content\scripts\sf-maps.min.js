/*!*  filename: sf-maps.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[37],{"./bundles/sf-maps.js":function(e,t,i){"use strict";i.r(t);i("./modules/sf-maps.js")},"./modules/sf-maps.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Maps=function(){"use strict";var e=function(e,t,i,n){return new(i||(i=Promise))((function(s,o){function a(e){try{l(n.next(e))}catch(e){o(e)}}function r(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){e.done?s(e.value):new i((function(t){t(e.value)})).then(a,r)}l((n=n.apply(e,t||[])).next())}))},t=function(e,t){var i,n,s,o,a={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]};return o={next:r(0),throw:r(1),return:r(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function r(o){return function(r){return function(o){if(i)throw new TypeError("Generator is already executing.");for(;a;)try{if(i=1,n&&(s=2&o[0]?n.return:o[0]?n.throw||((s=n.return)&&s.call(n),0):n.next)&&!(s=s.call(n,o[1])).done)return s;switch(n=0,s&&(o=[2&o[0],s.value]),o[0]){case 0:case 1:s=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,n=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(s=a.trys,(s=s.length>0&&s[s.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!s||o[1]>s[0]&&o[1]<s[3])){a.label=o[1];break}if(6===o[0]&&a.label<s[1]){a.label=s[1],s=o;break}if(s&&a.label<s[2]){a.label=s[2],a.ops.push(o);break}s[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],n=0}finally{i=s=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,r])}}},i=document.createElementNS("http://www.w3.org/2000/svg","rect"),n=function(e,t){this.x=e,this.y=t};function s(e){var t=document.createElementNS("http://www.w3.org/2000/svg","g");return t.setAttribute("id",e),t}function o(e,t,i){var n="";switch(e){case"Balloon":n="M15,0C8.8,0,3.8,5,3.8,11.2C3.8,17.5,9.4,24.4,15,30c5.6-5.6,11.2-12.5,11.2-18.8C26.2,5,21.2,0,15,0z M15,16c-2.8,0-5-2.2-5-5s2.2-5,5-5s5,2.2,5,5S17.8,16,15,16z";break;case"Cross":n="M "+i.x+" "+(i.y-t.height/2)+" L "+i.x+" "+(i.y+t.height/2)+" M "+(i.x-t.width/2)+" "+i.y+" L "+(i.x+t.width/2)+" "+i.y;break;case"Diamond":n="M "+i.x+" "+(i.y-t.height/2)+" L "+(i.x+t.width/2)+" "+i.y+" L "+i.x+" "+(i.y+t.height/2)+" L "+(i.x-t.width/2)+" "+i.y+" Z";break;case"Star":n="M "+(i.x+t.width/3)+" "+(i.y-t.height/2)+" L "+(i.x-t.width/2)+" "+(i.y+t.height/6)+" L "+(i.x+t.width/2)+" "+(i.y+t.height/6)+" L "+(i.x-t.width/3)+" "+(i.y-t.height/2)+" L "+i.x+" "+(i.y+t.height/2)+" L "+(i.x+t.width/3)+" "+(i.y-t.height/2)+" Z";break;case"Triangle":n="M "+i.x+" "+(i.y-t.height/2)+" L "+(i.x+t.width/2)+" "+(i.y+t.height/2)+" L "+(i.x-t.width/2)+" "+(i.y+t.height/2)+" Z";break;case"HorizontalLine":n=" M "+(i.x-t.width/2)+" "+i.y+" L "+(i.x+t.width/2)+" "+i.y;break;case"VerticalLine":n="M "+i.x+" "+(i.y-t.height/2)+" L "+i.x+" "+(i.y+t.height/2);break;case"InvertedTriangle":n="M "+(i.x-t.width/2)+" "+(i.y-t.height/2)+" L "+(i.x+t.width/2)+" "+(i.y-t.height/2)+" L "+i.x+" "+(i.y+t.height/2)+" Z";break;case"Pentagon":for(var s=void 0,o=void 0,a=0;a<5;a++)s=t.width/2*Math.cos(Math.PI/180*(72*a)),o=t.height/2*Math.sin(Math.PI/180*(72*a)),n+=(0===a?"M ":"L ")+(i.x+s)+" "+(i.y+o);n+=" Z"}return n}var a=function(){function a(e,t,i,n){this.previousMarkerId="",this.previousId="",this.moveClientX=0,this.moveClientY=0,this.clientX=0,this.clientY=0,this.isPinch=!1,this.enableSelectionZoom=!1,this.allowPanning=!1,this.isPanning=!1,this.isPan=!0,this.zoomClick=!1,this.mouseClick=!1,this.position=null,this.height=0,this.width=0,this.mapsBorderWidth=0,this.toolbarButtonOpacity=1,this.toolbarShapeOpacity=1,this.markerClusterExpandCheck=!1,this.svgCreated=!1,this.zoomIn=1,this.zoomOut=1,this.scaleFactor=1,this.factorCount=0,this.svg=document.createElementNS("http://www.w3.org/2000/svg","svg"),this.lastScale=1,this.clusterLocation=[],this.isTouch=!1,this.touchMoveList=[],this.touchStartList=[],this.tileTranslatePoint=null,this.isToolbarTooltip=!1,this.marginTop=0,this.marginLeft=0,this.isPinchZoomed=!1,this.panComplete=!1,this.pinchComplete=!1,this.mouseMoveId="",this.isReset=!1,window.sfBlazor=window.sfBlazor,this.tapCount=0,this.isMarkerDrag=!1,this.allowMarkerDragStartEvent=!1,this.id=e,this.element=t,this.dotNetRef=n,this.layerCount=i.layerCount,this.clusterSettings=i.markerCluster?JSON.parse(i.markerCluster):null,this.dataSource=i.dataSource?JSON.parse(i.dataSource):null,this.polygonDataSource=i.polygonDataSource?JSON.parse(i.polygonDataSource):null,this.shapeTranslatePoint=i.shapeTranslatePoint?i.shapeTranslatePoint:null,this.shapeBorderWidth=i.shapeBorderWidth,this.projectionType=i.projectionType,this.options=i,this.options.layerHighlightSettings=JSON.parse(this.options.layerHighlightSettings),this.options.markerHighlightSettings=JSON.parse(this.options.markerHighlightSettings),this.options.bubbleHighlightSettings=JSON.parse(this.options.bubbleHighlightSettings),this.options.navigationHighlightSettings=JSON.parse(this.options.navigationHighlightSettings),this.options.polygonHighlightSettings=JSON.parse(this.options.polygonHighlightSettings),this.options.selectionSettings=JSON.parse(this.options.selectionSettings),this.marginLeft=0,this.urlTemplate=i.urlTemplate?i.urlTemplate:null,this.isToolbarTooltip=!1,this.isTouch=!1,this.pinchFactor=1,this.lastScale=1,this.touchStartList=[],this.touchMoveList=[],this.dataId=e,window.sfBlazor.setCompInstance(this)}return a.prototype.wireEvents=function(){sf.base.EventHandler.add(this.element,"wheel",this.mapMouseWheel.bind(this),this),sf.base.EventHandler.add(this.element,"touchmove mousemove",this.mouseMove.bind(this),this),sf.base.EventHandler.add(this.element,"touchend mouseup mouseleave",this.mouseUp.bind(this),this),sf.base.EventHandler.add(this.element,"touchstart mousedown",this.mouseDown.bind(this),this),sf.base.EventHandler.add(this.element,"click pointerdown",this.click.bind(this),this),sf.base.EventHandler.add(this.element,"dblclick",this.doubleClick.bind(this),this),sf.base.EventHandler.add(this.element,"contextmenu",this.rightClick.bind(this),this),sf.base.EventHandler.add(this.element,"keydown",this.keyDown.bind(this),this),sf.base.EventHandler.add(this.element,"keyup",this.keyUp.bind(this),this),window.addEventListener("resize",this.reSize.bind(this))},a.prototype.unwireEvents=function(){sf.base.EventHandler.remove(this.element,"wheel",this.mapMouseWheel),sf.base.EventHandler.remove(this.element,"touchmove mousemove",this.mouseMove),sf.base.EventHandler.remove(this.element,"touchend mouseup mouseleave",this.mouseUp),sf.base.EventHandler.remove(this.element,"touchstart mousedown",this.mouseDown),sf.base.EventHandler.remove(this.element,"click pointerdown",this.click),sf.base.EventHandler.remove(this.element,"dblclick",this.doubleClick),sf.base.EventHandler.remove(this.element,"contextmenu",this.rightClick),sf.base.EventHandler.remove(this.element,"keydown",this.keyDown),sf.base.EventHandler.remove(this.element,"keyup",this.keyUp),window.removeEventListener("resize",this.reSize.bind(this))},a.prototype.destroy=function(){this.unwireEvents(),this.dotNetRef=null},a.prototype.keyUp=function(e){var t=e.target.id;if(this.options.enablePanning&&this.isPanning){this.isPanning=!1;var i=document.getElementById(this.element.id+"_animated_tiles");if(this.isTileMap&&!sf.base.isNullOrUndefined(this.currentTiles)&&!sf.base.isNullOrUndefined(i)&&this.currentTiles.childElementCount!=i.childElementCount&&this.currentTiles.childElementCount<i.childElementCount)for(var n=i.childElementCount-1;n>=this.currentTiles.childElementCount;n--)i.removeChild(i.children[n]);this.currentTiles=null,sf.base.isNullOrUndefined(this.dotNetRef)||this.dotNetRef.invokeMethodAsync("UpdateTranslatePoint",this.isTileMap?this.translatePoint:this.shapeTranslatePoint,this.tileTranslatePoint,this.scaleFactor,!1)}if("Tab"===e.code&&t.indexOf("_LayerIndex_")>-1){var s=parseInt(t.split("_LayerIndex_")[1].split("_")[0],10);if(t.indexOf("shapeIndex")>-1){this.targetElementPath=e.target;var o=parseInt(t.split("_shapeIndex_")[1].split("_")[0],10);t.indexOf("shapeIndex")>-1&&this.options.layerHighlightSettings[s]&&this.options.layerHighlightSettings[s].Enable&&(this.removeAllHighlight(),this.targetElementPath.classList.contains("mapShapeSelection")||sf.base.isNullOrUndefined(this.dotNetRef)||this.dotNetRef.invokeMethodAsync("TriggerShapeHighlight",s,o,t))}}},a.prototype.triggerEnableZoom=function(e,t,i){e>=1&&(this.scaleFactor=e,this.removeCluster(),sf.base.isNullOrUndefined(this.dotNetRef)||this.dotNetRef.invokeMethodAsync("TriggerZoom",t,i))},a.prototype.keyDown=function(e){var t=1,i=e.type,n=e.target.id;this.isReset=!1;var s=document.getElementById(this.element.id+"_animated_tiles");if(this.isTileMap&&!sf.base.isNullOrUndefined(s)&&(this.currentTiles=s.cloneNode(!0)),"+"===e.key||"Equal"===e.code)this.options.enableZoom&&(e.preventDefault(),this.mergeSeparation(),t=0===this.factorCount?this.options.factor+1:this.scaleFactor+1,this.factorCount++,this.triggerEnableZoom(t,"ZoomIn",i));else if("-"===e.key||"Minus"===e.code)this.options.enableZoom&&(e.preventDefault(),this.mergeSeparation(),t=0===this.factorCount?this.options.factor-1:this.scaleFactor-1,t=this.options.maxZoom>=t&&this.options.minZoom<=t?t:this.options.minZoom,this.factorCount++,this.triggerEnableZoom(t,"ZoomOut",i));else if(82===e.keyCode)this.options.enableZoom&&(e.preventDefault(),this.mergeSeparation(),this.zoomClick=!1,"Reset",this.enableSelectionZoom=!1,this.isReset=!0,this.scaleFactor=t=this.options.minZoom,this.removeCluster(),sf.base.isNullOrUndefined(this.dotNetRef)||this.dotNetRef.invokeMethodAsync("TriggerZoom","Reset",i));else if("ArrowUp"!==e.code&&"ArrowDown"!==e.code||!this.options.enablePanning)if("ArrowLeft"!==e.code&&"ArrowRight"!==e.code||!this.options.enablePanning){if("Enter"===e.code)if(e.preventDefault(),(n.indexOf("_Left_Page_Rect")>-1||n.indexOf("_Right_Page_Rect")>-1)&&!sf.base.isNullOrUndefined(this.dotNetRef)){var o=document.getElementById(this.id+"_Paging_Text").textContent,a=(o=o.trim().replace("\n","")).split("/").map(Number);n.indexOf("_Left_Page_Rect")>-1?document.getElementById(n).style.outlineColor=a[0]+1!=a[1]?"":"transparent":document.getElementById(n).style.outlineColor=a[0]!=a[1]+1?"":"transparent",this.dotNetRef.invokeMethodAsync("TriggerLegendPaging",n.indexOf("_Left_Page_Rect")>-1?1:0)}else if(n.indexOf("_LayerIndex_")>-1&&n.indexOf("shapeIndex")>-1){var r=parseInt(n.split("_LayerIndex_")[1].split("_")[0],10);this.shapeSelection(n,r)}}else this.isPanning=!0,e.preventDefault(),this.mergeSeparation(),this.clientY=0,this.moveClientX=this.areaRect.width/7,this.panning("ArrowLeft"===e.code?-this.moveClientX:this.moveClientX,this.clientY,e.layerX,e.layerY,this.scaleFactor,!0),this.clientX=this.moveClientX;else this.isPanning=!0,e.preventDefault(),this.mergeSeparation(),this.moveClientY=this.areaRect.height/7,this.clientX=0,this.panning(this.clientX,"ArrowUp"===e.code?-this.moveClientY:this.moveClientY,e.layerX,e.layerY,this.scaleFactor,!0),this.clientY=this.moveClientY;this.setToolbarButtonColor(t,this.isReset?this.element.id+"_Zooming_":this.mouseMoveId),sf.base.Browser.isDevice||this.setToolbarButtonColor(t,this.mouseMoveId),this.clientX=this.moveClientX,this.clientY=this.moveClientY},a.prototype.shapeSelection=function(e,t){var i=parseInt(e.split("_shapeIndex_")[1].split("_")[0],10),n=-1!==e.indexOf("_dataIndex_")?parseInt(e.split("_dataIndex_")[1].split("_")[0],10):null;this.options.selectionSettings&&this.options.selectionSettings[t]&&this.options.selectionSettings[t].Enable&&this.removeAllHighlight(),sf.base.isNullOrUndefined(this.dotNetRef)||this.dotNetRef.invokeMethodAsync("SelectMap",e,t,i,"Shape",n)},a.prototype.rightClick=function(e){var t=e.target.id,i=this.getMousePosition(e.pageX,e.pageY);sf.base.isNullOrUndefined(this.dotNetRef)||this.dotNetRef.invokeMethodAsync("TriggerMouseClick",i.x,i.y,document.getElementById(t),e.layerX,e.layerY)},a.prototype.reSize=function(){var e,t,i,n,s;if(this.mergeSeparation(),null!==this.element){var o=document.getElementById(this.element.id+"_svg");if(!sf.base.isNullOrUndefined(o)){if(o.style.display="none",this.isTileMap){(i=document.getElementById(this.element.id+"_tile_parent")).style.display="none",(n=document.getElementById(this.element.id+"_Secondary_Element")).style.display="none";for(var a=0;a<this.element.children.length;a++)this.element.children[a].id===this.element.id+"_svg"&&(this.element.children[a].style.display="none",s=a)}var r=this.element.getBoundingClientRect();e=r.width,t=r.height,o.style.removeProperty("display"),sf.base.isNullOrUndefined(i)||(i.style.removeProperty("display"),n.style.removeProperty("display"),this.element.children[s].style.removeProperty("display"))}}this.previousHeight===t&&this.previousWidth===e||(this.previousHeight=t,this.previousWidth=e,sf.base.isNullOrUndefined(this.dotNetRef)||this.dotNetRef.invokeMethodAsync("TriggerResize",e,t))},a.prototype.doubleClick=function(e){if(!sf.base.isNullOrUndefined(this.dotNetRef)){if(2===e.detail&&this.options.enableZoom&&this.options.doubleClickZoom){var t=document.getElementById(this.id).getBoundingClientRect(),i=this.scaleFactor+1;i>=1&&(this.scaleFactor=i,this.removeCluster(),this.dotNetRef.invokeMethodAsync("MouseWheelZoom",e.pageX-t.x,e.pageY-t.y,e.which,"doubleClick"))}var n=e.target.id,s=this.getMousePosition(e.pageX,e.pageY);if("DoubleClick"===this.options.tooltipDisplayMode){var o=parseInt(n.split("_LayerIndex_")[1].split("_")[0],10);if(n.indexOf("shapeIndex")>-1){var a=parseInt(n.split("_shapeIndex_")[1].split("_")[0],10);this.dotNetRef.invokeMethodAsync("TriggerShapeTooltip",n,s.x,s.y,o,a,"Shape",0)}else if(n.indexOf("_MarkerIndex_")>-1&&-1===n.indexOf("_cluster_")){var r=parseInt(n.split("_MarkerIndex_")[1].split("_")[0],10),l=parseInt(n.split("_dataIndex_")[1].split("_")[0],10);this.dotNetRef.invokeMethodAsync("TriggerShapeTooltip",n,s.x,s.y,o,l,"Marker",r)}else if(n.indexOf("_BubbleIndex_")>-1){r=parseInt(n.split("_BubbleIndex_")[1].split("_")[0],10),l=parseInt(n.split("_dataIndex_")[1].split("_")[0],10);this.dotNetRef.invokeMethodAsync("TriggerShapeTooltip",n,s.x,s.y,o,l,"Bubble",r)}else if(n.indexOf("_PolygonIndex_")>-1){var h=parseInt(n.split("_PolygonIndex_")[1].split("_")[0],10);this.dotNetRef.invokeMethodAsync("TriggerShapeTooltip",n,s.x,s.y,o,-1,"Polygon",h)}else-1===n.indexOf("_shapeIndex_")&&-1===n.indexOf("_MarkerIndex_")&&-1===n.indexOf("_BubbleIndex_")&&-1===n.indexOf("_PolygonIndex_")&&this.dotNetRef.invokeMethodAsync("TriggerShapeTooltip","",s.x,s.y,0,0,"",0)}this.dotNetRef.invokeMethodAsync("TriggerDoubleClick",s.x,s.y,document.getElementById(n),e.layerX,e.layerY)}},a.prototype.mapMouseWheel=function(e){this.mergeSeparation();document.getElementById(this.element.id).getBoundingClientRect();var t=sf.base.isNullOrUndefined(e.wheelDelta)?-e.deltaY:e.wheelDelta,i="ZoomIn"===(t/120>0?"ZoomIn":"ZoomOut")?this.scaleFactor+1:this.scaleFactor-1,n=0==i;this.scaleFactor=i=this.options.maxZoom>=i&&this.options.minZoom<=i?i:i>this.options.maxZoom?this.options.maxZoom:this.options.minZoom,this.isReset=!1,this.setToolbarButtonColor(i,sf.base.Browser.isDevice?this.element.id+"_Zooming_":e.target.id);var s=this.getMousePosition(e.pageX,e.pageY);this.options.enableZoom&&this.options.enableMouseWheelZoom&&i>=1&&!n&&(e.preventDefault(),this.scaleFactor=i,this.removeCluster(),sf.base.isNullOrUndefined(this.dotNetRef)||this.dotNetRef.invokeMethodAsync("MouseWheelZoom",s.x,s.y,t,"wheelZoom"))},a.prototype.getGeoLocation=function(e,t,i){var n=this.calculateFactor(i),s=this.shapeTranslatePoint.x*this.scaleFactor,o=this.shapeTranslatePoint.y*this.scaleFactor,a=Math.min(this.areaRect.height,this.areaRect.width)*n*this.scaleFactor,r=Math.min(Math.max(e-s,0),a-1)/a-.5,l=.5-Math.min(Math.max(t-o,0),a-1)/a;return{latitude:90-360*Math.atan(Math.exp(2*-l*Math.PI))/Math.PI,longitude:360*r}},a.prototype.getClickLocation=function(e,t){var i=null;if(!this.isTileMap&&"Normal"===this.geometryType[this.markerDragArgument.layerIndex]&&!sf.base.isNullOrUndefined(this.markerDragArgument)){var n=this.calculateFactor(this.markerDragArgument.layerIndex),s=document.getElementById(this.element.id),o=s.getBoundingClientRect(),a=s.offsetTop-o.top,r=s.offsetLeft-o.left,l=document.getElementById(this.element.id+"_LayerIndex_"+this.markerDragArgument.layerIndex).getBoundingClientRect(),h={x:e+this.positionX>l.left?Math.abs(l.left-(e+this.positionX))-r:0,y:t+this.positionY>l.top?Math.abs(l.top-(t+this.positionY))-a:0},d=Math.abs(-this.baseMapBounds.longitudeMin*(n*this.scaleFactor)),p=this.convertGeoToPoint(0,0,n,this.markerDragArgument.layerIndex);i={latitude:Math.abs(this.baseMapBounds.latitudeMax-h.y/(n*this.scaleFactor)),longitude:Math.abs(h.x/(n*this.scaleFactor)+this.baseMapBounds.longitudeMin)},this.baseMapBounds.longitudeMin<0&&d>h.x&&(i.longitude=-i.longitude),(p.y*this.scaleFactor<h.y||this.baseMapBounds.latitudeMin<0&&this.baseMapBounds.latitudeMax<0)&&(i.latitude=-i.latitude)}return this.isTileMap?this.pointToLatLong(e-this.areaRect.x,t-this.areaRect.y):sf.base.isNullOrUndefined(i)?this.getGeoLocation(e,t,this.markerDragArgument.layerIndex):i},a.prototype.getMarkerClickLocation=function(e,t,i,n,s){var o=this.markerDragArgument,a=this.calculateFactor(o.layerIndex),r=document.getElementById(o.targetId);this.changeCursorPointer(o.targetId);var l=this.getClickLocation(e,t),h=this.isTileMap?this.convertTileLatLongToPoint({x:l.longitude,y:l.latitude},this.tileZoomLevel,this.tileTranslatePoint,!0):this.convertGeoToPoint(l.latitude,l.longitude,a,o.layerIndex),d=this.shapeTranslatePoint,p=this.isTileMap?h.x:(h.x+d.x)*this.scaleFactor,c=this.isTileMap?h.y:(h.y+d.y)*this.scaleFactor,g=this.markerSettings[o.layerIndex][o.markerIndex];if("Balloon"===this.dataSource[o.layerIndex][o.markerIndex][o.dataIndex][this.markerSettings[o.layerIndex][o.markerIndex].ShapeValuePath]||"Balloon"==g.Type?r.setAttribute("transform","translate( "+(p-g.Width/2)+" "+(c-g.Height)+" )scale("+g.Width/30+" "+g.Height/30+" )"):"Rectangle"===this.dataSource[o.layerIndex][o.markerIndex][o.dataIndex][this.markerSettings[o.layerIndex][o.markerIndex].ShapeValuePath]||"Rectangle"==g.Type?r.setAttribute("transform","translate( "+(p-g.Width/2)+" "+(c-g.Height/2)+" )"):r.setAttribute("transform","translate( "+p+" "+c+" )"),s){var u=this.getClickLocation(e-g.X,t-g.Y);this.dotNetRef.invokeMethodAsync("TriggerMarkerDragEnd",this.markerDragArgument.layerIndex,this.markerDragArgument.markerIndex,this.markerDragArgument.dataIndex,e,t,u.latitude,u.longitude)}},a.prototype.changeCursorPointer=function(e){if(e.indexOf("_MarkerIndex_")>-1&&e.indexOf("_LayerIndex_")>-1&&!(e.indexOf("cluster")>-1)){var t=parseInt(e.split("_LayerIndex_")[1].split("_")[0],10),i=parseInt(e.split("_MarkerIndex_")[1].split("_")[0],10);this.markerSettings[t][i].EnableDrag&&sf.base.isNullOrUndefined(this.markerDragArgument)?document.getElementById(this.element.id+"_svg").setAttribute("style","cursor: pointer"):this.markerSettings[t][i].EnableDrag&&!sf.base.isNullOrUndefined(this.markerDragArgument)?document.getElementById(this.element.id+"_svg").setAttribute("style","cursor: grabbing"):document.getElementById(this.element.id+"_svg").setAttribute("style","cursor: auto")}},a.prototype.mouseUp=function(e){var t,i,n=e.target.id,s=this.isTileMap?this.tileTranslatePoint:this.shapeTranslatePoint,o=!1;if(this.lastScale=1,"touchend"===e.type){var a=e.target.closest("#"+this.element.id+"_Tooltip_TooltipTemplate");sf.base.isNullOrUndefined(a)&&(n.indexOf("LayerIndex")>-1||this.options.enableZoom&&(this.options.enablePinchZooming||this.options.enablePanning||this.enableSelectionZoom)||!sf.base.isNullOrUndefined(this.markerDragArgument))&&e.preventDefault(),this.moveClientX=t=e.changedTouches[0].pageX,this.moveClientY=i=e.changedTouches[0].pageY,o=!0,this.isTouch=!1,this.touchMoveList=[],this.touchStartList=[]}else this.moveClientX=e.pageX,this.moveClientY=e.pageY,t=e.layerX,i=e.layerY;var r=this.getMousePosition(this.moveClientX,this.moveClientY);this.moveClientX=r.x,this.moveClientY=r.y;n.split("_")[0];if(this.options.doubleClickZoom&&"touchend"===e.type){this.tapCount=this.tapCount>1?0:this.tapCount+1;var l=this;sf.base.isNullOrUndefined(this.tapCountTimer)&&(this.tapCountTimer=setTimeout((function(){window.clearTimeout(l.tapCountTimer),l.tapCountTimer=null,l.tapCount=0}),1e3))}if(!(n.indexOf("_Zooming_")>-1)&&this.options.enableZoom&&!this.isPanning&&"mouseleave"!==e.type&&(this.options.zoomOnClick||this.options.doubleClickZoom&&"touchend"===e.type&&this.tapCount>1)&&!this.isPinch){this.tapCount=0;var h=this.scaleFactor+(this.isPinchZoomed?0:1);if(h>=1){this.scaleFactor=h,this.removeCluster();var d="touchend"===e.type?e.changedTouches[0].pageX:e.pageX,p="touchend"===e.type?e.changedTouches[0].pageY:e.pageY,c=this.getMousePosition(d,p);sf.base.isNullOrUndefined(this.dotNetRef)||this.dotNetRef.invokeMethodAsync("MouseWheelZoom",c.x,c.y,"touchend"===e.type?1:e.which,this.options.zoomOnClick?"click":"doubleClick")}}if(this.allowPanning&&this.isPanning&&this.options.enablePanning||this.isPinchZoomed){var g=document.getElementById(this.element.id+"_animated_tiles");if(this.isTileMap&&this.isPinchZoomed&&(g.style.removeProperty("transform"),this.scaleFactor=this.tileZoomLevel=Math.round(this.scaleFactor),this.getTileTranslatePosition(this.scaleFactor,this.scaleFactor,this.currentTouchCenter,null),this.translatePoint.x=(this.tileTranslatePoint.x-.01*this.scaleFactor)/Math.pow(2,this.scaleFactor-1),this.translatePoint.y=(this.tileTranslatePoint.y-.01*this.scaleFactor)/Math.pow(2,this.scaleFactor-1),this.generateTiles(),this.renderMarkers()),this.isTileMap&&!sf.base.isNullOrUndefined(this.currentTiles)&&!sf.base.isNullOrUndefined(g)&&this.currentTiles.childElementCount!=g.childElementCount&&this.currentTiles.childElementCount<g.childElementCount)for(var u=g.childElementCount-1;u>=this.currentTiles.childElementCount;u--)g.removeChild(g.children[u]);this.currentTiles=null,sf.base.isNullOrUndefined(this.dotNetRef)||this.isPinchZoomed||(this.dotNetRef.invokeMethodAsync("UpdateTranslatePoint",this.isTileMap?this.translatePoint:this.shapeTranslatePoint,this.tileTranslatePoint,this.scaleFactor,!1),this.panComplete&&this.dotNetRef.invokeMethodAsync("TriggerPanningComplete",this.previousPoint.x,this.previousPoint.y,s.x,s.y,t,i,this.scaleFactor))}if(this.isPinchZoomed&&!sf.base.isNullOrUndefined(this.dotNetRef)&&(this.dotNetRef.invokeMethodAsync("UpdateTranslatePoint",this.isTileMap?this.translatePoint:this.shapeTranslatePoint,this.tileTranslatePoint,this.scaleFactor,!0),this.pinchComplete&&this.dotNetRef.invokeMethodAsync("TriggerOnZoomComplete",s.x,s.y)),this.isPanning=!1,this.allowPanning=!1,this.options.enableZoom&&this.options.enableSelectionZooming&&this.mouseClick&&this.zoomClick){this.svgCreated=!1,this.mouseClick=!1;var m=document.getElementById(this.element.id+"drawRect");if(m){m.remove(),this.mergeSeparation(),this.removeCluster();var f=0;if(this.height>0&&this.width>0){var y=this.element.getBoundingClientRect();this.isTileMap?(f=this.scaleFactor+Math.round(this.scaleFactor+(y.width/this.width+y.height/this.height)/2),f-=2):f=this.scaleFactor+Math.round((y.width/this.width+y.height/this.height)/2),this.scaleFactor=f}this.setToolbarButtonColor(this.scaleFactor,this.element.id+"_Zooming_"),f>=1&&!sf.base.isNullOrUndefined(this.dotNetRef)&&this.dotNetRef.invokeMethodAsync("TriggerZoomSelection",this.position.x,this.position.y,this.height,this.width)}}if(n.indexOf("_LayerIndex_")>-1){var _=void 0,b=void 0,x=void 0,v=void 0,I=parseInt(n.split("_LayerIndex_")[1].split("_")[0],10);if(n.indexOf("shapeIndex")>-1&&this.shapeSelection(n,I),n.indexOf("_BubbleIndex_")>-1&&!sf.base.isNullOrUndefined(this.dotNetRef)&&(b=parseInt(n.split("_BubbleIndex_")[1].split("_")[0],10),_=-1!==n.indexOf("_dataIndex_")?parseInt(n.split("_dataIndex_")[1].split("_")[0],10):null,this.removeAllHighlight(),this.dotNetRef.invokeMethodAsync("SelectMap",n,I,_,"Bubble",b)),n.indexOf("_NavigationIndex_")>-1&&!sf.base.isNullOrUndefined(this.dotNetRef)&&(x=parseInt(n.split("_NavigationIndex_")[1].split("_")[0],10),_=-1!==n.indexOf("_Line_")?parseInt(n.split("_Line_")[1].split("_")[0],10):null,this.removeAllHighlight(),this.dotNetRef.invokeMethodAsync("SelectMap",n,I,_,"NavigationLine",x)),n.indexOf("_PolygonIndex_")>-1&&!sf.base.isNullOrUndefined(this.dotNetRef)&&(v=parseInt(n.split("_PolygonIndex_")[1].split("_")[0],10),this.removeAllHighlight(),this.dotNetRef.invokeMethodAsync("SelectMap",n,I,v,"Polygon",v)),o&&n.indexOf("MarkerIndex")>-1&&!(n.indexOf("cluster")>-1)&&!sf.base.isNullOrUndefined(this.dotNetRef)){var T=parseInt(n.split("_LayerIndex_")[1].split("_")[0],10),M=parseInt(n.split("_MarkerIndex_")[1].split("_")[0],10);_=parseInt(n.split("_dataIndex_")[1].split("_")[0],10),this.dotNetRef.invokeMethodAsync("TriggerMouseLeave",T,M,_,t,i,o)}}var S=document.getElementById(this.element.id+"_AnnotationGroup"),C=!sf.base.isNullOrUndefined(S)&&S.contains(e.target);n.indexOf("_Zooming_")>-1||!sf.base.isNullOrUndefined(this.markerDragArgument)||"mouseleave"===e.type||this.isPinchZoomed||!this.element.contains(e.target)||sf.base.isNullOrUndefined(this.dotNetRef)||C||this.dotNetRef.invokeMethodAsync("TriggerClickEventArgs",!1,r.x,r.y,this.getDomObject("Target",document.getElementById(n)),!sf.base.isNullOrUndefined(this.isTileMap)&&this.isTileMap),sf.base.isNullOrUndefined(this.markerDragArgument)||"mouseleave"===e.type||(this.getMarkerClickLocation(this.moveClientX,this.moveClientY,t,i,!0),this.dotNetRef.invokeMethodAsync("TriggerMouseMove",this.markerDragArgument.layerIndex,this.markerDragArgument.markerIndex,this.markerDragArgument.dataIndex,n,this.moveClientX,this.moveClientY,n.indexOf("cluster")>-1?"cluster":"marker",o,!1,!1,t,i),this.markerDragArgument=null,this.changeCursorPointer(n),this.isMarkerDrag=!0),(sf.base.Browser.isDevice||n.indexOf("Reset")>-1)&&(this.enableSelectionZoom=!(n.indexOf("Reset")>-1)&&this.enableSelectionZoom,this.isReset=n.indexOf("Reset")>-1,this.setToolbarButtonColor(n.indexOf("Reset")>-1?this.options.minZoom:this.scaleFactor,this.element.id+"_Zooming_")),this.isPinchZoomed=!1},a.prototype.getDomObject=function(e,t){return null!=t?window.sfBlazor.getDomObject(e,t):null},a.prototype.targetTouches=function(e){for(var t=[],i=0;i<e.length;i++)t.push({pageX:e[i].pageX,pageY:e[i].pageY});return t},a.prototype.mouseMove=function(e){var t,n,s=e.target.id;this.mouseMoveId=s,this.moveClientX=e.pageX,this.moveClientY=e.pageY;var o,a,r=this.isTileMap?this.tileTranslatePoint:this.shapeTranslatePoint,l=!1;"touchmove"===e.type?(this.moveClientX=o=e.touches[0].pageX,this.moveClientY=a=e.touches[0].pageY,2===e.touches.length&&(this.touchMoveList=this.targetTouches(e.touches)),(this.options.enableZoom&&(this.options.enablePinchZooming||this.options.enablePanning||this.enableSelectionZoom)||!sf.base.isNullOrUndefined(this.markerDragArgument))&&e.preventDefault(),t=0,n=0,l=!0):(this.moveClientX=e.pageX,this.moveClientY=e.pageY,o=e.clientX,a=e.clientY,t=e.layerX,n=e.layerY),this.tooltipVisiblityChange(s),this.setToolbarButtonColor(this.scaleFactor,s),this.renderInteractiveLegend(s,e);var h=this.getMousePosition(this.moveClientX,this.moveClientY);this.moveClientX=h.x,this.moveClientY=h.y;var d=s.split("_")[0];if(-1==s.indexOf("_MapAreaBorder")?this.highlightMap(e):this.removeAllHighlight(),this.options.enableZoom&&this.options.enableSelectionZooming&&this.mouseClick&&this.zoomClick&&this.options.maxZoom>this.options.factor&&this.enableSelectionZoom){e.preventDefault(),this.svgCreated||(this.startValue=this.svgPoint(this.svg,o,a),this.svgCreated=!0),this.position=this.svgPoint(this.svg,o,a);var p=document.getElementById(this.element.id).getBoundingClientRect();this.width=Math.abs(this.position.x-this.startValue.x),this.height=Math.abs(this.position.y-this.startValue.y),this.position.x>this.startValue.x&&(this.position.x=this.startValue.x),this.position.y>this.startValue.y&&(this.position.y=this.startValue.y),this.position.x=this.position.x-p.x,this.position.y=this.position.y-p.y,i.setAttributeNS(null,"x",this.position.x.toString()),i.setAttributeNS(null,"y",this.position.y.toString()),i.setAttributeNS(null,"id",this.element.id+"drawRect"),i.setAttributeNS(null,"width",this.width.toString()),i.setAttributeNS(null,"height",this.height.toString()),i.setAttributeNS(null,"fill",this.options.rectangleZoomFillColor),i.setAttributeNS(null,"stroke-dasharray","3"),i.setAttributeNS(null,"opacity",this.options.rectangleZoomFillOpacity.toString()),i.setAttributeNS(null,"stroke",this.options.rectangleZoomBorderColor),null!==document.getElementById(this.element.id+"_tile_parent")?document.getElementById(this.element.id+"_LayerCollections").parentNode.appendChild(i):document.getElementById(this.element.id+"_svg").appendChild(i)}if(this.isPinch=!1,this.options.enableZoom&&this.options.enablePinchZooming&&this.touchMoveList.length>=2&&this.touchStartList.length>=2&&(this.isPinchZoomed=this.isPinch=!0,this.pinchComplete||this.panComplete||sf.base.isNullOrUndefined(this.dotNetRef)||this.dotNetRef.invokeMethodAsync("TriggerOnZoom",r.x,r.y),this.pinchZooming(e),this.pinchComplete=!0),sf.base.isNullOrUndefined(this.markerDragArgument)&&this.allowPanning&&this.options.enablePanning&&(this.clientX!==this.moveClientX||this.clientY!==this.moveClientY)&&!this.isPinchZoomed){if(this.isPanning=!0,!this.isTileMap){for(var c=document.getElementById(this.element.id+"_LayerCollections"),g=0;g<c.children.length;g++)for(var u=0;u<c.children[g].childElementCount;u++)if(c.children[g].children[u].id.indexOf("Polygon_Group")>-1){this.scaleFactor=c.children[g].children[u].transform.baseVal[0].matrix.a;break}if(this.clientX!==this.moveClientX&&this.clientY!==this.moveClientY){var m=this.clientX-this.moveClientX,f=this.clientY-this.moveClientY,y=r.x-m/this.scaleFactor,_=r.y-f/this.scaleFactor;this.panComplete||this.pinchComplete||sf.base.isNullOrUndefined(this.dotNetRef)||(this.previousPoint=this.isTileMap?this.tileTranslatePoint:this.shapeTranslatePoint,this.dotNetRef.invokeMethodAsync("TriggerPanning",r.x,r.y,y,_,t,n,this.scaleFactor)),this.panning(m,f,t,n,this.scaleFactor,!1),this.panComplete=!this.isTouch||2!==this.touchMoveList.length||2!==this.touchStartList.length,this.clientX=this.moveClientX,this.clientY=this.moveClientY}}if(s.indexOf(d)>-1)if(null!==(c=document.getElementById(e.currentTarget.id+"_animated_tiles"))){this.isTileMap=!0,this.scaleFactor=parseInt(c.className,10);m=this.clientX-this.moveClientX,f=this.clientY-this.moveClientY;this.panComplete||sf.base.isNullOrUndefined(this.dotNetRef)||2===this.touchMoveList.length||2===this.touchStartList.length||(this.previousPoint=this.isTileMap?this.tileTranslatePoint:this.shapeTranslatePoint,this.dotNetRef.invokeMethodAsync("TriggerTilePanning",this.previousPoint.x,this.previousPoint.y,r.x,r.y,t,n,this.scaleFactor)),this.panning(m,f,t,n,this.scaleFactor,!1),this.panComplete=!this.isTouch||2!==this.touchMoveList.length||2!==this.touchStartList.length,this.clientX=this.moveClientX,this.clientY=this.moveClientY}}if(!sf.base.isNullOrUndefined(this.dotNetRef)){if(!(this.allowPanning||this.mouseClick&&this.zoomClick))if(s.indexOf("shapeIndex")>-1&&"MouseMove"===this.options.tooltipDisplayMode){var b=parseInt(s.split("_LayerIndex_")[1].split("_")[0],10),x=parseInt(s.split("_shapeIndex_")[1].split("_")[0],10);this.dotNetRef.invokeMethodAsync("TriggerShapeTooltip",s,h.x,h.y,b,x,"Shape",0)}else if(s.indexOf("_PolygonIndex_")>-1&&"MouseMove"===this.options.tooltipDisplayMode){b=parseInt(s.split("_LayerIndex_")[1].split("_")[0],10);var v=parseInt(s.split("_PolygonIndex_")[1].split("_")[0],10);this.dotNetRef.invokeMethodAsync("TriggerShapeTooltip",s,h.x,h.y,b,-1,"Polygon",v)}if((""===s||-1===s.indexOf(this.element.id))&&!sf.base.isNullOrUndefined(e.target.offsetParent)){var I=e.target.offsetParent.id;s=I.indexOf("_MarkerIndex_")>-1?I:s}if(((s.indexOf("_MapAreaBorder")>-1||s.indexOf("_MapMargin")>-1||""==s||s.indexOf("_svg")>-1)&&"MouseMove"===this.options.tooltipDisplayMode||this.isToolbarTooltip&&s.indexOf("_Zooming_ToolBar_")>-1)&&this.dotNetRef.invokeMethodAsync("TriggerShapeTooltip","",h.x,h.y,0,0,"",0),this.isToolbarTooltip=!1,s.indexOf("_Zooming_ToolBar_")>-1){this.isToolbarTooltip=!0;var T=s.split("_Zooming_ToolBar_")[1].split("_")[0];this.dotNetRef.invokeMethodAsync("TriggerShapeTooltip",s,h.x,h.y,0,0,T,0)}if(this.renderTitleTooltip(s,h,e),this.renderLegendTitleTooltip(s,h,e),this.renderLegendTextTooltip(s,h,e),!this.isMarkerDrag&&s.indexOf("_LayerIndex_")>-1&&s.indexOf("_MarkerIndex_")>-1||l&&s.indexOf("MarkerIndex")>-1&&!(s.indexOf("cluster")>-1)){b=parseInt(s.split("_LayerIndex_")[1].split("_")[0],10);var M=parseInt(s.split("_MarkerIndex_")[1].split("_")[0],10),S=parseInt(s.split("_dataIndex_")[1].split("_")[0],10);this.changeCursorPointer(s),this.dotNetRef.invokeMethodAsync("TriggerMouseMove",b,M,S,s,h.x,h.y,s.indexOf("cluster")>-1?"cluster":"marker",l,!sf.base.isNullOrUndefined(this.markerDragArgument),!0,t,n)}if(this.isMarkerDrag=!1,s.indexOf("_LayerIndex_")>-1&&s.indexOf("_BubbleIndex_")>-1){b=parseInt(s.split("_LayerIndex_")[1].split("_")[0],10),M=parseInt(s.split("_BubbleIndex_")[1].split("_")[0],10),S=parseInt(s.split("_dataIndex_")[1].split("_")[0],10);this.dotNetRef.invokeMethodAsync("TriggerBubbleMouseMove",b,M,S,s,h.x,h.y,t,n)}if(this.previousMarkerId.indexOf("_MarkerIndex_")>-1&&this.previousMarkerId!=s){b=parseInt(this.previousMarkerId.split("_LayerIndex_")[1].split("_")[0],10),M=parseInt(this.previousMarkerId.split("_MarkerIndex_")[1].split("_")[0],10),S=parseInt(this.previousMarkerId.split("_dataIndex_")[1].split("_")[0],10);this.dotNetRef.invokeMethodAsync("TriggerMouseLeave",b,M,S,t,n,l)}}sf.base.isNullOrUndefined(this.markerDragArgument)?s.indexOf("MarkerIndex")>-1||document.getElementById(this.element.id+"_svg").setAttribute("style","cursor: auto"):(this.getMarkerClickLocation(this.moveClientX,this.moveClientY,t,n,!1),this.changeCursorPointer(s)),this.previousMarkerId=s.indexOf("MarkerIndex")>-1&&!(s.indexOf("cluster")>-1)?s:""},a.prototype.removeInteractiveLegend=function(){var e=document.getElementById(this.element.id+"_Interactive_Legend_Group");null!==e&&e.setAttribute("style","display: none;")},a.prototype.renderInteractiveLegend=function(e,t){var i=this;if(!sf.base.isNullOrUndefined(this.legendSettings)&&"Interactive"===this.legendSettings.mode&&e.indexOf("_LayerIndex")>-1&&!sf.base.isNullOrUndefined(this.dotNetRef)){var n=document.getElementById(t.target.id).getAttribute("fill");if(e.indexOf("_shapeIndex")>-1&&"Layers"===this.legendSettings.type){var s=parseInt(e.split("_LayerIndex_")[1].split("_")[0],10),o=parseInt(e.split("_shapeIndex_")[1].split("_")[0],10);document.getElementById(e);clearTimeout(this.arrowCountTimer),this.renderInteractive(n,s,o,0),"touch"===t.pointerType&&(this.arrowCountTimer=setTimeout((function(){i.removeInteractiveLegend()}),1500))}else if(e.indexOf("_MarkerIndex_")>-1&&"Markers"===this.legendSettings.type){s=parseInt(e.split("_LayerIndex_")[1].split("_")[0],10);var a=parseInt(e.split("_MarkerIndex_")[1].split("_")[0],10),r=-1!==e.indexOf("_dataIndex_")?parseInt(e.split("_dataIndex_")[1].split("_")[0],10):null;clearTimeout(this.arrowCountTimer),this.renderInteractive(n,s,a,r),"touch"===t.pointerType&&(this.arrowCountTimer=setTimeout((function(){i.removeInteractiveLegend()}),1500))}else if(e.indexOf("_BubbleIndex_")>-1&&"Bubbles"===this.legendSettings.type){s=parseInt(e.split("_LayerIndex_")[1].split("_")[0],10);var l=parseInt(e.split("_BubbleIndex_")[1].split("_")[0],10);r=-1!==e.indexOf("_dataIndex_")?parseInt(e.split("_dataIndex_")[1].split("_")[0],10):null;clearTimeout(this.arrowCountTimer),this.renderInteractive(n,s,l,r),"touch"===t.pointerType&&(this.arrowCountTimer=setTimeout((function(){i.removeInteractiveLegend()}),1500))}else this.removeInteractiveLegend()}else sf.base.isNullOrUndefined(this.legendSettings)||"Interactive"!==this.legendSettings.mode||sf.base.isNullOrUndefined(this.dotNetRef)||this.removeInteractiveLegend()},a.prototype.renderInteractive=function(e,t,i,n){for(var s=0;s<(sf.base.isNullOrUndefined(this.legendDetails)?0:this.legendDetails.length);s++){for(var o=!1,a=0;a<this.legendDetails[s].length;a++){var r=this.legendDetails[s][a];if("Bubbles"===this.legendSettings.type&&parseInt(r.layerIndex)===t&&parseInt(r.dataIndex)===n||"Markers"===this.legendSettings.type&&parseInt(r.layerIndex)===t&&parseInt(r.dataIndex)===n&&parseInt(r.settingIndex)===i||"Layers"===this.legendSettings.type&&parseInt(r.layerIndex)===t&&parseInt(r.settingIndex)===i){var l=this.getLegendRect(this.legendShapeCollection[s].ShapeId);sf.base.isNullOrUndefined(l)||this.renderInteractivePointer(e,l),o=!0;break}this.removeInteractiveLegend()}if(o)break}},a.prototype.getLegendRect=function(e){var t=document.getElementById(e).getBoundingClientRect(),i=e.split("_Legend_")[0],n=document.getElementById(i).getBoundingClientRect();return{x:t.left-n.left,y:t.top-n.top,width:t.width,height:t.height}},a.prototype.renderInteractivePointer=function(e,t){var i,n,o,a="None"===this.legendSettings.orientation?"Top"===this.legendSettings.position||"Bottom"===this.legendSettings.position?"Horizontal":"Vertical":this.legendSettings.orientation;if(t.y="Float"===this.legendSettings.position&&this.isTileMap?t.y-this.baseMapBounds.mapsAreaRect.y:t.y,"Horizontal"===a){n=t.x+t.width/2-("Float"===this.legendSettings.position&&this.isTileMap?this.baseMapBounds.mapsAreaRect.y:0);var r=0;r="False"===this.legendSettings.invertedPointer?(o=t.y)-10:(o=t.y+t.height)+10,i=this.createInteractiveArrow(n,o,10,10,0,r,!0)}else{o=t.y+t.height/2;var l=0;l="False"===this.legendSettings.invertedPointer?(n=t.x+t.width-("Float"===this.legendSettings.position&&this.isTileMap?this.baseMapBounds.mapsAreaRect.x:0))+10:(n=t.x-("Float"===this.legendSettings.position&&this.isTileMap?this.baseMapBounds.mapsAreaRect.x:0))-10,i=this.createInteractiveArrow(n,o,10,10,l,0,!1)}var h={d:i,id:this.element.id+"_Interactive_Legend",fill:e,stroke:"#000000",opacity:1,strokeWidth:0},d=document.getElementById(this.element.id+"_Interactive_Legend_Group");null===d&&(d=s(this.element.id+"_Interactive_Legend_Group")),d.setAttribute("style","display: block;");var p=this.drawPath(h),c=document.getElementById(this.element.id+"_svg");d.appendChild(p),c.appendChild(d)},a.prototype.createInteractiveArrow=function(e,t,i,n,s,o,a){return"M "+e+" "+t+" L "+(a?e-i:s)+" "+(a?o:t-n)+" L "+(a?e+i:s)+" "+(a?o:t+n)+" Z"},a.prototype.renderTitleTooltip=function(e,t,i){var n=this;e===this.element.id+"_Map_title"&&i.target.textContent.indexOf("...")>-1&&!this.isPanning&&(clearTimeout(this.tooltipCountTimer),this.dotNetRef.invokeMethodAsync("TriggerShapeTooltip","_Map_title",t.x,t.y,0,0,"Title",0),"touch"===i.pointerType&&(this.tooltipCountTimer=setTimeout((function(){n.dotNetRef.invokeMethodAsync("TriggerShapeTooltip","",t.x,t.y,0,0,"",0)}),1500)))},a.prototype.renderLegendTitleTooltip=function(e,t,i){var n=this;e===this.element.id+"_LegendTitle"&&i.target.textContent.indexOf("...")>-1&&!this.isPanning&&(clearTimeout(this.tooltipCountTimer),this.dotNetRef.invokeMethodAsync("TriggerShapeTooltip","_LegendTitle",t.x,t.y,0,0,"LegendTitle",0),"touch"===i.pointerType&&(this.tooltipCountTimer=setTimeout((function(){n.dotNetRef.invokeMethodAsync("TriggerShapeTooltip","",t.x,t.y,0,0,"",0)}),1500)))},a.prototype.renderLegendTextTooltip=function(e,t,i){var n=this;e.indexOf("_Legend_Text_Index_")>-1&&i.target.textContent.indexOf("...")>-1&&!this.isPanning&&(clearTimeout(this.tooltipCountTimer),this.dotNetRef.invokeMethodAsync("TriggerShapeTooltip","_LegendText_",t.x,t.y,0,parseInt(e.split("_Legend_Text_Index_")[1]),"LegendText",0),"touch"===i.pointerType&&(this.tooltipCountTimer=setTimeout((function(){n.dotNetRef.invokeMethodAsync("TriggerShapeTooltip","",t.x,t.y,0,0,"",0)}),1500)))},a.prototype.tooltipVisiblityChange=function(e){var t=this.getTooltipDuration(e);if(sf.base.Browser.isDevice){var i=this;window.clearTimeout(i.tooltipCountTimer),t>0&&(this.tooltipCountTimer=setTimeout((function(){i.tooltipCountTimer=null;for(var e=0,t=["_LayerTooltipTemplate","_MarkerTooltipTemplate","_BubbleTooltipTemplate","_PolygonTooltipTemplate","_Tooltip"];e<t.length;e++){var n=t[e],s=document.getElementById(i.element.id+n);if(s){s.style.visibility="hidden";break}}}),t))}},a.prototype.getTooltipDuration=function(e){var t=2e3;if(e.indexOf("_LayerIndex_")>-1){var i=parseInt(e.split("_LayerIndex_")[1].split("_")[0],10);if(t=sf.base.isNullOrUndefined(this.layerTooltipSettings[i])?t:parseInt(this.layerTooltipSettings[i]),e.indexOf("_MarkerIndex_")>-1){var n=parseInt(e.split("_MarkerIndex_")[1].split("_")[0],10);t=sf.base.isNullOrUndefined(this.markerSettings[i][n])||sf.base.isNullOrUndefined(this.markerSettings[i][n].TooltipDuration)?t:this.markerSettings[i][n].TooltipDuration}else if(e.indexOf("_BubbleIndex_")>-1){var s=parseFloat(e.split("_BubbleIndex_")[1].split("_")[0]);t=sf.base.isNullOrUndefined(this.bubbleCollection[i][s])||sf.base.isNullOrUndefined(this.bubbleCollection[i][s].TooltipDuration)?t:parseInt(this.bubbleCollection[i][s].TooltipDuration)}else if(e.indexOf("_PolygonIndex_")>-1){var o=parseInt(e.split("_PolygonIndex_")[1].split("_")[0],10);t=sf.base.isNullOrUndefined(this.polygon[i][o])||sf.base.isNullOrUndefined(this.polygon[i][o].TooltipDuration)?t:this.polygon[i][o].TooltipDuration}}return t},a.prototype.mouseDown=function(e){this.isPinch=!1;var t=e.target.id,i=this.options.factor+(t.indexOf("_ZoomIn_")>1&&"0.3"!==document.getElementById(this.element.id+"_Zooming_ToolBar_ZoomIn_Rect").getAttribute("stroke-opacity")?1:0)+(t.indexOf("_ZoomOut_")>1&&"0.3"!==document.getElementById(this.element.id+"_Zooming_ToolBar_ZoomOut_Rect").getAttribute("stroke-opacity")?-1:0);i=t.indexOf("_Reset_")>1?this.options.minZoom:i,this.isPinchZoomed=!1,this.isTouch=!1,this.panComplete=!1,this.pinchComplete=!1,this.currentTiles=null,this.isReset=!1;var n=document.getElementById(this.element.id+"_animated_tiles");if(this.isTileMap&&!sf.base.isNullOrUndefined(n)&&(this.currentTiles=n.cloneNode(!0)),"touchstart"===e.type){var s=e.target.closest("#"+this.element.id+"_Tooltip_TooltipTemplate");sf.base.isNullOrUndefined(s)&&this.options.enableZoom&&(this.options.enablePinchZooming||this.options.enablePanning||this.enableSelectionZoom||!sf.base.isNullOrUndefined(this.markerDragArgument))&&e.preventDefault(),this.clientX=e.touches[0].pageX,this.clientY=e.touches[0].pageY,2===e.touches.length&&(this.touchStartList=this.targetTouches(e.touches)),this.isTouch=!0}else this.clientX=e.pageX,this.clientY=e.pageY;this.tooltipVisiblityChange(t);var o=this.getMousePosition(this.clientX,this.clientY);if(this.clientX=o.x,this.clientY=o.y,this.options.enableZoom&&this.options.enableSelectionZooming&&this.zoomClick&&(this.mouseClick=!0,e.preventDefault()),this.mouseClick||-1!==t.indexOf("ToolBar_Pan")||(t.indexOf("_MapAreaBorder")>-1&&this.options.enablePanning&&(this.allowPanning=!0),!this.enableSelectionZoom&&t.indexOf("")>-1&&!(t.indexOf("Zooming")>-1)&&this.options.enablePanning&&(this.allowPanning=!0)),t.indexOf("MarkerIndex")>-1&&!(t.indexOf("cluster")>-1)&&!sf.base.isNullOrUndefined(this.dotNetRef)){var a=parseInt(t.split("_LayerIndex_")[1].split("_")[0],10),r=parseInt(t.split("_MarkerIndex_")[1].split("_")[0],10),l=parseInt(t.split("_dataIndex_")[1].split("_")[0],10),h=!1;!sf.base.isNullOrUndefined(this.clusterData)&&this.clusterData.length>0&&(h=this.clusterData[0].data.filter((function(e){return e.index==l})).length>0&&this.clusterData[0].layerIndex===a&&this.clusterData[0].markerIndex===r),this.markerSettings[a][r].EnableDrag&&!h&&(this.markerDragArgument={layerIndex:a,markerIndex:r,dataIndex:l,latitude:this.dataSource[a][r][l].Latitude,longitude:this.dataSource[a][r][l].Longitude,targetId:t},this.allowMarkerDragStartEvent&&this.dotNetRef.invokeMethodAsync("TriggerMarkerDragStart",a,r,l,this.clientX,this.clientY))}-1==t.indexOf("Reset")&&this.setToolbarButtonColor(i,t),t.indexOf("_Zoom_")>-1&&this.options.enableSelectionZooming&&i>this.options.max?(this.removeZoomColor(this.toolbarSelection),this.removePanColor(this.toolbarColor)):t.indexOf("_Pan_")>-1&&this.options.enablePanning&&!this.options.isTileMap&&i>1&&(this.removeZoomColor(this.toolbarColor),this.removePanColor(this.toolbarSelection))},a.prototype.removeToolbarClass=function(e,t,i,n,s){this.getElementByID(this.element.id+"_Zooming_KitCollection")&&(document.getElementById(this.element.id+"_Zooming_ToolBar_ZoomIn_Group")&&this.getElementByID(this.element.id+"_Zooming_ToolBar_ZoomIn_Group").setAttribute("class",t),document.getElementById(this.element.id+"_Zooming_ToolBar_ZoomOut_Group")&&this.getElementByID(this.element.id+"_Zooming_ToolBar_ZoomOut_Group").setAttribute("class",i),document.getElementById(this.element.id+"_Zooming_ToolBar_Reset_Group")&&this.getElementByID(this.element.id+"_Zooming_ToolBar_Reset_Group").setAttribute("class",s),document.getElementById(this.element.id+"_Zooming_ToolBar_Zoom_Group")&&this.getElementByID(this.element.id+"_Zooming_ToolBar_Zoom_Group").setAttribute("class",e),document.getElementById(this.element.id+"_Zooming_ToolBar_Pan_Group")&&this.getElementByID(this.element.id+"_Zooming_ToolBar_Pan_Group").setAttribute("class",n))},a.prototype.setOpacity=function(e,t,i,n){this.getElementByID(this.element.id+e)&&(this.getElementByID(this.element.id+e).setAttribute("stroke-opacity",i.toString()),this.getElementByID(this.element.id+e).setAttribute("fill-opacity",i.toString()),this.getElementByID(this.element.id+t).setAttribute("stroke-opacity",n.toString()),this.getElementByID(this.element.id+t).setAttribute("fill-opacity",n.toString()))},a.prototype.removeZoomOpacity=function(e,t,i,n,s,o,a,r,l,h){this.setOpacity("_Zooming_ToolBar_Zoom_Rect","_Zooming_ToolBar_Zoom_Path",t,e),this.setOpacity("_Zooming_ToolBar_ZoomIn_Rect","_Zooming_ToolBar_ZoomIn_Path",n,i),this.setOpacity("_Zooming_ToolBar_ZoomOut_Rect","_Zooming_ToolBar_ZoomOut_Path",o,s),this.setOpacity("_Zooming_ToolBar_Pan_Rect","_Zooming_ToolBar_Pan_Path",r,a),this.setOpacity("_Zooming_ToolBar_Reset_Rect","_Zooming_ToolBar_Reset_Path",h,l)},a.prototype.getElementByID=function(e){return document.getElementById(e)},a.prototype.setToolbarButtonColor=function(e,t){if(document.getElementById(this.element.id+"_Zooming_KitCollection")&&t.indexOf(this.element.id+"_Zooming_")>-1&&this.options.enableZoom){document.getElementById(this.element.id+"_Zooming_KitCollection").setAttribute("opacity","1"),sf.base.Browser.isDevice?this.removeToolbarClass("","","","",""):this.removeToolbarClass(this.options.enableSelectionZooming?"e-maps-toolbar":"","e-maps-toolbar","e-maps-toolbar",this.options.enablePanning?"e-maps-toolbar":"","e-maps-toolbar");var i=this.toolbarShapeOpacity,n=this.toolbarButtonOpacity;this.isTileMap&&e<=1.2?(sf.base.Browser.isDevice||this.removeToolbarClass(this.options.enableSelectionZooming?"e-maps-toolbar":"","e-maps-toolbar","",this.options.enablePanning?"e-maps-toolbar":"",""),this.options.enablePanning&&this.removePanColor(this.toolbarSelection),this.enableSelectionZoom&&this.options.enableSelectionZooming&&!this.isReset&&(this.removeZoomColor(this.toolbarSelection),this.removePanColor(this.toolbarColor)),this.isReset&&(this.removeZoomColor(this.toolbarColor),this.removePanColor(this.toolbarSelection)),this.removeZoomOpacity(this.options.enableSelectionZooming?i:.3,this.options.enableSelectionZooming?n:.3,i,n,.3,.3,this.options.enablePanning?i:.3,this.options.enablePanning?n:.3,.3,.3)):e<=1.2?(sf.base.Browser.isDevice||this.removeToolbarClass(this.options.enableSelectionZooming?"e-maps-toolbar":"","e-maps-toolbar","","",""),!this.enableSelectionZoom&&this.options.enablePanning&&this.removePanColor(this.toolbarColor),this.enableSelectionZoom&&this.options.enableSelectionZooming&&!this.isReset&&(this.removeZoomColor(this.toolbarSelection),this.removePanColor(this.toolbarColor)),this.isReset&&this.removeZoomColor(this.toolbarColor),this.removeZoomOpacity(this.options.enableSelectionZooming?i:.3,this.options.enableSelectionZooming?n:.3,i,n,.3,.3,.3,.3,.3,.3)):e<this.options.maxZoom?(sf.base.Browser.isDevice||this.removeToolbarClass(this.options.enableSelectionZooming?"e-maps-toolbar":"","e-maps-toolbar","e-maps-toolbar",this.options.enablePanning?"e-maps-toolbar":"","e-maps-toolbar"),this.removeZoomOpacity(this.options.enableSelectionZooming?i:.3,this.options.enableSelectionZooming?n:.3,i,n,i,n,this.options.enablePanning?i:.3,this.options.enablePanning?n:.3,i,n),(this.isReset||e===this.options.minZoom)&&(this.removeZoomOpacity(this.options.enableSelectionZooming?i:.3,this.options.enableSelectionZooming?n:.3,i,n,.3,.3,this.options.enablePanning?i:.3,this.options.enablePanning?n:.3,.3,.3),this.removeToolbarClass(this.options.enableSelectionZooming?"e-maps-toolbar":"","e-maps-toolbar","",this.options.enablePanning?"e-maps-toolbar":"","")),this.enableSelectionZoom&&this.options.enableSelectionZooming?(this.removeZoomColor(this.toolbarSelection),this.isPan&&this.options.enablePanning&&this.removePanColor(this.toolbarColor)):!this.enableSelectionZoom&&this.options.enablePanning&&(this.removePanColor(this.toolbarSelection),this.options.enableSelectionZooming&&this.removeZoomColor(this.toolbarColor))):(sf.base.Browser.isDevice||this.removeToolbarClass("","","e-maps-toolbar",this.options.enablePanning?"e-maps-toolbar":"","e-maps-toolbar"),this.removeZoomOpacity(.3,.3,.3,.3,i,n,this.options.enablePanning?i:.3,this.options.enablePanning?n:.3,i,n),this.options.enableSelectionZooming&&this.removeZoomColor(this.toolbarColor),!this.enableSelectionZoom&&this.options.enablePanning&&this.removePanColor(this.toolbarSelection))}else sf.base.Browser.isDevice||(this.removePanColor(this.toolbarColor),this.removeZoomColor(this.toolbarColor),this.removeZoomOpacity(1,1,1,1,1,1,1,1,1,1),document.getElementById(this.element.id+"_Zooming_KitCollection")&&document.getElementById(this.element.id+"_Zooming_KitCollection").setAttribute("opacity","0.3"))},a.prototype.pinchZooming=function(e){this.pinchFactor=this.scaleFactor;var t=this.scaleFactor,i=this.getBound(this.element.id);if(this.isTileMap){r=this.getTouchCenterPoint();var n=Math.sqrt(Math.pow(this.touchMoveList[0].pageX-this.touchMoveList[1].pageX,2)+Math.pow(this.touchMoveList[0].pageY-this.touchMoveList[1].pageY,2)),s=this.pinchFactor;sf.base.isNullOrUndefined(this.pinchDistance)||(this.pinchDistance>n?s-=1:this.pinchDistance<n&&(s+=1),(s=Math.min(this.options.maxZoom,Math.max(this.options.minZoom,s)))!=this.pinchFactor&&(this.pinchFactor=s,this.tileZoomLevel=this.pinchFactor,this.scaleFactor=this.pinchFactor,this.currentTouchCenter=r,this.getTileTranslatePosition(t,this.pinchFactor,{x:r.x,y:r.y},null),this.translatePoint.x=(this.tileTranslatePoint.x-.01*this.scaleFactor)/Math.pow(2,this.scaleFactor-1),this.translatePoint.y=(this.tileTranslatePoint.y-.01*this.scaleFactor)/Math.pow(2,this.scaleFactor-1),this.generateTiles(),this.renderMarkers(),this.clusterMarkerProcess())),this.pinchDistance=n}else{var o=this.areaRect;this.previousScale=this.scaleFactor,this.previousPoint=this.shapeTranslatePoint;this.tileTranslatePoint;var a=this.calculateScale(this.touchStartList,this.touchMoveList),r=this.getTouchCenter(this.touchMoveList),l=a/this.lastScale;this.lastScale=a,this.pinchFactor*=l,this.pinchFactor=Math.min(this.options.maxZoom,Math.max(this.options.minZoom,this.pinchFactor));var h=Math.abs(this.baseMapBounds.minBounds.x-this.baseMapBounds.maxBounds.x),d=Math.abs(this.baseMapBounds.minBounds.y-this.baseMapBounds.maxBounds.y),p=Math.abs(this.baseMapBounds.maxBounds.y-this.baseMapBounds.minBounds.y)*this.pinchFactor,c=this.shapeTranslatePoint.x-(o.width/this.scaleFactor-o.width/this.pinchFactor)/(o.width/(r.x-i.x)),g=this.shapeTranslatePoint.y-(o.height/this.scaleFactor-o.height/this.pinchFactor)/(o.height/(r.y-i.y));c=p<this.areaRect.height&&!sf.base.Browser.isDevice?o.x+(-this.baseMapBounds.minBounds.x+(o.width/2-h/2)):c,g=p<this.areaRect.height&&!sf.base.Browser.isDevice?o.y+(-this.baseMapBounds.minBounds.y+(o.height/2-d/2)):g,this.scaleFactor=this.pinchFactor,this.shapeTranslatePoint={x:c,y:g},this.applyTransform(this.scaleFactor,this.shapeTranslatePoint)}},a.prototype.removePanColor=function(e){document.getElementById(this.element.id+"_Zooming_ToolBar_Pan_Rect")&&this.options.enablePanning&&(document.getElementById(this.element.id+"_Zooming_ToolBar_Pan_Path").setAttribute("fill",e),document.getElementById(this.element.id+"_Zooming_ToolBar_Pan_Path").setAttribute("stroke",e))},a.prototype.removeZoomColor=function(e){document.getElementById(this.element.id+"_Zooming_ToolBar_Zoom_Rect")&&this.options.enableSelectionZooming&&(document.getElementById(this.element.id+"_Zooming_ToolBar_Zoom_Path").setAttribute("fill",e),document.getElementById(this.element.id+"_Zooming_ToolBar_Zoom_Path").setAttribute("stroke",e))},a.prototype.getTileTranslatePosition=function(e,t,i,n){var s=256*Math.pow(2,e),o=256*Math.pow(2,t),a=(i.x-this.tileTranslatePoint.x)/s*100,r=(i.y-this.tileTranslatePoint.y)/s*100,l=this.baseMapBounds.availableSize;this.tileTranslatePoint.x=1===t?l.width/2-256:i.x-a*o/100,this.tileTranslatePoint.y=1===t?l.height/2-256:i.y-r*o/100},a.prototype.calculateScale=function(e,t){var i=this.getDistance(e[0],e[1]);return this.getDistance(t[0],t[1])/i},a.prototype.getDistance=function(e,t){var i=e.pageX-t.pageX,n=e.pageY-t.pageY;return Math.sqrt(i*i+n*n)},a.prototype.sum=function(e,t){return e+t},a.prototype.getTouchCenter=function(e){return{x:e.map((function(e){return e.pageX})).reduce(this.sum)/e.length,y:e.map((function(e){return e.pageY})).reduce(this.sum)/e.length}},a.prototype.getTouchCenterPoint=function(){for(var e=[],t=0;t<this.touchMoveList.length;t++)e.push(this.getMousePosition(this.touchMoveList[t].pageX,this.touchMoveList[t].pageY));return{x:(e[0].x+e[1].x)/2,y:(e[0].y+e[1].y)/2}},a.prototype.highlightMap=function(e){var t=e.target,i=t.id;if(t.classList.contains("highlightLine")||t.classList.contains("highlightShape")||t.classList.contains("highlightMarker")||t.classList.contains("highlightBubble")||t.classList.contains("highlightNavigation")||t.classList.contains("highlightPolygon")||this.removeAllHighlight(),this.targetElementPath=t,i.indexOf("_LayerIndex_")>-1&&!sf.base.isNullOrUndefined(this.dotNetRef)){var n=parseInt(i.split("_LayerIndex_")[1].split("_")[0],10),s=i.indexOf("_dataIndex_")>-1?parseInt(i.split("_dataIndex_")[1].split("_")[0],10):0;if(i.indexOf("shapeIndex")>-1&&-1==i.indexOf("LabelIndex_")&&this.options.layerHighlightSettings[n]&&this.options.layerHighlightSettings[n].Enable){if(!t.classList.contains("mapShapeSelection")){var o=parseInt(i.split("_shapeIndex_")[1].split("_")[0],10);this.dotNetRef.invokeMethodAsync("TriggerShapeHighlight",n,o,i)}}else if(i.indexOf("_BubbleIndex_")>-1){var a=parseInt(i.split("_BubbleIndex_")[1].split("_")[0],10);this.options.bubbleHighlightSettings[n][a]&&this.options.bubbleHighlightSettings[n][a].Enable&&!t.classList.contains("mapBubbleSelection")&&this.dotNetRef.invokeMethodAsync("TriggerItemHighlight","Bubble",n,a,s,i)}else if(i.indexOf("_NavigationIndex_")>-1){var r=parseInt(i.split("_NavigationIndex_")[1].split("_")[0],10);this.options.navigationHighlightSettings[n][r]&&this.options.navigationHighlightSettings[n][r].Enable&&!t.classList.contains("mapNavigationSelection")&&this.dotNetRef.invokeMethodAsync("TriggerItemHighlight","Navigation",n,r,0,i)}else if(i.indexOf("_PolygonIndex_")>-1){var l=parseInt(i.split("_PolygonIndex_")[1].split("_")[0],10);this.options.polygonHighlightSettings[n]&&this.options.polygonHighlightSettings[n].Enable&&!t.classList.contains("mapPolygonSelection")&&this.dotNetRef.invokeMethodAsync("TriggerItemHighlight","Polygon",n,l,0,i)}else if(i.indexOf("_MarkerIndex_")>-1&&-1===i.indexOf("_cluster_")&&!(i.indexOf("markerClusterConnectorLine")>-1)){var h=parseInt(i.split("_MarkerIndex_")[1].split("_")[0],10);this.options.markerHighlightSettings[n][h]&&this.options.markerHighlightSettings[n][h].Enable&&!t.classList.contains("mapMarkerSelection")&&this.dotNetRef.invokeMethodAsync("TriggerItemHighlight","Marker",n,h,s,i)}}else if(i.indexOf("_Legend_Shape_")>-1){var d=parseInt(i.split("_Index_")[1],10),p=this.legendDetails[d];if("Layers"===this.legendSettings.type){if(!t.classList.contains("mapShapeSelection"))for(var c=0;c<p.length;c++)if(this.options.layerHighlightSettings[p[c].layerIndex]&&!sf.base.isNullOrUndefined(this.options.layerHighlightSettings[p[c].layerIndex])&&this.options.layerHighlightSettings[p[c].layerIndex].Enable){t.classList.add("highlightShape");var g=document.getElementById(this.element.id+"_LayerIndex_"+p[c].layerIndex+"_shapeIndex_"+p[c].settingIndex);this.handleHighlight(g,"Shape",this.options.layerHighlightSettings[p[c].layerIndex])}}else if("Markers"===this.legendSettings.type){if(!t.classList.contains("mapMarkerSelection"))for(c=0;c<p.length;c++)if(this.options.markerHighlightSettings[p[c].layerIndex]&&!sf.base.isNullOrUndefined(this.options.markerHighlightSettings[p[c].layerIndex][p[c].settingIndex])&&this.options.markerHighlightSettings[p[c].layerIndex][p[c].settingIndex].Enable){t.classList.add("highlightMarker");g=document.getElementById(this.element.id+"_LayerIndex_"+p[c].layerIndex+"_MarkerIndex_"+p[c].settingIndex+"_dataIndex_"+p[c].dataIndex);this.handleHighlight(g,"Marker",this.options.markerHighlightSettings[p[c].layerIndex][p[c].settingIndex])}}else if("Bubbles"===this.legendSettings.type&&!t.classList.contains("mapBubbleSelection"))for(c=0;c<p.length;c++)if(this.options.bubbleHighlightSettings[p[c].layerIndex]&&!sf.base.isNullOrUndefined(this.options.bubbleHighlightSettings[p[c].layerIndex][p[c].settingIndex])&&this.options.bubbleHighlightSettings[p[c].layerIndex][p[c].settingIndex].Enable){t.classList.add("highlightBubble");g=document.getElementById(this.element.id+"_LayerIndex_"+p[c].layerIndex+"_BubbleIndex_"+p[c].settingIndex+"_dataIndex_"+p[c].dataIndex);this.handleHighlight(g,"Bubble",this.options.bubbleHighlightSettings[p[c].layerIndex][p[c].settingIndex])}}},a.prototype.removeAllHighlight=function(){this.removeHighlight("Shape"),this.removeHighlight("Line"),this.removeHighlight("Marker"),this.removeHighlight("Bubble"),this.removeHighlight("Navigation"),this.removeHighlight("Polygon")},a.prototype.handleHighlight=function(e,t,i){if(("Marker"===t||"Bubble"===t||"Shape"===t)&&!sf.base.isNullOrUndefined(this.legendDetails)&&0!==this.legendDetails.length)for(var n=0;n<this.legendDetails.length;n++)for(var s=0;s<this.legendDetails[n].length;s++)if("Marker"===t&&e.id===this.element.id+"_LayerIndex_"+this.legendDetails[n][s].layerIndex+"_MarkerIndex_"+this.legendDetails[n][s].settingIndex+"_dataIndex_"+this.legendDetails[n][s].dataIndex||"Bubble"===t&&e.id===this.element.id+"_LayerIndex_"+this.legendDetails[n][s].layerIndex+"_BubbleIndex_"+this.legendDetails[n][s].settingIndex+"_dataIndex_"+this.legendDetails[n][s].dataIndex||"Shape"===t&&e.id===this.element.id+"_LayerIndex_"+this.legendDetails[n][s].layerIndex+"_shapeIndex_"+this.legendDetails[n][s].settingIndex){document.getElementById(this.element.id+"_Legend_Shape_Index_"+n).classList.add("highlight"+t);break}if("Arrow"!==t){var o=!sf.base.isNullOrUndefined(e.getAttribute("data-geometry"))&&e.getAttribute("data-geometry").indexOf("LineString")>-1?"Line":t;("Bubble"===o&&e.id.indexOf("_BubbleIndex_")>-1&&i.Enable||"Marker"===o&&e.id.indexOf("_MarkerIndex_")>-1&&i.Enable||("Shape"===o||"Line"===o)&&e.id.indexOf("_shapeIndex_")>-1&&i.Enable||"Navigation"===o&&e.id.indexOf("_NavigationIndex_")>-1&&i.Enable||"Polygon"===o&&e.id.indexOf("_PolygonIndex_")>-1&&i.Enable)&&(e.classList.add("highlight"+o),null!==document.querySelector("#highlight"+o+"Style")&&0!==document.querySelector("#highlight"+o+"Style").length?this.customizeStyle("highlight"+o+"Style","highlight"+o,i,e.id):this.createStyle("highlight"+o+"Style","highlight"+o,i,e.id))}else e.setAttribute("fill",i.Fill),e.setAttribute("fill-opacity",i.opacity),e.setAttribute("stroke-opacity",i.BorderOpacity)},a.prototype.removeHighlight=function(e){var t=document.querySelectorAll(".highlight"+e);if(t&&t.length>0)for(var i=0;i<t.length;i++)if(t[i].classList.remove("highlight"+e),"Navigation"===e&&""!==t[i].id&&(""!==t[i].getAttribute("marker-start")||""!==t[i].getAttribute("marker-end"))){var n=document.getElementById(t[i].id+"_triangle"),s=parseInt(t[i].id.split("_LayerIndex_")[1].split("_")[0],10),o=parseInt(t[i].id.split("_NavigationIndex_")[1].split("_")[0],10);sf.base.isNullOrUndefined(n)||(n.setAttribute("fill",this.navigation[s][o].ArrowColor),n.setAttribute("opacity","1"))}},a.prototype.createStyle=function(e,t,i,n){var s=document.createElement("style");s.id=e,this.highlightStyle(s,t,i,n),document.body.appendChild(s)},a.prototype.highlightStyle=function(e,t,i,n){if(n.indexOf("NavigationIndex")>-1){var s=sf.base.isNullOrUndefined(i.Fill)?sf.base.isNullOrUndefined(i.BorderColor)?"transparent":i.BorderColor:i.Fill,o=parseInt(n.split("_LayerIndex_")[1].split("_")[0],10),a=parseInt(n.split("_NavigationIndex_")[1].split("_")[0],10);this.navigation[o][a].Angle,e.innerHTML="."+t+" {fill: none;fill-opacity:"+i.Opacity+";stroke:"+s+";stroke-width:"+i.BorderWidth+";stroke-opacity:"+i.BorderOpacity}else t.indexOf("Line")>-1?e.innerHTML="."+t+" {fill: transparent;fill-opacity:"+i.Opacity+";stroke:"+i.Fill+";stroke-width:"+(this.isTileMap?i.BorderWidth/Math.pow(2,this.options.factor-1):i.BorderWidth/this.options.factor)+";stroke-opacity:"+i.BorderOpacity:e.innerHTML="."+t+" {fill:"+i.Fill+";fill-opacity:"+i.Opacity+";stroke:"+i.BorderColor+";stroke-width:"+(this.isTileMap?i.BorderWidth/Math.pow(2,this.options.factor-1):i.BorderWidth/this.options.factor)+";stroke-opacity:"+i.BorderOpacity},a.prototype.customizeStyle=function(e,t,i,n){var s=document.getElementById(e);this.highlightStyle(s,t,i,n)},a.prototype.renderMarkers=function(){for(var e=document.getElementById(this.element.id+"_LayerCollections"),t=0;t<this.layerCount;t++){var i=document.getElementById(this.element.id+"_LayerIndex_"+t+"_Markers_Template_Group");this.markerTemplateTranslate(i,0,this.translatePoint.x,this.translatePoint.y,this.scaleFactor);var n=document.getElementById(this.element.id+"_LayerIndex_"+t+"_DataLabel_Group");this.labelTemplateTranslate(n,this.translatePoint.x,this.translatePoint.y,this.scaleFactor)}for(var s=0;s<e.childElementCount;s++){var o=e.children[s];if("g"===o.tagName){o.id.indexOf("_LayerIndex_")>-1&&parseFloat(o.id.split("_LayerIndex_")[1].split("_")[0]);for(var a=0;a<o.children.length;a++){var r=o.children[a];if(r.COMMENT_NODE!==r.nodeType)if(r.id.indexOf("_Polygon_Group")>-1)r.setAttribute("transform","scale("+Math.pow(2,this.scaleFactor-1)+") translate( "+this.translatePoint.x+" "+this.translatePoint.y+" )");else if(r.id.indexOf("_MarkerGroup")>-1)for(t=0;t<r.childElementCount;t++)this.markerTranslate(r.children[t],0,this.translatePoint.x,this.translatePoint.y,this.scaleFactor,!1);else if(r.id.indexOf("_Polygons_Group")>-1)if(this.isTileMap)for(var l=0;l<r.childElementCount;l++){var h=document.getElementById(r.children[l].id);for(t=0;t<h.childElementCount;t++)if(""!==h.children[t].id){var d=parseInt(h.children[t].id.split("_LayerIndex_")[1].split("_")[0],10),p=parseInt(h.children[t].id.split("_PolygonIndex_")[1].split("_")[0],10);this.polygonTranslate(h.children[t],0,d,p)}}else r.setAttribute("transform","scale("+this.scaleFactor+") translate( "+this.translatePoint.x+" "+this.translatePoint.y+" )");else if(r.id.indexOf("_line_Group")>-1)this.isTileMap?r.setAttribute("transform","scale("+Math.pow(2,this.scaleFactor-1)+") translate( "+this.translatePoint.x+" "+this.translatePoint.y+" )"):r.setAttribute("transform","scale("+this.scaleFactor+") translate( "+this.translatePoint.x+" "+this.translatePoint.y+" )");else if(r.id.indexOf("_dataLableIndex_Group")>-1){var c=Math.pow(2,this.scaleFactor-1);for(t=0;t<r.children.length;t++)this.dataLabelTranslate(r.children[t],0,this.translatePoint.x,this.translatePoint.y,c,"DataLabel")}}}}},a.prototype.setMarkerTranslate=function(e,t,i,n,s){"Balloon"!==this.dataSource[t][i][n][this.markerSettings[t][i].ShapeValuePath]&&"Balloon"!=this.markerSettings[t][i].Type||e.id.indexOf("cluster")>-1?"Rectangle"!==this.dataSource[t][i][n][this.markerSettings[t][i].ShapeValuePath]&&"Rectangle"!=this.markerSettings[t][i].Type||e.id.indexOf("cluster")>-1?this.isTileMap&&e.classList.contains("e-maps-cluster")?e.setAttribute("transform","translate( "+(s.x-this.areaRect.x)+" "+(s.y-this.areaRect.y)+" )"):e.setAttribute("transform","translate( "+s.x+" "+s.y+" )"):e.setAttribute("transform","translate( "+(s.x-this.markerSettings[t][i].Width/2)+" "+(s.y-this.markerSettings[t][i].Height/2)+" )"):e.setAttribute("transform","translate( "+(s.x-this.markerSettings[t][i].Width/2)+" "+(s.y-this.markerSettings[t][i].Height)+" )scale("+this.markerSettings[t][i].Width/30+" "+this.markerSettings[t][i].Height/30+" )")},a.prototype.markerTranslate=function(e,t,i,n,s,o){if(e.COMMENT_NODE!==e.nodeType&&e.TEXT_NODE!==e.nodeType){var a=parseInt(e.id.split("_LayerIndex_")[1].split("_")[0],10),r=parseInt(e.id.split("_MarkerIndex_")[1].split("_")[0],10),l=parseInt(e.id.split("_dataIndex_")[1].split("_")[0],10),h=sf.base.isNullOrUndefined(this.markerSettings[a][r].LatitudeValuePath)||""===this.markerSettings[a][r].LatitudeValuePath?sf.base.isNullOrUndefined(this.dataSource[a][r][l].Latitude)?"latitude":"Latitude":this.markerSettings[a][r].LatitudeValuePath,d=sf.base.isNullOrUndefined(this.markerSettings[a][r].LongitudeValuePath)||""===this.markerSettings[a][r].LongitudeValuePath?sf.base.isNullOrUndefined(this.dataSource[a][r][l].Longitude)?"longitude":"Longitude":this.markerSettings[a][r].LongitudeValuePath,p=0!==this.dataSource[a].length&&0!==this.dataSource[a][r].length?this.dataSource[a][r][l][d]:null,c=0!==this.dataSource[a].length&&0!==this.dataSource[a][r].length?this.dataSource[a][r][l][h]:null;if(c&&p)if(this.isTileMap){var g=this.convertTileLatLongToPoint({x:p,y:c},this.tileZoomLevel,this.tileTranslatePoint,!0);g.x=g.x+this.markerSettings[a][r].X,g.y=g.y+this.markerSettings[a][r].Y,o?(e.style.left=g.x-this.areaRect.x+"px",e.style.top=g.y-this.areaRect.y+"px"):this.setMarkerTranslate(e,a,r,l,g)}else{var u=this.convertGeoToPoint(c,p,t,a);u.x=(u.x+i)*s+this.markerSettings[a][r].X,u.y=(u.y+n)*s+this.markerSettings[a][r].Y,o?(e.style.left=u.x-this.areaRect.x+"px",e.style.top=u.y-this.areaRect.y+"px"):this.setMarkerTranslate(e,a,r,l,u)}}},a.prototype.polygonTranslate=function(e,t,i,n){if(!sf.base.isNullOrUndefined(this.polygon[i])){for(var s="",o=[],a=0;a<this.polygonDataSource[i][n].length;a++){var r=this.isTileMap?this.convertTileLatLongToPoint({x:this.polygonDataSource[i][n][a].Longitude,y:this.polygonDataSource[i][n][a].Latitude},this.tileZoomLevel,this.tileTranslatePoint,!0):this.convertGeoToPoint(this.polygonDataSource[i][n][a].Latitude,this.polygonDataSource[i][n][a].Longitude,t,i);o.push(r)}for(var l=0;l<o.length;l++)s+=0===l?"M "+o[l].x+","+o[l].y:" L "+o[l].x+","+o[l].y;s+=" z ",document.getElementById(this.id+"_LayerIndex_"+i+"_PolygonIndex_"+n).setAttribute("d",s)}},a.prototype.navigationTranslate=function(e,t,i,n){if(!sf.base.isNullOrUndefined(this.navigation[i])){var s=this.navigation[i][n].Angle,o=void 0,a=void 0,r=[];if(this.navigation[i][n].Latitude.length===this.navigation[i][n].Longitude.length)for(var l=0;l<this.navigation[i][n].Latitude.length;l++){var h=this.isTileMap?this.convertTileLatLongToPoint({x:this.navigation[i][n].Longitude[l],y:this.navigation[i][n].Latitude[l]},this.tileZoomLevel,this.tileTranslatePoint,!0):this.convertGeoToPoint(this.navigation[i][n].Latitude[l],this.navigation[i][n].Longitude[l],t,i);r.push(h)}for(var d=0;d<r.length-1;d++){s=1<(s=-1>s?-1:s)?1:s;var p=this.convertRadius(r[d],r[d+1]);s<=1&&s>0&&(o=0,r[d].x>r[d+1].x&&(o=1)),s>=-1&&s<0&&(o=1,r[d].x>r[d+1].x&&(o=0)),a=0===(s=Math.abs(s))?"M "+r[d].x+","+r[d].y+"L "+r[d+1].x+","+r[d+1].y+" ":"M "+r[d].x+","+r[d].y+" A "+(p/2+(1-s)*p/(10*s))+" "+(p/2+(1-s)*p/(10*s))+" 0,0,"+o+" , "+r[d+1].x+","+r[d+1].y+" ",document.getElementById(this.id+"_LayerIndex_"+i+"_NavigationIndex_"+n+"_Line_"+d).setAttribute("d",a)}}},a.prototype.dataLabelTranslate=function(e,t,i,n,s,o){if(e.COMMENT_NODE!==e.nodeType&&e.TEXT_NODE!==e.nodeType){var a,r,l=parseInt(e.id.split("_LayerIndex_")[1].split("_")[0],10),h=parseFloat(e.id.split("_shapeIndex_")[1].split("_")[0]);if("Template"!==o){a=(d=this.labelCollection[l][h]).LocationX,r=d.LocationY;a=(a+i)*s-d.BorderSize.Height/4+d.OffsetX,r=(r+n)*s+d.BorderSize.Height/4+d.OffsetY,e.id.indexOf("_RectIndex_")>-1?(e.setAttribute("x",(a-parseInt(e.getAttribute("width"))/2).toString()),e.setAttribute("y",(r-(5+parseInt(e.getAttribute("height"))/2)).toString())):e.setAttribute("transform","translate( "+a+" "+r+" )")}else{var d;a=((d=this.dataLabelTemplateCollection[l][h]).LocationX+i)*s+d.OffsetX,r=(d.LocationY+n)*s+d.OffsetY,e.style.left=a+"px",e.style.top=r+"px"}}},a.prototype.convertTileLatLongToPoint=function(e,t,i,n){var s=256*Math.pow(2,t),o=(e.x+180)/360,a=Math.sin(e.y*Math.PI/180),r=.5-Math.log((1+a)/(1-a))/(4*Math.PI),l=e.x,h=e.y;return n&&(l=o*s+.5+i.x,h=r*s+.5+i.y),{x:l,y:h}},a.prototype.panning=function(e,t,i,n,s,o){if(this.mergeSeparation(),this.isTileMap){a=this.tileTranslatePoint.x-e-(sf.base.Browser.isDevice&&1==s?e/2:0),r=this.tileTranslatePoint.y-t;this.tileTranslatePoint.x=a,this.tileTranslatePoint.y=r,(this.tileTranslatePoint.y>-10&&t<0||this.tileTranslatePoint.y<-256*(Math.pow(2,s)-2)&&t>0)&&(this.tileTranslatePoint.x=a+e,this.tileTranslatePoint.y=r+t),o&&(e=0,t=0),this.translatePoint.x=this.tileTranslatePoint.x/Math.pow(2,s-1),this.translatePoint.y=this.tileTranslatePoint.y/Math.pow(2,s-1),this.generateTiles(),this.renderMarkers()}else{this.shapeTranslatePoint;var a=this.shapeTranslatePoint.x-e/s,r=this.shapeTranslatePoint.y-t/s,l=document.getElementById(this.element.id+"_LayerCollections").getBoundingClientRect(),h=document.getElementById(this.element.id+"_svg").getBoundingClientRect(),d=document.getElementById(this.element.id+"_Legend_Group"),p=sf.base.isNullOrUndefined(d)?0:d.getClientRects()[0].height,c=e<0?l.left<=h.left+this.areaRect.x:l.left+l.width>=h.left+h.width+this.areaRect.x+this.marginLeft,g=t<0?l.top<=h.top+this.areaRect.y:l.top+l.height+p+this.marginTop>=h.top+h.height;c&&g?(this.shapeTranslatePoint={x:a,y:r},this.applyTransform(this.scaleFactor,this.shapeTranslatePoint)):c?(this.shapeTranslatePoint={x:a,y:this.shapeTranslatePoint.y},this.applyTransform(this.scaleFactor,this.shapeTranslatePoint)):g&&(this.shapeTranslatePoint={x:this.shapeTranslatePoint.x,y:r},this.applyTransform(this.scaleFactor,this.shapeTranslatePoint))}},a.prototype.applyTransform=function(e,t){for(var i=this.isTileMap?this.translatePoint.x:t.x,n=this.isTileMap?this.translatePoint.y:t.y,s=document.getElementById(this.element.id+"_LayerCollections"),o=0;o<s.children.length;o++){var a=s.children[o];if(a.COMMENT_NODE!==a.nodeType&&"g"===a.tagName)for(var r=this.calculateFactor(parseFloat(a.id.split("_LayerIndex_")[1].split("_")[0])),l=(a.id.indexOf("_LayerIndex_")>-1&&parseFloat(a.id.split("_LayerIndex_")[1].split("_")[0]),0);l<a.children.length;l++){var h=a.children[l];if(h.COMMENT_NODE!==h.nodeType&&!sf.base.isNullOrUndefined(h.id))if(h.id.indexOf("Polygon")>-1||h.id.indexOf("_line_Group")>-1)h.setAttribute("transform","scale("+e+") translate( "+i+" "+n+" )");else if(h.id.indexOf("_MarkerGroup")>-1)for(var d=0;d<h.children.length;d++)sf.base.isNullOrUndefined(h.children[d].id)||this.markerTranslate(h.children[d],r,i,n,e,!1);else if(h.id.indexOf("_bubble_Group_")>-1)for(d=0;d<h.children.length;d++){var p=h.children[d];if(!sf.base.isNullOrUndefined(p.id)){var c=parseFloat(p.id.split("_LayerIndex_")[1].split("_")[0]),g=parseFloat(p.id.split("_BubbleIndex_")[1].split("_")[0]),u=parseFloat(p.id.split("_BubbleIndex_")[1].split("_")[2]);if(this.bubbleCollection[c]&&this.bubbleCollection[c][g]){var m=this.bubbleCollection[c][g].BubbleProperties[u],f=(m.ZoomTranslatePoint.X+i)*e,y=(m.ZoomTranslatePoint.Y+n)*e;1==m.Type&&(f-=m.Radius,y-=m.Radius),p.setAttribute("transform","translate( "+f+" "+y+" )")}}}else if(h.id.indexOf("_dataLableIndex_Group")>-1)for(d=0;d<h.children.length;d++)this.dataLabelTranslate(h.children[d],r,i,n,e,"DataLabel")}}for(var _=0;_<this.layerCount;_++){r=this.calculateFactor(_);var b=document.getElementById(this.element.id+"_LayerIndex_"+_+"_Markers_Template_Group");this.markerTemplateTranslate(b,r,i,n,e);var x=document.getElementById(this.element.id+"_LayerIndex_"+_+"_DataLabel_Group");this.labelTemplateTranslate(x,i,n,e)}},a.prototype.markerTemplateTranslate=function(e,t,i,n,s){if(e)for(var o=0;o<e.childElementCount;o++)this.markerTranslate(e.children[o],t,i,n,s,!0)},a.prototype.labelTemplateTranslate=function(e,t,i,n){if(e)for(var s=this.isTileMap?Math.pow(2,n-1):n,o=0;o<e.childElementCount;o++)this.dataLabelTranslate(e.children[o],0,t,i,s,"Template")},a.prototype.calculateFactor=function(e){var t,i,n,s=1,o=this.baseMapBounds,a=this.areaRect.width,r=this.areaRect.height;if(o){var l=this.convertGeoToPoint(o.latitudeMin,o.longitudeMin,null,e),h=this.convertGeoToPoint(o.latitudeMax,o.longitudeMax,null,e);i=h.y-l.y,n=h.x-l.x}else i=n=500;return t=i<r?parseFloat(Math.abs(100*Number(r/Number(i.toString()+"e+1"))).toString().split(".")[0])/10:r/i,s=n<a?parseFloat(Math.abs(100*Number(a/Number(n.toString()+"e+1"))).toString().split(".")[0])/10:a/n,Math.min(s,t)},a.prototype.convertRadius=function(e,t){var i=t.x-e.x,n=t.y-e.y;return Math.sqrt(Math.pow(i,2)+Math.pow(n,2))},a.prototype.convertGeoToPoint=function(e,t,i,n){var s,o,a,r,l={width:this.areaRect.width,height:this.areaRect.height},h=this.degreesToRadians(e),d=this.degreesToRadians(t),p=null==i?Math.min(l.width,l.height):Math.min(l.width,l.height)*i,c=this.projectionType;if("Normal"!=this.geometryType[n])switch(c){case"Mercator":var g={x:p/2,y:p/2};s=g.x+t*(p/360);var u=this.calculateBound(Math.sin(this.degreesToRadians(e)),-.9999,.9999);o=g.y+.5*Math.log((1+u)/(1-u))*(-p/(2*Math.PI));break;case"Winkel3":((a=this.aitoff(d,h)).x+d/(Math.PI/2))/2,(a.y+h)/2;break;case"Miller":d,1.25*Math.log(Math.tan(Math.PI/4+.4*h));break;case"Eckert3":2/(r=Math.sqrt(Math.PI*(4+Math.PI)))*d*(1+Math.sqrt(1-4*h*h/(Math.PI*Math.PI))),4/r*h;break;case"AitOff":(a=this.aitoff(d,h)).x,a.y;break;case"Eckert5":d*(1+Math.cos(h))/Math.sqrt(2+Math.PI),2*h/Math.sqrt(2+Math.PI);break;case"Equirectangular":d,h;break;case"Eckert6":r=(1+Math.PI/2)*Math.sin(h);for(var m=1/0,f=0;f<10&&Math.abs(m)>1e-6;f++)h-=m=(h+Math.sin(h)-r)/(1+Math.cos(h));r=Math.sqrt(2+Math.PI),d*(1+Math.cos(h))/r,2*h/r}else s=null==i?t:Math.abs((t-this.baseMapBounds.longitudeMin)*i),o=null==i?e:Math.abs((this.baseMapBounds.latitudeMax-e)*i);return{x:s,y:o}},a.prototype.aitoff=function(e,t){var i=Math.cos(t),n=Math.sin(Math.cos(i*Math.cos(e/=2)));return{x:2*i*Math.sin(e)*n,y:Math.sin(t)*n}},a.prototype.degreesToRadians=function(e){return e*(Math.PI/180)},a.prototype.calculateBound=function(e,t,i){return sf.base.isNullOrUndefined(t)||(e=Math.max(e,t)),sf.base.isNullOrUndefined(i)||(e=Math.min(e,i)),e},a.prototype.getTileGeoLocation=function(e,t){var i=this.getBound(this.element.id),n=this.getBound(this.element.id+"_tile_parent");return this.pointToLatLong(e+this.areaRect.x-(n.x-i.x),t+this.areaRect.y-(n.y-i.y))},a.prototype.pointToLatLong=function(e,t){var i=256*Math.pow(2,this.scaleFactor),n=this.clip(e-this.translatePoint.x*Math.pow(2,this.scaleFactor-1),0,i-1)/i-.5,s=.5-this.clip(t-this.translatePoint.y*Math.pow(2,this.scaleFactor-1),0,i-1)/i;return{latitude:90-360*Math.atan(Math.exp(2*-s*Math.PI))/Math.PI,longitude:360*n}},a.prototype.generateTiles=function(){var e,t=this.baseMapBounds.availableSize.width,i=this.baseMapBounds.availableSize.height,n=0,s=0,o=0;n=e=Math.pow(2,this.tileZoomLevel);var a=[];this.tileTranslatePoint.x+256*n<t&&(s=this.tileTranslatePoint.x>0?Math.ceil(this.tileTranslatePoint.x/256):0,o=this.tileTranslatePoint.x+256*n<t?Math.ceil((t-(this.tileTranslatePoint.x+256*n))/256):0),n+=s+o,n=this.horizontalPanXCount>=n?this.horizontalPanXCount:n,this.horizontalPan=!1;for(var r=Math.min(e,(-this.tileTranslatePoint.y+i)/256+1),l=Math.min(n,(-this.tileTranslatePoint.x+t+256*o)/256+1),h=-(this.tileTranslatePoint.x+256*s+256)/256,d=-(this.tileTranslatePoint.y+256)/256,p=Math.round(h);p<Math.round(l);p++)for(var c=Math.round(d);c<Math.round(r);c++){var g=256*p+this.tileTranslatePoint.x,u=256*c+this.tileTranslatePoint.y;if(g>-256&&g<=t&&u>-256&&u<i&&c>=0){var m=p;p<0&&(m=m%e+e);var f={x:m%e,y:c,left:g,top:u,height:256,width:256,src:null};-1===this.urlTemplate.indexOf("virtualearth")?f.src=this.urlTemplate.replace("level",this.scaleFactor.toString()).replace("tileX",f.x.toString()).replace("tileY",f.y.toString()):f.src=this.getBingMap(f,this.urlTemplate),a.push(f)}}var y=document.getElementById(this.element.id+"_animated_tiles");for(c=0;c<a.length;c++){var _=document.getElementById(this.element.id+"tile"+c),b=null,x=!1;_?(_.style.removeProperty("display"),b=_.children[0]):((_=document.createElement("div")).id=this.element.id+"tile"+c,_.style.userSelect="none",b=document.createElement("img"),x=!0),b.src!==a[c].src&&b.setAttribute("src",a[c].src),_.style.position="absolute",_.style.left=a[c].left+"px",_.style.top=a[c].top+"px",_.style.height=a[c].height+"px",_.style.width=a[c].width+"px",x&&(_.appendChild(b),y.appendChild(_))}for(var v=a.length;v<y.childElementCount;v++){for(var I=!1,T=0;T<this.currentTiles.childElementCount;T++)I||this.currentTiles.children[T].id!==y.children[v].id||(I=!0);I?y.children[v].style.display="none":y.removeChild(y.children[v])}},a.prototype.getBingMap=function(e,t){for(var i="",n=["t0","t1","t2","t3"],s=Math.min(this.tileZoomLevel,this.options.maxZoom);s>0;s--){var o=0,a=1<<s-1;0!=(e.x&a)&&o++,0!=(e.y&a)&&(o+=2),i=i+""+o}var r=n[Math.min(parseInt(i.substr(i.length-1,1),10),n.length)];return(t=t.replace("{quadkey}",i).replace("{subdomain}",r))+"&mkt=en-US&ur=IN"},a.prototype.clip=function(e,t,i){return Math.min(Math.max(e,t),i)},a.prototype.svgPoint=function(e,t,i){var n=this.svg.createSVGPoint();return n.x=t-(this.isTileMap?this.areaRect.x:0),n.y=i-(this.isTileMap?this.areaRect.y:0),n.matrixTransform(e.getScreenCTM().inverse())},a.prototype.clusterExpand=function(e,t){var i,n=JSON.parse(t.dataSource),s=parseInt(e[0].split("_LayerIndex_")[1].split("_")[0],10),o=this.markerClusterCollection[s],a=e[0].split("_LayerIndex_"),r=parseInt(a[1].split("_")[0],10),l=[];if(e[0].indexOf("_MarkerIndex_")>-1){var h=parseInt(a[1].split("_MarkerIndex_")[1].split("_")[0],10),d=parseInt(a[1].split("_dataIndex_")[1].split("_")[0],10);if(!isNaN(h)){i=n[d];var p=[];if(e[0].indexOf("_cluster_")>-1&&o.AllowClustering)for(var c=0;c<n.length;c++)n[c].Latitude===i.Latitude&&n[c].Longitude===i.Longitude&&p.push({data:i,index:r});if(e[0].indexOf("_cluster_")>-1){var g=document.getElementById(e[0].indexOf("_datalabel_")>-1?e[0].split("_datalabel_")[0]:e[0]),u=void 0;"Balloon"===this.markerClusterCollection[s].Shape||sf.base.isNullOrUndefined(g)?sf.base.isNullOrUndefined(g.firstElementChild)||(u=g.firstElementChild.innerHTML.split(",").map(String)):u=g.innerHTML.split(",").map(String),p=[];for(var m=0;m<u.length;m++){var f=this.dataSource[s][parseInt(u[m].split(":")[0])][parseInt(u[m].split(":")[1])];p.push({data:f,index:parseInt(u[m].split(":")[1])}),f.text=""}!1,l.push({data:p,layerIndex:r,markerIndex:h,dataIndex:d,targetClusterIndex:+(e[0].split("_cluster_")[1].indexOf("_datalabel_")>-1?e[0].split("_cluster_")[1].split("_datalabel_")[0]:e[0].split("_cluster_")[1]),isClusterSame:!1})}}}if(l.length>0){this.clusterData=l;var y=document.getElementById(a[0]+"_LayerIndex_"+s+"_MarkerGroup");this.markerClusterExpandCheck?(this.mergeSeparateCluster(this.clusterData,a[0]),this.markerClusterExpandCheck=!1):(this.clusterSeparate(this.clusterData,y,!0,a[0],o),this.markerClusterExpandCheck=!0)}},a.prototype.mergeSeparateCluster=function(e,t){var i,n=e[0].layerIndex,s=e[0].targetClusterIndex,o=e[0].markerIndex,a=t+"_LayerIndex_"+n+"_MarkerIndex_"+o+"_dataIndex_"+e[0].dataIndex+"_cluster_"+s,r=this.getElementId(a),l=this.getElementId(a+"_datalabel_"+s);sf.base.isNullOrUndefined(r)||(r.style.visibility="visible"),sf.base.isNullOrUndefined(l)||(l.style.visibility="visible");var h=e[0].data.length;if(0!=this.clusterLocation.length){var d=[];d="g"==r.tagName?r.firstElementChild.innerHTML.split(",").map(String):r.innerHTML.split(",").map(String);for(var p=0;p<h;p++){var c=parseInt(d[p].split(":")[0]),g=parseInt(d[p].split(":")[1]),u=t+"_LayerIndex_"+n+"_MarkerIndex_"+c;if(i=this.getElementId(u+"_dataIndex_"+g),!sf.base.isNullOrUndefined(i)){if(-1===i.parentElement.id.indexOf("Template")){var m=this.markerSettings[n][o];"Balloon"===m.Type?i.setAttribute("transform","translate( "+this.clusterLocation[p].x+" "+this.clusterLocation[p].y+")scale( "+m.Width/30+" "+m.Height/30+" ) "):i.setAttribute("transform","translate( "+this.clusterLocation[p].x+" "+this.clusterLocation[p].y+")")}else i.style.left=this.clusterLocation[p].x+"px",i.style.top=this.clusterLocation[p].y+"px";i.style.visibility="hidden",i.style.content="marker",i.classList.add("e-maps-marker-hidden")}}}this.clusterLocation=[],this.removeElement(t+"_LayerIndex_"+n+"_MarkerIndex_"+o+"_markerClusterConnectorLine")},a.prototype.removeElement=function(e){var t=document.getElementById(e);sf.base.isNullOrUndefined(t)||t.remove()},a.prototype.drawPath=function(e){var t=document.getElementById(e.id);return sf.base.isNullOrUndefined(t)&&(t=document.createElementNS("http://www.w3.org/2000/svg","path")),t.setAttribute("id",e.id),t.setAttribute("d",e.d),t.setAttribute("opacity",e.opacity.toString()),t.setAttribute("stroke",e.stroke),t.setAttribute("stroke-width",e.strokeWidth.toString()),sf.base.isNullOrUndefined(e.fill)||t.setAttribute("fill",e.fill),t},a.prototype.clusterSeparate=function(e,t,i,n,o){var a=e[0].layerIndex,r=e[0].markerIndex,l=e[0].targetClusterIndex,h=e[0].dataIndex,d=(document.getElementById(n).getBoundingClientRect(),i?this.getElementId:t.querySelector.bind(t)),p=i?"":"#",c=n+"_LayerIndex_"+a+"_MarkerIndex_"+r,g=c+"_dataIndex_"+h+"_cluster_"+l,u=d(p+""+g),m=d(p+""+g+"_datalabel_"+l);u.style.visibility="hidden",m.style.visibility="hidden";var f=d(p+""+c+"_dataIndex_"+h),y=+u.getAttribute("transform").split("translate(")[1].trim().split(" ")[0],_=+u.getAttribute("transform").split("translate(")[1].trim().split(" ")[1].split(")")[0].trim(),b=30,x=6.28*b,v=0,I=Math.round(x/25);v+=I;var T=e[0].data.length,M=Math.round(25/x*100),S=(M=T<I?100/T:M)/100*360,C=T<I?45:0,k=1,B="M "+y+" "+_+" ",O="",P=[];P="g"==u.tagName?u.firstElementChild.innerHTML.split(",").map(String):u.innerHTML.split(",").map(String);for(var L=0;L<T;L++){var w=parseInt(P[L].split(":")[0]),N=parseInt(P[L].split(":")[1]),E=n+"_LayerIndex_"+a+"_MarkerIndex_"+w;if(v===L||Math.round(C)>=360){for(C=0,x=6.28*(b=30*++k),I=Math.round(x/25),M=Math.round(25/x*100);(0===M?1:M)*I<100;)I++;S=M/100*360,v+=I}var A=y+b*Math.sin(2*Math.PI*C/360),Z=_+b*Math.cos(2*Math.PI*C/360);if(O+=B+"L "+A+" "+Z+" ",f=d(p+""+E+"_dataIndex_"+N),sf.base.isNullOrUndefined(f.getAttribute("transform")))f.parentElement.id.indexOf("Template")>-1&&this.clusterLocation.push({x:parseFloat(f.style.left.replace("px","")),y:parseFloat(f.style.top.replace("px",""))});else{var R=parseFloat(f.getAttribute("transform").split("translate(")[1].trim().split(" ")[0]),H=parseFloat(f.getAttribute("transform").split("translate(")[1].trim().split(" ")[1].split(")")[0].trim());this.clusterLocation.push({x:R,y:H})}var D=this.markerSettings[a][w];-1===f.parentElement.id.indexOf("Template")?"Balloon"===D.Type?f.setAttribute("transform","translate( "+(A-D.Width/2)+" "+(Z-D.Height)+"  ) scale( "+D.Width/30+" "+D.Height/30+" ) "):f.setAttribute("transform","translate( "+A+" "+Z+")"):(f.style.left=A+"px",f.style.top=Z+"px"),f.style.visibility="visible",m.style.visibility="hidden",C+=S}var F={d:O,id:n+"_LayerIndex_"+a+"_MarkerIndex_"+r+"_dataIndex_"+h+"_markerClusterConnectorLine",stroke:sf.base.isNullOrUndefined(o.LineColor)?"#000000":o.LineColor,opacity:o.LineOpacity,strokeWidth:o.LineWidth};t=i?d(n+"_LayerIndex_"+a+"_MarkerGroup"):t;var U=s(n+"_LayerIndex_"+a+"_MarkerIndex_"+r+"_markerClusterConnectorLine");U.appendChild(this.drawPath(F)),t.insertBefore(U,t.querySelector("#"+c+"_dataIndex_0"))},a.prototype.getElementId=function(e){return document.getElementById(e)},a.prototype.shapeHighlightHandle=function(e,t,i){var n=this.options.layerHighlightSettings[i];this.handleHighlight(this.targetElementPath,"Shape",{BorderColor:n.BorderColor,BorderWidth:n.BorderWidth/this.scaleFactor,BorderOpacity:n.BorderOpacity,Enable:n.Enable,EnableMultiSelect:n.EnableMultiSelect,Fill:e,Opacity:t})},a.prototype.itemHighlightHandle=function(e,t,i,n,s){if("Bubble"===s){var o=this.options.bubbleHighlightSettings[i][n];this.handleHighlight(this.targetElementPath,"Bubble",{BorderColor:o.BorderColor,BorderWidth:o.BorderWidth,BorderOpacity:o.BorderOpacity,Enable:o.Enable,EnableMultiSelect:o.EnableMultiSelect,Fill:e,Opacity:t})}if("Marker"===s){var a=this.options.markerHighlightSettings[i][n];this.handleHighlight(this.targetElementPath,"Marker",{BorderColor:a.BorderColor,BorderWidth:a.BorderWidth,BorderOpacity:a.BorderOpacity,Enable:a.Enable,EnableMultiSelect:a.EnableMultiSelect,Fill:e,Opacity:t})}if("Navigation"===s){for(var r=this.options.navigationHighlightSettings[i][n],l=0;l<=this.navigation[i][n].Latitude.length;l++){var h=document.getElementById(this.element.id+"_LayerIndex_"+i+"_NavigationIndex_0_Line_"+l);this.handleHighlight(h,"Navigation",{BorderColor:r.BorderColor,BorderWidth:r.BorderWidth,BorderOpacity:r.BorderOpacity,Enable:r.Enable,EnableMultiSelect:r.EnableMultiSelect,Fill:e,Opacity:t})}if(""!==this.targetElementPath.getAttribute("marker-start")||""!==this.targetElementPath.getAttribute("marker-end")){var d=document.getElementById(this.targetElementPath.id+"_triangle");this.handleHighlight(d,"Arrow",{BorderColor:d.BorderColor,BorderWidth:d.BorderWidth,BorderOpacity:d.BorderOpacity,Enable:d.Enable,EnableMultiSelect:d.EnableMultiSelect,Fill:e,Opacity:t})}}if("Polygon"===s){var p=this.options.polygonHighlightSettings[i],c=document.getElementById(this.element.id+"_LayerIndex_"+i+"_PolygonIndex_"+n);this.handleHighlight(c,"Polygon",{BorderColor:p.BorderColor,BorderWidth:p.BorderWidth,BorderOpacity:p.BorderOpacity,Enable:p.Enable,EnableMultiSelect:p.EnableMultiSelect,Fill:e,Opacity:t})}},a.prototype.datalabelAnimate=function(e,t,i,n){var s=0;new sf.base.Animation({}).animate(e,{duration:t,delay:0,progress:function(t){t.timeStamp>t.delay&&(s=(t.timeStamp-t.delay)/t.duration,e.setAttribute("style","user-select: none; visibility: visible;"),e.setAttribute(n?"fill-opacity":"opacity",(i*s).toString()))},end:function(t){e.style.visibility="visible",e.setAttribute(n?"fill-opacity":"opacity",i.toString())}})},a.prototype.elementAnimate=function(e,t,i,n,s,o,a,r,l,h,d){var p=this;sf.base.isNullOrUndefined(a)&&(a=0);var c=n,g=s,u=0;new sf.base.Animation({}).animate(e,{duration:i,delay:t,progress:function(t){t.timeStamp>t.delay&&(u=(t.timeStamp-t.delay)/t.duration,e.style.visibility="visible",e.setAttribute("transform","translate( "+(c-a*u)+" "+(g-a*u)+" ) scale("+u+")"))},end:function(t){e.style.visibility="visible",e.setAttribute("transform",""),"Balloon"===r||"Balloon"===l?e.setAttribute("transform","translate( "+n+" "+s+"  ) scale( "+h/30+" "+d/30+" ) "):e.setAttribute("transform","translate( "+n+" "+s+"  )"),sf.base.isNullOrUndefined(p.dotNetRef)||p.dotNetRef.invokeMethodAsync("TriggerAnimation",e)}})},a.prototype.markerAnimation=function(e,t){for(var i=JSON.parse(t.markerData),n=0;n<i.length;n++)for(var s=0;s<i[n].MarkerAnimation.length;s++)if(i[n].MarkerAnimation[s].IsMarkerShape)for(var o=0;o<i[n].MarkerAnimation[s].DataSourceLength;o++){var a=document.getElementById(e+"_LayerIndex_"+n+"_MarkerIndex_"+s+"_dataIndex_"+o),r=sf.base.isNullOrUndefined(this.dataSource[i[n].MarkerAnimation[s].Layer][s][o][i[n].MarkerAnimation[s].ShapeValuePath])?" ":this.dataSource[i[n].MarkerAnimation[s].Layer][s][o][i[n].MarkerAnimation[s].ShapeValuePath];this.elementAnimate(a,i[n].MarkerAnimation[s].AnimationDelay,i[n].MarkerAnimation[s].AnimationDuration,a.transform.baseVal[0].matrix.e,a.transform.baseVal[0].matrix.f,null,0,i[n].MarkerAnimation[s].MarkerType,r,i[n].MarkerAnimation[s].Width,i[n].MarkerAnimation[s].Height)}for(n=0;n<this.labelCollection.length;n++){if(null!=this.labelCollection[n]&&this.labelCollection[n].length>0&&this.labelCollection[n][0].AnimationDuration>0)for(s=0;s<(null!=this.labelCollection[n]?this.labelCollection[n].length:0);s++){var l=this.labelCollection[n][s],h=document.getElementById(e+"_LayerIndex_"+n+"_shapeIndex_"+s+"_LabelIndex_"+s),d=document.getElementById(e+"_LayerIndex_"+n+"_shapeIndex_"+s+"_RectIndex_"+s);sf.base.isNullOrUndefined(h)||(this.datalabelAnimate(h,l.AnimationDuration,l.Font.Opacity,!1),sf.base.isNullOrUndefined(d)||this.datalabelAnimate(d,l.AnimationDuration,l.Opacity,!0))}}},a.prototype.bubbleAnimation=function(e,t){for(var i=JSON.parse(t.bubbleData),n=0;n<i.length;n++)for(var s=0;s<i[n].MarkerAnimation.length;s++)for(var o=0;o<i[n].MarkerAnimation[s].DataSourceLength;o++){var a=document.getElementById(e+"_LayerIndex_"+n+"_BubbleIndex_"+s+"_dataIndex_"+o);sf.base.isNullOrUndefined(a)||this.elementAnimate(a,i[n].MarkerAnimation[s].AnimationDelay,i[n].MarkerAnimation[s].AnimationDuration,a.transform.baseVal[0].matrix.e,a.transform.baseVal[0].matrix.f,null,0," "," ",0,0)}},a.prototype.toolbarOpacity=function(e,t){this.removeZoomOpacity(t,e,t,e,t,e,t,e,t,e)},a.prototype.clusterMarkerProcess=function(){for(var e=!1,t=0;t<this.layerCount;t++)e||(this.mergeSeparation(),this.removeCluster(),e=!0),this.clusterMarkers(t)},a.prototype.clusterMarkers=function(e){var t=this.markerClusterCollection[e];if(t.AllowClustering){var i=void 0,n=void 0,a=this.id,r=[],l=[],h=0,d=0,p=s(a+"_LayerIndex_"+e+"_cluster"),c=3.75,g=0,u=void 0,m=void 0,f=void 0,y=[],_=void 0,b=document.getElementById(a),x=document.getElementById(a+"_LayerIndex_"+e+"_MarkerGroup");if(!sf.base.isNullOrUndefined(x)&&x.childElementCount<=0&&x.childElementCount<=0&&(x=document.getElementById(a+"_LayerIndex_"+e+"_Markers_Template_Group"),_=!0),x=sf.base.isNullOrUndefined(x)?document.getElementById(a+"_LayerIndex_"+e+"_Markers_Template_Group"):x)for(var v=0;v<x.childElementCount;v++)if(!(x.children[v].id.indexOf("markerClusterConnectorLine")>-1)){r=[];var I=parseInt(x.children[v].id.split("_MarkerIndex_")[1].split("_")[0],10),T=parseInt(x.children[v].id.split("_dataIndex_")[1].split("_")[0],10),M=("Balloon"===this.markerSettings[e][I].Type||"Image"===this.markerSettings[e][I].Type)&&sf.base.isNullOrUndefined(this.markerSettings[e][I].ShapeValuePath);if("hidden"!==x.children[v].style.visibility){var S=(m=x.children[v]).getBoundingClientRect();if(r.push(I.toString()+":"+T.toString()),null!==S){for(var C=v+1;C<x.childElementCount;C++)if("hidden"!==x.children[C].style.visibility){m=x.children[C];var k=parseInt(m.id.split("_MarkerIndex_")[1].split("_")[0],10),B=parseInt(m.id.split("_dataIndex_")[1].split("_")[0],10),O=m.getBoundingClientRect();null!==O&&(S.left>O.right||S.right<O.left||S.top>O.bottom||S.bottom<O.top||(l.push(O),x.children[C].style.visibility="hidden",x.children[C].style.content="marker",x.children[C].classList.add("e-maps-marker-hidden"),r.push(k.toString()+":"+B.toString())))}if(h=S.left+S.width/2,d=S.top+(M?S.height:S.height/2),l.length>0){var P=b.getBoundingClientRect();h=this.isTileMap?h-P.left-this.areaRect.x:h-P.left,d=this.isTileMap?d-P.top-this.areaRect.y:d-P.top;var L=parseInt(x.children[v].id.split("_dataIndex_")[1].split("_")[0],10),w=parseInt(x.children[v].id.split("_LayerIndex_")[1].split("_")[0],10);x.children[v].getAttribute("transform")?(h+=this.isTileMap?0:_?this.areaRect.x:0,d+=this.isTileMap?0:_?this.areaRect.y:0):(h=parseFloat(x.children[v].style.left.replace("px",""))+(this.isTileMap?0:_?this.areaRect.x:0),d=parseFloat(x.children[v].style.top.replace("px",""))+(this.isTileMap?0:_?this.areaRect.y:0));var N=a+"_LayerIndex_"+w+"_MarkerIndex_"+I+"_dataIndex_"+L+"_cluster_"+g,E=a+"_LayerIndex_"+w+"_MarkerIndex_"+I+"_dataIndex_"+L+"_cluster_"+g+"_datalabel_"+g;g++;var A=void 0;switch(t.Shape){case"Circle":(A=this.createClusterShape("circle",N,t.Fill,t.Opacity,"translate( "+h+" "+d+" )",!0,r,t)).setAttribute("r",(t.Height+t.Width)/4);break;case"Rectangle":(A=this.createClusterShape("rect",N,t.Fill,t.Opacity,"translate( "+h+" "+d+" )",!0,r,t)).setAttribute("x",-t.Width/2),A.setAttribute("y",-t.Height/2),A.setAttribute("height",t.Height),A.setAttribute("width",t.Width);break;case"Balloon":f=s(N);(A=this.createClusterShape("path",N,t.Fill,t.Opacity,"translate( "+-t.Width/2+", "+-t.Height+" ) scale( "+t.Width/30+", "+t.Height/30+" )",!0,r,t)).classList.remove("clusterGroup"),A.setAttribute("d","M15,0C8.8,0,3.8,5,3.8,11.2C3.8,17.5,9.4,24.4,15,30c5.6-5.6,11.2-12.5,11.2-18.8C26.2,5,21.2,0,15,0z M15,16c-2.8,0-5-2.2-5-5s2.2-5,5-5s5,2.2,5,5S17.8,16,15,16z"),f.appendChild(A),f.style.visibility="visible",f.style.cursor="pointer",f.setAttribute("class","clusterGroup"),_&&f.classList.add("e-maps-cluster"),f.setAttribute("transform","translate( "+h+" "+d+" )");break;case"Image":var Z=-(0+t.Width/2),R=-(0+t.Height/2);(A=this.createClusterShape("image",N,null,null,"translate( "+h+" "+d+" )",!0,r,t)).setAttributeNS(null,"height",t.Height),A.setAttributeNS(null,"width",t.Width),A.setAttributeNS("http://www.w3.org/1999/xlink","href",t.ImageUrl),A.setAttributeNS(null,"x",Z),A.setAttributeNS(null,"y",R);break;case"Cross":case"Diamond":case"Star":case"Triangle":case"HorizontalLine":case"VerticalLine":case"InvertedTriangle":case"Pentagon":var H;H=o(t.Shape,{height:t.Height,width:t.Width},{x:0,y:0}),(A=this.createClusterShape("path",N,t.Fill,t.Opacity,"translate( "+h+" "+d+" )",!1,r,t)).setAttribute("d",H),"Cross"!==t.Shape&&"HorizontalLine"!==t.Shape&&"VerticalLine"!==t.Shape||(A.setAttribute("stroke",t.Fill),A.setAttribute("stroke-width",((t.Width+t.Height)/4).toString()))}"Balloon"!==t.Shape&&(_&&this.isTileMap||A.setAttribute("class","clusterGroup")),_&&"Balloon"!==t.Shape&&A.classList.add("e-maps-cluster");var D=document.createElementNS("http://www.w3.org/2000/svg","text");D.setAttribute("id",E),D.setAttribute("x","0"),D.setAttribute("y",c.toString()),D.setAttribute("fill",t.LabelColor),D.setAttribute("font-size",t.LabelSize),D.setAttribute("font-style",t.LabelFontStyle),D.setAttribute("font-family",t.LabelFontFamily),D.setAttribute("font-weight",t.LabelFontWeight),D.setAttribute("text-anchor","middle"),D.setAttribute("transform","translate( "+h+" "+d+" )"),D.setAttribute("opacity",t.LabelOpacity),D.style.visibility="visible",D.style.cursor="pointer",D.setAttribute("class","clusterGroup"),D.innerHTML=(l.length+1).toString(),_&&D.classList.add("e-maps-cluster"),p.appendChild(D),p.setAttribute("class","clusterGroup"),"Balloon"!==t.Shape?p.appendChild(A):p.appendChild(f),x.children[v].style.visibility="hidden",x.children[v].style.content="marker",x.children[v].classList.add("e-maps-marker-hidden")}l=[]}}}if(p.childElementCount>0){var F=document.getElementById(a+"_LayerIndex_"+e);F.appendChild(p),u=document.getElementById(a+"_LayerIndex_"+e+"_MarkerGroup");for(v=0;v<p.childElementCount;v++)if("hidden"!==p.children[v].style.visibility&&null!==(i=(m=p.children[v]).getBoundingClientRect())&&!(m.id.indexOf("_datalabel_")>-1)){for(C=v+1;C<p.childElementCount;C++)if("hidden"!==p.children[C].style.visibility){var U=p.children[C];I=parseInt(U.id.split("_MarkerIndex_")[1].split("_")[0],10),T=parseInt(U.id.split("_dataIndex_")[1].split("_")[0],10);null===(n=U.getBoundingClientRect())||U.id.indexOf("_datalabel_")>-1||i.left>n.right||i.right<n.left||i.top>n.bottom||i.bottom<n.top||(y.push(U),y.push(p.children[C-1]),p.children[C].style.visibility="hidden",p.children[C-1].style.visibility="hidden",r.push(I.toString()+":"+T.toString()))}if(y.length>0){m=p.children[v];for(var X=0;X<y.length;X++)"g"===m.tagName?m.children[0].textContent=m.children[0].textContent+","+y[X].textContent:m.textContent=m.textContent+","+y[X].textContent,p.children[v-1].textContent=(+p.children[v-1].textContent+ +y[X+1].textContent).toString(),X++}y=[]}for(;0<p.children.length;)u.insertBefore(p.children[0],u.firstChild);(F=document.getElementById(a+"_LayerIndex_"+e)).appendChild(u),F.removeChild(p)}}},a.prototype.createClusterShape=function(e,t,i,n,s,o,a,r){var l=document.createElementNS("http://www.w3.org/2000/svg",e);return l.setAttribute("id",t),l.setAttribute("fill",i),l.setAttribute("stroke",r.BorderColor),l.setAttribute("stroke-width",r.BorderWidth),l.setAttribute("stroke-dasharray",r.DashArray),l.setAttribute("fill-opacity",n),l.setAttribute("stroke-opacity",r.BorderOpacity),l.setAttribute("transform",s),l.setAttribute("style","visibility:visible"),l.setAttribute("style","cursor: pointer"),o?l.setAttribute("class","clusterGroup"):l.setAttribute(null,"class","clusterGroup"),l.innerHTML=a,l},a.prototype.mergeSeparation=function(){this.markerClusterExpandCheck&&this.clusterData&&(this.mergeSeparateCluster(this.clusterData,this.id),this.markerClusterExpandCheck=!1)},a.prototype.click=function(e){var t=this;if("pointerdown"!==e.type||"mouse"!==e.pointerType){var i=e.target.id,n=this.getMousePosition(e.pageX,e.pageY);i.split("_")[0];if((i.indexOf("_Left_Page_Rect")>-1||i.indexOf("_Right_Page_Rect")>-1)&&!sf.base.isNullOrUndefined(this.dotNetRef)&&this.dotNetRef.invokeMethodAsync("TriggerLegendPaging",i.indexOf("_Left_Page_Rect")>-1?1:0),i.indexOf("_Zooming_")>-1&&this.options.enableZoom){var s="pointerdown"===e.type?"click":e.type,o=1;if(this.mergeSeparation(),i.indexOf("_ZoomIn_")>-1&&(o=0===this.factorCount?this.options.factor+1:this.scaleFactor+1,this.factorCount++,this.triggerEnableZoom(o,i.split("_Zooming_ToolBar_")[1].split("_")[0],s)),i.indexOf("_ZoomOut_")>-1&&(o=0===this.factorCount?this.options.factor-1:this.scaleFactor-1,this.factorCount++,this.triggerEnableZoom(o,i.split("_Zooming_ToolBar_")[1].split("_")[0],s)),i.indexOf("_Reset_")>-1&&!sf.base.isNullOrUndefined(this.dotNetRef)){this.zoomClick=!1;var a=i.split("_Zooming_ToolBar_")[1].split("_")[0];this.removeCluster(),this.dotNetRef.invokeMethodAsync("TriggerZoom",a,s)}i.indexOf("_Zoom_")>-1&&"0.3"!==document.getElementById(this.id+"_Zooming_ToolBar_Zoom_Rect").getAttribute("stroke-opacity")&&(this.zoomClick=!0,this.allowPanning=!1,this.enableSelectionZoom=!0),i.indexOf("_Pan_")>-1&&"0.3"!==document.getElementById(this.id+"_Zooming_ToolBar_Pan_Rect").getAttribute("stroke-opacity")&&(this.zoomClick=!1,this.allowPanning=!1,this.enableSelectionZoom=!1,this.isPan=!0)}if((i.indexOf("MapAreaBorder")>-1||i.indexOf("shapeIndex")>-1||i.indexOf("_Legend_")>-1||i.indexOf("BubbleIndex")>-1||i.indexOf("Zooming")>-1||i.indexOf("MapMargin")>-1||""===i||this.markerClusterExpandCheck)&&this.mergeSeparation(),(""===i||-1===i.indexOf(this.element.id))&&!sf.base.isNullOrUndefined(e.target.offsetParent)){var r=e.target.offsetParent.id;i=r.indexOf("_MarkerIndex_")>-1?r:i}if(i.indexOf("_LayerIndex_")>-1&&i.indexOf("_MarkerIndex_")>-1&&!sf.base.isNullOrUndefined(this.dotNetRef)){var l=parseInt(i.split("_LayerIndex_")[1].split("_")[0],10),h=parseInt(i.split("_MarkerIndex_")[1].split("_")[0],10),d=parseInt(i.split("_dataIndex_")[1].split("_")[0],10),p=document.getElementById(i),c=void 0;(i.indexOf("cluster")>-1||i.indexOf("Cluster")>-1)&&(this.mergeSeparation(),i.indexOf("_cluster_")>-1&&i.indexOf("_datalabel_")>-1&&(p=document.getElementById(i.split("_datalabel")[0])),"Balloon"===this.markerClusterCollection[l].Shape?!sf.base.isNullOrUndefined(p.firstElementChild)&&p.firstElementChild.innerHTML.length>0&&(c=p.firstElementChild.innerHTML.split(",")):!sf.base.isNullOrUndefined(p)&&p.innerHTML.length>0&&(c=p.innerHTML.split(","))),this.removeAllHighlight(),(!(i.indexOf("markerClusterConnectorLine")>-1)&&i.indexOf("_MarkerIndex_")>-1||i.indexOf("cluster")>-1&&("click"==e.type||"pointerdown"==e.type))&&this.dotNetRef.invokeMethodAsync("TriggerMarkerClick",l,h,d,i,n.x,n.y,c,i.indexOf("cluster")>-1?"cluster":"marker",this.isTouch,e.layerX,e.layerY)}if(i.indexOf("shapeIndex")>-1&&"Click"===this.options.tooltipDisplayMode&&!sf.base.isNullOrUndefined(this.dotNetRef)){l=parseInt(i.split("_LayerIndex_")[1].split("_")[0],10);var g=parseInt(i.split("_shapeIndex_")[1].split("_")[0],10);this.dotNetRef.invokeMethodAsync("TriggerShapeTooltip",i,n.x,n.y,l,g,"Shape",0)}if(i.indexOf("_PolygonIndex_")>-1&&"Click"===this.options.tooltipDisplayMode&&!sf.base.isNullOrUndefined(this.dotNetRef)){l=parseInt(i.split("_LayerIndex_")[1].split("_")[0],10);var u=parseInt(i.split("_PolygonIndex_")[1].split("_")[0],10);this.dotNetRef.invokeMethodAsync("TriggerShapeTooltip",i,n.x,n.y,l,-1,"Polygon",u)}if(i.indexOf("_LayerIndex_")>-1&&i.indexOf("BubbleIndex")>-1&&!sf.base.isNullOrUndefined(this.dotNetRef)){l=parseInt(i.split("_LayerIndex_")[1].split("_")[0],10),h=parseInt(i.split("_BubbleIndex_")[1].split("_")[0],10),d=parseInt(i.split("_dataIndex_")[1].split("_")[0],10),p=document.getElementById(i);this.dotNetRef.invokeMethodAsync("TriggerBubbleClick",l,h,d,i,n.x,n.y,e.layerX,e.layerY)}i.indexOf("_Legend_")>-1&&!sf.base.isNullOrUndefined(this.dotNetRef)&&(this.removeAllHighlight(),"touch"===e.pointerType&&"pointerdown"===e.type||this.dotNetRef.invokeMethodAsync("TriggerLegendClick",parseInt(i.split("_Index_")[1].split("_")[0],10)));var m=e.target.closest("#"+this.element.id+"_Tooltip_TooltipTemplate");if(-1===i.indexOf("_shapeIndex_")&&-1===i.indexOf("_MarkerIndex_")&&-1===i.indexOf("_BubbleIndex_")&&-1===i.indexOf("_PolygonIndex_")&&"Click"===this.options.tooltipDisplayMode&&sf.base.isNullOrUndefined(m)&&!sf.base.isNullOrUndefined(this.dotNetRef)&&this.dotNetRef.invokeMethodAsync("TriggerShapeTooltip","",n.x,n.y,0,0,"",0),"touch"===e.pointerType&&-1===i.indexOf("_cluster_")&&(i.indexOf("_shapeIndex_")>-1||i.indexOf("BubbleIndex")>-1||i.indexOf("_MarkerIndex_")>-1||i.indexOf("_PolygonIndex_")>-1)){l=parseInt(i.split("_LayerIndex_")[1].split("_")[0],10),h=i.indexOf("_BubbleIndex_")>-1?parseInt(i.split("_BubbleIndex_")[1].split("_")[0],10):i.indexOf("_PolygonIndex_")>-1?parseInt(i.split("_PolygonIndex_")[1].split("_")[0],10):i.indexOf("_MarkerIndex_")>-1?parseInt(i.split("_MarkerIndex_")[1].split("_")[0],10):parseInt(i.split("_shapeIndex_")[1].split("_")[0],10),d=i.indexOf("_BubbleIndex_")>-1||i.indexOf("_MarkerIndex_")>-1?parseInt(i.split("_dataIndex_")[1].split("_")[0],10):0;var f=i.indexOf("_BubbleIndex_")>-1?"Bubble":i.indexOf("_MarkerIndex_")>-1?"Marker":i.indexOf("_PolygonIndex_")>-1?"Polygon":"Shape";clearTimeout(this.tooltipCountTimer),this.dotNetRef.invokeMethodAsync("TriggerShapeTooltip",i,n.x,n.y,l,"Shape"===f?h:"Polygon"===f?-1:d,f,h);var y=this.getTooltipDuration(i);y>0&&(this.tooltipCountTimer=setTimeout((function(){sf.base.isNullOrUndefined(t.dotNetRef)||t.dotNetRef.invokeMethodAsync("TriggerShapeTooltip","",n.x,n.y,0,0,"",0)}),y))}this.renderTitleTooltip(i,n,e),this.renderLegendTitleTooltip(i,n,e),this.renderLegendTextTooltip(i,n,e),this.renderInteractiveLegend(i,e)}},a.prototype.removeCluster=function(){var e=document.querySelectorAll('[class="clusterGroup"]');if(e.length>0)for(var t=0;t<e.length;t++)document.getElementById(e[t].id).remove();else if((e=document.querySelectorAll('[class="clusterGroup e-maps-cluster"]')).length>0)for(var i=0;i<e.length;i++)document.getElementById(e[i].id).remove();for(var n=document.querySelectorAll('[class="e-maps-marker-hidden"]'),s=0;s<n.length;s++){var o=document.getElementById(n[s].id);o.style.visibility="visible",o.classList.remove("e-maps-marker-hidden")}},a.prototype.createImageUrl=function(e,t){var i='<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">'+e.outerHTML+"</svg>";return window.URL.createObjectURL(new Blob("SVG"===t?[i]:[(new XMLSerializer).serializeToString(e)],{type:"image/svg+xml"}))},a.prototype.imageExport=function(i,n,s,o){return e(this,void 0,void 0,(function(){var e;return t(this,(function(t){return e=this,[2,new Promise((function(t,a){var r=document.createElement("canvas"),l=e.element.getBoundingClientRect();r.width=l.width,r.height=l.height;var h=document.getElementById(e.element.id+"_svg"),d=e.createImageUrl(h,i),p=document.getElementById(e.element.id+"_animated_tiles").children.length,c=r.getContext("2d"),g=document.getElementById(e.element.id+"_Map_title"),u=document.getElementById(e.element.id+"_MapMargin");sf.base.isNullOrUndefined(u)||(c.fillStyle=u.getAttribute("fill"),"Tailwind"!==o&&"Bootstrap5"!==o&&"Fluent"!==o&&"Material3"!==o&&"Fluent2"!==o||"rgba(255, 255, 255, 0)"!==c.fillStyle&&"rgba(0, 0, 0, 0)"!==c.fillStyle&&"transparent"!==c.fillStyle?"TailwindDark"!==o&&"Bootstrap5Dark"!==o&&"FluentDark"!==o&&"Material3Dark"!==o&&"Fluent2Dark"!==o&&"Fluent2HighContrast"!==o||"rgba(255, 255, 255, 0)"!==c.fillStyle&&"rgba(0, 0, 0, 0)"!==c.fillStyle&&"transparent"!==c.fillStyle||(c.fillStyle="rgba(0, 0, 0, 1)"):c.fillStyle="rgba(255,255,255, 1)",c.fillRect(0,0,l.width,l.height)),sf.base.isNullOrUndefined(g)||(c.font=g.getAttribute("font-size")+" Arial",c.fillStyle=g.getAttribute("fill"),c.fillText(g.textContent,parseFloat(g.getAttribute("x")),parseFloat(g.getAttribute("y")))),c.save();var m=document.getElementById(e.element.id+"_Tile_SVG_Parent");c.rect(parseFloat(m.style.left),parseFloat(m.style.top),parseFloat(m.style.width),parseFloat(m.style.height)),c.clip(),c.fillRect(0,0,l.width,l.height),e.exportedCount=0;for(var f=[],y=0;y<p;y++){var _=document.getElementById(e.element.id+"tile"+y),b=new Image;b.crossOrigin="Anonymous",f.push(e.renderImages(r,_,c,b,p,i,n,d,e,s)),b.src=_.children[0].getAttribute("src")}Promise.all(f).then((function(e){for(var i=0;i<e.length;i++)if(null!=e[i]){t(e[i]);break}}))}))]}))}))},a.prototype.renderImages=function(e,t,i,n,s,o,a,r,l,h){return new Promise((function(d,p){n.onload=function(){l.exportedCount++,i.setTransform(1,0,0,1,parseFloat(t.style.left)+10,parseFloat(t.style.top)+parseFloat(document.getElementById(l.element.id+"_tile_parent").style.top));if(i.drawImage(n,0,0,257,257),l.exportedCount===s){var p=document.getElementById(l.element.id+"_Tile_SVG_Parent");r=l.createImageUrl(p.children[0],o);var c=new Image;c.onload=function(){if(i.setTransform(1,0,0,1,parseFloat(p.style.left),parseFloat(p.style.top)),i.drawImage(c,0,0),h)l.triggerDownload(o,a,e.toDataURL("image/png").replace("image/png","image/octet-stream")),d(null);else{var t="JPEG"===o?e.toDataURL("image/jpeg"):"PNG"===o?e.toDataURL("image/png"):"";d(t)}},c.src=r}else d(null)}}))},a.prototype.triggerDownload=function(e,t,i){var n=document.createElement("a");n.download=t+"."+e.toLocaleLowerCase(),n.href=i,n.click()},a.prototype.getMousePosition=function(e,t){var i=this.element.getBoundingClientRect(),s=this.element.ownerDocument.defaultView.pageXOffset,o=this.element.ownerDocument.defaultView.pageYOffset,a=this.element.ownerDocument.documentElement.clientTop,r=this.element.ownerDocument.documentElement.clientLeft;return this.positionX=i.left+s-r,this.positionY=i.top+o-a,new n(e-this.positionX,t-this.positionY)},a.prototype.getBound=function(e){var t=document.getElementById(e);return{x:t.offsetLeft,y:t.offsetTop}},a.prototype.createToolbarStyle=function(e,t){var i=document.getElementById(this.element.id+"_Zooming_ToolBar_Zoom_Path");sf.base.isNullOrUndefined(i)||i.setAttribute("stroke",this.toolbarColor),this.enableSelectionZoom||(this.zoomClick=!1);var n=document.getElementById(this.element+"_zoomToolbar_style");n&&n.parentNode.removeChild(n);var s=".e-maps-toolbar:hover { opacity:1; } .e-maps-toolbar:hover > circle { stroke:"+e+"; } .e-maps-toolbar:hover > path { fill: "+e+" ;  stroke: "+e+"; }.e-maps-toolbar:hover { cursor: pointer; } .e-maps-cursor-disable:hover { cursor: not-allowed; } .e-maps-panning:hover { cursor: pointer; } .e-maps-popup-close { display: block; opacity: 0; }",o=document.createElement("style");o.id=this.element+"_zoomToolbar_style",o.appendChild(document.createTextNode(s)),t.appendChild(o)},a.prototype.render=function(){this.wireEvents()},a}();return{getMarker:function(e,t,i){var n=window.sfBlazor.getCompInstance(i);n&&n.element&&n.clusterExpand(e,t)},markerCluster:function(e){var t=window.sfBlazor.getCompInstance(e);t&&t.element&&t.clusterMarkerProcess()},clusterSeperation:function(e){var t=window.sfBlazor.getCompInstance(e);t&&t.element&&t.mergeSeparation()},itemHighlight:function(e,t,i,n,s,o){var a=window.sfBlazor.getCompInstance(o);a&&a.element&&a.itemHighlightHandle(e,t,i,n,s)},shapeHighlight:function(e,t,i,n){var s=window.sfBlazor.getCompInstance(n);s&&s.element&&s.shapeHighlightHandle(e,t,i)},panDirection:function(e,t,i,n,s,o){var a=window.sfBlazor.getCompInstance(o);if(a&&a.element){var r=document.getElementById(a.element.id+"_animated_tiles");a.isTileMap&&!sf.base.isNullOrUndefined(r)&&(a.currentTiles=r.cloneNode(!0)),a.panning(e,t,i,n,s,!1),sf.base.isNullOrUndefined(a.dotNetRef)||a.dotNetRef.invokeMethodAsync("UpdateTranslatePoint",a.isTileMap?a.translatePoint:a.shapeTranslatePoint,a.tileTranslatePoint,a.scaleFactor,!1)}},initialize:function(e,t,i){return new a(e.id,e,t,i).render(),this.getElementSize(e.id)},updateInstance:function(e,t){var i=window.sfBlazor.getCompInstance(e);i&&i.element&&(i.dataSource=t.dataSource?JSON.parse(t.dataSource):null,i.options.layerHighlightSettings=JSON.parse(t.layerHighlightSettings),i.options.markerHighlightSettings=JSON.parse(t.markerHighlightSettings),i.options.bubbleHighlightSettings=JSON.parse(t.bubbleHighlightSettings),i.options.navigationHighlightSettings=JSON.parse(t.navigationHighlightSettings),i.options.polygonHighlightSettings=JSON.parse(t.polygonHighlightSettings),i.options.selectionSettings=JSON.parse(t.selectionSettings))},getElementSize:function(e){var t,i,n,s,o=document.getElementById(e);if(null!==o){var a=o.getBoundingClientRect();t=a.width,i=a.height,n=a.x,s=a.y}return{width:t,height:i,isIE:sf.base.Browser.isIE,x:n,y:s,isDevice:sf.base.Browser.isDevice}},getElementBounds:function(e){var t=document.getElementById(e);if(t){var i=t.getBoundingClientRect();return{width:i.width,height:i.height,top:i.top,bottom:i.bottom,left:i.left,right:i.right}}return null},getBound:function(e){var t=document.getElementById(e);return{x:t.offsetLeft,y:t.offsetTop}},getBoundData:function(e){var t=document.getElementById(e);return{x:t.getBoundingClientRect().x,y:t.getBoundingClientRect().y}},getTileZoom:function(i,n,s,o,a,r,l){return e(this,void 0,void 0,(function(){var e,h,d,p,c,g,u,m,f,y,_;return t(this,(function(t){switch(t.label){case 0:e=document.getElementById(i+"_animated_tiles"),h=parseInt(e.className,10),e.children[l].setAttribute("style","top:"+s+"; height:"+a+";width:"+r+"; left:"+n+"; position:absolute;"),e.children[l].innerHTML="",(d=document.createElement("img")).src=o,e.children[l].appendChild(d),p=document.getElementById(i+"_LayerCollections"),c=0,t.label=1;case 1:if(!(c<p.children.length))return[3,8];g=0,t.label=2;case 2:if(!(g<p.children[c].childElementCount))return[3,7];if(!(p.children[c].children[g].id.indexOf("MarkerGroup")>-1))return[3,6];u=0,t.label=3;case 3:return u<p.children[c].children[g].childElementCount?(m=parseInt(p.children[c].children[g].children[u].id.split("_LayerIndex_")[1].split("_")[0],10),f=parseInt(p.children[c].children[g].children[u].id.split("_MarkerIndex_")[1].split("_")[0],10),y=parseInt(p.children[c].children[g].children[u].id.split("_dataIndex_")[1].split("_")[0],10),[4,this.dotNetRef.invokeMethodAsync("ZoomMarker",m,f,y,0,0,h)]):[3,6];case 4:_=t.sent(),p.children[c].children[g].children[u].setAttribute("transform","translate("+_[0]+","+_[1]+")"),t.label=5;case 5:return u++,[3,3];case 6:return g++,[3,2];case 7:return c++,[3,1];case 8:return[2,l]}}))}))},getLayer:function(e){var t=document.getElementById(e+"_LayerCollections").getBoundingClientRect();return{Bottom:t.bottom,Height:t.height,Left:t.left,Right:t.right,Top:t.top,Width:t.width,X:t.x,Y:t.y}},getElement:function(e){var t=document.getElementById(e).getBoundingClientRect();return{Bottom:t.bottom,Height:t.height,Left:t.left,Right:t.right,Top:t.top,Width:t.width,X:t.x,Y:t.y}},getSvg:function(e){var t=document.getElementById(e+"_svg").getBoundingClientRect();return{Bottom:t.bottom,Height:t.height,Left:t.left,Right:t.right,Top:t.top,Width:t.width,X:t.x,Y:t.y}},updateTileTranslatePoint:function(e,t,i,n,s,o,a,r,l,h,d,p){var c=window.sfBlazor.getCompInstance(p);c&&c.element&&(c.tileTranslatePoint={x:e.x,y:e.y},c.translatePoint={x:t.x,y:t.y},c.areaRect={x:i,y:n,width:s,height:o},c.marginLeft=a,c.marginTop=r,c.labelCollection=JSON.parse(h),c.dataLabelTemplateCollection=JSON.parse(d),c.tileZoomLevel=c.options.factor=c.scaleFactor=l,(l===c.options.maxZoom||l>1||1===l)&&""===c.previousId&&(sf.base.Browser.isDevice&&c.setToolbarButtonColor(c.tileZoomLevel,p+"_Zooming_"),l===c.options.maxZoom&&document.getElementById(c.id+"_Zooming_ToolBar_Zoom_Rect")&&(document.getElementById(c.id+"_Zooming_ToolBar_Zoom_Path").setAttribute("fill",c.toolbarColor),document.getElementById(c.id+"_Zooming_ToolBar_Zoom_Path").setAttribute("stroke",c.toolbarColor),c.isPan=!1)),c.isTileMap&&c.previousId.indexOf("_Reset_")>-1&&(c.options.enablePanning||document.getElementById(c.id+"_Zooming_ToolBar_Pan_Rect")&&(document.getElementById(c.id+"_Zooming_ToolBar_Pan_Path").setAttribute("fill",c.toolbarColor),document.getElementById(c.id+"_Zooming_ToolBar_Pan_Path").setAttribute("stroke",c.toolbarColor)),c.enableSelectionZoom=!c.enableSelectionZoom&&c.enableSelectionZoom),c.isPan=!!(c.previousId.indexOf("_Reset_")>-1&&c.options.enablePanning)||c.isPan)},updateTranslatePoint:function(e,t,i,n,s,o,a,r,l,h,d){var p=window.sfBlazor.getCompInstance(d);p&&p.element&&(p.shapeTranslatePoint=e,p.areaRect={x:t,y:i,width:n,height:s},p.marginLeft=o,p.marginTop=a,p.options.factor=p.scaleFactor=r,p.labelCollection=JSON.parse(l),p.dataLabelTemplateCollection=JSON.parse(h))},updateZoomSettings:function(e,t){var i=window.sfBlazor.getCompInstance(e);i&&i.element&&(i.options.enableZoom=t.enable,i.options.zoomOnClick=t.zoomOnClick,i.options.doubleClickZoom=t.doubleClickZoom,i.options.enablePanning=t.enablePanning,i.options.enablePinchZooming=t.pinchZooming,i.options.enableSelectionZooming=t.enableSelectionZooming,i.options.enableMouseWheelZoom=t.mouseWheelZoom)},updateMarkerCollection:function(e,t){var i=window.sfBlazor.getCompInstance(e);i&&i.element&&(i.dataSource=JSON.parse(t.markerDataSource))},updateCollection:function(e,t){var i=window.sfBlazor.getCompInstance(e);i&&i.element&&(i.options.layerHighlightSettings=JSON.parse(t.layerHighlightSettings),i.options.markerHighlightSettings=JSON.parse(t.markerHighlightSettings),i.options.navigationHighlightSettings=JSON.parse(t.navigationHighlightSettings),i.options.polygonHighlightSettings=JSON.parse(t.polygonHighlightSettings),i.bubbleCollection=JSON.parse(t.bubble),i.labelCollection=JSON.parse(t.dataLabel),i.dataLabelTemplateCollection=JSON.parse(t.dataLabelTemplate),i.markerSettings=JSON.parse(t.markerSettings),i.geometryType=JSON.parse(t.geometryType),i.layerTooltipSettings=JSON.parse(t.layerTooltipSettings),i.allowMarkerDragStartEvent=t.allowMarkerDragStartEvent,i.navigation=JSON.parse(t.navigationLine),i.polygon=JSON.parse(t.polygon),i.markerClusterCollection=JSON.parse(t.markerCluster),i.baseMapBounds={latitudeMax:t.latitudeMax,latitudeMin:t.latitudeMin,longitudeMax:t.longitudeMax,longitudeMin:t.longitudeMin,minBounds:t.minBounds,maxBounds:t.maxBounds,availableSize:t.availableSize,mapsAreaRect:t.mapsAreaRect},i.isTileMap=t.isTileMap,i.legendSettings=JSON.parse(t.legendSettings),i.legendDetails=null!=t.legendDetails?JSON.parse(t.legendDetails):null,i.legendFill=null!=t.legendFill?JSON.parse(t.legendFill):null,i.legendShapeCollection=null!=t.legendShapeCollection?JSON.parse(t.legendShapeCollection):null,i.urlTemplate=t.urlTemplate,i.dataSource=JSON.parse(t.markerDataSource),i.polygonDataSource=JSON.parse(t.polygonDataSource),i.toolbarSelection=t.toolbarSelection,i.toolbarColor=t.toolbarColor,i.mapsBorderWidth=t.mapsBorder,i.toolbarButtonOpacity=t.toolbarButtonOpacity,i.toolbarShapeOpacity=t.toolbarShapeOpacity,i.removeAllHighlight())},updateLayerHighlight:function(e,t,i,n){var s=window.sfBlazor.getCompInstance(n);s&&s.element&&(i?(s.options.layerHighlightSettings[e].BorderColor=t.borderColor,s.options.layerHighlightSettings[e].BorderWidth=t.borderWidth,s.options.layerHighlightSettings[e].BorderOpacity=t.borderOpacity):(s.options.layerHighlightSettings[e].Fill=t.fill,s.options.layerHighlightSettings[e].Opacity=t.opacity,s.options.layerHighlightSettings[e].Enable=t.enable))},updateMarkerHighlight:function(e,t,i,n,s){var o=window.sfBlazor.getCompInstance(s);o&&o.element&&(n?(o.options.markerHighlightSettings[e][t].BorderColor=i.borderColor,o.options.markerHighlightSettings[e][t].BorderWidth=i.borderWidth,o.options.markerHighlightSettings[e][t].BorderOpacity=i.borderOpacity):(o.options.markerHighlightSettings[e][t].Fill=i.fill,o.options.markerHighlightSettings[e][t].Opacity=i.opacity,o.options.markerHighlightSettings[e][t].Enable=i.enable))},updateBubbleHighlight:function(e,t,i,n,s){var o=window.sfBlazor.getCompInstance(s);o&&o.element&&(n?(o.options.bubbleHighlightSettings[e][t].BorderColor=i.borderColor,o.options.bubbleHighlightSettings[e][t].BorderWidth=i.borderWidth,o.options.bubbleHighlightSettings[e][t].BorderOpacity=i.borderOpacity):(o.options.bubbleHighlightSettings[e][t].Fill=i.fill,o.options.bubbleHighlightSettings[e][t].Opacity=i.opacity,o.options.bubbleHighlightSettings[e][t].Enable=i.enable))},updateTooltipDisplayMode:function(e,t){var i=window.sfBlazor.getCompInstance(e);i&&i.element&&(i.options.tooltipDisplayMode=t)},updateNavigationHighlight:function(e,t,i,n,s){var o=window.sfBlazor.getCompInstance(s);o&&o.element&&(n?(o.options.navigationHighlightSettings[e][t].BorderColor=i.borderColor,o.options.navigationHighlightSettings[e][t].BorderWidth=i.borderWidth,o.options.navigationHighlightSettings[e][t].BorderOpacity=i.borderOpacity):(o.options.navigationHighlightSettings[e][t].Fill=i.fill,o.options.navigationHighlightSettings[e][t].Opacity=i.opacity,o.options.navigationHighlightSettings[e][t].Enable=i.enable))},updatePolygonHighlight:function(e,t,i,n){var s=window.sfBlazor.getCompInstance(n);s&&s.element&&(i?(s.options.polygonHighlightSettings[e].BorderColor=t.borderColor,s.options.polygonHighlightSettings[e].BorderWidth=t.borderWidth,s.options.polygonHighlightSettings[e].BorderOpacity=t.borderOpacity):(s.options.polygonHighlightSettings[e].Fill=t.fill,s.options.polygonHighlightSettings[e].Opacity=t.opacity,s.options.polygonHighlightSettings[e].Enable=t.enable))},exportToImage:function(i,n,s,o,a){return e(this,void 0,void 0,(function(){var r,l,h=this;return t(this,(function(d){switch(d.label){case 0:return(l=window.sfBlazor.getCompInstance(o))&&l.element?[4,l.imageExport(i,n,s,a)]:[3,2];case 1:r=d.sent(),d.label=2;case 2:return r instanceof Promise?[4,r.then((function(i){return e(h,void 0,void 0,(function(){return t(this,(function(e){return[2,i]}))}))}))]:[3,4];case 3:return d.sent(),[3,5];case 4:return[2,r];case 5:return[2]}}))}))},getLegendRect:function(e,t){var i=document.getElementById(e).getBoundingClientRect(),n=e.split("_Legend_")[0],s=document.getElementById(n).getBoundingClientRect();return{x:i.left-s.left,y:i.top-s.top,width:i.width,height:i.height}},markerAnimation:function(e,t){var i=window.sfBlazor.getCompInstance(e);i&&i.element&&i.markerAnimation(e,t)},layerAnimation:function(e,t,i,n,s,o){var a=window.sfBlazor.getCompInstance(e);if(a&&a.element)for(var r=document.getElementById(e+"_LayerIndex_0"),l=0;l<r.children.length;l++)null!==r.children[l].getAttribute("transform")&&this.zoomAnimation(r.children[l],0,o,n,s,t,i,a)},slope:function(e,t){return e.x===t.x?null:(t.y-e.y)/(t.x-e.x)},interception:function(e,t){return null===t?e.x:e.y-t*e.x},zoomAnimation:function(e,t,i,n,s,o,a,r){var l=0,h={x:n.x,y:n.y},d=s-a,p={x:0,y:0},c=1;if(s!==a){var g=this.slope(o,h),u=this.interception(o,g),m=h.x-o.x,f=h.y-o.y;e.style.removeProperty("visibility"),this.animate(e,i,(function(e){e.timeStamp>e.delay&&(l=(e.timeStamp-e.delay)/e.duration,c=a+l*d,p.x=o.x+l*m/(c/s),sf.base.isNullOrUndefined(g)?p.y=o.y+l*f:p.y=g*p.x+u,r.applyTransform(c,p))}),(function(t){r.applyTransform(s,h),this.markerCluster(r.id),sf.base.isNullOrUndefined(this.dotNetRef)||this.dotNetRef.invokeMethodAsync("TriggerAnimation",e)}))}else e.setAttribute("transform","scale( "+s+" ) translate( "+h.x+" "+h.y+" )")},bubbleAnimation:function(e,t){var i=window.sfBlazor.getCompInstance(e);i&&i.element&&i.bubbleAnimation(e,t)},animate:function(e,t,i,n){var s,o=this,a=null;s=window.requestAnimationFrame((function r(l){a||(a=l);var h=l-a;h<t?(i.call(o,{element:e,delay:0,timeStamp:h,duration:t}),window.requestAnimationFrame(r)):(window.cancelAnimationFrame(s),n.call(o,{element:e}),e.setAttribute("style","visibility:visible"))}))},getToolbarAlign:function(e,t){var i,n,s=t.verticalAlignment,o=t.horizontalAlignment,a=document.getElementById(e+"_Zooming_KitCollection");sf.base.isNullOrUndefined(a)||(i=a.getBoundingClientRect());var r=document.getElementById(e+"_ToolBar");sf.base.isNullOrUndefined(r)||(n=r.getBoundingClientRect());var l=0,h=0;switch(s){case 0:h=t.y;break;case 1:h=t.height/2-i.height/2+t.mapsBorder;break;case 2:h=t.height-i.height+t.mapsBorder-2*t.buttonPadding}switch(o){case 0:l=t.x;break;case 1:l=t.width/2-(sf.base.isNullOrUndefined(i)?0:0===i.width?n.width:i.width)/2+t.mapsBorder;break;case 2:l=t.width-(sf.base.isNullOrUndefined(i)?0:0===i.width?n.width:i.width)-2*t.buttonPadding+t.mapsBorder}var d=document.getElementById(e+"_ToolBar");d.style.left=l+"px",d.style.top=h+"px",d.style.visibility="visible";var p=t.color,c=window.sfBlazor.getCompInstance(e);c&&c.element&&c.createToolbarStyle(p,d)},getTemplate:function(e,t,i,n,s,o,a,r){for(var l=e[0],h=Object.keys(l),d=t,p=0;p<h.length;p++)"latitude"!==h[p].toLowerCase()&&"longitude"!==h[p].toLowerCase()&&(d=d.replace(new RegExp("{{:"+h[p]+"}}","g"),l[h[p].toString()]));var c=document.createElementNS("http://www.w3.org/2000/svg","div");c.setAttribute("id",s),c.style.position="absolute",c.style.transform="translate(-50 %, -50 %)",c.style.left=n.toString(),c.style.top=i.toString(),c.innerHTML=d.toString();var g=document.getElementById(r+"_LayerIndex_0_Markers_Template_Group");g.appendChild(c);var u=document.getElementById(r+"_Secondary_Element");return u.appendChild(g),document.getElementById(r).appendChild(u),d},dispose:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.destroy()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfmaps');})})();