/*!*  filename: sf-colorpicker.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[16],{"./bundles/sf-colorpicker.js":function(e,t,s){"use strict";s.r(t);s("./modules/sf-colorpicker.js")},"./modules/sf-colorpicker.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.ColorPicker=function(){"use strict";var e=function(){function e(e,t,s,n){this.dataId=e,this.element=t,this.dotnetRef=s,this.inline=n,window.sfBlazor.setCompInstance(this),n&&(this.addScrollEvents(!0),this.setPaletteWidth(this.element.querySelector(".e-container"),!1))}return e.prototype.getOffset=function(e){var t=e.querySelector(".e-hsv-container");if(!t)return{IsDevice:sf.base.Browser.isDevice};var s=t.getBoundingClientRect(),n=t.querySelector(".e-hsv-color"),i=t.querySelector(".e-handler");return i&&i.focus(),{Left:s.left,Top:s.top,Width:s.width,Height:s.height,Right:s.right,ClientLeft:n.offsetLeft,ClientTop:n.offsetTop,ClientWidth:n.offsetWidth,ClientHeight:n.offsetHeight,IsDevice:sf.base.Browser.isDevice}},e.prototype.setOffset=function(e,t){var s=this.getOffset(e);t&&!sf.base.Browser.isDevice&&(this.setZIndex(this.element,t,".e-split-colorpicker"),this.setZIndex(this.element,t,".e-dropdown-btn")),s&&this.dotnetRef.invokeMethodAsync("SetOffset",s)},e.prototype.setZIndex=function(e,t,s){var n=e.querySelector(s);n&&(n.style.zIndex=t+1+"")},e.prototype.removeZIndex=function(){var e=this.element.querySelector(".e-split-colorpicker"),t=this.element.querySelector(".e-dropdown-btn");e&&(e.style.zIndex=""),t&&(t.style.zIndex="")},e.prototype.setPaletteWidth=function(e,t,s){if(sf.base.Browser.isDevice&&!sf.base.isNullOrUndefined(s)){var n=e.querySelector(".e-colorpicker.e-modal");n&&(n.style.zIndex=s-1+"",document.body.insertBefore(n,e.parentElement),n.style.display="")}if(e.querySelector(".e-hsv-container"))e.style.width="";else{var i=parseInt(getComputedStyle(e).borderBottomWidth,10);e.style.width=e.children[0].offsetWidth+i+i+"px";var o=e.querySelector(".e-selected-value");o&&(o.style.width="100%",(o=e.querySelector(".e-switch-ctrl-btn"))&&(o.style.width="100%"))}if(t){var l=window.sfBlazor.getCompInstance(this.dataId);l.element.querySelector(".sf-colorpicker")&&sf.base.Browser.isDevice&&(l=window.sfBlazor.getCompInstance(l.element.parentElement.getAttribute("dataid")))&&l.setPosition(-1===l.element.id.indexOf("imageEditor"))}},e.prototype.scrollHandler=function(e){this.element.parentElement?this.setOffset(this.element):sf.base.EventHandler.remove(e.target,"scroll",this.scrollHandler)},e.prototype.addScrollEvents=function(e){for(var t=0,s=sf.popups.getScrollableParent(this.element);t<s.length;t++){var n=s[t];e?sf.base.EventHandler.add(n,"scroll",this.scrollHandler,this):sf.base.EventHandler.remove(n,"scroll",this.scrollHandler)}e?sf.base.EventHandler.add(this.element,"keydown",this.paletteKeyDown,this):sf.base.EventHandler.remove(this.element,"keydown",this.paletteKeyDown)},e.prototype.focusIn=function(e,t){if(t){var s=e.querySelector(".e-container");s&&(s.classList.contains("e-color-palette")?this.setFocus(".e-palette"):this.setFocus(".e-handler"))}else this.setFocus(".e-split-colorpicker")},e.prototype.setFocus=function(e){var t=this.element.querySelector(e);if(this.element&&this.element.parentElement){var s=window.sfBlazor.getCompInstance(this.element.parentElement.getAttribute("dataid"));s&&s.element.classList.contains("e-dropdown-btn")&&(t=s.element)}t&&t.focus()},e.prototype.paletteKeyDown=function(e,t){var s=e.target;if(s.classList.contains("e-palette")||s.classList.contains("e-tile")){var n=void 0,i=void 0,o=void 0;s.classList.contains("e-tile")&&"SPAN"===s.tagName&&(s=s.offsetParent.querySelector(".e-palette"));var l=[].slice.call(sf.base.selectAll(".e-tile",s)),r=l.filter((function(e){return e.classList.contains("e-selected")})).pop(),a=s.parentElement;switch(!e.altKey&&e.keyCode){case 39:e.preventDefault(),o=(n=r?l[this.tilePosition(l,r,t?-1:1)]:l[t?l.length-1:0]).getAttribute("aria-label"),this.keySelectionChanges(n,a,o);break;case 37:e.preventDefault(),o=(n=r?l[this.tilePosition(l,r,t?1:-1)]:l[t?0:l.length-1]).getAttribute("aria-label"),this.keySelectionChanges(n,a,o);break;case 38:e.preventDefault(),o=(n=l[i=r?this.tilePosition(l,r,-10):0]?l[i]:l[i-10]).getAttribute("aria-label"),this.keySelectionChanges(n,a,o);break;case 40:e.preventDefault(),l[i=r?this.tilePosition(l,r,10):l.length-1]?n=l[i]:(i%=l.length,n=l[i+=l[l.length-1].parentElement.childElementCount]),o=n.getAttribute("aria-label"),this.keySelectionChanges(n,a,o);break;case 13:if(e.preventDefault(),r){r.getAttribute("aria-label");this.dotnetRef.invokeMethodAsync("Close"),this.dotnetRef.invokeMethodAsync("Focus")}}}},e.prototype.tilePosition=function(e,t,s){var n=(e=Array.prototype.slice.call(e)).length,i=10-e[n-1].parentElement.childElementCount,o=e.indexOf(t);return(o+=s)<0?o+=n+i:o%=n+i,o},e.prototype.keySelectionChanges=function(e,t,s){this.removeTileSelection(t),this.addTileSelection(e),this.dotnetRef.invokeMethodAsync("Click",s)},e.prototype.removeTileSelection=function(e){[].slice.call(sf.base.selectAll(".e-selected",e.children[0])).forEach((function(e){e.classList.remove("e-selected"),e.setAttribute("aria-selected","false")}))},e.prototype.addTileSelection=function(e){e.classList.add("e-selected"),e.setAttribute("aria-selected","true")},e}();return{initialize:function(t,s,n,i){return sf.base.isNullOrUndefined(s)?null:(new e(t,s,n,i),window.sfBlazor.getCompInstance(t).getOffset(s))},getOffset:function(e,t){var s=window.sfBlazor.getCompInstance(e);return sf.base.isNullOrUndefined(s)?null:(s.setPaletteWidth(t,!0),s.getOffset(t))},focusIn:function(e,t,s){var n=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(n)||n.focusIn(t,s)},destroy:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.addScrollEvents(!1)}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfcolorpicker');})})();