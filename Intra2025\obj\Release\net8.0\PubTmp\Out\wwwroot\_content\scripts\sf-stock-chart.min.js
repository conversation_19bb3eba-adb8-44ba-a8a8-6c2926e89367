/*!*  filename: sf-stock-chart.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[61],{"./bundles/sf-stock-chart.js":function(e,t,o){"use strict";o.r(t);o("./modules/sf-stock-chart.js")},"./modules/sf-stock-chart.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.StockChart=function(){"use strict";var e=function(e,t,o,n){return new(o||(o=Promise))((function(i,s){function r(e){try{l(n.next(e))}catch(e){s(e)}}function a(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){e.done?i(e.value):new o((function(t){t(e.value)})).then(r,a)}l((n=n.apply(e,t||[])).next())}))},t=function(e,t){var o,n,i,s,r={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(s){return function(a){return function(s){if(o)throw new TypeError("Generator is already executing.");for(;r;)try{if(o=1,n&&(i=2&s[0]?n.return:s[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,s[1])).done)return i;switch(n=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return r.label++,{value:s[1],done:!1};case 5:r.label++,n=s[1],s=[0];continue;case 7:s=r.ops.pop(),r.trys.pop();continue;default:if(!(i=r.trys,(i=i.length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){r=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){r.label=s[1];break}if(6===s[0]&&r.label<i[1]){r.label=i[1],i=s;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(s);break}i[2]&&r.ops.pop(),r.trys.pop();continue}s=t.call(e,r)}catch(e){s=[6,e],n=0}finally{o=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}},o=function(){function e(e,t,o,n){window.sfBlazor=window.sfBlazor,this.mouseY=0,this.mouseX=0,this.eventInterval=80,this.mouseMoveRef=null,this.mouseMoveThreshold=null,this.mouseLeaveRef=null,this.documentKeyHandler=function(e){e.altKey&&74===e.keyCode&&!sf.base.isNullOrUndefined(this.element)&&this.element.focus()},this.id=t,this.element=o,this.dotnetref=n,this.dataId=e,window.sfBlazor.setCompInstance(this)}return e.prototype.render=function(){this.unWireEvents(this.id,this.dotnetref),this.wireEvents(this.id,this.dotnetref)},e.prototype.destroy=function(){this.unWireEvents(this.id,this.dotnetref)},e.prototype.unWireEvents=function(e,t){var o=document.getElementById(e);if(o){this.dotnetref=t,n.dotnetrefCollection=n.dotnetrefCollection.filter((function(t){return t.id!==e}));var i=sf.base.Browser.isPointer?"pointerleave":"mouseleave";o.removeEventListener("mousemove",this.mouseMoveRef),o.removeEventListener("touchmove",this.mouseMoveRef),sf.base.EventHandler.remove(o,i,this.mouseLeaveRef),sf.base.EventHandler.remove(document.body,"keydown",this.documentKeyHandler);var s=sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize";sf.base.EventHandler.remove(window,s,n.resizeBound)}},e.prototype.wireEvents=function(e,t){var o=document.getElementById(e);if(o){this.dotnetref=t,n.dotnetrefCollection.push({id:e,dotnetref:t});var i=sf.base.Browser.isPointer?"pointerleave":"mouseleave";this.mouseMoveRef=this.mouseMove.bind(this,t,e),this.mouseLeaveRef=this.mouseLeave.bind(this,t,e),o.addEventListener("mousemove",this.mouseMoveRef),o.addEventListener("touchmove",this.mouseMoveRef),sf.base.EventHandler.add(o,i,this.mouseLeaveRef),sf.base.EventHandler.add(document.body,"keydown",this.documentKeyHandler),n.resizeBound=n.chartResize.bind(this,n.dotnetrefCollection);var s=sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize";sf.base.EventHandler.add(window,s,n.resizeBound)}},e.prototype.getEventArgs=function(e,t){var o=e.changedTouches?e.changedTouches[0].clientX:e.clientX,n=e.changedTouches?e.changedTouches[0].clientY:e.clientY;this.setMouseXY(o,n,t);var i=e.touches,s=[];if(e.type.indexOf("touch")>-1)for(var r=0,a=i.length;r<a;r++)s.push({pageX:i[r].clientX,pageY:i[r].clientY,pointerId:e.pointerId||0});return{type:e.type,clientX:e.clientX,clientY:e.clientY,mouseX:this.mouseX,mouseY:this.mouseY,pointerType:e.pointerType,target:e.target.id,changedTouches:{clientX:e.changedTouches?e.changedTouches[0].clientX:0,clientY:e.changedTouches?e.changedTouches[0].clientY:0},touches:s,pointerId:e.pointerId}},e.prototype.setMouseXY=function(e,t,o){var n=document.getElementById(o.replace("_stockChart_chart","")+"_svg")?document.getElementById(o.replace("_stockChart_chart","")+"_svg").getBoundingClientRect():null;if(n){var i=document.getElementById(o).getBoundingClientRect();this.mouseY=t-i.top-Math.max(n.top-i.top,0),this.mouseX=e-i.left-Math.max(n.left-i.left,0)}},e.prototype.mouseMove=function(e,t,o){if(null==this.mouseMoveThreshold||(new Date).getTime()-this.mouseMoveThreshold>this.eventInterval){this.mouseMoveThreshold=(new Date).getTime();var n=void 0,i=void 0,s=void 0;"touchmove"===o.type?(this.isTouch=!0,n=(s=o).changedTouches[0].clientX,i=s.changedTouches[0].clientY,s.cancelable&&o.preventDefault()):(this.isTouch="touch"===o.pointerType||"2"===o.pointerType||this.isTouch,n=o.clientX,i=o.clientY),this.dotnetref=e,document.getElementById(t.replace("_stockChart_chart","")+"_svg")&&(this.setMouseXY(n,i,t),e.invokeMethodAsync("OnStockChartMouseMove",this.getEventArgs(o,t)))}return!1},e.prototype.mouseLeave=function(e,t,o){return this.dotnetref=e,e.invokeMethodAsync("OnStockChartMouseLeave",this.getEventArgs(o,t)),!1},e}(),n={initialize:function(e,t,n){new o(e,t.id,t,n).render()},destroy:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.destroy()},eventInterval:80,dotnetref:{},dotnetrefCollection:[],renderTooltip:function(e,t,o,n){var i=document.getElementById(t+"_svg"),s=!(i&&parseInt(i.getAttribute("opacity"),10)>0),r=JSON.parse(e),a=window.sfBlazor.getCompInstance(n);s&&!sf.base.isNullOrUndefined(a)?(a.tooltip=new sf.svgbase.Tooltip(r),a.tooltip.appendTo("#"+t)):sf.base.isNullOrUndefined(a.tooltip)||(a.tooltip.location=new sf.svgbase.TooltipLocation(r.location.x,r.location.y),a.tooltip.content=r.content,a.tooltip.header=r.header,a.tooltip.offset=r.offset,a.tooltip.palette=r.palette,a.tooltip.shapes=r.shapes,a.tooltip.data=r.data,a.tooltip.template=r.template,a.tooltip.textStyle.color=r.textStyle.color||a.tooltip.textStyle.color,a.tooltip.textStyle.fontFamily=r.textStyle.fontFamily||a.tooltip.textStyle.fontFamily,a.tooltip.textStyle.fontStyle=r.textStyle.fontStyle||a.tooltip.textStyle.fontStyle,a.tooltip.textStyle.fontWeight=r.textStyle.fontWeight||a.tooltip.textStyle.fontWeight,a.tooltip.textStyle.opacity=r.textStyle.opacity||a.tooltip.textStyle.opacity,a.tooltip.textStyle.size=r.textStyle.size||a.tooltip.textStyle.size,a.tooltip.isNegative=r.isNegative,a.tooltip.clipBounds=new sf.svgbase.TooltipLocation(r.clipBounds.x,r.clipBounds.y),a.tooltip.arrowPadding=r.arrowPadding,a.tooltip.dataBind())},fadeOut:function(e){var t=window.sfBlazor.getCompInstance(e);if(!(sf.base.isNullOrUndefined(t)||!sf.base.isNullOrUndefined(t)&&sf.base.isNullOrUndefined(t.tooltip))){var o=document.getElementById(t.tooltip.element.id+"_svg");o&&"0"===o.getAttribute("opacity")||t.tooltip.fadeOut()}},getParentElementBoundsById:function(e){var t=document.getElementById(e);if(t&&t.parentElement){var o=t.parentElement.style.width;t.parentElement.style.width="100%";var n=t.parentElement.getBoundingClientRect(),i=window.getComputedStyle(t.parentElement),s=parseFloat(i.paddingLeft)+parseFloat(i.paddingRight),r={width:Math.max(0,n.width-s),height:"article"===t.parentElement.tagName.toLowerCase()?0:n.height||t.clientHeight||t.offsetHeight,left:n.left,top:n.top,right:n.right,bottom:n.bottom};return t.parentElement.style.width=o,r}return{width:0,height:0,left:0,top:0,right:0,bottom:0}},getElementBoundsById:function(e){var t=document.getElementById(e);if(t){var o=t.getBoundingClientRect();return{width:t.clientWidth||t.offsetWidth,height:t.clientHeight||t.offsetHeight,left:o.left,top:o.top,right:o.right,bottom:o.bottom}}return{width:0,height:0,left:0,top:0,right:0,bottom:0}},getBrowserDeviceInfo:function(){return{browserName:sf.base.Browser.info.name,isPointer:sf.base.Browser.isPointer,isDevice:sf.base.Browser.isDevice,isTouch:sf.base.Browser.isTouch,isIos:sf.base.Browser.isIos||sf.base.Browser.isIos7}},setAttribute:function(e,t,o){var n=document.getElementById(e);n&&n.setAttribute(t,o)},createTooltip:function(e,t,o,n,i){var s=document.getElementById(e),r="top:"+o.toString()+"px;left:"+n.toString()+"px;color:black !important; background:#FFFFFF !important; position:absolute;border:1px solid #707070;font-size:"+i+";border-radius:2px; z-index:1";s?(s.setAttribute("innerHTML","&nbsp;"+t+"&nbsp;"),s.setAttribute("styles",r)):(s=sf.base.createElement("div",{id:e,innerHTML:"&nbsp;"+t+"&nbsp;",styles:r}),document.body.appendChild(s))},removeElement:function(e){var t=this.getElement(e);t&&sf.base.remove(t)},onAfterStockChartPanning:function(e,t){""!==e&&window.sfBlazor.getCompInstance(e).fadeOutTooltip(!0);document.getElementById(t+"_stockChart_chart").setAttribute("cursor","auto")},exportToImage:function(e,t,o,n){var i=document.getElementById(o+"_stockChart_Title"),s=document.getElementById(o+"_svg");if(s){var r=document.createElementNS("http://www.w3.org/2000/svg","svg");r.id="stockChart_exportElement";var a=s.clientHeight,l=s.clientWidth;i&&(a+=i.clientHeight,l=Math.max(i.clientWidth,l),s.setAttribute("y",i.getAttribute("height")),r.appendChild(i.cloneNode(!0))),r.setAttribute("height",a+"px"),r.setAttribute("width",l+"px"),r.appendChild(s.cloneNode(!0)),document.body.appendChild(r);var c=window.sfExport.exportToImage(e,t,r.id,n);return document.body.removeChild(r),c}},getTooltipPosition:function(o){return e(this,void 0,void 0,(function(){return t(this,(function(e){switch(e.label){case 0:return o?[4,o.invokeMethodAsync("GetStockChartTooltipPosition")]:[3,2];case 1:return[2,e.sent()];case 2:return[2,null]}}))}))},resizeBound:{},resize:{},chartResize:function(e,t){var o=this;return this.resize&&clearTimeout(this.resize),this.resize=setTimeout((function(){for(var n=e.length,i=0;i<n;i++)e[i].dotnetref.invokeMethodAsync("OnStockChartResize",t);clearTimeout(o.resize)}),500),!1}};return n}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfstockchart');})})();