/*!*  filename: sf-barcode.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[8],{"./bundles/sf-barcode.js":function(e,t,n){"use strict";n.r(t);n("./modules/sf-barcode.js")},"./modules/sf-barcode.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Barcode=function(){"use strict";var e=function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n((function(t){t(e.value)})).then(a,s)}l((r=r.apply(e,t||[])).next())}))},t=function(e,t){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}};return{getBarcodeSize:function(e){var t=e.getBoundingClientRect(),n={};return n.Width=t.width,n.Height=t.height,n},createHtmlElement:function(e,t){var n=sf.base.createElement(e);return t&&this.setAttribute(n,t),n},setAttribute:function(e,t){for(var n=Object.keys(t),r=0;r<n.length;r++)e.setAttribute(n[r],t[n[r]])},createMeasureElements:function(){if(window.barcodeMeasureElement)window.barcodeMeasureElement.usageCount+=1;else{var e=this.createHtmlElement("div",{id:"barcodeMeasureElement",class:"barcodeMeasureElement",style:"visibility:hidden ; height: 0px ; width: 0px; overflow: hidden;"}),t=this.createHtmlElement("span",{style:"display:inline-block ; line-height: normal"});e.appendChild(t);var n=document.createElementNS("http://www.w3.org/2000/svg","svg");n.setAttribute("xlink","http://www.w3.org/1999/xlink"),e.appendChild(n);var r=document.createElementNS("http://www.w3.org/2000/svg","text");r.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),n.appendChild(r),window.barcodeMeasureElement=e,window.barcodeMeasureElement.usageCount=1,document.body.appendChild(e)}},measureText:function(e,t,n){window.barcodeMeasureElement.style.visibility="visible";var r=window.barcodeMeasureElement.children[1],i=this.getChildNode(r)[0];i.textContent=e,i.setAttribute("style","font-size:"+t+"px; font-family:"+n+";");var o=i.getBBox(),a={};return a.Width=o.width,a.Height=o.height,window.barcodeMeasureElement.style.visibility="hidden",a},getChildNode:function(e){var t,n=[];if("msie"===sf.base.Browser.info.name||"edge"===sf.base.Browser.info.name)for(var r=0;r<e.childNodes.length;r++)1===(t=e.childNodes[r]).nodeType&&n.push(t);else n=e.children;return n},checkOverlapTextPosition:function(e,t,n,r,i,o,a,s){return(s=s||{}).stringSize=t,r+i-(o+this.measureText(e,t,n).Width)<=a&&t>2&&(s.stringSize-=.2,this.checkOverlapTextPosition(e,s.stringSize,n,r,i,o,a,s)),s.stringSize},triggerDownload:function(e,t,n){var r=document.createElement("a");r.download=t+"."+e.toLocaleLowerCase(),r.href=n,r.click()},exportAsImage:function(n,r,i,o){return e(this,void 0,void 0,(function(){var e;return t(this,(function(t){switch(t.label){case 0:return[4,this.imageExport(n,r,i,o)];case 1:return(e=t.sent())instanceof Promise?(e.then((function(e){return e})),[2,e]):[2,e]}}))}))},imageExport:function(n,r,i,o){return e(this,void 0,void 0,(function(){var e;return t(this,(function(t){return e=this,[2,new Promise((function(t,a){var s="<svg xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink>"+i.children[0].outerHTML+"</svg>",l=window.URL.createObjectURL(new Blob("SVG"===n?[s]:[(new window.XMLSerializer).serializeToString(i)],{type:"image/svg+xml"}));if("SVG"===n)e.triggerDownload(n,r,l),t(null);else{var c=document.createElement("canvas");c.height=i.clientHeight,c.width=i.clientWidth;var u=c.getContext("2d"),d=new Image;d.onload=function(){if(u.drawImage(d,0,0),window.URL.revokeObjectURL(l),o){var i="JPEG"===n?c.toDataURL("image/jpeg"):"PNG"===n?c.toDataURL("image/png"):"";t(i)}else e.triggerDownload(n,r,c.toDataURL("image/png").replace("image/png","image/octet-stream")),t(null)},d.src=l}}))]}))}))}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfbarcodegenerator');})})();